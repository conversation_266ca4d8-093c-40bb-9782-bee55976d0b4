<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void
    {
        $loans = \Modules\Common\Models\Loan::all();

        if (!$loans->count()) {
            return;
        }

        /** @var \Modules\Common\Models\Loan $loan **/
        foreach ($loans as $loan) {
            /** @var \Modules\Common\Models\Product $product **/

            $product = $loan->product;

            $update = [];
            if (is_null($loan->period_approved)) {
                $update['period_approved'] = 7;
            }
            $update['interest_percent'] = (int)$product->getInterestRate($loan->period_approved ?? 7, $loan->amount_approved);
            if ($update['interest_percent'] === 0) {
                $update['interest_percent'] = 36;
            }

            $update['penalty_percent'] = (int)$product->getPenaltyRate($loan->period_approved ?? 7, $loan->amount_approved);
            if ($update['penalty_percent'] === 0) {
                $update['penalty_percent'] = 136;
            }

            $update['installment_modifier'] = $product->getInstallmentModifier();

            $loan->update($update);
        }
    }

    public function down(): void
    {
        //
    }
};
