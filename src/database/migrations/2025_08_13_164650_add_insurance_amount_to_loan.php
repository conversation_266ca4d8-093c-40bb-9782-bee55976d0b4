<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->integer('insurance_amount')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->dropColumn('insurance_amount');
        });
    }
};
