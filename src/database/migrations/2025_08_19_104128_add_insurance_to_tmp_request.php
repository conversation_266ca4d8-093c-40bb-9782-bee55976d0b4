<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('tmp_request', function (Blueprint $table) {
            $table->boolean('insurance')->nullable();
        });

        Schema::table('tmp_request_history', function (Blueprint $table) {
            $table->boolean('insurance')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('tmp_request', function (Blueprint $table) {
            $table->dropColumn('insurance');
        });

        Schema::table('tmp_request_history', function (Blueprint $table) {
            $table->dropColumn('insurance');
        });
    }
};
