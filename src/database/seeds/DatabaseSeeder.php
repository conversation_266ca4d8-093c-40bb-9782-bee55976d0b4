<?php

use Illuminate\Database\Seeder;
use Modules\Accounting\Database\Seeders\AccountingDatabaseSeeder;
use Modules\Admin\Database\Seeders\AdministratorOfficeSeeder;
use Modules\Admin\Database\Seeders\AdministratorSeeder;
use Modules\Admin\Database\Seeders\BranchSeedTableSeeder;
use Modules\Admin\Database\Seeders\OfficeCheckTableSeeder;
use Modules\Admin\Database\Seeders\OfficeSettingSeeder;
use Modules\Admin\Database\Seeders\OfficeTableSeeder;
use Modules\Admin\Database\Seeders\OfficeTypeSeedTableSeeder;
use Modules\Admin\Database\Seeders\RolePermissionSeeder;
use Modules\Admin\Database\Seeders\SettingsSeeder;
use Modules\Approve\Database\Seeders\ApproveDecisionReasonSeeder;
use Modules\Approve\Database\Seeders\ApproveDecisionSeeder;
use Modules\Collect\Database\Seeders\BucketSeeder;
use Modules\Collect\Database\Seeders\CollectorDecisionSeeder;
use Modules\Common\Database\Seeders\AgreementSeeder;
use Modules\Common\Database\Seeders\AreaSeeder;
use Modules\Common\Database\Seeders\BankSeeder;
use Modules\Common\Database\Seeders\BlockReasonSeeder;
use Modules\Common\Database\Seeders\CitySeeder;
use Modules\Common\Database\Seeders\ContactTypeSeeder;
use Modules\Common\Database\Seeders\CurrencySeeder;
use Modules\Common\Database\Seeders\DeleteReasonSeeder;
use Modules\Common\Database\Seeders\FileStorageSeeder;
use Modules\Common\Database\Seeders\FileTypeSeeder;
use Modules\Common\Database\Seeders\GuarantTypeSeeder;
use Modules\Common\Database\Seeders\IdCardIssuedSeeder;
use Modules\Common\Database\Seeders\InterestTermSeeder;
use Modules\Common\Database\Seeders\LoanStatusSeeder;
use Modules\Common\Database\Seeders\LoanTypeSeeder;
use Modules\Common\Database\Seeders\LocalDevSeeder;
use Modules\Common\Database\Seeders\MunicipalitySeeder;
use Modules\Common\Database\Seeders\OfficeEmailSmsSeederTableSeeder;
use Modules\Common\Database\Seeders\OfficeProductSeeder;
use Modules\Common\Database\Seeders\PaymentMethodSeeder;
use Modules\Common\Database\Seeders\PenaltyTermSeeder;
use Modules\Common\Database\Seeders\ProdSetMinRepayAmountSeeder;
use Modules\Common\Database\Seeders\ProductSeeder;
use Modules\Common\Database\Seeders\ProductSettingSeeder;
use Modules\Common\Database\Seeders\ProductTypeSeeder;
use Modules\Docs\Database\Seeders\DocumentTemplateSeeder;
use Modules\Docs\Database\Seeders\ProductDocumentTemplateSeeder;
use Modules\Payments\Database\Seeders\PaymentTaskDecisionSeeder;
use Modules\Sales\Database\Seeders\SaleDecisionReasonSeeder;
use Modules\Sales\Database\Seeders\SaleDecisionSeeder;
use Modules\Sales\Database\Seeders\SaleTaskTypeSeeder;

final class DatabaseSeeder extends Seeder
{

    public function run(): void
    {
        $this->call([
            AdministratorSeeder::class,
            RolePermissionSeeder::class,
            //Geography
            AreaSeeder::class,
            MunicipalitySeeder::class,
            CitySeeder::class,
            IdCardIssuedSeeder::class,
            BranchSeedTableSeeder::class,
            //Types
            OfficeTypeSeedTableSeeder::class,
            ProductTypeSeeder::class,
            FileTypeSeeder::class,
            GuarantTypeSeeder::class,
            ContactTypeSeeder::class,
            LoanTypeSeeder::class,
            SaleTaskTypeSeeder::class,
            //Decisions
            SaleDecisionSeeder::class,
            \Modules\Sales\Database\Seeders\AddNewSaleDecisionSeeder::class,
            ApproveDecisionSeeder::class,
            \Modules\Approve\Database\Seeders\AddNewApproveDecisionSeeder::class,
            CollectorDecisionSeeder::class,
            PaymentTaskDecisionSeeder::class,
            //Reasons
            SaleDecisionReasonSeeder::class,
            BlockReasonSeeder::class,
            DeleteReasonSeeder::class,
            ApproveDecisionReasonSeeder::class,
            //Listings
            FileStorageSeeder::class,
            CurrencySeeder::class,
            PaymentMethodSeeder::class,
            LoanStatusSeeder::class,
            BankSeeder::class,
            \Modules\Common\Database\Seeders\BankAccountSeeder::class,
            BucketSeeder::class,
            SettingsSeeder::class,
            //Office and product
            OfficeTableSeeder::class,
            AdministratorOfficeSeeder::class,
            \Modules\Admin\Database\Seeders\AddConsultantAKPZTableSeeder::class,
            OfficeSettingSeeder::class,
            OfficeCheckTableSeeder::class,
            OfficeEmailSmsSeederTableSeeder::class,
            ProductSeeder::class,
            OfficeProductSeeder::class,
            ProductSettingSeeder::class,
            ProdSetMinRepayAmountSeeder::class,
            ProductDocumentTemplateSeeder::class,
            InterestTermSeeder::class,
            PenaltyTermSeeder::class,
            //Templates, sources and campaigns
            DocumentTemplateSeeder::class,

            \Modules\Communication\Database\Seeders\DatabaseSeeder::class,

            AgreementSeeder::class,

            /// Accounting
            AccountingDatabaseSeeder::class,

            \Modules\Common\Database\Seeders\AffiliateSeeder::class,

            //Local
            LocalDevSeeder::class,

            /// Admin blog post seeder
            \Modules\Admin\Database\Seeders\BlogPostSeeder::class,
            \Modules\Admin\Database\Seeders\ClientRateUsSeeder::class,
            \Modules\Admin\Database\Seeders\InitOfficePaymentMethodSeeder::class
        ]);
    }
}
