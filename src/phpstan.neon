includes:
    - ./vendor/larastan/larastan/extension.neon
    - ./vendor/nesbot/carbon/extension.neon

parameters:
    # The level of analysis (0 to 9)
    level: 2

    # Paths to analyze
    paths:
        - app/
        - Modules/

    ignoreErrors:
        # 1 level errors
        - '#Access to an undefined property#' # has 1300+ problems
        - '#Relation .* is not found in#' # has 10+ problems
        # 2 level errors
        - '#Call to an undefined method Illuminate\\Database\\Schema\\ColumnDefinition::references#' # has 20+ problems
        - '#PHPDoc#' # has 300+ problems
        - '#Method Illuminate\\Filesystem\\FilesystemAdapter::makeDirectory#'
        - '#Binary operation#'
        - '#Call to an undefined method object::save#'
        - '#Cannot access property \$uneditable on array|Illuminate\\Contracts\\Pagination\\LengthAwarePaginator#'
        - '#EloquentCollectionWrapper#'
        - '#::build#'
        - '#Variable \$\w+ in empty\(\) always exists and is not falsy#'
        # LoanForUpdate update method
        - '#Call to an undefined method Modules\\Sales\\Domain\\Entities\\Loan\\NewLoan::update\(\)#'

    excludePaths:
        analyse:
            - tests
            - Modules/Api/Tests
            - Modules/Approve/Tests
            - Modules/Collect/Tests
            - Modules/Docs/Tests
            - Modules/Head/Tests
            - Modules/Payments/Tests
            - Modules/Sales/Tests
            - Modules/ThirdParty/Tests
            - Modules/ThirdParty/Libraries/Tremol
            - Modules/Core/Libraries/Scoring/StikScoring.php
            - Modules/Common/Console/Migration
