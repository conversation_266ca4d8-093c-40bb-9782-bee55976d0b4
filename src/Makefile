target?=
envFile := .env.$(target)

ifeq ($(envFile),.env.)
	envFile := .env
endif

include $(envFile)
export $(shell sed 's/=.*//' $(envFile))

appName := $(APP_NAME)_app

envFileCmd := --env-file $(envFile)
envFileEqCmd := --env-file=$(envFile)
envFCmd := -f $(envFile)
projectNameCmd := --project-name=$(target)

altFind := ./
altRepl := ../

altVolumePathPg    = $(subst $(altFind),$(altRepl),${VOLUME_PATH_PG})
altVolumePathRedis = $(subst $(altFind),$(altRepl),${VOLUME_PATH_REDIS})
docker_exec = @docker exec -i $(appName)

#a:
#	make down target=testing && make down
#	make up target=testing && make up

# create and init  project
init:
#	@echo '=== Make symlink ==='
#	@cp -n .env.example .env
#	@ln -s .env ../.env
	@make up
	@make composer-install
	@make npm-install
	@make project-init

link:
	ln -sf .editorconfig ../.editorconfig
	ln -s .env ../
	ln -sf _ide_helper.php ../_ide_helper.php
	ln -sf _ide_helper_models.php ../_ide_helper_models.php
	ln -sf .phpstorm.meta.php ../.phpstorm.meta.php

reinit:
	@make down
	@make init

reinit-testing:
	@make down target=testing
	@make init target=testing

# composer install
composer-install:
	@echo '===== INSTALL COMPOSER ====='
	@docker exec -i $(appName) composer install --no-interaction

# npm install
npm-install:
	@echo '===== INSTALL NPM ====='
	@docker exec -i $(appName) npm install
	@docker exec -i $(appName) npm run dev

# run PHPStan
phpstan:
	@docker exec -i $(appName) ./vendor/bin/phpstan analyse

# run PHPStan with debug
phpstan-debug:
	@docker exec -i $(appName) ./vendor/bin/phpstan analyse -v

# run PHPStan
phpstan-clear:
	@docker exec -i $(appName) ./vendor/bin/phpstan clear-result-cache

# get top errors from PHPStan
phpstan-top:
	@docker exec -i $(appName) ./vendor/bin/phpstan analyse --memory-limit=2G --error-format=json > storage/logs/phpstan.json || true
	@docker exec -i $(appName) php artisan phpstan:json-top --limit=25

# initialize project
project-init:
	@echo '===== INIT PROJECT ====='
	@docker exec -i $(appName) php artisan project:init

build:
	@docker compose $(envFileCmd) $(projectNameCmd) build

# build and create docker containers
up:
	@echo '===== CLEAN DATA ====='
	@sudo rm -f -R $(altVolumePathPg) $(altVolumePathRedis)
	@echo '===== CREATE DOCKER CONTAINER ====='
	@make build
	@echo '===== UP DOCKER CONTAINER ====='
	@make start

# drop containers and remove data files
down:
	@echo '===== DOCKER DOWN ====='
	@docker compose $(envFileCmd) $(projectNameCmd) down
	@echo '===== DROP DATA ====='
	@sudo rm -f -R $(altVolumePathPg) $(altVolumePathRedis)
	@echo '===== CLEAR ALL LOGS ====='
	@find ./storage -name "*.log" -exec rm -rf {} \;

# start project docker containers
start:
	@docker compose $(envFileCmd) $(projectNameCmd) up -d

# stop project docker containers
stop:
	@docker compose $(envFileCmd) $(projectNameCmd) stop

#restart project docker containers
restart:
	@make stop
	@make start

connect:
	@docker exec -it $(appName) bash

test:
	@docker exec -i credit_hunter_test_app vendor/bin/phpunit --testsuite Refactored

horizon:
	@docker exec -i $(appName) php artisan horizon

js-watch:
	@docker exec -i $(appName) npm run watch

# clean data docker
tool-cleanup:
	@docker system prune -f
	#docker container stop $(docker container ls -aq)
	#docker container rm $(docker container ls -aq) -f
	#docker rmi $(docker images -aq) -f
	@docker volume prune -f

tool-ide-helper:
	@echo '===== MAKE IDE HELPER CLASSES ====='
	@docker exec -i $(appName) php artisan clear-compiled
	@docker exec -i $(appName) php artisan ide-helper:generate
	@docker exec -i $(appName) php artisan ide-helper:meta
	@docker exec -i $(appName) php artisan ide-helper:models -M

tool-fix-rights:
	@docker exec -i $(appName) php artisan script:permissions:register

tool-gen-routes:
	@docker exec -i $(appName) php artisan gen:frontRoutes

tool-cache-clear:
	@docker exec -i $(appName) php artisan cache:clear
	@docker exec -i $(appName) php artisan config:clear
	@docker exec -i $(appName) php artisan route:clear

clear-log:
	@rm storage/logs/laravel.log

db-fresh:
	@docker exec -i $(appName) php artisan migrate:fresh --seed

db-migrate:
	@make composer-install
	@docker exec -i $(appName) php artisan migrate
