.infinteTabs {
  border-radius: 2px 2px 0 0;
  position: relative;
  width: 100%;
  overflow: hidden;
  box-sizing: content-box;
}

.infinteTabs .nav-tabs {
  border-radius: 2px 2px 0 0;
  display: table;
  margin: 0;
  white-space: nowrap;
}

.infinteTabs .nav-tabs > li {
  display: table-cell;
  float: none;
}

.infinteTabs .infinteTabs-nav {
  position: absolute;
  height: 100%;
  margin: 0;
  width: 50px;
  z-index: 1000;
  display: none;
  background-color: #fff;
  border: 1px solid #ddd;
  box-shadow: 0 0 15px #fff;
}

.infinteTabs .infinteTabs-nav.active {
  display: block;
}

.infinteTabs .infinteTabs-nav.prev {
  border-radius: 4px 0 0 0;
  left: 0;
}

.infinteTabs .infinteTabs-nav.next {
  border-radius: 0 4px 0 0;
  right: 0;
}
