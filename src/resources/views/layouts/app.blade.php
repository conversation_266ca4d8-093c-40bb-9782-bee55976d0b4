<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Stikcredit: {{ config('app.name', 'CRM') }}</title>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/png"
          href="{{ asset('images/icons/' . env('COMPANY_FAVICON', 'favicon.ico')) }}">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap"
          rel="stylesheet">

    <link rel="stylesheet" href="{{ asset('dist/css/style.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/search-user-input.css') }}">
    <link rel="stylesheet" href="{{ asset('css/notifications-style.css') }}">
    <link rel="stylesheet" href="{{ asset('css/table-style.css') }}">
    <link rel="stylesheet" href="{{ asset('css/general.css') }}">

    <link rel="stylesheet" href="{{ asset('dist/css/flatpickr.min.css') }}">
    <link rel="stylesheet" href="{{ asset('dist/css/fonts/font-awesome.min.css') }}"
          integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
    <link rel="stylesheet" href="{{ asset('fontawersome/css/all.min.css') }}"/>

    <link href="{{ asset('css/daterangepicker.css') }}" rel="stylesheet">

    <script src="{{ asset('assets/libs/jquery/dist/jquery.min.js') }}"></script>

    <!-- CRM Core - Additional Styles -->
    <link rel="stylesheet" href="{{ asset('crm/dist/styles/core.css') }}">

    <!-- CRM Core - Autoload JS Core -->
    <script src="{{ asset('crm/dist/scripts/core.js') }}"></script>
    @yield('style')
    @stack('styles')


    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="{{ asset('js/html5shiv.js') }}"></script>
    <script src="{{ asset('js/respond.min.js') }}"></script>
    <![endif]-->

    <link rel="stylesheet" href="{{ mix('css/app.css') }}"/>
    @livewireStyles
</head>
<body>
<!-- preloader area start -->
<div class="preloader">
    <div class="lds-ripple">
        <div class="lds-pos"></div>
        <div class="lds-pos"></div>
    </div>
</div>
<!-- preloader area end -->


<!-- page container area start -->
<div id="main-wrapper" data-theme="light" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
     data-sidebar-position="fixed" data-header-position="fixed" data-boxed-layout="full">

    @include('theme.header')

    @include('theme.sidebar-menu')

    <div class="page-wrapper">
        <x-flash-messages/>

        @include('theme.breadcrumb')

        <div class="container-fluid" id="right-wrapper">
            @yield('content')
        </div>

        @include('theme.footer')
    </div>
    <!-- End ./page-wrapper -->
</div>
<!-- page container area end -->

<x-delete-modal/>

<script id="disableFormButtonOnSubmit" type="text/javascript">
    $(document).on('click', '.disable-on-click', function (e) {
        e.preventDefault();
        $(this).addClass('disabled');

        if (this.hasAttribute('target') && $(this).attr('target') === '_blank') {
            window.open(this.href, '_blank');
        } else {
            window.location.replace($(this).attr('href'));
        }
    });

    function disableSubmitButtonWithClass(form) {
        let buttons = form.querySelectorAll('button[type="submit"]');
        buttons.forEach(button => {
            button.classList.add("disabled");
        });
    }

    function enableSubmitButton(form) {
        let buttons = form.querySelectorAll('button[type="submit"]');
        buttons.forEach(button => {
            if (button) {
                button.disabled = false;
                button.classList.remove("disabled");
            }
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        const forms = document.querySelectorAll("form");

        forms.forEach(form => {
            form.addEventListener("submit", function (e) {
                if (!form.checkValidity()) {
                    // Form is invalid
                    form.classList.add("was-validated"); // Optional (Bootstrap style)
                    enableSubmitButton(form); // Re-enable just in case
                    return;
                }

                disableSubmitButtonWithClass(form);
            });
        });
    });
</script>

@include('cashdesk::layouts.scriptBeforeApp')

<script src="{{ mix('js/app.js') }}"></script>
<script src="{{ asset('dist/js/feather.min.js') }}"></script>
<script src="{{ asset('assets/libs/perfect-scrollbar/dist/perfect-scrollbar.jquery.min.js') }}"></script>
<script src="{{ asset('dist/js/sidebarmenu.js') }}"></script>
<script src="{{ asset('dist/js/custom.min.js') }}"></script>
<script src="{{ asset('dist/js/app-style-switcher.js') }}"></script>
<script src="{{ asset('js/dateRangePicker.js') }}"></script>
<script src="{{ asset('dist/js/flatpickr.min.js') }}"></script>
@include('cashdesk::layouts.scriptAfterApp')

@stack('scripts')

<script>
    $(document).ready(function () {
        $("#right-wrapper").on('click', '.disableOnClick', function () {
            // Ye we need 0 timeout or form won't submit...
            setTimeout(() => {
                $(this).prop('disabled', true);
            }, 0);
        });
    });
</script>
@livewireScripts
</body>
</html>
