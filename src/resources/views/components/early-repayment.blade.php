<h4>Предсрочно погасяване</h4>
<div class="row" id="earlyRepaymentMessages"></div>
<div class="row">
    <form action="{{route('head.loans.earlyRepayment')}}" id="earlyRepaymentForm">
        @csrf
        <input type="hidden" name="loanId" value="{{$loan->getKey()}}">
        <input type="text" autocomplete="off" id="earlyRepaymentDate" name="repaymentDate" class="form-control w-100"
               placeholder="{{__('table.Date')}}">
    </form>
</div>
<div class="row">
    <div class="col-4">Главница</div>
    <div class="col-8" id="principal"></div>
</div>
<div class="row">
    <div class="col-4">Лихва</div>
    <div class="col-8" id="interest"></div>
</div>
<div class="row">
    <div class="col-4">Неустойка</div>
    <div class="col-8" id="penalty"></div>
</div>
<div class="row">
    <div class="col-4">Наказателна лихва</div>
    <div class="col-8" id="late-interest"></div>
</div>
<div class="row">
    <div class="col-4">Такси</div>
    <div class="col-8" id="taxes"></div>
</div>
<div class="row">
    <div class="col-4">Общо</div>
    <div class="col-8" id="sumEarlyRepayment"></div>
</div>
<div class="row">
    <div class="col-6">
        <form action="{{ route('head.loans.sendEarlyRepaymentSms') }}" id="earlyRepaymentSendSms">
            @csrf
            <input type="hidden" name="loanId" value="{{$loan->getKey()}}">
            <input type="hidden" name="repaymentDate">
            <input type="hidden" name="principal">
            <input type="hidden" name="interest">
            <input type="hidden" name="penalty">
            <input type="hidden" name="lateInterest">
            <input type="hidden" name="taxes">
            <input type="hidden" name="sum">

            <button type="submit" class="btn btn-info">Изпрати SMS</button>
        </form>
    </div>
    <div class="col-6">
        <button class="btn btn-info">Изпрати E-mail</button>
    </div>
</div>
@push('scripts')
    <script>
        $('#earlyRepaymentDate').daterangepicker({
            autoUpdateInput: false,
            autoApply: true,
            "singleDatePicker": true,
            locale: {
                format: 'DD.MM.YYYY',
            }
        });
        $('#earlyRepaymentDate').on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('DD.MM.YYYY'));

            let form = $("#earlyRepaymentForm");

            let data = form.serialize();

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                headers: {
                    "Accept": "application/json",
                },
                data: data,
                success: function (response) {
                    $("#principal").text(response.principal);
                    $("#interest").text(response.interest);
                    $("#penalty").text(response.penalty);
                    $("#late-interest").text(response.lateInterest);
                    $("#taxes").text(response.taxes);
                    $("#sumEarlyRepayment").text(response.sum);

                    $("#earlyRepaymentSendSms input[name=repaymentDate]").val(ev.format());
                    $("#earlyRepaymentSendSms input[name=principal]").val(response.principal);
                    $("#earlyRepaymentSendSms input[name=interest]").val(response.interest);
                    $("#earlyRepaymentSendSms input[name=penalty]").val(response.penalty);
                    $("#earlyRepaymentSendSms input[name=lateInterest]").val(response.lateInterest);
                    $("#earlyRepaymentSendSms input[name=taxes]").val(response.taxes);
                    $("#earlyRepaymentSendSms input[name=sum]").val(response.sum);
                }
            });
        });

        $("#earlyRepaymentSendSms").on('submit', function (event) {
            event.preventDefault();

            let data = $(this).serialize();

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                headers: {
                    "Accept": "application/json",
                },
                data: data,
                success: function (response) {
                    $("#earlyRepaymentMessages").text('Success!');
                },
                error: function (error) {
                    $("#earlyRepaymentMessages").text('Failure!');
                }
            });
        });
    </script>
@endpush
