<div style="margin-bottom: 20px;" class="single-guarant-container" id="guarant{{$seqNumGuarant}}" data-seqnum="{{$seqNumGuarant}}">
    <div class="sale_block_titles">
        <h4>Поръчител {{$seqNumGuarant}}</h4>
        <button 
            class="btn btn-circle-small d-flex btn-danger fa-minus fa justify-content-center guarantAdditionalDelete"
            type="button"
            id="remove{{$seqNumGuarant}}"
            name="guarant[{{$seqNumGuarant}}][delete]"
        >
        </button>
    </div>
    <div id="cstm-danger-alert-guarant" style="color: #bd2130">
        
        <div 
            id="cstm-danger"
            class="alert alert-warning bg-danger text-white alert-dismissible fade show"
            role="alert"
        >
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">×</span>
            </button>
            
            <span class="message"></span>
        </div>
    </div>

    <input type="hidden" id="guarant_type_id" value="1" name="guarant[{{$seqNumGuarant}}][guarant_type_id]"/>

    <input type="text" id="guarant_pin" class="form-control" value="{{$pin ?? ''}}"
           name="guarant[{{$seqNumGuarant}}][pin]"
           maxlength="10"
           placeholder="{{__('table.Pin')}}"
    />

    <div class="input-group-append">
        <button type="button" class="btn btn-primary rounded-pill guarant-pin-btn mb-4 searchByPinGuarant">
            {{ __('btn.SearchByPin') }}
        </button>

    </div>

    <div class="form-inline w-100 d-block">
        <input type="text" class="form-control w-100" value="{{$phone ?? ''}}"
               name="guarant[{{$seqNumGuarant}}][phone]"
               placeholder="{{__('table.Phone')}}"
               id="guarant_phone"
        />

    </div>

    <input type="text" class="form-control"
           value="{{$idCardNumber ?? ''}}"
           id="guarant_idcard_number"
           maxlength="10"
           name="guarant[{{$seqNumGuarant}}][idcard_number]"
           placeholder="{{ __('table.NumberIdCard') }}"
    />

    <button type="button" class="btn btn-primary btn-rounded mb-4 guarant-mvr-check"
            id="guarantMvrCheck" data-container-id="guarant{{$seqNumGuarant}}">{{ __('btn.CheckMVR') }}</button>

    <input type="text" class="form-control" value="{{$nameGuarant ?? ''}}"
           name="guarant[{{$seqNumGuarant}}][first_name]"
           id="guarant_first_name"
           placeholder="{{__('table.Name')}}"
    />

    <input type="text" class="form-control" value="{{$middleName ?? ''}}"
           name="guarant[{{$seqNumGuarant}}][middle_name]"
           id="guarant_middle_name"
           placeholder="{{__('table.MiddleName')}}"
    />

    <input type="text" class="form-control" value="{{$lastName ?? ''}}"
           id="guarant_last_name"
           name="guarant[{{$seqNumGuarant}}][last_name]"
           placeholder="{{__('table.LastName')}}"
    />

    {{--    <input class="form-control" type="text"--}}
    {{--           name="guarant[{{$seqNumGuarant}}][issued_by]"--}}
    {{--           value="{{$issueDate ?? ''}}"--}}
    {{--           id="guarant_issue_by"--}}
    {{--           placeholder="{{__('table.IdCardIssuedBy')}}">--}}

    <select class="form-control form-inline no-read-only mb-2"
            name="guarant[{{$seqNumGuarant}}][idcard_issued_id]"
            id="guarant_idcard_issued_id">

        @foreach($idCardIssuedTypes as $idCardIssuedType)
            <option
                value="{{ $idCardIssuedType->idcard_issued_id}}">
                {{ $idCardIssuedType->name}}
            </option>
        @endforeach
    </select>


    <textarea class="form-control mb-4" type="text" id="guarant_address" name="guarant[{{$seqNumGuarant}}][address]"
              placeholder="{{__('table.Address')}}">{{$address ?? ''}}</textarea>

    <div class="d-flex align-items-baseline">
        <label>Добави настоящ адрес</label>
        <button class="btn btn-primary btn-circle-small btn-circle-blue d-flex
        fa fa-plus justify-content-center ml-2 person-card-input-button person-card-input-button-add-current-address-guarant" type="button"
                id="currentGuarantAddressBtn"
                data-toggle="collapse"
                data-target="#collapseGuarant{{$seqNumGuarant}}" aria-expanded="false"
                aria-controls="collapseGuarant{{$seqNumGuarant}}">
        </button>
    </div>

<div class="collapse" id="collapseGuarant{{$seqNumGuarant}}">
    <div class="card">
        <div class="card-body">
            <div class="form-group">
                <x-current-address-guarant
                    addressName="guarant[{{$seqNumGuarant}}][currentAddress][address]"
                    address=" "
                    :cities="$cities"
                    seqNumGuarant="{{$seqNumGuarant}}"
                    citiesName="guarant[{{$seqNumGuarant}}][currentAddress][city_id]"
                    citiesId="guarant[{{$seqNumGuarant}}][currentAddress][city_id]"
                    selectedCityId=" "
                />
            </div>
        </div>
    </div>
</div>
</div>


@if ($seqNumGuarant - 1 === 0)

    @push('scripts')
    <script>
        function searchByPinGuarant (element) {
            let elementId = element.attr('id');
            elementId = '#' + elementId;
            let guarantPin = $(elementId + ' #guarant_pin').val();
            let searchByGuarantPinErrorMsg = '{{ __('messages.SearchByClientPinLength') }}';

            const fieldIdPrefix = '#guarant_';

            $('#cstm-danger-alert-guarant .message').text('');

            if (guarantPin.length < 10) {
                $(elementId + ' #cstm-danger-alert-guarant').show('slow');
                $(elementId + ' #cstm-danger-alert-guarant .message').html(searchByGuarantPinErrorMsg);

                setTimeout(
                    function () {
                        $(' #cstm-danger-alert-guarant').hide('slow');
                    },
                    5000
                );
            } else {
                $.ajax({
                    url: '{{route('head.clients.guarantPrefill')}}',
                    method: 'GET',
                    data: {guarantPin: guarantPin},
                    dataType: 'json',
                    success: function (data) {
                        if (data.error) {
                            $("#cstm-danger-alert").html(data.error);
                            return;
                        }

                        const fillableData = (
                            data.guarant[0] ?
                            data.guarant[0] :
                            (
                                data.client[0] ?
                                data.client[0] :
                                null
                            )
                        );

                        if (!fillableData) { return; }

                        Object.keys(fillableData).forEach((key, index) => {

                            const selector = `${elementId} ${fieldIdPrefix}${key}`;
                            const element = jQuery(selector);

                            if (element.length > 0) {

                                const value = fillableData[key];

                                element.val(value).trigger('change');
                            }
                        });
                    }
                })
            }
        }

        $("#addNewGuarantButton").click(function () {
            //searchByPinGuarant($('#guarant1 #guarant_pin').parent());
            $('#cstm-danger-alert-guarant').hide();
        })

        $(document).on('click', '.guarant-mvr-check', function (event) {
            event.preventDefault();

            const element = jQuery(this);
            let elementId = element.attr('data-container-id');
            elementId = '#' + elementId;

            const fieldIdPrefix = '#guarant_';

            const fields = {
                'idcard_number': 'client_idcard[idcard_number]',
                'pin': 'client_idcard[pin]',
            };

            const mvrGuarantErrorMsg = '{{ __('messages.MvrErrorSubmit') }}';
            const guarantMvrCheckUrl = "{{ route('sales.checkClientFromMvr') }}";

            const data = {};

            const state = {
                hasError: false,
            };
            Object.keys(fields).forEach((key, index) => {

                const selector = `${elementId} ${fieldIdPrefix}${key}`;
                const element = jQuery(selector);

                if (element.length > 0) {
                    
                    const value = element.val().trim();

                    if (value.length > 0) {
                        const fieldDataKey = fields[key];

                        data[fieldDataKey] = value;
                    } else if (
                        value.length === 0 &&
                        !state.hasError
                    ) {
                        state.hasError = true;
                        $(`${elementId} #cstm-danger-alert-guarant`).show('slow');
                        $(`${elementId} #cstm-danger-alert-guarant .message`).html(mvrGuarantErrorMsg);
                        setTimeout(function () {
                            $(`${elementId} #cstm-danger-alert-guarant`).hide('slow');
                        },
                        5000);
                    }
                }
            }); 

            if (state.hasError) {
                return;
            }

            $.ajax({
                url: guarantMvrCheckUrl,
                method: 'GET',
                data: data,
                dataType: 'json',
                success: function (data) {

                    if (data.error) {
                        $(`${elementId} #cstm-danger-alert-guarant .message`).html(data.error);
                        $(`${elementId} #cstm-danger-alert-guarant`).fadeTo(7000, 0).slideUp(1000, function () {
                        });
                        return;
                    }


                    const fillableData = Object.assign({}, data.client, data.client_idcard);

                    if (!fillableData) { return; }

                    Object.keys(fillableData).forEach((key, index) => {

                        const selector = `${elementId} ${fieldIdPrefix}${key}`;
                        const element = jQuery(selector);

                        if (element.length > 0) {

                            const value = fillableData[key];

                            element.val(value).trigger('change');
                        }
                    });
                },
                error: function (response) {
                    const idCardErrorContainer = $(`${elementId} #cstm-danger-alert-guarant`);

                    const {message} = response.responseJSON;

                    showErrorMessage(
                        idCardErrorContainer,
                        message,
                        window.SalesNewApplicationHelper.parent.config.newApplicationDefaultDelayTimeout
                    );
                }
            });

            return undefined;
        });
    </script>
    @endpush
@endif