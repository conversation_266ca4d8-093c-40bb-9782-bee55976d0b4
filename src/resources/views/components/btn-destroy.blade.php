@php
    $hash = md5($url);
@endphp
<a href="javascript:void(0);"
   role="button"
   title="{{ __($title??'') }}"
   data-toggle="modal"
   data-target="#confirm-destroy-{{$hash}}"
   class="btn btn-danger btn-sm"
   data-url="{{$url}}"
>
    <span><i aria-hidden="true" class="fa fa-trash"></i></span>
</a>

<x-common::modal
    modal-title="Потвърждаване на действие"
    modal-id="confirm-destroy-{{$hash}}"
>
    <form action="{{$url}}" method="POST" onsubmit="disableSubmitButtonWithClass(this)">
        @csrf
        @method('DELETE')

        <div class="modal-body">Сигурни ли сте, че искате да изтриете?</div>
        <!-- End ./modal-body -->

        <div class="modal-footer">
            <button type="button"
                    class="btn btn-secondary"
                    data-dismiss="modal"
            >
                <i class="fa fa-close"></i>&nbsp;
                {{__('head::clientCard.Back')}}
            </button>

            <button type="submit" class="btn btn-danger default-btn-last">
                <i class="fa fa-save"></i>&nbsp;
                Изтрий
            </button>
        </div>
        <!-- End ./modal-footer -->

    </form>
</x-common::modal>

<script>
    $(document).ready(function () {
        $('#confirm-destroy-{{$hash}}').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var url = button.data('url');
            $(this).find('form').attr('action', url);
        });
    });
</script>
