@php
    $cardId = $cardId ?? md5(date('Y'));
    if(isset($title)){
        $cardId = str($title)->slug();
    }
    $collapseBodyId = $collapseBodyId ?? 'body-'.$cardId;
    $collapsed = $collapsed??'show';
@endphp
<div class="card" id="{{$cardId}}" data-id="{{$cardId}}">
    @isset($customHeader)
        {!! $customHeader !!}
    @endisset
    @isset($header)
        <div class="card-header bg-secondary">
            <div class="row">
                <div class="col-lg-7">
                    <h4 class="card-title mb-0 text-white">{!! $header !!}</h4>
                </div>
                <!-- End ./col -->
                <div class="col-lg-5 text-right">
                    <a href="#{{$collapseBodyId}}"
                       data-toggle="collapse"
                       role="button" aria-expanded="true"
                       class="text-white"
                       title="{{__('Collapse')}}"
                    >
                        &nbsp;<i class="fa fa-minus"></i>&nbsp;
                    </a>
                </div>
            </div>
            <!-- End ./row -->
        </div>
        <!-- End ./card-header -->
    @endisset

    <div class="card-body collapse {{$collapsed}}" id="{{$collapseBodyId}}">
        <div class="row">
            @isset($title)
                <div class="col-lg-8">
                    <h4 class="card-title">{!! $title !!}</h4>
                </div>
            @endisset
            @isset($cardOptions)
                <div class="@if(isset($title)) col-lg-4 @else col-lg-12 @endif text-right">
                    {!! $cardOptions !!}
                </div>
            @endisset
        </div>
        <!-- End ./row -->
        @isset($title)
            <hr class="mt-0"/>
        @endisset

        {!! $slot !!}
    </div>
    <!-- End ./card-body -->

    @isset($cardFooter)
        <div class="card-footer">
            {!! $cardFooter !!}
        </div>
    @endisset
</div>
