<select class="form-control noClear" name="limit" id="maxRows"
        style="right: {{ $rightPosition ?? '0px' }}; bottom: {{ $bottomPosition ?? '0px' }}; z-index: 10;">
    @foreach($rowsShow as $row)
        <option class="paginationValueLimit"
                @if($selected != null && $selected == $row)
                    selected
                @endif
                value="{{$row}}"
        >{{ $row != 0 ? $row : __('menu.All') }}</option>
    @endforeach
</select>

<script>
    $(document).ready(function () {
        $("#right-wrapper").on('change', '#maxRows', function () {
            const sorting = $('{{ $sortingHead }}').find('input').serialize();

            let data = {limit: $(this).val()};
            let formId = '{{ $formId }}';
            if (formId.length > 0) {
                data = $('{{ $formId }}').serialize() + '&limit=' + $(this).val();

                if (sorting) {
                    data += '&' + sorting
                }
            }

            let paginaitonUrl = '{{ $url }}';

            $.ajax({
                type: 'get',
                url: paginaitonUrl,
                data: data,

                success: function (data) {
                    $('{{ $tableId }}').html(data);
                },
            });
        });
    });
</script>
