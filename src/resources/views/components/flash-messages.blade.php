@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show no-radius" role="alert">
        {!! session('success') !!}
        <button type="button"
                class="close"
                data-dismiss="alert"
                aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    @php
        session()->forget('success')
    @endphp
@endif

@if(session('warning'))
    <div class="alert alert-warning alert-dismissible fade show no-radius" role="alert">
        {!! session('warning') !!}
        <button type="button"
                class="close"
                data-dismiss="alert"
                aria-label="Close"
        >
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

@if(session('errors') || session('fail'))
    <div class="alert alert-danger alert-dismissible fade show no-radius" role="alert">
        {{ session('fail') }}

        @if(count($errors->messages()))
            <h5>
                <i class="fa fa-message"></i>&nbsp;
                {{__('Oops. Something wrong')}}
            </h5>
            <ol class="mb-0">
                @foreach($errors->messages() as $errorKey => $error)
                    <li>
                        <strong>{{ str($errorKey)->camel()->ucfirst() }}</strong>:
                        {{ implode(PHP_EOL, $errors->get($errorKey)) }}
                    </li>
                @endforeach
            </ol>
        @endif
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

@if(session('successfullyImported'))
    <div
            class="alert alert-danger alert-dismissible bg-success text-white border-0 fade show auto-close-alert no-radius"
            role="alert">
        {{ session('successfullyImported') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

{{--@if(session('successfullyProcessed'))--}}
{{--    <div--}}
{{--        class="alert alert-danger alert-dismissible bg-success text-white border-0 fade show auto-close-alert no-radius"--}}
{{--        role="alert">--}}
{{--        {{ session('successfullyProcessed') }}--}}
{{--        <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">--}}
{{--            <span aria-hidden="true">&times;</span>--}}
{{--        </button>--}}
{{--    </div>--}}
{{--@endif--}}
