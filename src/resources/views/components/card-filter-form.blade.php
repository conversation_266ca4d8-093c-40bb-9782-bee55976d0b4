@if(isset($filterForm))
    @php
        $savedFilters = $filterForm->loadSavedFilters(request()->route()->getName());
    @endphp
    <x-card card-id="filters-{{$filterForm->getFormName()}}">
        <x-slot:header>
            <i class="fa-duotone fa-filters"></i>
            {{ __('other.Filters') }}
        </x-slot:header>
        <!-- End ./header -->

        {!! form_start($filterForm, ['autocomplete' => 'off', 'id' => $filterForm->getFormName()]) !!}

        @if(!empty($savedFilters))
            <div class="row">
                <div class="col-lg-2 pr-1">
                    <div class="form-group">
                        <label for="filter_id" class="control-label">{{__('table.SavedFilters')}}</label>
                        <select name="filter_id" id="filter_id" class="form-control form-control-sm">
                            <option value="">{{__('table.SelectOption')}}</option>
                            @foreach($savedFilters as $filterId => $filterName)
                                <option value="{{$filterId}}"
                                    @selected(request('filter_id') == $filterId)
                                >
                                    {{$filterName}}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                @if(request('filter_id'))
                    <div class="col-lg-2 pr-1">
                        <div class="form-group">
                            <label class="control-label w-100">&nbsp;</label>
                            <a href="{{route('common.delete-administrator-filter', request('filter_id'))}}"
                               class="btn btn-sm btn-danger"
                               id="delete-admin-filter">
                                {{__('btn.DeleteFilter')}}
                            </a>
                        </div>
                    </div>
                @endif
            </div>
            <hr/>
        @endif

        <div class="row">
            @foreach($filterForm->getFields() as $fieldName)
                <div
                    class="col-lg-{{$attributes['col-lg']??2}} col-md-{{$attributes['col-md']??3}} col-sm-{{$attributes['col-sm']??3}} pr-1 no-wrap-overflow-hidden">
                    {!! form_row($fieldName, ['attr' => ['class' => 'form-control form-control-sm']]) !!}
                </div>
                <!-- End ./col-lg-2 -->
            @endforeach
        </div>
        <!-- End ./row -->

        <hr class="mb-2 mt-0"/>
        <div class="">
            <button type="submit" class="btn btn-sm btn-primary apply-filter">
                <i class="fa-duotone fa-filter"></i>
                {{ __('other.Apply_filter') }}
            </button>
            @if(isset($attributes['clearFiltersRoute']))
                <a href="{{ $attributes['clearFiltersRoute'] }}" class="btn btn-sm btn-danger">
                    <i class="fa-duotone fa-filter-slash"></i>
                    {{ __('other.Reset_filters') }}
                </a>
            @else
                @php
                    $routeProps = $filterForm->getFormOption('route');
                    if(!is_array($routeProps)){
                        $routeProps = [$routeProps];
                    }
                @endphp
                <a href="{{ route(...$routeProps) }}" class="btn btn-sm btn-danger">
                    <i class="fa-duotone fa-filter-slash"></i>
                    {{ __('other.Reset_filters') }}
                </a>
            @endif

            @php
                $refreshDataRoute = $filterForm->getFormOption('refreshDataRoute');
                $refreshDataInfo = $filterForm->getFormOption('refreshDataInfo');

                $exportRoute = $filterForm->getFormOption('exportRoute');
                $actionBtn = $filterForm->getFormOption('actionBtn');
                $genDocsBtn = $filterForm->getFormOption('genDocsBtn');
            @endphp
            @if(!empty($exportRoute) && !isset($queryString))
                <a id="exportBtn" href="{{ route($exportRoute) }}" target="_blank" class="btn btn-sm btn-primary">
                    <i class="fa-duotone fa-file-export"></i>
                    {!! $bntExportLabel ?? 'Експорт' !!}
                </a>
            @endif

            @if(!empty($exportRoute) && isset($queryString))
                <a href="{{ route($exportRoute, request()->getQueryString()) }}" target="_blank"
                   class="btn btn-sm btn-primary">
                    <i class="fa-duotone fa-file-export"></i>
                    {!! $bntExportLabel ?? 'Експорт' !!}
                </a>
            @endif
            @if(!empty($actionBtn))
                <a id="actionBtn" href="" class="btn btn-sm btn-primary">
                    <i class="fa-solid fa-scale-unbalanced-flip"></i>
                    Действия
                </a>
            @endif
            @if(!empty($genDocsBtn))
                <a id="genDocsBtn" href="" class="btn btn-sm btn-primary">
                    <i class="fa-regular fa-id-card"></i>
                    Генерирай документи
                </a>
            @endif

            @if(!empty($refreshDataRoute))
                <a id="refreshDataBtn" href="{{ route($refreshDataRoute) }}" class="btn btn-sm btn-primary">
                    <i class="fa-duotone fa-fire-alt"></i>
                    Обнови данните
                </a>
                {{ (!empty($refreshDataInfo) ? $refreshDataInfo : '') }}
            @endif

            <button type="button" class="btn btn-sm btn-warning"
                    @disabled(empty(array_filter(request()->all())) || request('filter_id'))
                    data-toggle="modal"
                    data-target="#save-search-request"
            >
                {{__('btn.SaveSearchRequest')}}
            </button>
        </div>
        <!-- End btn-group -->

        {!! form_end($filterForm) !!}
    </x-card>
    <!-- End ./card -->
@endif

<x-common::modal
    modal-id="save-search-request"
    modal-title="{{__('btn.SaveSearchRequest')}}"
>
    <form action="{{route('common.create-administrator-filter')}}" method="POST" data-parsley-validate="true">
        @csrf
        <input type="hidden" name="administrator_id" value="{{getAdminId()}}"/>
        <input type="hidden" name="redirect_to_route" value="{{request()->getUri()}}"/>
        <input type="hidden" name="page_route" value="{{request()->route()->getName()}}"/>
        @foreach(request()->all() as $paramName => $paramVal)
            @if(is_array($paramVal))
                @foreach($paramVal as $val)
                    <input type="hidden" name="params[{{$paramName}}][]" value="{{$val}}"/>
                @endforeach
            @else
                <input type="hidden" name="params[{{$paramName}}]" value="{{$paramVal}}"/>
            @endif
        @endforeach

        <div class="modal-body">
            <div class="form-group">
                <label for="filter-name">{{__('table.Name')}}</label>
                <input type="text" name="name" class="form-control" required="required"/>
            </div>
            <!-- End ./form-group -->
        </div>
        <x-common::modal-footer/>

    </form>
</x-common::modal>

<script>
    $(document).ready(function () {
        $('a#delete-admin-filter').click(function (event) {
            event.preventDefault();
            $(this).toggleClass('disabled')
            if (confirm('{{ __('Are you sure you want to delete the filter?') }}')) {
                $.get($(this).attr('href'), function (resp) {
                    if (resp?.success === true) {
                        location.replace(resp.redirectTo);
                    } else {
                        alert(resp?.message);
                        $(this).toggleClass('disabled')
                    }
                });
            } else {
                $(this).toggleClass('disabled')
            }
        });

        $('select[name="filter_id"]').on('change', function () {
            $('button.apply-filter').addClass('disabled');
            $('button.apply-filter').attr('disabled', 'disabled');

            if ($(this).val() === '') {
                return location.replace($('input[name="redirect_to_route"]').val());
            }

            let url = '/common/{administratorFilter}/load-filter';
            $.get(url.replace('{administratorFilter}', $(this).val()), function (resp) {
                if (resp?.success === true) {
                    location.replace(resp.redirectTo);
                } else {
                    alert(resp?.message);
                }
            });
        });

        $('#refreshDataBtn').on('click', function (event) {
            var $this = $(this);
            if ($this.hasClass('disabled')) {
                event.preventDefault();
                return false;
            }

            $this.addClass('disabled').attr('disabled', true);
        });
    });
</script>
