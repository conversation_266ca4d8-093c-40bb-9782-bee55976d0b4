@if(isset($filterForm))
    {!! form_start($filterForm, ['autocomplete' => 'off', 'id' => $filterForm->getFormName()]) !!}

    <div class="row">
        @foreach($filterForm->getFields() as $fieldName)
            @if($fieldName->getType() != 'hidden')
                <div
                        class="col-lg-{{$attributes['col-lg'] ?? 2}} col-md-{{$attributes['col-md'] ?? 2}} col-xs-12 pr-1 no-wrap-overflow-hidden">
                    {!! form_row($fieldName, ['attr' => ['class' => 'form-control form-control-sm']]) !!}
                </div>
                <!-- End ./col-lg-2 -->
            @else
                {!! form_row($fieldName, ['attr' => ['class' => 'form-control form-control-sm']]) !!}
            @endif
        @endforeach
    </div>
    <!-- End ./row -->

    <div class="">
        <button type="submit"
                class="btn btn-sm btn-primary"
                name="btn{{$filterForm->getFormName()}}"
        >
            <i class="fa fa-filter"></i>&nbsp;
            {{ __('other.Apply_filter') }}
        </button>

        @if(isset($attributes['clearFiltersRoute']))
            <a href="{{ $attributes['clearFiltersRoute'] }}" class="btn btn-sm btn-danger">
                <i class="fa fa-refresh"></i>&nbsp;
                {{ __('other.Reset_filters') }}
            </a>
        @else
            @php
                $routeProps = $filterForm->getFormOption('route');
                if(!is_array($routeProps)){
                    $routeProps = [$routeProps];
                }
            @endphp
            <a href="{{ route(...$routeProps) }}" class="btn btn-sm btn-danger clear-filters-btn">
                <i class="fa fa-refresh"></i>&nbsp;
                {{ __('other.Reset_filters') }}
            </a>
        @endif

    </div>
    <!-- End btn-group -->

    {!! form_end($filterForm) !!}
@endif
