<button type="button" id="{{ $url ?? '#no-id' }}" class="btn btn-sm btn-success">
    <i class="fa fa-copy"></i>&nbsp;
    <span id="btn-text">{{ $btnName ?? __('btn.Copy') }}</span>
</button>
<p style="display: none">
    <input type="hidden" id="copyBtnVal">
</p>
@push('scripts')
    <script>
        const copyUrlBtn = $("#{{ $url }}");
        const hiddenInput = $('#copyBtnVal');
        const defaultText = copyUrlBtn.find('span#btn-text').text();

        hiddenInput.on('click', e => {
            document.execCommand('copy');
            navigator.clipboard.writeText(e.target.value)
                .then(() => {
                    setTimeout(() => {
                        copyUrlBtn.find('span#btn-text').text(defaultText);
                    }, 100);
                })
                .catch(err => {
                    console.log(err)
                });
        });

        copyUrlBtn.on('click', event => {
            copyUrlBtn.find('span#btn-text').text("{{__('btn.Copied')}}");

            event.preventDefault();
            $.ajax({
                async: false,
                type: "GET",
                url: "{{ $urlGetData ?? '#' }}",
                success: response => {
                    let href = $(location).attr('href');

                    href = href.split('?')[0];

                    let url = href + '?' + $.param(response);

                    hiddenInput.val(url);
                }
            });
            hiddenInput.click();
        });
    </script>
@endpush
