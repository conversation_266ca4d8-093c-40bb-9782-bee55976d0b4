@php
    $routeName = Modules\Common\Http\Controllers\BaseController::getControllerRouteName();
    $selectedLimit = session($routeName, config('view.paginationLimit'));

    $allRowsLimit = 120;
    if(isset($maxPerPage)){
        $allRowsLimit = $maxPerPage;
    }
@endphp
<div class="row mt-2">
    <div class="col-lg-6">
        {!! $rows->appends(request()->query())->links() !!}&nbsp;
        <p class="text-secondary m-0">{{__('table.TotalRows')}}: <strong>{{$rows->total()}}</strong></p>
    </div>
    <div class="col-md-2 offset-4">
        <div class="input-group">
            <select name="pageLimit" class="form-control text-center" data-route-name="{{$routeName}}">
                <option value="10" @if($selectedLimit == 10) selected="selected" @endif>
                    10
                </option>
                <option value="25" @if($selectedLimit == 25) selected="selected" @endif>
                    25
                </option>
                <option value="50" @if($selectedLimit == 50) selected="selected" @endif>
                    50
                </option>
                <option value="100" @if($selectedLimit == 100) selected="selected" @endif>
                    100
                </option>
                @if(!isset($showAllPages))
                    <option value="{{$allRowsLimit}}" @if($selectedLimit == $allRowsLimit) selected="selected" @endif>
                        {{ __('All rows') }}
                    </option>
                @endif

                @if(isset($maxPerPage))
                    <option value="{{$allRowsLimit}}" @if($selectedLimit == $allRowsLimit) selected="selected" @endif>
                        {{ $maxPerPage }}
                    </option>
                @endif
            </select>
            <div class="input-group-prepend">
                <span class="input-group-text">{{ __('per page') }}</span>
            </div>
        </div>
        <!-- End ./input-group -->
    </div>
    <!-- End ./col-lg-1 -->
</div>
<!-- End ./row -->
