<?php

/** @var RouteCollection $routeCollection */

use Illuminate\Routing\RouteCollection;

$methodClass = function ($type) {
    switch ($type) {
        case 'DELETE':
            return 'danger';
        case 'GET':
            return 'success';
        case 'POST':
            return 'info';
        case 'PUT':
            return 'primary';
    }
}
?>
    <!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>DevTools::Routes</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
          integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css"
          integrity="sha256-ENFZrbVzylNbgnXx0n3I1g//2WeO47XxoPe0vkp3NC8=" crossorigin="anonymous"/>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"
            integrity="sha256-3blsJd4Hli/7wCQ+bmgXfOdK7p/ZUMtPXY08jmxSSgk=" crossorigin="anonymous"></script>

    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css">
    <script type="text/javascript" charset="utf8"
            src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
    <script>
        function copyText(text) {
            var inp = document.createElement('input');
            document.body.appendChild(inp);
            inp.value = text;
            inp.select();
            document.execCommand('copy', false);
            inp.remove();

        }

        function copyActionController(text) {
            copyText(text);
            toastr.info('' + text + '', 'Copy action controller');
        }

        function copyName(text) {
            copyText(text);
            toastr.info('' + text + '', 'Copy name');
        }

        function copyUrl(text) {
            copyText(text);
            toastr.info('' + text + '', 'Copy URL');
        }
    </script>
    <style>
        .c-pointer {
            cursor: pointer;
        }
    </style>
</head>
<body>
<table id="table">
    <thead>
    <tr>
        <th>HTTP Method</th>
        <th>URI</th>
        <th>Name</th>
        <th>Middleware</th>
        <th>Corresponding Action</th>
    </tr>
    </thead>
    <tbody>
    <?php
    /** @var $route \Illuminate\Routing\Route */ ?>
    @foreach($routeCollection as $route)
        @php
            /** @var $route \Illuminate\Routing\Route */
            $middlewares = collect($route->getAction('middleware'))->filter(function($item){
                return strpos($item,'\\') === false;
            })->implode('</span><span class="badge badge-secondary ml-1">');
            if($middlewares){
                $middlewares = '<span class="badge badge-secondary">'.$middlewares.'</span>';
            }
            $description = $route->defaults['description']??null;
            $name = $route->getName();
            $actionName = $route->getActionName();
            $uri = $route->uri();
            $method = $route->methods()[0]??null;
        @endphp
        <tr>
            <td>
                <span class="badge badge-pill badge-{{$methodClass($method)}}">{{$method}}</span>
            </td>
            <td class="c-pointer" onclick="copyUrl('{{$uri}}')">{{$uri}}</td>
            <td class="c-pointer" onclick="copyName('{{$name}}')">
                @if($name)
                    {{$name}}
                @else
                    <span class="text-danger">{{'!!! no name !!!'}}</span>
                @endif
                <br/>
                <small class="text-info">{{$description?:'!!! no description !!!'}}</small>
            </td>
            <td>{!! $middlewares !!}</td>
            <td class="c-pointer"
                onclick="copyActionController('{{str_replace('@','->',str_replace('\\','\\\\',$actionName))}}')">
                @if($actionName !== 'Closure')
                    {{$actionName}}
                @else
                    <span class="text-danger">{{$actionName}}</span>
                @endif
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
<script>
    $(document).ready(function () {
        $('#table').DataTable({
            paging: false,
            stateSave: true
        });
    });
</script>
</body>
</html>