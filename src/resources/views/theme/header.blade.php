@php use Modules\CashDesk\Enums\TremolProcessingStackEnum; @endphp
<header class="topbar" data-navbarbg="skin6">
    <nav class="navbar top-navbar navbar-expand-md">
        <div class="navbar-header" data-logobg="skin6">
            <!-- This is for the sidebar toggle which is visible on mobile only -->
            <a class="nav-toggler waves-effect waves-light d-block d-md-none" href="javascript:void(0)"><i
                    class="ti-menu ti-close"></i></a>
            {{--            <!-- ============================================================== -->--}}
            {{--            <!-- Logo -->--}}
            {{--            <!-- ============================================================== -->--}}
            {{--            <div class="navbar-brand">--}}
            {{--                <!-- Logo icon -->--}}
            {{--                <a href="/">--}}
            {{--                    <b class="logo-icon">--}}
            {{--                        <!-- Dark Logo icon -->--}}
            {{--                        <img class="img-fluid" src="{{ asset('images/icons/logo.png') }}" alt="homepage"--}}
            {{--                             class="dark-logo"/>--}}
            {{--                    </b>--}}
            {{--                    <!--End Logo icon -->--}}
            {{--                </a>--}}
            {{--            </div>--}}
            {{--            <!-- ============================================================== -->--}}
            {{--            <!-- End Logo -->--}}
            {{--            <!-- ============================================================== -->--}}
            <!-- ============================================================== -->
            <!-- Toggle which is visible on mobile only -->
            <!-- ============================================================== -->
            <a class="topbartoggler d-block d-md-none waves-effect waves-light" href="javascript:void(0)"
               data-toggle="collapse" data-target="#navbarSupportedContent"
               aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><i
                    class="ti-more"></i></a>
        </div>
        <!-- ============================================================== -->
        <!-- End Logo -->
        <!-- ============================================================== -->

        <div class="navbar-collapse collapse" id="navbarSupportedContent">
            <!-- ============================================================== -->
            <!-- toggle and nav items -->
            <!-- ============================================================== -->

            <div style="width: 310px;"></div>
            <!-- Search bar -->
            <div class="">
                <a class="nav-link" href="#">
                    {{-- <form action="{{ route('head.clients.search') }}"> --}}
                    <form>
                        <div class="customize-input" style="min-width: 400px;">
                            <input autocomplete="off" type="text" id="search" name="search"
                                   placeholder="{{ __('menu.Search') }}..."
                                   required
                                   class="form-control custom-shadow custom-radius border-0 bg-white">
                            <i class="form-control-icon" data-feather="search"></i>
                        </div>
                    </form>

                    <div id="clients" class="list-group list-group-item clients-list" style="display: none;">
                    </div>
                </a>
            </div>
            <ul class="navbar-nav">
                <div class="navbar-nav">
                    @include('theme.notifications')

                    <!-- Profile and logout -->
                    <li class="nav-item dropdown">
                        @auth
                            <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown"
                               aria-haspopup="true" aria-expanded="false">
                                <img src="{{ url(Auth::user()->getAvatarPath()) }}"
                                     alt="user"
                                     class="rounded-circle"
                                     width="40">

                                <span class="ml-2 d-none d-lg-inline-block"><span>{{ __('header.Hello') }},</span> <span
                                        class="text-dark">{{ Auth::user()->first_name . ' ' . Auth::user()->last_name }}</span> <i
                                        data-feather="chevron-down"
                                        class="svg-icon"></i></span>
                            </a>

                            <div class="dropdown-menu dropdown-menu-right user-dd animated flipInY">
                                <a class="dropdown-item"
                                   href="{{ route('admin.administrators.edit', Auth::user()->getAuthIdentifier()) }}">
                                    <i data-feather="user" class="svg-icon mr-2 ml-1"></i>
                                    {{ __('header.ProfileSetting') }}
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="javascript:void(0)">
                                    <i data-feather="mail" class="svg-icon mr-2 ml-1"></i>
                                    {{ __('header.Messages') }}
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault();
                                    document.getElementById('logout-form').submit();">
                                    <i data-feather="power" class="svg-icon mr-2 ml-1"></i>
                                    {{ __('header.Logout') }}
                                </a>
                                <form id="logout-form" action="{{ route('logout') }}" method="POST"
                                      style="display: none;">
                                    @csrf
                                </form>
                        @endauth
                    </li>
                </div>
            </ul>
        </div>
    </nav>
</header>

@push('scripts')
    <script type="text/javascript" src="{{ asset('js/clientsSearch.js') }}"></script>

    <script>
        let url = "{{ route('head.clients.searchForClientCard') }}";
        let urlClientProfile = "{{ url('head/clientCard/') }}";
        let translation = {
            notFoundClient: "{{__('menu.NoResult') }}",
        };
    </script>
@endpush
