<ul class="navbar-nav">
    <!-- Notification -->
    <li class="nav-item">
        @php
            $numResults =
            (bool) session('fail') + (bool) session('errors') + (bool) session('success') + (bool) session('info');
        @endphp
        <a class="nav-link dropdown-toggle pl-md-3 position-relative" href="javascript:void(0)"
           id="bell" role="button" data-toggle="dropdown" aria-haspopup="true"
           aria-expanded="true">
            <span><i data-feather="bell" class="svg-icon"></i></span>
            <span class="badge
            @if(session()->has('success'))
                badge-success
            @elseif(session()->has('info'))
                badge-info
            @elseif(session()->has('fail') || session()->has('errors'))
                badge-danger
            @else
                badge-primary
            @endif
                notify-no rounded-circle">
                {{ $numResults }}
            </span>
        </a>
        <div class="dropdown-menu dropdown-menu-left mailbox animated bounceInDown">
            <ul class="list-style-none">
                <li>
                    <div class="message-center notifications position-relative">
                        @if (session('fail'))

                            <div href="javascript:void(0)"
                                 class="message-item d-flex align-items-center border-bottom px-3 py-2">
                                <div class="btn btn-danger rounded-circle btn-circle"><i
                                        data-feather="airplay" class="text-white"></i></div>
                                <div class="d-inline-block v-middle pl-2">
                                    <h6 class="message-title mb-0 mt-1">Fail</h6>
                                    <span class="font-12 d-block text-muted">
                                                    {{ session('fail') }}
                                                </span>
                                </div>
                            </div>

                        @endif

                        @if (session('errors'))

                            <div href="javascript:void(0)"
                                 class="message-item d-flex align-items-center border-bottom px-3 py-2">
                                <div class="btn btn-danger rounded-circle btn-circle"><i
                                        data-feather="airplay" class="text-white"></i></div>
                                <div class="d-inline-block v-middle pl-2">
                                    <h6 class="message-title mb-0 mt-1">Error</h6>
                                    <span class="font-12 text-nowrap d-block text-muted">
                                                    {{ session('errors') }}
                                                </span>
                                </div>
                            </div>

                        @endif

                        @if (session('success'))
                            <div href="javascript:void(0)"
                                 class="message-item d-flex align-items-center border-bottom px-3 py-2">
                                            <span class="btn btn-success text-white rounded-circle btn-circle"><i
                                                    data-feather="calendar" class="text-white"></i></span>
                                <div class="d-inline-block v-middle pl-2">
                                    <h6 class="message-title mb-0 mt-1">Success</h6>
                                    <span class="font-12 text-nowrap d-block text-muted text-truncate">
                                                    {{ session('success') }}
                                                </span>
                                </div>
                            </div>
                        @endif

                        @if (session('info'))
                            <div href="javascript:void(0)"
                                 class="message-item d-flex align-items-center border-bottom px-3 py-2">
                                            <span class="btn btn-primary rounded-circle btn-circle"><i
                                                    data-feather="box" class="text-white"></i></span>
                                <div class="w-75 d-inline-block v-middle pl-2">
                                    <h6 class="message-title mb-0 mt-1">Info</h6>
                                    <span class="font-12 text-nowrap d-block text-muted">
                                                    {{ session('info') }}
                                                </span>
                                    <span class="font-12 text-nowrap d-block text-muted">9:02 AM</span>
                                </div>
                            </div>
                        @endif
                    </div>
                </li>
            </ul>
        </div>
    </li>
</ul>

{{--<script src="{{ asset('dist/js/errorHandler.js') }}"></script>--}}
{{--<script>--}}
{{--    window.setTimeout(function () {--}}
{{--        $('.auto-close-alert').fadeTo(500, 0).slideUp(500, function () {--}}
{{--            $(this).remove();--}}
{{--        });--}}
{{--    }, 1500);--}}
{{--</script>--}}
