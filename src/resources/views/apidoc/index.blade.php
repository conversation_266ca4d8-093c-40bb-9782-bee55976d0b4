<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API Reference</title>

    <link rel="stylesheet" href="{{ asset('/docs/css/style.css') }}" />
    <script src="{{ asset('/docs/js/all.js') }}"></script>


          <script>
        $(function() {
            setupLanguages(["bash","javascript"]);
        });
      </script>
      </head>

  <body class="">
    <a href="#" id="nav-button">
      <span>
        NAV
        <img src="/docs/images/navbar.png" />
      </span>
    </a>
    <div class="tocify-wrapper">
        <img src="/docs/images/logo.png" />
                    <div class="lang-selector">
                                  <a href="#" data-language-name="bash">bash</a>
                                  <a href="#" data-language-name="javascript">javascript</a>
                            </div>
                            <div class="search">
              <input type="text" class="search" id="input-search" placeholder="Search">
            </div>
            <ul class="search-results"></ul>
              <div id="toc">
      </div>
                    <ul class="toc-footer">
                                  <li><a href='http://github.com/mpociot/documentarian'>Documentation Powered by Documentarian</a></li>
                            </ul>
            </div>
    <div class="page-wrapper">
      <div class="dark-box"></div>
      <div class="content">
          <!-- START_INFO -->
<h1>Info</h1>
<p>Welcome to the generated API reference.
<a href="{{ route("apidoc.json") }}">Get Postman Collection</a></p>
<!-- END_INFO -->
<h1>general</h1>
<!-- START_a9cf33d0519765bd4ab92faccd52edf0 -->
<h2>api/v1</h2>
<blockquote>
<p>Example request:</p>
</blockquote>
<pre><code class="language-bash">curl -X GET \
    -G "http://localhost:8000/api/v1?test_id=sit&amp;test_id2=me&amp;test_id3=4" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"test_id4":9,"test_id5":"cumque","test_id6":true}'
</code></pre>
<pre><code class="language-javascript">const url = new URL(
    "http://localhost:8000/api/v1"
);

let params = {
    "test_id": "sit",
    "test_id2": "me",
    "test_id3": "4",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "test_id4": 9,
    "test_id5": "cumque",
    "test_id6": true
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
<blockquote>
<p>Example response (200):</p>
</blockquote>
<pre><code class="language-json">{
    "id": 4,
    "name": "Jessica Jones",
    "roles": [
        "admin"
    ]
}</code></pre>
<h3>HTTP Request</h3>
<p><code>GET api/v1</code></p>
<h4>Query Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>test_id</code></td>
<td>required</td>
<td>The id of the location.</td>
</tr>
<tr>
<td><code>test_id2</code></td>
<td>required</td>
<td>The id of the user.</td>
</tr>
<tr>
<td><code>test_id3</code></td>
<td>required</td>
<td>The page number.</td>
</tr>
</tbody>
</table>
<h4>Body Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>test_id4</code></td>
<td>integer</td>
<td>required</td>
<td>The id of the user.</td>
</tr>
<tr>
<td><code>test_id5</code></td>
<td>string</td>
<td>optional</td>
<td>The id of the room.</td>
</tr>
<tr>
<td><code>test_id6</code></td>
<td>boolean</td>
<td>optional</td>
<td>Whether to ban the user forever. Example:</td>
</tr>
</tbody>
</table>
<!-- END_a9cf33d0519765bd4ab92faccd52edf0 -->
<!-- START_05ec7ec3fd33272955524a4d7e0b683b -->
<h2>api/v1/admin-test</h2>
<blockquote>
<p>Example request:</p>
</blockquote>
<pre><code class="language-bash">curl -X GET \
    -G "http://localhost:8000/api/v1/admin-test?hello=dicta&amp;hello2=me&amp;hello3=4" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"hello4":9,"hello5":"occaecati","hello6":true}'
</code></pre>
<pre><code class="language-javascript">const url = new URL(
    "http://localhost:8000/api/v1/admin-test"
);

let params = {
    "hello": "dicta",
    "hello2": "me",
    "hello3": "4",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "hello4": 9,
    "hello5": "occaecati",
    "hello6": true
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
<blockquote>
<p>Example response (200):</p>
</blockquote>
<pre><code class="language-json">{
    "id": 4,
    "name": "Jessica Jones",
    "roles": [
        "admin"
    ]
}</code></pre>
<h3>HTTP Request</h3>
<p><code>GET api/v1/admin-test</code></p>
<h4>Query Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>hello</code></td>
<td>required</td>
<td>The id of the location.</td>
</tr>
<tr>
<td><code>hello2</code></td>
<td>required</td>
<td>The id of the user.</td>
</tr>
<tr>
<td><code>hello3</code></td>
<td>required</td>
<td>The page number.</td>
</tr>
</tbody>
</table>
<h4>Body Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>hello4</code></td>
<td>integer</td>
<td>required</td>
<td>The id of the user.</td>
</tr>
<tr>
<td><code>hello5</code></td>
<td>string</td>
<td>optional</td>
<td>The id of the room.</td>
</tr>
<tr>
<td><code>hello6</code></td>
<td>boolean</td>
<td>optional</td>
<td>Whether to ban the user forever. Example:</td>
</tr>
</tbody>
</table>
<!-- END_05ec7ec3fd33272955524a4d7e0b683b -->
<!-- START_b06b25dda6374b9cd705090b0df09b9f -->
<h2>api/v1/tmp-request</h2>
<blockquote>
<p>Example request:</p>
</blockquote>
<pre><code class="language-bash">curl -X POST \
    "http://localhost:8000/api/v1/tmp-request" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"</code></pre>
<pre><code class="language-javascript">const url = new URL(
    "http://localhost:8000/api/v1/tmp-request"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
<h3>HTTP Request</h3>
<p><code>POST api/v1/tmp-request</code></p>
<!-- END_b06b25dda6374b9cd705090b0df09b9f -->
<!-- START_1336f9a0501e4ace11ad26e53614db8b -->
<h2>api/v2</h2>
<blockquote>
<p>Example request:</p>
</blockquote>
<pre><code class="language-bash">curl -X GET \
    -G "http://localhost:8000/api/v2?location_id=quia&amp;user_id=me&amp;page=4" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"user_id":9,"room_id":"esse","forever":false}'
</code></pre>
<pre><code class="language-javascript">const url = new URL(
    "http://localhost:8000/api/v2"
);

let params = {
    "location_id": "quia",
    "user_id": "me",
    "page": "4",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "user_id": 9,
    "room_id": "esse",
    "forever": false
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response =&gt; response.json())
    .then(json =&gt; console.log(json));</code></pre>
<blockquote>
<p>Example response (200):</p>
</blockquote>
<pre><code class="language-json">{
    "id": 4,
    "name": "Jessica Jones",
    "roles": [
        "admin"
    ]
}</code></pre>
<h3>HTTP Request</h3>
<p><code>GET api/v2</code></p>
<h4>Query Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>location_id</code></td>
<td>required</td>
<td>The id of the location.</td>
</tr>
<tr>
<td><code>user_id</code></td>
<td>required</td>
<td>The id of the user.</td>
</tr>
<tr>
<td><code>page</code></td>
<td>required</td>
<td>The page number.</td>
</tr>
</tbody>
</table>
<h4>Body Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>user_id</code></td>
<td>integer</td>
<td>required</td>
<td>The id of the user.</td>
</tr>
<tr>
<td><code>room_id</code></td>
<td>string</td>
<td>optional</td>
<td>The id of the room.</td>
</tr>
<tr>
<td><code>forever</code></td>
<td>boolean</td>
<td>optional</td>
<td>Whether to ban the user forever.</td>
</tr>
</tbody>
</table>
<!-- END_1336f9a0501e4ace11ad26e53614db8b -->
      </div>
      <div class="dark-box">
                        <div class="lang-selector">
                                    <a href="#" data-language-name="bash">bash</a>
                                    <a href="#" data-language-name="javascript">javascript</a>
                              </div>
                </div>
    </div>
  </body>
</html>