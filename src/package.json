{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.19", "bootstrap": "^4.0.0", "cross-env": "^7.0", "dotenv": "^17.2.1", "jquery": "^3.2", "laravel-mix-merge-manifest": "^2.1.0", "lodash": "^4.17.13", "popper.js": "^1.16.0", "resolve-url-loader": "^2.3.1", "sass": "^1.20.1", "sass-loader": "^8.0.0", "vue-loader": "^16.8.3", "vue-template-compiler": "^2.7.14"}, "optionalDependencies": {"fsevents": "^2.1.2"}, "dependencies": {"bootstrap-datepicker": "^1.9.0", "bootstrap-select": "^1.13.18", "daterangepicker": "^3.1.0", "laravel-mix": "^6.0.49", "laravel-vue-datatable": "^0.5.11", "luxon": "^3.5.0", "moment": "^2.29.4", "nouislider": "^15.6.1", "parsleyjs": "^2.9.2", "pusher-js": "^8.4.0", "select2": "^4.1.0-rc.0", "sortablejs": "^1.15.0", "sprintf-js": "^1.1.2", "vue": "^3.2.45", "vuex": "^4.1.0"}}