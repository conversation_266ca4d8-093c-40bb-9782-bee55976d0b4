<?php

namespace Modules\Api\Http\Controllers\V1;

use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Http\Requests\FinishVerificationRequest;
use Modules\Api\Http\Requests\SkipVerificationRequest;
use Modules\Api\Http\Requests\StartVerificationRequest;
use Modules\Api\Http\Requests\VeriffGetUrlRequest;
use Modules\Api\Http\Requests\VeriffOfferedRequest;
use Modules\Api\Http\Responses\ResponseHandler;
use Modules\Api\Jobs\SaveVeriffDocumentsJob;
use Modules\Common\Enums\VeriffProviderStatusEnum;
use Modules\Common\Models\Client;
use Modules\Common\Services\VeriffWebhookLogService;
use Modules\ThirdParty\Services\Veriff\VeriffActionService;
use Modules\ThirdParty\Services\Veriff\VeriffApiService;
use RuntimeException;

class VeriffVerificationController extends Controller
{
    const REUSE_LINK_HOURS = 7 * 24;

    public function __construct(
        private VeriffActionService $actionService,
        private VeriffApiService $apiService,
        private readonly ResponseHandler $handler
    ) {
    }

    public function verifOffered(VeriffOfferedRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $client = Client::whereClientId($data['client_id'])->first();
            if (!$client) {
                throw new ClientNotFoundById($data['client_id']);
            }

            $this->actionService->setOffered($client);

            return $this->handler->jsonResponseSimple($request, [
                'success' => true,
                'response' => []
            ]);
        } catch (\Exception $e) {
            Log::debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return $this->handler->jsonResponseSimple($request, [
                'success' => false,
                'response' => []
            ], false);
        }
    }

    public function getVeriffProviderUrl(
        VeriffGetUrlRequest $request
    ): JsonResponse {
        try {
            $data = $request->validated();
            $client = Client::whereClientId($data['client_id'])->first();
            if (!$client) {
                throw new ClientNotFoundById($data['client_id']);
            }

            /// reuse verification link
            if (
                !empty($client->verif_provider_url)
                && $client->verif_provider_status != VeriffProviderStatusEnum::VERIF_STATUS_DECLINED
                && !Carbon::parse($client->verif_provider_status_updated_at)->addHours(self::REUSE_LINK_HOURS)->isPast()
            ) {
                return $this->handler->jsonResponseSimple($request, [
                    'success' => true,
                    'response' => [
                        'verification' => [
                            'id' => $client->verif_provider_session_id,
                            'url' => $client->verif_provider_url,
                        ]
                    ]
                ]);
            }

            $responseData = $this->apiService->getSessionUrl($client);
            if (empty($responseData['verification']['id']) || empty($responseData['verification']['url'])) {
                throw new RuntimeException('Verification url is empty');
            }

            // tuning - test BG locale
            $responseData['verification']['url'] = $responseData['verification']['url'] . '?lang=bg';

            if ($responseData['verification']['status'] === VeriffProviderStatusEnum::VERIF_STATUS_CREATED->value) {
                $responseData['loan_id'] = $data['loan_id'];
                $this->actionService->updateClientVerifProviderOnWaitingStatus(
                    $client,
                    $responseData
                );
            }

            return $this->handler->jsonResponseSimple($request, [
                'success' => true,
                'response' => $responseData
            ]);
        } catch (\Throwable $e) {
            Log::debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return $this->handler->jsonResponseSimple($request, [
                'success' => false,
                'response' => []
            ], false);
        }
    }

    /**
     * @param StartVerificationRequest $request
     * @return void
     * Response:
     * {
     * "id":"3e99c2f6-777a-46fd-b4c5-44cfa9f8e7bc",
     * "attemptId":"2b14afb9-fd07-48d7-9369-0034cc0950d0",
     * "feature":"selfid",
     * "code":7001,
     * "action":"started",
     * "vendorData":"103578_Richard_Joierher_1724050912",
     * "endUserId":null
     * }
     */
    public function startVerification(StartVerificationRequest $request): JsonResponse
    {
        try {
            $verificationData = $request->validated();

            /// 1. Create webhook log
            $webhookLog = app(VeriffWebhookLogService::class)->create($verificationData);

            /// 2. Get client
            $client = Client::where('verif_provider_session_id', $verificationData['id'])
                ->whereIn('verif_provider_status', [
                    VeriffProviderStatusEnum::VERIF_STATUS_CREATED->value,
                    VeriffProviderStatusEnum::VERIF_STATUS_WAITING->value,
                    VeriffProviderStatusEnum::VERIF_STATUS_STARTED->value,
                ])
                ->first();

            if (empty($client->client_id) && !empty($verificationData['client_id'])) {
                $client = Client::where('client_id', $verificationData['client_id'])->first();
            }

            if (empty($client->client_id)) {
                Log::error(
                    'Failed to find a client: ' . (is_array($verificationData) ? json_encode($verificationData) : '')
                );

                return $this->handler->jsonResponseSimple($request, [
                    'success' => false,
                ], false);
            }

            /// 3. Update verif provider status
            $this->actionService->updateClientVerifProviderOnStartedStatus($client);

            /// 4. Add relation to webhook
            $webhookLog->setAttribute('client_id', $client->client_id);
            $webhookLog->saveQuietly();

            /// 5. Send response to site
            return $this->handler->jsonResponseSimple($request, [
                'success' => true,
            ]);
        } catch (\Throwable $e) {
            Log::debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return $this->handler->jsonResponseSimple($request, [
                'success' => false,
            ], false);
        }
    }

    /**
     * @param FinishVerificationRequest $request
     * @return void
     * Response:
     * {
     * "status":"success",
     * "verification":{
     * "acceptanceTime":"2024-08-19T07:01:52.722157Z",
     * "decisionTime":"2024-08-19T07:57:25.796723Z",
     * "code":9001,
     * "id":"3e99c2f6-777a-46fd-b4c5-44cfa9f8e7bc",
     * "vendorData":"103578_Richard_Joierher_1724050912",
     * "endUserId":null,
     * "status":"approved",
     * "reason":null,
     * "reasonCode":null,
     * "person":{
     * "firstName":null,
     * "lastName":null,
     * "citizenship":null,
     * "idNumber":null,
     * "gender":null,
     * "dateOfBirth":null,
     * "yearOfBirth":null,
     * "placeOfBirth":null,
     * "nationality":null,
     * "pepSanctionMatch":null
     * },
     * "document":{
     * "number":null,
     * "type":"PASSPORT",
     * "country":"DE",
     * "validFrom":null,
     * "validUntil":null,
     * "state":null
     * },
     * "comments":[],
     * "additionalVerifiedData":[]
     * },
     * "technicalData":{"ip":"**************"}
     * }
     *
     * Possible statuses:
     * "created": The verification session has been created.
     * "submitted": The user has submitted their documents.
     * "resubmission_requested": Veriff has requested the user to resubmit documents.
     * "approved": The user's verification has been approved.
     * "declined": The user's verification has been declined.
     * "expired": The verification session has expired.
     * "abandoned": The user has abandoned the verification process.
     */
    public function finishVerification(FinishVerificationRequest $request): JsonResponse
    {
        try {
            $verificationData = $request->validated();

            /// 1. Create webhook log
            $webhookLog = app(VeriffWebhookLogService::class)->create($verificationData['verification']);

            /// 2. try to find client
            $client = Client::where('verif_provider_session_id', $verificationData['verification']['id'])
                ->whereIn(
                    'verif_provider_status',
                    [VeriffProviderStatusEnum::VERIF_STATUS_WAITING, VeriffProviderStatusEnum::VERIF_STATUS_STARTED]
                )
                ->first();

            /// 3. if not found try to find with vendorData
            if (!$client && !empty($verificationData['verification']['vendorData'])) {
                if (preg_match('/^(\d+)_/', $verificationData['verification']['vendorData'], $matches)) {
                    $client = Client::where('client_id', $matches[1] ?? null)
                        ->whereIn(
                            'verif_provider_status',
                            [
                                VeriffProviderStatusEnum::VERIF_STATUS_WAITING,
                                VeriffProviderStatusEnum::VERIF_STATUS_STARTED
                            ]
                        )
                        ->first();
                }
            }

            /// 4. if client not found throw exception
            if (empty($client?->client_id)) {
                throw new RuntimeException('Системна грешка, не успяхме да намерим клиент.');
            }


            /// 5. set relations to webhook log
            $webhookLog->setAttribute('client_id', $client->client_id);
            $webhookLog->setAttribute('veriff_status', $verificationData['verification']['status']);
            $webhookLog->saveQuietly();

            /// 6. update client data
            $client->setAttribute('verif_provider_data', $verificationData);
            $client->saveQuietly();

            match ($verificationData['verification']['status']) {
                /// approved
                VeriffProviderStatusEnum::VERIF_STATUS_APPROVED->value => $this->actionService->updateClientVerifProviderOnApprovedStatus(
                    $client
                ),
                /// declined
                VeriffProviderStatusEnum::VERIF_STATUS_DECLINED->value => $this->actionService->updateClientVerifProviderOnRejectedStatus(
                    $client
                )
            };

            /// 7. if verif status is approved try to save all documents
            $client->refresh();
            if (
                $client->verif_provider_status->is(VeriffProviderStatusEnum::VERIF_STATUS_APPROVED)
                || $client->verif_provider_status->is(VeriffProviderStatusEnum::VERIF_STATUS_DECLINED)
            ) {
                SaveVeriffDocumentsJob::dispatch($client)->onQueue('docs');
            }

            return $this->handler->jsonResponseSimple($request, [
                'success' => true,
            ]);
        } catch (\Throwable $e) {
            Log::debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return $this->handler->jsonResponseSimple($request, [
                'success' => false,
            ], false);
        }
    }

    public function skipVerification(SkipVerificationRequest $request): JsonResponse
    {
        $data = $request->validated();

        $client = Client::whereClientId($data['client_id'])->first();
        if (!$client) {
            throw new ClientNotFoundById($data['client_id']);
        }

        if (!is_null($client->verif_skipped)) {
            throw new RuntimeException('Системна грешка.');
        }

        $client->setAttribute('verif_provider_status', VeriffProviderStatusEnum::VERIF_STATUS_SKIPPED->value);
        $client->setAttribute('verif_skip_type', 'client');
        $client->setAttribute('verif_skipped', true);
        $client->setAttribute('verif_processed_at', now());
        $client->setAttribute('verif_processed_loan_id', $data['loan_id'] ?? null);
        $client->saveQuietly();

        return $this->handler->jsonResponseSimple($request, [
            'success' => true,
        ]);
    }
}
