<?php

namespace Modules\Api\Http\Controllers\V1;

use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Modules\Admin\Models\BlogPost;
use Modules\Admin\Models\ClientRateUs;
use Modules\Admin\Repositories\BlogPostRepository;
use Modules\Admin\Repositories\ClientRateUsRepository;
use Modules\Api\Application\Actions\Auth\LoginBySmsCodeAction;
use Modules\Api\Application\Actions\Auth\SendSmsCodeByPinAction;
use Modules\Api\Application\Actions\GetClientFileAction;
use Modules\Api\Application\Actions\GetFileAction;
use Modules\Api\Application\Actions\GetProductsForWebAction;
use Modules\Api\Application\Actions\PhoneRequestAction;
use Modules\Api\Application\Actions\SendUrlInSmsToClientAction;
use Modules\Api\Application\Actions\SurveyHashAction;
use Modules\Api\Application\Actions\ValidateUrlAction;
use Modules\Api\Http\Requests\ClientFileRequest;
use Modules\Api\Http\Requests\GenerateFileRequest;
use Modules\Api\Http\Requests\GetProductsRequest;
use Modules\Api\Http\Requests\LoginBySmsCodeRequest;
use Modules\Api\Http\Requests\PhoneRequestRequest;
use Modules\Api\Http\Requests\SendSmsCodeRequest;
use Modules\Api\Http\Requests\SendUrlInSmsToClientRequest;
use Modules\Api\Http\Requests\StoreIdCardSelfieRequest;
use Modules\Api\Http\Requests\StoreUtmRequest;
use Modules\Api\Http\Requests\SurveyHashRequest;
use Modules\Api\Http\Requests\ValidateUrlRequest;
use Modules\Api\Http\Responses\ResponseHandler;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Enums\YesNoEnum;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\SiteMapOffice;
use Modules\Common\Services\AffiliateApplicationService;
use Modules\Common\Services\UtmTrackerService;
use Modules\Docs\Services\ClientDocumentService;
use Modules\Head\Repositories\CityRepository;
use Modules\Head\Repositories\LoanRepository;
use RuntimeException;


final class PublicApiController extends Controller
{
    public function __construct(
        private readonly ResponseHandler $handler,
    ) {
    }

    public function storeIdCardSelfie(
        StoreIdCardSelfieRequest $request,
        ClientDocumentService $clientDocumentService
    ): JsonResponse {
        try {
            $data = $request->validated();
            $loan = app(LoanRepository::class)->getByHash($data['hash']);
            if (!in_array($loan->loan_status_id, [LoanStatus::SIGNED_STATUS_ID, LoanStatus::PROCESSING_STATUS_ID])) {
                throw new RuntimeException('Заявката вече е обработена.');
            }

            $clientDocument = $clientDocumentService->upload(
                $data['selfie'],
                $loan->client_id,
                FileTypeEnum::ID_CARD,
                $loan->loan_id,
                'Селфи с лична карта.'
            );

            // if we get the selfie, null skip flag, so the loan will be visible on approve listing
            if ($clientDocument) {
                $loan->updateSkipTill(null, 'selfie received');
            }


            return $this->handler->jsonResponseSimple($request, [
                'success' => true,
                'message' => 'Благодарим, документи са качени и чакат обработка.'
            ]);
        } catch (\Throwable $e) {
            Log::channel('api')->debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            /// if loan is already processed
            if ($e instanceof RuntimeException) {
                return $this->handler->jsonResponseSimple($request, [
                    'success' => false,
                    'message' => $e->getMessage()
                ], false);
            }

            return $this->handler->jsonResponseSimple($request, [
                'success' => false,
                'message' => 'Системна грешка.'
            ], false);
        }
    }

    public function storeUtm(StoreUtmRequest $request): JsonResponse
    {
        return $this->handler->jsonResponseSimple($request, [
            'utm_tracker_id' => app(UtmTrackerService::class)->firstOrCreate($request->validated())->getKey()
        ]);
    }

    /**************************** LISTS ***************************/

    public function getActiveAffiliateList(AffiliateApplicationService $affiliateApplicationService)
    {
        return Response::json($affiliateApplicationService->getCachedAffiliates());
    }

    public function getOfficeList(): JsonResponse
    {
        $offices = SiteMapOffice::with('city')->get();

        $cities = [];
        $offices->map(function (SiteMapOffice $office) use (&$cities) {
            if (!$office->city?->city_id) {
                return null;
            }

            $cities[$office->city?->city_id] = [
                'city_id' => $office->city?->city_id,
                'cityName' => $office->city?->name,
                'sortName' => str_replace(['гр. '], '', $office->city?->name),
                'citySlug' => $office->city?->slug,
            ];
        });
        $cities = collect($cities)->sortBy('sortName');

        $officeData = $offices->map(function (SiteMapOffice $office) {
            if (!$office->city?->name) {
                return null;
            }

            return [
                'officeId' => $office->getKey(),
                'citySlug' => $office->city->slug,
                'officeName' => $office->name,
                'lat' => $office->lat,
                'lng' => $office->lng,
                'phone' => $office->phone,
                'working_time' => trim($office->working_time),
            ];
        })->filter();

        return response()->json([
            'cities' => $cities,
            'offices' => $officeData
        ]);
    }

    public function getClientRateUs(): JsonResponse
    {
        $cacheKey = 'client_rate_us';

        $clientRateUsRows = Cache::get($cacheKey, function () use ($cacheKey) {
            $rows = app(ClientRateUsRepository::class)->getRowsByConditions([
                'is_active' => YesNoEnum::YES
            ]);

            $rows = $rows->map(function (ClientRateUs $clientRateUs) {
                $data = $clientRateUs->toArray();
                $data['youtubeId'] = null;
                $data['clientImage'] = $clientRateUs->getFirstMediaUrl('clientImage');

                $pattern = '/(?:https?:\/\/(?:www\.)?youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
                // Use preg_match to find the video ID
                if (preg_match($pattern, $data['youtube_url'], $matches)) {
                    $data['youtubeId'] = $matches[1];
                }

                return $data;
            });

            Cache::put($cacheKey, $rows);

            return $rows;
        });

        return Response::json($clientRateUsRows);
    }

    /**
     * @todo for another task if we will not use blog remove all
     * @return JsonResponse
     */
    public function getBlogPosts(): JsonResponse
    {
        /**
         * @var LengthAwarePaginator $rows
         */
        $rows = app(BlogPostRepository::class)->getRowsFilterBy([
            'is_active' => YesNoEnum::YES->value,
            'post_type' => 'post',
        ]);
        $blogPostRows = $rows->map(function (BlogPost $blogPost) {
            $data = $blogPost->toArray();
            $data['postImage'] = $blogPost->getFirstMediaUrl('blogPostImage') ?? null;

            return $data;
        });

        $env = app()->environment();
        $rows->setPath(config("site-domain.{$env}.domain") . '/blog');

        return Response::json([
            'data' => $blogPostRows,
            'links' => $rows->links()->toHtml()
        ]);
    }

    public function getBlogPost(string $slug): JsonResponse
    {
        $data = app(BlogPostRepository::class)->getFirstByConditions([
            'is_active' => YesNoEnum::YES,
            'slug' => $slug,
        ]);

        return Response::json($data);
    }

    public function getPaymentMethods(): JsonResponse
    {
        return Response::json(PaymentMethod::getOnlinePaymentMethods());
    }

    public function getCities(): JsonResponse
    {
        return Response::json(CityRepository::getAllActiveWithIdAsKey());
    }

    public function getProducts(GetProductsRequest $request, GetProductsForWebAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    /**************************** AUTH ***************************/
    public function sendSmsCodeByPin(SendSmsCodeRequest $request, SendSmsCodeByPinAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    public function loginBySmsCode(LoginBySmsCodeRequest $request, LoginBySmsCodeAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    /**************************** SURVEY ***************************/
    // Serves as Survey: Hash + Rate validator
    public function surveyHash(SurveyHashRequest $request, SurveyHashAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    /******************* FILE, CLIENT_FILE, PHONE *********************/
    public function getFile(GenerateFileRequest $request, GetFileAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    public function getClientFile(ClientFileRequest $request, GetClientFileAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    public function phoneRequest(PhoneRequestRequest $request, PhoneRequestAction $action): JsonResponse
    {
        return $this->handler->jsonResponse($request, $action);
    }

    public function sendUrlInSmsToClient(
        SendUrlInSmsToClientRequest $request,
        SendUrlInSmsToClientAction $action
    ) {
        return $this->handler->jsonResponse($request, $action);
    }

    public function validateUrl(
        ValidateUrlRequest $request,
        ValidateUrlAction $action
    ) {
        return $this->handler->jsonResponse($request, $action);
    }
}
