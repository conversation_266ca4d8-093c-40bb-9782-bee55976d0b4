<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\TmpCreateDto;
use Modules\Common\Http\Requests\BaseRequest;

class TmpCreateRequest extends BaseRequest implements ApiRequestInterface
{
    public function rules(): array
    {
        return [
            'utm_tracker_id' => 'nullable|numeric',
            'product_id' => 'required|numeric',
            'amount_requested' => 'required|numeric|between:5000,300000',
            'period_requested' => 'required|numeric|digits_between:1,12',
            'phone' => $this->getConfiguration('requestRules.phone'),
            'session_id' => 'required',
            'agreements' => 'required',
            'last_page_accessed' => 'required',
            'ip' => 'required',
            'browser' => 'required',
            'insurance' => 'sometimes|integer|in:0,1'
        ];
    }

    public function asDto(): TmpCreateDto
    {
        $data = $this->validated();
        if (!is_array($data['agreements'])) {
            $data['agreements'] = json_decode($data['agreements'], true);
        }

        return TmpCreateDto::from($data);
    }
}
