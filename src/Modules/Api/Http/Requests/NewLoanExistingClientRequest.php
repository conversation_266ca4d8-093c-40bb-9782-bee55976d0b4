<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\NewLoanExistingClientDto;
use Modules\Common\Http\Requests\BaseRequest;

class NewLoanExistingClientRequest extends BaseRequest implements ApiRequestInterface
{
    public function rules(): array
    {
        return [
            'ip' => 'required|string',
            'browser' => 'required|string',
            'client_id' => 'required|numeric',
            'product_id' => 'required|numeric',
            'amount_requested' => 'required|numeric',
            'period_requested' => 'required|numeric',
            'payment_method_id' => 'required|numeric',
            'iban' => 'nullable|string',
            'utm_tracker_id' => 'nullable|numeric',
            'insurance' => 'sometimes|integer|in:0,1',
        ];
    }

    public function asDto(): NewLoanExistingClientDto
    {
        return NewLoanExistingClientDto::from($this->validated());
    }
}
