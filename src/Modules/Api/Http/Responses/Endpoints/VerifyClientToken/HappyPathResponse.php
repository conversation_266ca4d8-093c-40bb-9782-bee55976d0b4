<?php

namespace Modules\Api\Http\Responses\Endpoints\VerifyClientToken;

use Throwable;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class HappyPathResponse extends AbstractResponse implements GetClientResponseInterface
{
    public const HTTP_CODE = Response::HTTP_OK;
    public function matches(?Throwable $e): bool
    {
        return is_null($e);
    }

    public function get(array $data = []): array
    {
        return [
            'success' => true,
            'response' => [],
        ];
    }
}
