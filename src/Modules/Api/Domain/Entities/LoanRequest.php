<?php

namespace Modules\Api\Domain\Entities;

use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Domain\Exceptions\TmpRequestNotFound;
use Modules\Api\Domain\Exceptions\TmpRequestNotSaved;
use Modules\Api\Http\Dto\MvrParsedReportDto;
use Modules\Api\Http\Dto\NewLoanExistingClientDto;
use Modules\Api\Http\Dto\TmpCreateDto;
use Modules\Api\Http\Dto\TmpFinalizeRequestDto;
use Modules\Api\Http\Dto\TmpUpdateDto;
use Modules\Api\Repositories\TmpRequestRepository as Repo;
use Modules\Common\Domain\AggregateRootInterface;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\City;
use Modules\Common\Models\Client;
use Modules\Common\Models\TmpRequest as DbModel;
use Modules\Head\Repositories\ClientRepository;
use StikCredit\Calculators\LoanCalculator;

final class LoanRequest extends DomainModel implements AggregateRootInterface
{
    public Client $client;

    public function __construct(
        private DbModel $dbModel,
        private Repo $repo,
        private readonly ClientRepository $clRepo,
        private Product $product,
        private CurrentDate $currentDate,
        private LoanCalculator $calculator,
        private MvrReport $mvrReport,
        private Agreements $agreements,
    ){}

    public static function loadSelf(int $id): self
    {
        return app()->make(LoanRequest::class)->loadById($id);
    }

    public function loadById(int $id): self
    {
        $dbModel = $this->repo->getById($id);

        if(!$dbModel?->exists){
            throw new TmpRequestNotFound($id);
        }

        return $this->buildFromExisting($dbModel);
    }

    public function buildFromExisting(DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    /**
     * @throws TmpRequestNotSaved
     */
    public function buildNew(TmpCreateDto $dto): self
    {
        return $this
            ->setDbModel($dto)
            ->setProduct($dto->product_id)
            ->setStepOneData($dto)
            ->setCalculator()
            ->setStepOneCalculatedData()
            ->save()
            ->addAgreements($dto->agreements);
    }

    /**
     * TODO: Check for the dead code
     */
    public function buildForExistingClient(NewLoanExistingClientDto $dto): self
    {
        return $this->setExistingClientData($dto->client_id)
            ->setMvrReport()
            ->setProduct($dto->product_id)
            ->setStepOneDataForExistingClient($dto);
    }

    public function updateAndAddMvrReport(TmpUpdateDto $dto): self
    {
        return $this->setStepTwoData($dto)
            ->save()
            ->setMvrReport()
            ->setStepThreeDataFromMvrReport($this->mvrReport->dto)
            ->save();
    }

    public function updateMvrReportRelations(Client $client, int $loanId): self
    {
        $this->mvrReport->updateRelations($client, $loanId);

        return $this;
    }

    public function reportManually(TmpFinalizeRequestDto $dto): self
    {
        return $this->setStepThreeDataFromRequest($dto)->save();
    }

    private function setExistingClientData(int $clientId): self
    {
        $client = $this->clRepo->getById($clientId);
        if(! $client){
            throw new ClientNotFoundById($clientId);
        }
        $this->client = $client;
        $this->dbModel->fill($client->toArray());
        return $this;
    }

    private function setDbModel(TmpCreateDto $dto): self
    {
        $existing = $this->repo->getByPhone($dto->phone);
        if($existing){
            $this->dbModel = $existing;
        }
        return $this;
    }

    private function setStepOneDataForExistingClient(NewLoanExistingClientDto $dto): self
    {
        $this->dbModel
            ->setAttribute('amount_requested', $dto->amount_requested)
            ->setAttribute('period_requested', $dto->period_requested)
            ->setAttribute('ip', $dto->ip)
            ->setAttribute('browser', $dto->browser)
            ->setAttribute('iban', $dto->iban)
            ->setAttribute('payment_method_id', $dto->payment_method_id)
            ->setAttribute('last_status_update_date', $this->currentDate->now())
            ->setAttribute('insurance', $dto->insurance);
        return $this;
    }

    private function setStepOneData(TmpCreateDto $dto): self
    {
        $this->dbModel->setAttribute('session_id', $dto->session_id)
            ->setAttribute('amount_requested', $dto->amount_requested)
            ->setAttribute('period_requested', $dto->period_requested)
            ->setAttribute('phone', $dto->phone)
            ->setAttribute('ip', $dto->ip)
            ->setAttribute('browser', $dto->browser)
            ->setAttribute('last_page_accessed', $dto->last_page_accessed)
            ->setAttribute('tmp_request_step_id', 1)
            ->setAttribute('last_status_update_date', $this->currentDate->now())
            ->setAttribute('insurance', $dto->insurance);

        return $this;
    }

    private function setStepOneCalculatedData(): self
    {
        $this->dbModel->annual_percentage_rate = $this->product->dbModel()
            ->getInterestRate(
                $this->dbModel->period_requested,
                $this->dbModel->amount_requested
            );
        return $this;
    }

    private function setStepTwoData(TmpUpdateDto $dto): self
    {
        $this->dbModel->setAttribute('email', $dto->email)
            ->setAttribute('pin', $dto->pin)
            ->setAttribute('idcard_number', $dto->idcard_number)
            ->setAttribute('payment_method_id', $dto->payment_method_id)
            ->setAttribute('iban', $dto->iban)
            ->setAttribute('ip', $dto->ip)
            ->setAttribute('browser', $dto->browser)
            ->setAttribute('tmp_request_step_id', 2)
            ->setAttribute('last_status_update_date', $this->currentDate->now());
        return $this;
    }

    private function setStepThreeDataFromMvrReport(MvrParsedReportDto $dto): self
    {
        $this->dbModel->setAttribute('first_name', $dto->first_name)
            ->setAttribute('middle_name', $dto->middle_name)
            ->setAttribute('last_name', $dto->last_name)
            ->setAttribute('idcard_issue_date', $dto->issue_date)
            ->setAttribute('idcard_valid_date', $dto->valid_date)
            ->setAttribute('city_id', $dto->city_id)
            ->setAttribute('address', $dto->address)
            ->setAttribute('city_name', $dto->city_name)
            ->setAttribute('image', $dto->image)
            ->setAttribute('issue_by', $dto->issue_by)
            ->setAttribute('issue_by_city_id', $dto->issue_by_city_id)
            ->setAttribute('issue_by_city_name', $dto->issue_by_city_name)
            ->setAttribute('sex', $dto->sex);
        return $this;
    }

    private function setStepThreeDataFromRequest(TmpFinalizeRequestDto $dto): self
    {
        $city = City::find($dto->city_id);
        $this->dbModel->setAttribute('first_name', $dto->first_name)
            ->setAttribute('middle_name', $dto->middle_name)
            ->setAttribute('last_name', $dto->last_name)
            ->setAttribute('idcard_issue_date', $dto->issue_date)
            ->setAttribute('idcard_valid_date', $dto->valid_date)
            ->setAttribute('city_id', $dto->city_id)
            ->setAttribute('address', $dto->address)
            //TODO: demand those fields from api client when not parsed from MVR REPORT!
            ->setAttribute('city_name', $city?->name)
            ->setAttribute('image', $dto->image)
            ->setAttribute('issue_by', $dto->issue_by ?: $city?->name)
            ->setAttribute('issue_by_city_id', $dto->issue_by_city_id ?: 1/*$dto->city_id*/)
            ->setAttribute('issue_by_city_name', $dto->issue_by_city_name ?: 'МВР')
            ->setAttribute('sex', $dto->sex ?: ''/*$this->getSexFromPin($tmpRequest->pin)*/);
        return $this;
    }

    private function setProduct(int $productId): self
    {
        $this->dbModel->product_id = $this->product
            ->loadById($productId)
            ->dbModel()->product_id;
        return $this;
    }

    private function save(): self
    {
        if (!$this->dbModel->save()) {
            throw new TmpRequestNotSaved($this->dbModel->phone);
        }

        return $this;
    }

    private function setMvrReport(): self
    {
        $this->mvrReport->buildFromLoanRequest($this);

        return $this;
    }

    private function addAgreements(array $agreements): self
    {
        $this->agreements->addToTmpRequest($this->dbModel->getKey(), $agreements);
        return $this;
    }

    private function setCalculator(): self
    {
        $dbProduct = $this->product->dbModel();
        $this->calculator->build([
            'air' => $dbProduct->getInterestRate($this->dbModel->period_requested, $this->dbModel->amount_requested),
            'apr' => $dbProduct->getPenaltyRate($this->dbModel->period_requested, $this->dbModel->amount_requested),
            'discountPercent' => 0,
            'installmentModifier' => $this->product->dbModel()->getInstallmentModifier(),
            'utilisationDate' => $this->currentDate->nowString(),
            'startFromDate' => $this->currentDate->nowString(),
            'currentDate' => $this->currentDate->nowString(),
            'amount' => $this->dbModel->amount_requested,
            'numberOfInstallments' => $this->dbModel->period_requested,
            'productType' => $this->product->dbModel()->productGroup->name,
        ]);
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function product(): Product
    {
        return $this->product;
    }

    public function calculator(): LoanCalculator
    {
        return $this->calculator;
    }

    public function mvrReport(): MvrReport
    {
        return $this->mvrReport;
    }
}
