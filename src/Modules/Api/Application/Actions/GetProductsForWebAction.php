<?php

namespace Modules\Api\Application\Actions;

use Illuminate\Support\Facades\Cache;
use Modules\Api\Http\Dto\GetProductsDto;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\ClientProduct;
use Modules\Common\Models\Office;
use Modules\Common\Models\Product;
use Modules\Product\Repository\ProductRepository;
use StikCredit\Calculators\Calculator;
use StikCredit\Calculators\LoanCarton;

class GetProductsForWebAction implements ApiActionInterface
{
    private array $prodSettingNames = [
        'amount_step',
        'min_amount',
        'max_amount',
        'default_amount',

        'period_step',
        'min_period',
        'max_period',
        'default_period',
        'period', // type: day, week, month

        'discount_step',
        'min_discount_percent',
        'max_discount_percent',
        'default_discount_percent',
    ];

    public function __construct(
        private ProductRepository $productRepository
    ) {}

    public function execute(GetProductsDto|Dto $dto): array
    {
        // return $this->getProductDataForWebSiteOnly();

        $clientId = !empty($dto->client_id) ? (int) $dto->client_id : null;

        $key = 'api.getProducts';
        if (!empty($clientId)) {
            $key = 'api.getProducts' . '.' . $clientId;
        }

        // Cache::forget($key);
        return Cache::remember($key, 10, function () use($clientId) {
            return $this->getProductDataForWebSiteOnly($clientId);
        });
    }

    private function getProductDataForWebSiteOnly(?int $clientId = null): array
    {
        $result = [];
        $products = $this->productRepository->getForOnlineOffice(true);

        $clientCustomProducts = [];
        if (!empty($clientId)) {
            $clientCustomProducts = ClientProduct::where('client_id', $clientId)
                ->where('active', 1)
                ->pluck('product_id')
                ->toArray();
        }

        /**
         * @var Product $product
         */
        foreach ($products as $product) {
            $pid = $product->product_id;

            if (!empty($product->client_related) && !in_array($pid, $clientCustomProducts)) {
                continue;
            }

            $settings = getKeyValueArray(
                $this->productRepository->getProductSettingsByNames($pid, $this->prodSettingNames)
            );
            $result[$pid] = [
                'product_id' => $pid,
                'trade_name' => $product->trade_name,
                'description' => $product->description,
                'slider_data' => $settings,
                'slider_stats' => $this->getLoanParams($product),
                'productGroup' => $product->productGroup->name
            ];
        }

        ksort($result);

        $result = $this->addEur($result);

        return $result;
    }

    private function addEur(array $result): array
    {
        foreach ($result as $productId => &$productData) {
            $productData['slider_data']['default_amount_eur'] = amountEur($productData['slider_data']['default_amount'], '');
            $productData['slider_data']['min_amount_eur'] = amountEur($productData['slider_data']['min_amount'], '');
            $productData['slider_data']['max_amount_eur'] = amountEur($productData['slider_data']['max_amount'], '');

            $productData['slider_stats']['totalAmount_eur'] = amountEur($productData['slider_stats']['totalAmount'], '');
            $productData['slider_stats']['installmentAmount_eur'] = amountEur($productData['slider_stats']['installmentAmount'], '');
            $productData['slider_stats']['interest_eur'] = amountEur($productData['slider_stats']['interest'], '');
            $productData['slider_stats']['paid_interest_eur'] = amountEur($productData['slider_stats']['paid_interest'], '');
            $productData['slider_stats']['penalty_eur'] = amountEur($productData['slider_stats']['penalty'], '');
            $productData['slider_stats']['paid_penalty_eur'] = amountEur($productData['slider_stats']['paid_penalty'], '');
            $productData['slider_stats']['totalIncreasedAmount_eur'] = amountEur($productData['slider_stats']['totalIncreasedAmount'], '');
            $productData['slider_stats']['totalIncreasedAmount_eur'] = amountEur($productData['slider_stats']['totalIncreasedAmount'], '');

            if (isset($productData['slider_stats']['discounts'])) {
                $productData['slider_stats']['discounts']['totalAmountDiscount_eur'] = amountEur($productData['slider_stats']['discounts']['totalAmountDiscount'], '');
                $productData['slider_stats']['discounts']['installmentAmountDiscount_eur'] = amountEur($productData['slider_stats']['discounts']['installmentAmountDiscount'], '');
                $productData['slider_stats']['discounts']['interestDiscount_eur'] = amountEur($productData['slider_stats']['discounts']['interestDiscount'], '');
                $productData['slider_stats']['discounts']['penaltyDiscount_eur'] = amountEur($productData['slider_stats']['discounts']['penaltyDiscount'], '');
                $productData['slider_stats']['discounts']['totalIncreasedAmountDiscount_eur'] = amountEur($productData['slider_stats']['discounts']['totalIncreasedAmountDiscount'], '');
            }
        }

        return $result;
    }

    private function getLoanParams(Product $product): array
    {
        $loanCalculator = $product->getCalculatorWithDefaultValues(false);
        if (!$loanCalculator) {
            return [];
        }
        $loanCarton = $loanCalculator->loanCarton();

        $installmentTotalAmount = $loanCalculator->installmentsCollection()->sum('installmentAmount');
        $period = $loanCalculator->loanConfig()->numberOfInstallments;
        return [
            'totalAmount' => $installmentTotalAmount,
            'period' => $period,
            'periodLabel' => $product->getPeriodLabel($period),
            'installmentAmount' => $loanCarton->monthlyInstallmentAmount,
            'interest' => $loanCarton->interest,
            'paid_interest' => $loanCarton->paidInterest,
            'penalty' => $loanCarton->penalty,
            'paid_penalty' => $loanCarton->paidPenalty,
            'installmentsCount' => $loanCalculator->installmentsCollection()->count(),
            'totalIncreasedAmount' => Calculator::sub($installmentTotalAmount, $loanCalculator->loanConfig()->amount),
            'discounts' => $this->getDiscountData($product, $installmentTotalAmount, $loanCarton)
        ];
    }

    private function getDiscountData(Product $product, int $installmentTotalAmount, LoanCarton $loanCarton): array
    {
        $loanCalculatorWithDiscount = $product->getCalculatorWithDefaultValues();
        if (!$loanCalculatorWithDiscount) {
            return [];
        }

        $loanCartonWithDiscount = $loanCalculatorWithDiscount->loanCarton();
        $totalAmountDiscount = $installmentTotalAmount - $loanCalculatorWithDiscount->installmentsCollection()->sum('installmentAmount');

        return [
            'totalAmountDiscount' => $totalAmountDiscount,
            'installmentAmountDiscount' => $loanCarton->monthlyInstallmentAmount - $loanCartonWithDiscount->monthlyInstallmentAmount,
            'interestDiscount' => $loanCarton->interest - $loanCartonWithDiscount->interest,
            'penaltyDiscount' => $loanCarton->penalty - $loanCartonWithDiscount->penalty,
            'totalIncreasedAmountDiscount' => $loanCarton->monthlyInstallmentAmount - $loanCartonWithDiscount->monthlyInstallmentAmount,
        ];
    }
}
