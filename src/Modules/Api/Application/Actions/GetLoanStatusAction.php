<?php

namespace Modules\Api\Application\Actions;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Api\Services\TokenService;
use Modules\Common\Application\Actions\PrepareCalculatorDataAction;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Head\Repositories\InstallmentRepository;

class GetLoanStatusAction implements ApiActionInterface
{
    public function execute(SignContractDto|Dto $dto): array
    {
        /** @var Loan $loan */
        $loan = Loan::get($dto->loan_id);
        if (empty($loan?->loan_id)) {
            return [
                'success' => false,
                'response' => ''
            ];
        }

        $statusId = $loan->loan_status_id ?? null;

        TokenService::validateClientOwnership($loan->client_id);

        if (!empty($dto->client_id) && $dto->client_id != $loan->client_id) {
            throw new \Exception('Wrong loan!');
        }

        if (empty($statusId)) {
            return [
                'success' => false,
                'response' => ''
            ];
        }

        return ['loan_data' => $this->getLoanData($loan)];
    }

    public function getLoanData(Loan $loan): array
    {
        $nextInstallment = app(InstallmentRepository::class)->getNextInstallment($loan->getKey());
        if (!$nextInstallment || !$nextInstallment->due_date->greaterThanOrEqualTo(now())) {
            $nextInstallment = null;
        }

        return array_merge(
            $loan->getLoanMainData(),
            $this->getInstallmentsMainData($loan),
            $this->getAdditionalInstallmentsData($loan, $nextInstallment),
            $this->getLoanTotals($loan, $nextInstallment),
        );
    }

    private function getInstallmentsMainData(Loan $loan): array
    {
        $installments = [];

        $instObjects = $loan->getAllInstallments();
        foreach ($instObjects as $inst) {
            $innn = [
                'seq_num' => $inst->seq_num,
                'due_date' => $inst->due_date->format('Y-m-d'),

                'paid' => $inst->paid,

                'total_amount' => $inst->getPrimaryAmount(),
                'total_rest_amount' => $inst->getPrimaryTotalRestAmount(),
                'total_paid_amount' => $inst->getPrimaryTotalPaidAmount(),
            ];

            $innn['total_amount_eur'] = amountEur($innn['total_amount'], '');
            $innn['total_rest_amount_eur'] = amountEur($innn['total_rest_amount'], '');
            $innn['total_paid_amount_eur'] = amountEur($innn['total_paid_amount'], '');

            $installments[] = $innn;
        }

        $firstInst = $instObjects->first();
        $defaultInstallmentAmount = $firstInst->getPrimaryAmount();
        $defaultInstallmentAmountDiscounted = $firstInst->getPrimaryAmount();

        if ($loan->discount_percent > 0) {
            $cacheKey = 'getInstallmentsMainData_' . $loan->loan_id;

            $loanAmounts = null;
            if ($loan->loan_status_id >= 5 ) {
                $loanAmounts = Cache::get($cacheKey, null);
            }

            if (empty($loanAmounts)) {
                $loanAmounts = app(PrepareCalculatorDataAction::class)->execute([
                    'loanId' => $loan->loan_id,
                    'productId' => $loan->product_id,
                    'principle' => intToFloat($loan->amount_approved),
                    'period' => $loan->period_approved,
                    'discount' => $loan->discount_percent,
                    'interest' => $loan->interest_percent,
                    'penalty' => $loan->penalty_percent
                ]);
            }

            if ($loan->loan_status_id >= 5 ) {
                Cache::put($cacheKey, $loanAmounts, Carbon::now()->addMinutes(10));
            }

            $defaultInstallmentAmount = intToFloat($loanAmounts['installmentAmount'] + $loanAmounts['installmentAmountDiscount']);
            $defaultInstallmentAmountDiscounted = intToFloat($loanAmounts['installmentAmount']);
        }

        return [
            'installments' => $installments,
            'default_installment_amount' => $defaultInstallmentAmount,
            'default_installment_amount_eur' => amountEur($defaultInstallmentAmount, ''),
            'default_installment_amount_discounted' => $defaultInstallmentAmountDiscounted,
            'default_installment_amount_discounted_eur' => amountEur($defaultInstallmentAmountDiscounted, ''),
        ];
    }

    private function getAdditionalInstallmentsData(Loan $loan, ?Installment $nextInstallment = null): array
    {
        $arr = [];
        $arr['next_installment_due_date'] = 'изтекъл падеж';
        $arr['next_installment_index']    = null;
        $arr['next_installment_amount']   = intToFloat(0);

        if (!empty($nextInstallment->installment_id)) {
            $arr['next_installment_index'] = $nextInstallment->seq_num;
            $arr['next_installment_due_date'] = $nextInstallment->due_date->format('d.m.Y');
            $arr['next_installment_amount']   = $nextInstallment->getPrimaryTotalRestAmount();
        }

        $arr['next_installment_amount_eur'] = amountEur($arr['next_installment_amount'], '');

        return $arr;
    }

    private function getLoanTotals(Loan $loan, ?Installment $nextInstallment = null): array
    {
        $dbCarton = $loan->getCartonDb();

        $arr = [];
        $arr['total_amount'] = intToFloat(0);
        $arr['total_paid_amount'] = intToFloat(0);
        $arr['total_rest_amount'] = intToFloat(0);
        $arr['total_rest_amount_early'] = intToFloat($loan->amount_approved);

        if ($loan->isActive() || $loan->isApproved()) {
            $arr['total_amount'] = intToFloat($dbCarton['due_amount_total']);
            $arr['total_paid_amount'] = intToFloat($dbCarton['repaid_amount_total']);
            $arr['total_rest_amount'] = intToFloat($dbCarton['due_amount_total'] - $dbCarton['repaid_amount_total']);
            $arr['total_rest_amount_early'] = intToFloat($loan->getTotalAmountForRefinanceForAllActiveLoans());
        }

        $arr['current_overdue_amount'] = intToFloat($dbCarton['current_overdue_amount']);

        $arr['total_rest_next_payment'] = intToFloat($dbCarton['outstanding_amount_total']);
        if (!empty($nextInstallment->installment_id)) {
            $arr['total_rest_next_payment'] = array_sum([
                $nextInstallment->getPrimaryTotalRestAmount(),
                intToFloat($dbCarton['current_overdue_amount'])
            ]);
        }

        $arr['total_amount_eur'] = amountEur($arr['total_amount'], '');
        $arr['total_paid_amount_eur'] = amountEur($arr['total_paid_amount'], '');
        $arr['total_rest_amount_eur'] = amountEur($arr['total_rest_amount'], '');
        $arr['total_rest_amount_early_eur'] = amountEur($arr['total_rest_amount_early'], '');
        $arr['current_overdue_amount_eur'] = amountEur($arr['current_overdue_amount'], '');
        $arr['total_rest_next_payment_eur'] = amountEur($arr['total_rest_next_payment'], '');

        return $arr;
    }
}
