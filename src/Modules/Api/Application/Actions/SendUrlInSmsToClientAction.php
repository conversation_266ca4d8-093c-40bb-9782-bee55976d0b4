<?php

namespace Modules\Api\Application\Actions;

use Illuminate\Support\Facades\Log;
use Modules\Api\Http\Dto\SendUrlInSmsToClientDTO;
use Modules\Common\Enums\InviteUrlActionEnum;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Loan;
use Modules\Communication\Services\SmsService;
use Modules\Head\Services\ClientService;
use Throwable;

readonly class SendUrlInSmsToClientAction implements ApiActionInterface
{

    public function __construct(
        private SmsService $smsService,
        private ClientService $clientService,
    ) {
    }

    public function execute(SendUrlInSmsToClientDTO|Dto $dto): array
    {
        $payload = $dto->payload;
        $payload['client_id'] = $dto->clientId;

        $client = $this->clientService->getClientById($dto->clientId);
        $loan = $client->getClientLoanById($dto->loanId);

        $this->sendSmsLinkForSign($loan, $dto->action, $payload);

        return [];
    }

    public function sendSmsLinkForSign(
        Loan $loan,
        InviteUrlActionEnum $dtoAction,
        array $payload
    ): bool {

        try {
            $this->smsService->sendSignLink($loan);

        } catch (Throwable $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());
            throw  $e;
//            return false;
        }

        return true;
    }
}
