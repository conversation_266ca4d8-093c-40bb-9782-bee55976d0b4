<?php

namespace Modules\Api\Application\Actions;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Http\Dto\GetProductsDto;
use Modules\Api\Services\TokenService;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Client;
use Modules\Discounts\Repositories\DiscountByPhoneRepository;

class GetClientAction implements ApiActionInterface
{
    public function execute(Dto $dto): array
    {
        TokenService::validateClientOwnership($dto->client_id);

        $cacheKey = 'GetClientAction_' . $dto->client_id;
        $cacheData = Cache::get($cacheKey);
        if (!empty($cacheData)) {
            return $cacheData;
        }

        /** @var Client $client */
        $client = Client::get($dto->client_id);
        if (!$client) {
            throw new ClientNotFoundById($dto->client_id);
        }

        $arr = $client->getClientMainData();
        $arr['idcard'] = $this->getClientIdcardMainData($client);
        $arr['discounts'] = $this->getClientDiscountData($client, $dto);
        $arr['notifications'] = $client->mappedNotificationSettings();

        $loan = $client->getLastLoanByStatuses();
        if (!empty($loan->loan_id)) {
            $arr['loan_data'] = app(GetLoanStatusAction::class)->getLoanData($loan);
        }

        Cache::put($cacheKey, $arr, Carbon::now()->addSeconds(30));

        return $arr;
    }

    private function getClientIdcardMainData(Client $client): array
    {
        $idcard = $client->clientLastIdCard();
        if (!$idcard) {
            return [];
        }

        return [
            'client_idcard_id' => $idcard->client_idcard_id,
            'client_id'        => $idcard->client_id,
            'pin'              => $idcard->pin,
            'idcard_number'    => $idcard->idcard_number,
            'city_id'          => $idcard->city_id,
            'idcard_issued_id' => $idcard->idcard_issued_id,
            'issue_date'       => $idcard->issue_date,
            'valid_date'       => $idcard->valid_date,
            'post_code'        => $idcard->post_code,
            'address'          => $idcard->address,
            'sex'              => $idcard->sex,
            'lifetime_idcard'  => $idcard->lifetime_idcard,
        ];
    }

    // Returns array in format: [product_id_1 => 15, product_id_2 => 18, product_id_3 => 8];
    private function getClientDiscountData(Client $client, $dto): array
    {
        $cacheKey = 'getClientDiscountData' . $client->client_id;
        $cacheData = Cache::get($cacheKey);
        if (!empty($cacheData)) {
            return $cacheData;
        }

        $productsForWeb = app(GetProductsForWebAction::class)->execute(
            new GetProductsDto(
                $dto?->ip ?? '***********',
                $dto?->browser ?? 'internal_call',
            )
        );
        if (empty($productsForWeb)) {
            return [];
        }


        $productIds = array_keys($productsForWeb);

        // both are arrays in format: [product_id => discount_percent, product_id => discount_percent]
        $discountsByPhone = app(DiscountByPhoneRepository::class)->getPercentsByPhoneAndProducts($client->phone, $productIds);
        $discountsByClient = $client->getActualDiscountsForProducts($productIds);

        // merge and take highest
        $finalDiscounts = [];
        foreach (array_unique(array_merge(array_keys($discountsByPhone), array_keys($discountsByClient))) as $productId) {

            if (empty($discountsByPhone[$productId]['discount']) && empty($discountsByClient[$productId]['discount'])) {
                continue;
            }

            $finalDiscount = [];
            if (isset($discountsByPhone[$productId])) {
                $finalDiscount = $discountsByPhone[$productId];
            }
            if (isset($discountsByClient[$productId])) {
                if (empty($finalDiscount)) {
                    $finalDiscount = $discountsByClient[$productId];
                } else if ($discountsByClient[$productId]['discount'] > $finalDiscount['discount']) {
                    $finalDiscount = $discountsByClient[$productId];
                }
            }

            $finalDiscounts[$productId] = $finalDiscount;
        }


        $cacheMin = isStage() ? 1 : 5;
        Cache::put($cacheKey, $finalDiscounts, Carbon::now()->addMinutes($cacheMin));

        return $finalDiscounts;
    }
}
