<?php

namespace Modules\Api\Application\Actions;

use Modules\Api\Domain\Exceptions\LoanCalculatorProblem;
use Modules\Api\Domain\Exceptions\ProductNotFound;
use Modules\Api\Http\Dto\CalculateLoanStatsDto;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Product;
use StikCredit\Calculators\Calculator;
use StikCredit\Calculators\Installments\DefaultInstallment;

class CalculateLoanStatsAction implements ApiActionInterface
{
    public function __construct(private CurrentDate $currentDate) {}

    public function execute(CalculateLoanStatsDto|Dto $dto): array
    {
        /** @var Product $product */
        $product = Product::find($dto->product_id);
        if (!$product) {
            throw new ProductNotFound($dto->product_id);
        }

        $loanCalculator = $product->getCalculator(
            $dto->period,
            $dto->amount,
            0,
            $this->currentDate
        );
        $loanCarton = $loanCalculator?->loanCarton();
        if (!$loanCarton) {
            throw new LoanCalculatorProblem();
        }
        $installmentTotalAmount = $loanCalculator->installmentsCollection()->sum('installmentAmount');

        $discounts = [];
        if ($dto->discount > 0) {
            $loanCalculatorWithDiscount = $product->getCalculator(
                $dto->period,
                $dto->amount,
                $dto->discount,
                $this->currentDate
            );
            $loanCartonWithDiscount = $loanCalculatorWithDiscount->loanCarton();
            $installmentTotalAmountWithDiscount = $loanCalculatorWithDiscount->installmentsCollection()->sum('installmentAmount');

            $discounts = [
                'totalAmountDiscount' => $installmentTotalAmount - $installmentTotalAmountWithDiscount,
                'installmentAmountDiscount' => $loanCarton->monthlyInstallmentAmount - $loanCartonWithDiscount->monthlyInstallmentAmount,
                'interestDiscount' => $loanCarton->interest - $loanCartonWithDiscount->interest,
                'penaltyDiscount' => $loanCarton->penalty - $loanCartonWithDiscount->penalty,
                'totalIncreasedAmountDiscount' => $loanCarton->monthlyInstallmentAmount - $loanCartonWithDiscount->monthlyInstallmentAmount,
            ];

            $discounts = $this->addEurForDiscount($discounts);
        }

        $installmentCollection = $loanCalculator->installmentsCollection();

        $result = [
            'totalAmount' => intToFloat($installmentTotalAmount),
            'period' => $dto->period,
            'periodLabel' => $product->getPeriodLabel($dto->period),
            'installmentAmount' => intToFloat($loanCarton->monthlyInstallmentAmount),
            'interest' => intToFloat($loanCarton->interest),
            'paid_interest' => intToFloat($loanCarton->paidInterest),
            'penalty' => intToFloat($loanCarton->penalty),
            'paid_penalty' => intToFloat($loanCarton->paidPenalty),
            'installments' => $installmentCollection,
            'installmentsCount' => $installmentCollection->count(),
            'totalIncreasedAmount' => Calculator::sub($installmentTotalAmount, $dto->amount),
            'discounts' => $discounts,
            'product_id' => $product->product_id,
            'gpr' => round($loanCarton->gpr, 2)
        ];

        $result = $this->addEur($result);

        return $result;
    }

    private function addEur(array $result): array
    {
        $result['totalAmount_eur'] = amountEur($result['totalAmount'], '');
        $result['installmentAmount_eur'] = amountEur($result['installmentAmount'], '');
        $result['interest_eur'] = amountEur($result['interest'], '');
        $result['paid_interest_eur'] = amountEur($result['paid_interest'], '');
        $result['penalty_eur'] = amountEur($result['penalty'], '');
        $result['paid_penalty_eur'] = amountEur($result['paid_penalty'], '');
        $result['totalIncreasedAmount_eur'] = amountEur($result['totalIncreasedAmount'], '');

        return $result;
    }

    private function addEurForDiscount(array $discounts): array
    {
        $discounts['totalAmountDiscount_eur'] = amountEur($discounts['totalAmountDiscount'], '');
        $discounts['installmentAmountDiscount_eur'] = amountEur($discounts['installmentAmountDiscount'], '');
        $discounts['interestDiscount_eur'] = amountEur($discounts['interestDiscount'], '');
        $discounts['penaltyDiscount_eur'] = amountEur($discounts['penaltyDiscount'], '');
        $discounts['totalIncreasedAmountDiscount_eur'] = amountEur($discounts['totalIncreasedAmountDiscount'], '');

        return $discounts;
    }
}
