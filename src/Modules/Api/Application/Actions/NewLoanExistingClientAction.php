<?php

namespace Modules\Api\Application\Actions;

use Carbon\Carbon;
use Modules\Admin\Repositories\OfficeRepository;
use Modules\Api\Application\Actions\GetLoanStatusAction;
use Modules\Api\Domain\Exceptions\ClientHasUnreceivedEasyPayMoney;
use Modules\Api\Domain\Exceptions\ClientIsBlocked;
use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Domain\Exceptions\MaxLoanCountPerDayReached;
use Modules\Api\Http\Dto\NewLoanExistingClientDto;
use Modules\Api\Jobs\ProcessTmpRequestsAfterNewLoanJob;
use Modules\Api\Services\TokenService;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Discounts\Repositories\ClientDiscountActualRepository;
use Modules\Discounts\Repositories\DiscountByPhoneRepository;
use Modules\Head\Repositories\InstallmentRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Services\ClientCheckerService;
use Modules\ThirdParty\Services\MvrService;

final class NewLoanExistingClientAction implements ApiActionInterface
{
    public function __construct(
        private ExistingClientCreateLoanAction $action,
        private readonly DiscountByPhoneRepository $discountByPhoneRepository,
    ) {
    }

    public function execute(NewLoanExistingClientDto|Dto $dto): array
    {
        TokenService::validateClientOwnership($dto->client_id);

        /** @var Client $client */
        $client = Client::get($dto->client_id);
        if (!$client) {
            throw new ClientNotFoundById($dto->client_id);
        }

        $loansToday = app(LoanRepository::class)->countLoansForToday($client->getKey());
        if ($loansToday >= Loan::MAX_LOAN_COUNT_PER_DAY) {
            throw new MaxLoanCountPerDayReached();
        }

        if ($client->isBlocked()) {
            throw new ClientIsBlocked($client->getKey());
        }

        if (ClientCheckerService::hasUnreceivedEasyPayMoney($client->getKey())) {
            throw new ClientHasUnreceivedEasyPayMoney($client->getKey());
        }

        $discount = 0;
        if (!empty($dto->product_id)) {
            $clientDiscountActual = app(ClientDiscountActualRepository::class)->getClientDiscountWithProduct(
                $client->getKey(),
                $dto->product_id,
                Carbon::now(),
            );
            $clientDiscount = 0;
            if ($clientDiscountActual) {
                $clientDiscount = (int) $clientDiscountActual->percent;
            }

            $discountByPhone = (int) $this->discountByPhoneRepository
                ->getPercentByPhoneAndProduct($client->phone, $dto->product_id);


            if ($clientDiscount > 0 || $discountByPhone > 0) {
                $discount = max($clientDiscount, $discountByPhone);
            }
        }

        $loanDto = new LoanDto(
            null,
            Office::OFFICE_ID_WEB,
            Administrator::SYSTEM_ADMINISTRATOR_ID,
            $dto->payment_method_id,
            $dto->product_id,
            $dto->amount_requested,
            $dto->period_requested,
            $discount,
            [],
            null,
            null,
            $dto->ip,
            $dto->browser,
            null,
            null,
            null,
            $dto->iban,
            insurance: $dto->insurance
        );
        $loanDto->source = LoanSourceEnum::WEBSITE;
        $loanDto->bank_account_id = app(OfficeRepository::class)->getMainPaymentAccountId($dto->payment_method_id);

        // run mvr report
        $mvrService = app(MvrService::class);
        try {
            $mvrReport = $mvrService->addMvrReport(
                $client->pin,
                $client->idcard_number
            );
        } catch (\Throwable $e) {
            report($e);
        }

        $this->action->execute($client, $loanDto);

        $newLoan = $this->action->getDbLoan();
        $newLoan->refresh();

        if (!empty($mvrReport->mvr_report_id)) {
            $mvrParsedData = json_decode($mvrReport->parsed_data ?? '[]', true);

            $mvrService->linkReport(
                $mvrReport->mvr_report_id,
                $newLoan->client_id,
                $newLoan->loan_id,
                $mvrParsedData
            );
        }

        ProcessTmpRequestsAfterNewLoanJob::dispatch($newLoan, $dto->utm_tracker_id);

        return [
            'client_id' => $newLoan->client_id,
            'loan_id' => $newLoan->loan_id,
            'loan_data' => app(GetLoanStatusAction::class)->getLoanData($newLoan),
            'token' => TokenService::getForClient($client),
            'redirect' => [
                'url' => 'contract',
                'url_params' => [
                    'loan_id' => $newLoan->loan_id,
                    'loan_status_id' => $newLoan->loan_status_id,
                ],
            ]
        ];
    }
}
