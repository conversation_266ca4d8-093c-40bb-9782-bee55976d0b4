<?php

namespace Modules\Api\Application\Actions;

use Modules\Admin\Repositories\OfficeRepository;
use Modules\Api\Domain\Entities\LoanRequest;
use Modules\Api\Http\Dto\TmpFinalizeRequestDto;
use Modules\Api\Jobs\ProcessTmpRequestsAfterNewLoanJob;
use Modules\Api\Services\TokenService;
use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\NotificationSetting;
use Modules\Common\Models\Office;
use Modules\Common\Models\TmpRequest;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Http\Dto\AddressDto;
use Modules\Sales\Http\Dto\BankDto;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Dto\IdCardDto;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Http\Dto\NotificationSettingDto;
use Modules\Sales\Http\Dto\PhoneDto;
use Modules\Sales\Http\Dto\PictureDto;

class RegistrationStepThreeAction implements ApiActionInterface
{
    //use PinTrait;
    public function __construct(
        private LoanRequest  $loanRequest,
        private NewAppAction $newAppAction,
    ) {}

    public function execute(TmpFinalizeRequestDto|Dto $dto): array
    {
        $tmpReqDomainObj = $this->loanRequest
            ->loadById($dto->request_id);

        $tmpRequest = $tmpReqDomainObj
            ->reportManually($dto)
            ->dbModel();

        $clientDto = self::clientDtoFromTmpRequest($tmpRequest);
        $clientDto->relationDto->loanDto->source = LoanSourceEnum::WEBSITE;
        $clientDto->relationDto->idCardDto->lifetime_idcard = $dto->lifetime_idcard;

        $clientDto->relationDto->loanDto->setDiscount($clientDto->pin, $tmpRequest->phone, $tmpRequest->product_id);

        $client = $this->newAppAction->execute($clientDto);
        $loanModel = $client->dbLoan();

        ProcessTmpRequestsAfterNewLoanJob::dispatch($loanModel, $dto->utm_tracker_id);

        return [
            'client' => $client->dbModel()->getClientMainData(),
            'loan'   => $loanModel->getLoanMainData(),
            'token'  => app(TokenService::class)->getForClient($client->dbModel()),
        ];
    }

    public static function clientDtoFromTmpRequest(TmpRequest $t): ClientDto
    {
        $bank_account_id = app(OfficeRepository::class)->getMainPaymentAccountId($t->payment_method_id);

        return ClientDto::getFrom([
            'pin' => $t->pin,
            'idcard_number' => $t->idcard_number,
            'first_name' => $t->first_name,
            'middle_name' => $t->middle_name,
            'last_name' => $t->last_name,
            'first_name_latin' => $t->first_name_latin,
            'middle_name_latin' => $t->middle_name_latin,
            'last_name_latin' => $t->last_name_latin,
            'phoneDtoArr' => [new PhoneDto($t->phone, 1)],
            'email' => $t->email,
            'pictureDto' => PictureDto::getFrom([
                'image' => $t->image,
                'pin' => $t->pin
            ]),
            'addressDtoArr' => [
                AddressDto::from([
                    'city_id' => $t->city_id,
                    'address' => $t->address,
                    'type' => AddressTypeEnum::Current->value,
                ]),
                AddressDto::from([
                    'city_id' => $t->city_id,
                    'address' => $t->address,
                    'type' => AddressTypeEnum::IdCard->value,
                ]),
            ],
            'idCardDto' => IdCardDto::from([
                'pin' => $t->pin,
                'idcard_number' => $t->idcard_number,
                'issue_date' => $t->idcard_issue_date,
                'valid_date' => $t->idcard_valid_date,
                'issue_by' => $t->issue_by,
                'idcard_issued_id' => $t->issue_by_city_id,
                'issue_by_city_name' => $t->issue_by_city_name,
                'sex' => $t->sex,
                'city_id' => $t->city_id,
                'city_name' => $t->city_name,
                'address' => $t->address,
                'image' => $t->image
            ]),
            'loanDto' => LoanDto::getFrom([
                'loan_id' => null,
                'office_id' => Office::OFFICE_ID_WEB,
                'administrator_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'payment_method_id' => $t->payment_method_id,
                'product_id' => $t->product_id,
                'loan_sum' => $t->amount_requested,
                'loan_period_days' => $t->period_requested,
                'discount_percent' => 0,
                'refinanced_loans' => null,
                'contact' => null,
                'guarantor' => null,
                'ip' => $t->ip ?? null,
                'browser' => $t->browser ?? null,
                'bank_account_id' => $bank_account_id,
                'insurance' => (int)$t->insurance,
            ]),
            'representativeDto' => null,
            'bankDto' => $t->iban ? new BankDto($t->iban, null, null) : null,
            'legalStatus' => null,
            'citizenshipType' => null,
            'gender' => $t->sex,
            'legalStatusCode' => null,
            'economySectorCode' => null,
            'industryCode' => null,
            'notificationSettingDtoArr' => self::getNotificationSettingsDtos(),
        ]);
    }

    /* @return NotificationSettingDto[] */
    public static function getNotificationSettingsDtos(): array
    {
        $defaults = [];
        foreach (NotificationSetting::notificationDefaultValue() as $type => $channels) {
            foreach ($channels as $channel) {
                $defaults[] = new NotificationSettingDto($type, $channel, NotificationSetting::NOTIFICATION_SETTING_DEFAULT_VALUE);
            }
        }
        return $defaults;
    }

//    private function getSexFromPin(?string $pin): string
//    {
//        $parsedData = $this->getAgeAndSex($pin);
//        if (!empty($parsedData['sex'])) {
//            return $parsedData['sex'] == 2 ? 'male' : 'female';
//        }
//        return 'male';
//    }
}
