<?php

namespace Modules\Api\Application\Actions\Auth;

use Modules\Api\Application\Actions\ApiActionInterface;
use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Domain\Exceptions\Login\SmsCodeBelongsToDifferentClient;
use Modules\Api\Domain\Exceptions\Login\SmsCodeIsUsedOrExpired;
use Modules\Api\Domain\Exceptions\Login\SmsCodeNotFoundByCode;
use Modules\Api\Domain\Exceptions\Login\SmsCodeWasNotSaved;
use Modules\Api\Http\Dto\SmsCodeLoginDto;
use Modules\Api\Services\TokenService;
use Modules\Api\Services\ThrottleService;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Client;
use Modules\Common\Models\FailedLoginAttempt;
use Modules\Common\Models\LoanStatus;
use Modules\Communication\Repositories\SmsLoginCodeRepository;

readonly class LoginBySmsCodeAction implements ApiActionInterface
{
    public function __construct(
        private SmsLoginCodeRepository $smsLoginCodeRepository,
        private ThrottleService $throttleService,
    ) {
    }

    public function execute(SmsCodeLoginDto|Dto $dto): array
    {
        $this->throttleService->check(self::class, $dto->client_id, $dto->ip);

        /** @var Client $client */
        $client = Client::find($dto->client_id);
        if (!$client) {
            throw new ClientNotFoundById($dto->client_id);
        }

        $loginSms = $this->smsLoginCodeRepository->getUnusedByCode($dto->code);
        if (!$loginSms) {
            $exception = new SmsCodeNotFoundByCode($dto->code);
            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);

            throw $exception;
        }

        if (!$this->smsLoginCodeRepository->isValid($loginSms)) {
            $exception = new SmsCodeIsUsedOrExpired();

            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);
            throw $exception;
        }

        if ($loginSms->client_id !== $dto->client_id) {
            $exception = new SmsCodeBelongsToDifferentClient($dto->code);

            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);
            throw $exception;
        }

        if (strlen($dto->ip) > 255 || !$loginSms->useCode($dto->browser, $dto->ip)) {
            $exception = new SmsCodeWasNotSaved($loginSms->sms_login_code_id);

            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);
            throw $exception;
        }


        /// by default false;
        $token = '';
        $hasVerifAction = false;
        if (!empty($client->client_id)){
            $token = TokenService::getForClient($client);
            $hasVerifAction = $client->hasVeriffAction();
        }

        $isNew = true;
        if (!empty($client->client_id)) {
            $isNew = $client->isNew();
        }

        $loanL = $client->getLastLoanByStatuses();


        /**
         * has_action_verif (true|false)
         * Если клиент подал заявку, и не въбрал никакого действия (veriff),
         * Етот параметр используется только при опции когда у клиента есть завка на подпись, в других кейсах не исп
         * тоесть если client null, нет такого клиента то тогда етот параметр игнорится.
         *
         * Нова страница верификация
         * Създава се нова страничка за верификация с текст:
         * Не чакай. Верифицирай се и вземи парите веднага.
         * Бутон “Верифицирай ме”
         * Алтернативно, изчакай обаждане от оператор. Отнема до 30 минути в работно време.
         * Бутон “Ще изчакам”
         */
        $hasVerifAction = false;
        $redirect = [
            'url' => 'verify',
            'url_params' => [],
        ];
        if (!empty($loanL->loan_id)) {
            $hasVerifAction = $client->hasVeriffAction();
            $redirect = [
                'url' => $loanL->loan_status_id === LoanStatus::NEW_STATUS_ID ? 'contract' : 'active.loan',
                'url_params' => [
                    'loan_id' => $loanL->loan_id,
                    'loan_status_id' => $loanL->loan_status_id,
                ],
            ];
        }


        return [
            'token' => $token,
            'new_client' => (int) $isNew,
            'has_action_verif' => $hasVerifAction,
            'redirect' => $redirect,
        ];
    }
}
