<?php

namespace Modules\Api\Application\Actions;

use Illuminate\Support\Facades\Log;
use Modules\Api\Services\TokenService;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

class ActiveLoanAction
{
    public function hasActiveLoan(int $clientId): array
    {
        try {

            TokenService::validateClientOwnership($clientId);

            return $this->wrapWithErrorHandling(__FUNCTION__, function () use ($clientId) {
                $this->validateClientId($clientId);

                $exists = $this->getClientActiveLoansQuery($clientId)->exists();

                return [
                    'success' => true,
                    'response' => [
                        'has_active_loans' => $exists,
                    ],
                ];
            });

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error("hasActiveLoan($clientId) - " . $msg);

            return [
                'success' => false,
                'response' => [],
            ];
        }
    }

    public function hasActiveOrProcessingLoan(int $clientId): array
    {
        try {

            TokenService::validateClientOwnership($clientId);

            return $this->wrapWithErrorHandling(__FUNCTION__, function () use ($clientId) {
                $this->validateClientId($clientId);

                $exists = Loan::where('client_id', $clientId)
                    ->whereIn('loan_status_id', [
                        LoanStatus::NEW_STATUS_ID,
                        LoanStatus::SIGNED_STATUS_ID,
                        LoanStatus::PROCESSING_STATUS_ID,
                        LoanStatus::APPROVED_STATUS_ID,
                        LoanStatus::ACTIVE_STATUS_ID,
                    ])
                    ->exists();

                return [
                    'success' => true,
                    'response' => [
                        'has_active_processing_loans' => $exists,
                    ],
                ];
            });

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error("hasActiveOrProcessingLoan($clientId) - " . $msg);

            return [
                'success' => false,
                'response' => [],
            ];
        }
    }

    public function getActiveLoansDueAmount(int $clientId): array
    {
        try {
            TokenService::validateClientOwnership($clientId);

            return $this->wrapWithErrorHandling(__FUNCTION__, function () use ($clientId) {
                $this->validateClientId($clientId);

                $loans = $this->getClientActiveLoansQuery($clientId)->get();
                $total = intToFloat($loans->sum(fn($loan) => $loan->getEarlyRepaymentDebtDb()));

                return [
                    'success' => true,
                    'response' => [
                        'total_due' => $total,
                        'total_due_eur' => amountEur($total, ''),
                    ],
                ];
            });

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error("getActiveLoansDueAmount($clientId) - " . $msg);

            return [
                'success' => false,
                'response' => [],
            ];
        }
    }

    public function getClientLastActiveLoanData(int $clientId, string $format = 'loan_history'): array
    {
        try {

            TokenService::validateClientOwnership($clientId);
            return $this->wrapWithErrorHandling(__FUNCTION__, function () use ($clientId, $format) {
                $this->validateClientId($clientId);

                /** @var \Modules\Common\Models\Loan $loan **/
                $loan = $this->getClientActiveLoansQuery($clientId)->orderBy('created_at', 'DESC')->first();
                if (empty($loan->loan_id)) {
                    return [
                        'success' => true,
                        'response' => [],
                    ];
                }

                $lArray = null;
                switch ($format) {
                    case 'loan_history':
                        // same format as here:
                        // src/Modules/Api/Application/Actions/LoanHistoryAction.php
                        $lArray = $loan->getLoanMainData();
                        $lArray['early_repayment_amount'] = intToFloat($loan->getEarlyRepaymentDebtDb());
                        $lArray['early_repayment_amount_eur'] = amountEur($lArray['early_repayment_amount'], '');
                        $lArray['documents'] = [];
                        break;

                    case 'loan_status':
                        $lArray = app(GetLoanStatusAction::class)->getLoanData($loan);
                        break;
                }

                return [
                    'success' => true,
                    'response' => [
                        'loan' => $lArray,
                    ],
                ];
            });

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error("getClientLastActiveLoanData($clientId) - " . $msg);

            return [
                'success' => false,
                'response' => [],
            ];
        }
    }

    public function getExtendLoanStats(int $clientId, int $loanId): array
    {
        try {
            TokenService::validateClientOwnership($clientId);

            $loan = Loan::where('loan_id', $loanId)->where('client_id', $clientId)->first();
            if (!$loan) {
                return [
                    'success' => false,
                    'response' => [],
                ];
            }

            if (!$loan->isActive()) {
                return [
                    'success' => false,
                    'response' => [],
                ];
            }

            $key = 'api_extend_loan_' . $loanId;
            return \Cache::remember($key, 60 * 5, function () use ($loan) {

                $defaultDaysToExtend = 30;
                $extendAmount = intToFloat($loan->getExtendLoanFeeAmountDb($defaultDaysToExtend));
                $installment = $loan->getFirstInstallmentForExtend();
                $newDueDate = $installment->getExtendedDueDate($defaultDaysToExtend);

                return [
                    'success' => true,
                    'response' => [
                        'days_for_extend' => $defaultDaysToExtend,
                        'amount' => $extendAmount,
                        'amount_eur' => amountEur($extendAmount, ''),
                        'old_due_date' => $installment->due_date->format('Y-m-d'),
                        'new_due_date' => $newDueDate->format('Y-m-d'),
                    ],
                ];

            });

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error("getExtendLoanStats($clientId, $loanId) - " . $msg);

            return [
                'success' => false,
                'response' => [],
            ];
        }
    }

    public function getLoanPaymentScheduleData(int $clientId, int $loanId): array
    {
        try {

            TokenService::validateClientOwnership($clientId);

            $loan = Loan::where('loan_id', $loanId)->where('client_id', $clientId)->first();
            if (!$loan) {
                return [
                    'success' => false,
                    'response' => [],
                ];
            }

            if (!$loan->isActive()) {
                return [
                    'success' => false,
                    'response' => [],
                ];
            }

            $key = 'api_loan_psch_' . $loanId;
            return \Cache::remember($key, 60 * 5, function () use ($loan) {

                $installmentsArr = [];
                foreach ($loan->getAllInstallments() as $inst) {
                    $innn = [
                        'seq_num' => $inst->seq_num,
                        'due_date' => $inst->due_date,
                        'status' => (
                            $inst->isPaid()
                            ? 'paid'
                            : ($inst->isInOverdue() ? 'overdue' : 'scheduled')
                        ),
                        'amount' => $inst->getPrimaryAmount(),
                    ];
                    $innn['amount_eur'] = amountEur($innn['amount'], '');

                    $installmentsArr[] = $innn;
                }


                $loanStats = $loan->loanActualStats;
                $totalsArr = [
                    'total_installments_count' => $loanStats->total_installments_count,
                    'total_paid_installments_count' => $loanStats->paid_installments_count,
                    'total_unpaid_installments_count' => $loanStats->unpaid_installments_count,
                    'total_due_amount' => $loanStats->outstanding_amount_total,
                    'total_overdue_amount' => $loanStats->accrued_amount_total,

                    'total_due_amount_eur' => amountEur($loanStats->outstanding_amount_total, ''),
                    'total_overdue_amount_eur' => amountEur($loanStats->accrued_amount_total, ''),
                ];

                return [
                    'success' => true,
                    'response' => [
                        'installments' => $installmentsArr,
                        'totals' => $totalsArr,
                    ],
                ];

            });

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error("getLoanPaymentSchedule($clientId, $loanId) - " . $msg);

            return [
                'success' => false,
                'response' => [],
            ];
        }
    }

    private function getClientActiveLoansQuery(int $clientId)
    {
        return Loan::where('client_id', $clientId)
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
    }

    private function validateClientId(?int $clientId): void
    {
        if (empty($clientId)) {
            throw new \InvalidArgumentException('No client ID provided');
        }
    }

    private function wrapWithErrorHandling(string $context, \Closure $callback): array
    {
        try {
            return $callback();
        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            Log::error("ActiveLoanAction::{$context} - " . $msg);

            return [
                'success' => false,
                'error' => $msg,
            ];
        }
    }
}
