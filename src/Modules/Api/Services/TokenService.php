<?php

namespace Modules\Api\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Modules\Common\Models\Client;
use Modules\Common\Services\BaseService;

class TokenService extends BaseService
{
    public static function getForClient(Client $client): string
    {
        $key = Client::LOGIN_TOKEN_KEY . "|{$client->client_id}";
        $ttl = Carbon::now()->addMinutes(60);

        return Cache::remember($key, $ttl, static function () use ($client) {
            /// before create new token delete all old tokens
            $client->tokens()->delete();

            /// after delete create new token and remember it in cache
            /// other ways when is called this function with this client
            /// if token is available will be return available token
            /// if not will be created
            return $client
                ->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))
                ->plainTextToken;
        });
    }

    public static function validateClientOwnership(int $clientId): void
    {
        $client = Auth::guard('apiWebClient')->user();
        if (!$client) {
            throw new \RuntimeException('No authenticated client');
        }

        $accessToken =  $client->currentAccessToken();

        if ((int) $accessToken->tokenable_id !== $clientId) {
            throw new \RuntimeException('Token ownership mismatch');
        }
    }
}
