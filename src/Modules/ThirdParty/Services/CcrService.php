<?php

namespace Modules\ThirdParty\Services;

use \Exception;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\MvrReport;
use Modules\ThirdParty\Libraries\Bnb;
use Modules\ThirdParty\Libraries\DateTechnology;
use Modules\ThirdParty\Repositories\CcrReportRepository;
use Modules\ThirdParty\Traits\ReportTrait;
use StikCredit\Calculators\Calculator;
use Modules\Common\Models\CcrReport;
use Modules\Common\Models\Loan;
use Throwable;

class CcrService
{
    use ReportTrait;

    public const PERCENT_ADDITIVE_IF_NO_BANKS = 30;
    public const PERCENT_BORDER = 30;
    public const PERCENT_DEVIDER = 2;
    public const TOTAL_CREDIT_POINTS_FROM_1_TO_3 = 4;
    public const TOTAL_CREDIT_POINTS_FROM_4_TO_7 = 3;
    public const TOTAL_CREDIT_POINTS_FROM_7_TO_10 = 2;
    public const TOTAL_CREDIT_POINTS_OVER_10 = 0.5;
    public const TOTAL_CREDIT_POINTS_PRINCIPAL_SMALLER_ALLOWED_AMOUNT = -10;
    public const TOTAL_CREDIT_POINTS_PRINCIPAL_BIGGER_OR_EQUAL_ALLOWED_AMOUNT = 5;
    public const LOAN_AMOUNT_MULTIPLIER = 0.1;
    public const OVERDUE_PRINCIPAL_PERCENT_ZERO = 5;
    public const OVERDUE_PRINCIPAL_CONDITTION_1_FROM = 50;
    public const OVERDUE_PRINCIPAL_CONDITTION_1_TO = 400;
    public const OVERDUE_PRINCIPAL_CONDITTION_2_FROM = 400;
    public const OVERDUE_PRINCIPAL_PERCENT_FROM_50_TO_400 = -10;
    public const OVERDUE_PRINCIPAL_PERCENT_FROM_OVER_400 = -30;
    public const CATEGORIES_ASSOC = [
        '401' => 30,
        '402' => 6,
        '403' => 2.5,
        '404' => 1.5
    ];
    public const POSSIBLE_PERIODS_ASSOC = [
        'над 360 дни' => 0.05,
        'от 181 до 360 дни' => 1.5,
        'от 91 до 180 дни' => 1.7,
        'от 61 до 90 дни' => 2.5,
        'от 31 до 60 дни' => 6, //1.7
        'от 0 до 30 дни' => 30,
    ];

    private $bnb;
    private $ccrReportRepository;
    public $parsedData = [];

    /**
     * Check if we have a report for today, we will reuse it.
     * Else get new.
     *
     * Since we get a report we add link to loan for it.
     *
     * @param  Loan $loan
     * @return null|CcrReport
     */
    public function createReportForLoan(Loan $loan, string $source = 'job', ?int $attempt = 1): ?CcrReport
    {
        try {

            $pin = $loan->client->pin;

            // check if we already have a report for today
            $daysBack = (int) config('reports.ccr.cache_days', 0);
            $ccrReport = $this->getCcrRepo()->getReport($pin, $daysBack);
            if (
                !empty($ccrReport->ccr_report_id)
                && Carbon::parse($ccrReport->created_at)->format('Ymd') == Carbon::now()->format('Ymd')
            && !empty($ccrReport->parsed_data)
        ) {
                // if we already have it for today, we just need to create pivot link
                $this->linkReport(
                    $ccrReport->ccr_report_id,
                    $loan->client_id,
                    $loan->loan_id
                );

                return $ccrReport;
            }

            $ccrReport = $this->getAndSaveNewReport($pin, true, $source, $attempt);
            if (empty($ccrReport->ccr_report_id)) {
                throw new Exception('Failed to get fresh report');
            }

            $this->linkReport(
                $ccrReport->ccr_report_id,
                $loan->client_id,
                $loan->loan_id
            );

            return $ccrReport;

        } catch (Throwable $e) {

            $msg = 'Failed to get CcrReport. '
                . 'loan: ' . $loan->loan_id
                . ', msg: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            Log::channel('bnbErrors')->error($msg);
        }

        return null;
    }

    /**
     * Old name: checkEgn()
     *
     * If we have fresh report we returns it,
     * else we get new from BNB, save it in DB and return it
     *
     * @param string $pin
     * @param bool $force - mandatory get fresh data from BNB
     *
     * @return CcrReport
     */
    public function getReport(
        string $pin,
        bool $force = false
    ): ?CcrReport {
        return $this->getAndSaveNewReport($pin, true);
    }

    public function getAndSaveNewReport(
        string $pin,
        bool $doCalculations = true,
        string $source = 'job',
        ?int $attempt = 1
    ): ?CcrReport {

        try {
            $daysBack = (int) config('reports.ccr.cache_days', 0);
            $ccrReport = $this->getCcrRepo()->getReport($pin, $daysBack);

            if (!empty($ccrReport->ccr_report_id) && !empty($ccrReport->parsed_data)) {
                return $ccrReport;
            }

            if ($this->isRateLimitationExceeded($pin)) {
                return null;
            }

            // Get fresh data from BNB
            $start = $this->timer();

            $bnbResult = $this->getBnbReportExtended($pin);
            $bnbData = $bnbResult['encoded'];
            $bnbRaw = $bnbResult['raw'];

            $end = $this->timer();
            $execTime = $this->calculateExecTime($start, $end, 5);

            $stats = [];
            if (!empty($bnbData) && $doCalculations) {
                $stats = $this->getStats($bnbData);
            }

            $report = $this->getCcrRepo()->addReport(
                $pin,
                $execTime,
                $bnbData,
                $stats,
                $this->parsedData,
                $bnbRaw
            );
            $report->source = $source;
            $report->attempt = $attempt;
            $report->save();

            return $report;

        } catch (\Throwable $e) {
            Log::channel('bnbErrors')->error(
                'pin: ' . $pin . ', error: ' . $e->getMessage()
            );

            return null;
        }
    }

    private function isRateLimitationExceeded(string $pin): bool
    {
        $thirtyMinutesAgo = Carbon::now()->subMinutes(30);
        $reports = CcrReport::where('pin', $pin)
            ->where('created_at', '>=', $thirtyMinutesAgo)
            ->get();

        if ($reports->count() >= 5) {
            return true;
        }

        return false;
    }

    public function useOldReportFromProvision($pin, $loan, $bnbData, $createdAt = null): bool
    {
        $stats = '';
        if (!empty($bnbData)) {
            $stats = $this->getStats($bnbData);
        }

        $ccrReport = $this->getCcrRepo()->addReport(
            $pin,
            1,
            $bnbData,
            $stats,
            $this->parsedData,
            'none',
            $createdAt
        );

        $this->linkReport(
            $ccrReport->ccr_report_id,
            $loan->client_id,
            $loan->loan_id
        );

        return true;
    }

    public function getBnbReport(string $pin): string
    {
        $data = '';

        try {

            if (!isProdOrStage()) {
                return DummyService::get('Bnb');
            }

            $data = json_encode($this->getBnb()->getDataByPin($pin));

        } catch (Exception $e) {

            Log::channel('bnbErrors')->error(
                'pin: ' . $pin . ', error: ' . $e->getMessage()
            );
        }

        return $data;
    }

    public function getBnbReportExtended(string $pin): array
    {
        try {

            if (!isProdOrStage()) {
                return [
                    'raw' => '',
                    'encoded' => DummyService::get('Bnb'),
                ];
            }

            $rawStr = '';
            $encodedStr = '';


            $uid = null;
            $salt = null;

            $project = env('PROJECT');
            if (!empty($project) && $project == 'lendivo') {
                $uid = env('DATE_TECHNOLOGY_LENDIVO_UID', null);
                $salt = env('DATE_TECHNOLOGY_LENDIVO_SALT', null);
            }

            // new logic -> get data via DT
            $dt = new DateTechnology($uid, $salt);
            $idCardNum = $pin;
            $report = $dt->getReports($pin, $idCardNum, ['ccr']);
            if (!empty($report['reports']['ccr']['borrower'])) {
                $rawStr = $encodedStr = json_encode($report['reports']['ccr']['borrower']);
            }
            if (is_array($report)) {
                $rawStr = json_encode($report);
            }

            // old logic -> get data via BNB
            // $result = $this->getBnb()->getDataByPinExtended($pin);
            // $encodedStr = json_encode($result['decoded']);
            // $rawStr = $result['raw'];

            return [
                'raw' => $rawStr,
                'encoded' => $encodedStr,
            ];

        } catch (Exception $e) {
            Log::channel('bnbErrors')->error(
                'pin: ' . $pin . ', error: ' . $e->getMessage()
            );

            return [
                'raw' => '',
                'encoded' => '',
            ];
        }
    }

    public function getAllByPin(
        string $pin,
        array $order = []
    ): Collection {
        return $this->getCcrRepo()->getAllByPin($pin, $order);
    }

    public function getAllForLoan(
        int $loanId,
        int $clientId
    ): Collection {
        return $this->getCcrRepo()->getAllForLoan($loanId, $clientId);
    }

    /**
     * Parse BNB data and return mandatory stats:
     * - active_percent
     * - not_active_percent
     * - total_percent
     * - total_points
     *
     * Old name: updateTotal()
     */
    public function getStats(string $bnbDataStr, bool $returnStats = false): array
    {
        // important to null that variable at the begin
        $this->parsedData = [
            'date' => null,
            'bank' => [
                'active_credits' => [
                    'stats' => [
                        'count' => 0,
                        'source_count' => 0,
                    ],
                    'rows' => [],
                ],
                'overdue_credits' => [],
                'new_credits' => [],
                'debtors' => [],
            ],
            'non_bank' => [
                'active_credits_stats' => [
                    'count' => 0,
                    'source_count' => 0,
                ],
                'active_credits' => [],
                'overdue_credits' => [],
                'new_credits' => [],
                'debtors' => [],
            ],
        ];


        // keep old logic, will be reviewed and deleted later
        $bnbData = json_decode($bnbDataStr, true);
        if (empty($bnbData)) {
            return [
                'active_percent' => null,
                'not_active_percent' => null,
                'total_percent' => null,
                'total_points' => null,
            ];
        }


        $credits = $this->getCcrCreditsCount($bnbData);
        $percents = $this->getCcrPercents($bnbData);
        $totalPoints = $this->getTotalPoints(
            $bnbData,
            $credits['total_credits']
        );
        $totalPercent = $this->getTotalPercent(
            $bnbData,
            $totalPoints,
            $percents['total_start_percent']
        );

        // used for tests
        if ($returnStats) {
            return $this->parsedData;
        }

        return [
            'active_percent' => $percents['active_percent'],
            'not_active_percent' => $percents['not_active_percent'],
            'total_percent' => $totalPercent,
            'total_points' => $totalPoints,
        ];
    }

    /**
     * [getCcrPercents description]
     *
     * @param array $bnbData
     *
     * @return array
     */
    private function getCcrPercents(array $bnbData): array
    {
        $activePercent = $this->getCcrPercent($bnbData, true);
        $notActivePercent = $this->getCcrPercent($bnbData, false);

        // Ако едно от двете не е  = 30%
        if (
            $activePercent == self::PERCENT_BORDER
            && $notActivePercent != self::PERCENT_BORDER
        ) {
            $activePercent = $activePercent / self::PERCENT_DEVIDER;
        }

        if (
            $notActivePercent == self::PERCENT_BORDER
            && $activePercent != self::PERCENT_BORDER
        ) {
            $notActivePercent = $notActivePercent / self::PERCENT_DEVIDER;
        }

        return [
            'active_percent' => $activePercent,
            'not_active_percent' => $notActivePercent,
            'total_start_percent' => ($activePercent + $notActivePercent), // Начални % $totalPointsCCR
        ];
    }

    /**
     * [getCcrPercent description]
     *
     * @param array $bnbData
     * @param bool $active
     *
     * @return float
     */
    private function getCcrPercent(array $bnbData, bool $active = true): float
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        $count = 0;
        $percent = 0;
        $haveBanks = 0;
        $haveNonBanks = 0;

        $bankRows = collect($bnbData['section']);
        if (!empty($bankRows['overdue-history']['summary'])) {

             // Ако има само 1 ред
            if (!empty($bankRows['overdue-history']['summary']['@active'])) {
                $overdue = $bankRows['overdue-history']['summary'];

                if (
                    ($active == true && $overdue['@active'] == 'true')
                    || ($active == false && $overdue['@active'] == 'false')
                ) {
                    try {
                        $countIncrease = (int)$overdue['@max-cred-count'];
                        $percent = $this->calcOverduePercent(
                            $percent,
                            $countIncrease,
                            $overdue
                        );
                    } catch (Exception $e) {
                    }

                    $count += $countIncrease;

                    if ($bankRows['@entity-type'] === 'banks') {
                        $haveBanks++;
                    } elseif ($bankRows['@entity-type'] === 'nonbanks') {
                        $haveNonBanks++;
                    }
                }

            } else {
                foreach ($bankRows['overdue-history']['summary'] as $overdue) {
                    if (empty($overdue['@active'])) {
                        continue;
                    }

                    if (
                        ($active == true && $overdue['@active'] == 'true')
                        || ($active == false && $overdue['@active'] == 'false')
                    ) {

                        try {
                            $countIncrease = (int)$overdue['@max-cred-count'];
                            $percent = $this->calcOverduePercent(
                                $percent,
                                $countIncrease,
                                $overdue
                            );
                        } catch (Exception $e) {
                            // TODO: log
                        }

                        $count += $countIncrease;

                        if ($bankRows['@entity-type'] === 'banks') {
                            $haveBanks++;
                        } elseif ($bankRows['@entity-type'] === 'nonbanks') {
                            $haveNonBanks++;
                        }
                    }
                }
            }

        } else {
            foreach ($bankRows as $k => $bankRow) {
                if (!empty($bankRow['overdue-history']['summary'])) {

                    // Ако има само 1 ред
                    if (!empty($bankRow['overdue-history']['summary']['@active'])) {
                        $overdue = $bankRow['overdue-history']['summary'];

                        if (
                            ($active == true && $overdue['@active'] != 'true')
                            || ($active == false && $overdue['@active'] != 'false')
                        ) {
                            continue;
                        }

                        try {
                            $countIncrease = (int)$overdue['@max-cred-count'];
                            $percent = $this->calcOverduePercent(
                                $percent,
                                $countIncrease,
                                $overdue
                            );
                        } catch (Exception $e) {
                            // TODO: log
                            continue;
                        }

                        $count += $countIncrease;

                        if ($bankRow['@entity-type'] === 'banks') {
                            $haveBanks++;
                        } elseif ($bankRow['@entity-type'] === 'nonbanks') {
                            $haveNonBanks++;
                        }
                    } else {
                        foreach ($bankRow['overdue-history']['summary'] as $overdue) {
                            if (empty($overdue['@active'])) {
                                continue;
                            }

                            if (
                                ($active == true && $overdue['@active'] != 'true')
                                || ($active == false && $overdue['@active'] != 'false')
                            ) {
                                continue;
                            }

                            try {
                                $countIncrease = (int)$overdue['@max-cred-count'];
                                $percent = $this->calcOverduePercent(
                                    $percent,
                                    $countIncrease,
                                    $overdue
                                );
                            } catch (Exception $e) {
                                // TODO: log
                                continue;
                            }

                            $count += $countIncrease;

                            if ($bankRow['@entity-type'] === 'banks') {
                                $haveBanks++;
                            } elseif ($bankRow['@entity-type'] === 'nonbanks') {
                                $haveNonBanks++;
                            }
                        }
                    }

                }
            }
        }

        // Дали има закъснения по някой от типовете
        if ($haveBanks < 1) {
            $percent += self::PERCENT_ADDITIVE_IF_NO_BANKS;
            $count += 1;
        }

        if ($haveNonBanks < 1) {
            $percent += self::PERCENT_ADDITIVE_IF_NO_BANKS;
            $count += 1;
        }

        if ($percent == 0 || $count == 0) {
            return 0;
        }

        return number_format($percent / $count, 2, '.', '');
    }

    /**
     * [getCcrCreditsCount description]
     *
     * @param array $bnbData
     *
     * @return array
     */
    private function getCcrCreditsCount(array $bnbData): array
    {
        $bankCreditsCount = $this->getCreditsCountFromBanks($bnbData);
        $nonBankCreditsCount = $this->getCreditsCountFromNonBanks($bnbData);

        return [
            'bank_credits' => $bankCreditsCount,
            'non_bank_credits' => $nonBankCreditsCount,
            'total_credits' => ($bankCreditsCount + $nonBankCreditsCount),
        ];
    }

    /**
     * Get credit counts from banks
     * Брой кредити от банки
     *
     * @param array $bnbData
     *
     * @return int
     */
    private function getCreditsCountFromBanks(array $bnbData): int
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        if (isset($bnbData['section']['@entity-type'])) {
            $bankRow = $bnbData['section'];
            if ($bankRow['@entity-type'] != 'banks') {
                $bankRow = [];
            }
        } else {
            $bankRow = collect(
                collect($bnbData['section'])->where('@entity-type', 'banks')
            )->first();
        }

        if (
            $bankRow
            && (
                !empty($bankRow['active-credits']['@cred-count'])
                || !empty($bankRow['overdue-history']['summary'])
            )
        ) {

            $this->addActiveCreditsToParsedData($bankRow, 'bank');

            return !empty($bankRow['active-credits']['@cred-count']) ? $bankRow['active-credits']['@cred-count'] : 0;
        }

        return 0;
    }

    /**
     * Get credit counts from non banks
     * Брой кредити от нефинансови
     *
     * @param array $bnbData
     *
     * @return int
     */
    private function getCreditsCountFromNonBanks(array $bnbData): int
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        if (isset($bnbData['section']['@entity-type'])) {
            $bankRow = $bnbData['section'];
            if ($bankRow['@entity-type'] != 'nonbanks') {
                $bankRow = [];
            }
        } else {
            $bankRow = collect(
                collect($bnbData['section'])->where('@entity-type', 'nonbanks')
            )->first();
        }

        if (
            $bankRow
            && (
                !empty($bankRow['active-credits']['@cred-count'])
                || !empty($bankRow['overdue-history']['summary'])
            )
        ) {

            $this->addActiveCreditsToParsedData($bankRow, 'non_bank');

            return !empty($bankRow['active-credits']['@cred-count']) ? $bankRow['active-credits']['@cred-count'] : 0;
        }

        return 0;
    }

    private function addActiveCreditsToParsedData($bankRow, string $source)
    {
        $this->parsedData[$source]['active_credits']['stats']['count'] = (
            isset($bankRow['active-credits']['@cred-count'])
            ? $bankRow['active-credits']['@cred-count']
            : 0
        );
        $this->parsedData[$source]['active_credits']['stats']['source_count'] = (
            isset($bankRow['active-credits']['@source-entity-count'])
            ? $bankRow['active-credits']['@source-entity-count']
            : 0
        );



        if (!empty($bankRow['active-credits']['summaries']) && !empty($bankRow['active-credits']['@cred-count'])) {

            $totals = [
                'amount_approved' => 0,
                'amount_received' => 0,
                'amount_installment' => 0,
                'outstanding_principal' => 0,
                'outstanding_overdue_principal' => 0,
            ];

            foreach ($bankRow['active-credits']['summaries'] as $key => $rowData) {

                $typeRaw = $rowData['@grouping-attribute'];

                $type = '';
                switch ($typeRaw) {
                    case 'type':
                        $type = 'credit_type';
                        break;
                    case 'rest':
                        $type = 'rest_period';
                        break;
                    case 'term':
                        $type = 'term_period';
                        break;
                    case 'overdue-payment-period':
                        $type = 'overdue_period';
                        break;
                }

                if (empty($type)) {
                    continue;
                }

                $this->parsedData[$source]['active_credits']['rows'][$type] = [];

                $rows = $rowData['summary'] ?? [];

                if (isset($rows['@date-from'])) {
                    $totals = $this->handleActiveCreditRow($rows, $source, $type, $totals);
                } else {
                    foreach ($rows as $row) {
                        $totals = $this->handleActiveCreditRow($row, $source, $type, $totals);
                    }
                }
            }

            $this->parsedData[$source]['active_credits']['rows_totals'] = $totals;
        }



        if (!empty($bankRow['overdue-history']['summary'])) {

            if (!empty($bankRow['overdue-history']['summary']["@active"])) {

                $type = (
                    $bankRow['overdue-history']['summary']["@active"] == 'true'
                    ? 'active'
                    : 'non_active'
                );

                if (!isset($this->parsedData[$source]['overdue_credits']['rows'][$type])) {
                    $this->parsedData[$source]['overdue_credits']['rows'][$type] = [];
                }

                $el = [
                    'year' => $bankRow['overdue-history']['summary']['@year'] ?? '-',
                    'period' => $bankRow['overdue-history']['summary']['@overdue-payment-period'] ?? '-',
                    'months_count' => $bankRow['overdue-history']['summary']['@months-count'] ?? '-',
                    'credits_count' => $bankRow['overdue-history']['summary']['@max-cred-count'] ?? '-',
                    'outstanding_principal' => $bankRow['overdue-history']['summary']['@max-outstanding-overdue-principal'] ?? '-',
                    'outstanding_interest' => $bankRow['overdue-history']['summary']['@max-outstanding-overdue-interest-and-others'] ?? '-',
                    'balance_due' => $bankRow['overdue-history']['summary']['@max-off-balance-sheet-dues'] ?? '-',
                ];

                $this->parsedData[$source]['overdue_credits']['rows'][$type][] = $el;

            } else {
                foreach ($bankRow['overdue-history']['summary'] as $key => $row) {

                    $type = isset($row["@active"]) && $row["@active"] == 'true'
                        ? 'active'
                        : 'non_active'
                    ;

                    if (!isset($this->parsedData[$source]['overdue_credits']['rows'][$type])) {
                        $this->parsedData[$source]['overdue_credits']['rows'][$type] = [];
                    }

                    $el = [
                        'year' => $row['@year'] ?? '-',
                        'period' => $row['@overdue-payment-period'] ?? '-',
                        'months_count' => $row['@months-count'] ?? '-',
                        'credits_count' => $row['@max-cred-count'] ?? '-',
                        'outstanding_principal' => $row['@max-outstanding-overdue-principal'] ?? '-',
                        'outstanding_interest' => $row['@max-outstanding-overdue-interest-and-others'] ?? '-',
                        'balance_due' => $row['@max-off-balance-sheet-dues'] ?? '-',
                    ];

                    $this->parsedData[$source]['overdue_credits']['rows'][$type][] = $el;
                }
            }
        }




        if (!empty($bankRow['new-credits']['summary'])) {
            if (!empty($bankRow['new-credits']['summary']['@type'])) {

                $el = [
                    'type' => $bankRow['new-credits']['summary']['@type'],
                    'amount' => $bankRow['new-credits']['summary']['@amount-approved'],
                ];

                $this->parsedData[$source]['new_credits']['rows'][] = $el;

            } else {
                foreach ($bankRow['new-credits']['summary'] as $key => $row) {
                    $el = [
                        'type' => $row['@type'],
                        'amount' => $row['@amount-approved'],
                    ];

                    $this->parsedData[$source]['new_credits']['rows'][] = $el;
                }
            }
        }




        if (!empty($bankRow['related-active-credits']['credits-set'])) {
            foreach ($bankRow['related-active-credits']['credits-set'] as $row) {


                if (!empty($row['summary'])) {
                    foreach ($row['summary'] as $rr) {
                        $el = [
                            'date' => !empty($rr['@date-from']) ? $rr['@date-from'] : '-',
                            'role' => $row['@person-role'],
                            'overdue_period' => !empty($rr['@overdue-payment-period']) ? $rr['@overdue-payment-period'] : '-',
                            'credit_count' => !empty($rr['@cred-count']) ? $rr['@cred-count'] : '-',
                            'amount' => !empty($rr['@amount-approved']) ? $rr['@amount-approved'] : '-',
                            'balance_due' => !empty($rr['@balance-sheet-value']) ? $rr['@balance-sheet-value'] : '-',
                            'balance_due_off' => !empty($rr['@off-balance-sheet-value']) ? $rr['@off-balance-sheet-value'] : '-',
                        ];

                        $this->parsedData[$source]['debtors']['rows'][] = $el;
                    }
                } else {
                    $el = [
                        'date' => !empty($row['@date-from']) ? $row['@date-from'] : '-',
                        'role' => $row['@person-role'],
                        'overdue_period' => !empty($row['@overdue-payment-period']) ? $row['@overdue-payment-period'] : '-',
                        'credit_count' => !empty($row['@cred-count']) ? $row['@cred-count'] : '-',
                        'amount' => !empty($row['@amount-approved']) ? $row['@amount-approved'] : '-',
                        'balance_due' => !empty($row['@balance-sheet-value']) ? $row['@balance-sheet-value'] : '-',
                        'balance_due_off' => !empty($row['@off-balance-sheet-value']) ? $row['@off-balance-sheet-value'] : '-',
                    ];

                    $this->parsedData[$source]['debtors']['rows'][] = $el;
                }
            }
        }
    }

    private function handleActiveCreditRow(
        $row,
        string $source,
        string $type,
        array $totals
    ) {
        // set report date, it's same on every row
        if (empty($this->parsedData['date']) && !empty($row['@date-from'])) {
            $this->parsedData['date'] = $row['@date-from'];
        }

        $el = [
            'date_from' => !empty($row['@date-from']) ? $row['@date-from'] : '-',
            'amount_approved' => $row['@amount-approved'],
            'amount_received' => $row['@amount-drawn'],
            'amount_installment' => $row['@monthly-installment'],
            'outstanding_principal' => $row['@outstanding-performing-principal'],
            'outstanding_overdue_principal' => $row['@outstanding-overdue-principal'],
            'balance_due' => !empty($row['@balance-sheet-value']) ? $row['@balance-sheet-value'] : '-',
            'balance_due_off' => !empty($row['@off-balance-sheet-value']) ? $row['@off-balance-sheet-value'] : '-',
            'balance_due_off_dues' => !empty($row['@off-balance-sheet-dues']) ? $row['@off-balance-sheet-dues'] : '-',
            'balance_due_off_conditional' => !empty($row['@off-balance-sheet-conditional']) ? $row['@off-balance-sheet-conditional'] : '-',
            'balance_due_off_unused' => !empty($row['@off-balance-sheet-unused']) ? $row['@off-balance-sheet-unused'] : '-',
        ];

        if (isset($row['@type'])) {
            $el['type'] = $row['@type'];
        }

        if (isset($row['@overdue-payment-period'])) {
            $el['type'] = $row['@overdue-payment-period'];
        }

        if (isset($row['@rest'])) {
            $el['type'] = $row['@rest'];
        }

        if (isset($row['@term'])) {
            $el['type'] = $row['@term'];
        }

        if ('credit_type' == $type) {
            $totals['amount_approved'] += $el['amount_approved'];
            $totals['amount_received'] += $el['amount_received'];
            $totals['amount_installment'] += $el['amount_installment'];
            $totals['outstanding_principal'] += $el['outstanding_principal'];
            $totals['outstanding_overdue_principal'] += $el['outstanding_overdue_principal'];
        }

        $this->parsedData[$source]['active_credits']['rows'][$type][] = $el;

        return $totals;
    }

    /**
     * [getTotalPoints description]
     *
     * @param array $bnbData
     * @param int $totalCredits
     *
     * @return float|int
     */
    private function getTotalPoints(array $bnbData, int $totalCredits)
    {
        $totalPointsStart = $this->getTotalPointAccordingToRange($totalCredits);

        $remainPrincipal = $this->getRemainingRegularPrincipal($bnbData);
        $approvedAmount = $this->getApprovedAmount($bnbData);
        $utilizedAmount = $this->getUtilizedAmount($bnbData);

        $principal = $remainPrincipal - (
                $utilizedAmount * self::LOAN_AMOUNT_MULTIPLIER
            );

        // условие 1 - главница да е по малко с 10 % от разрешената сума
        if ($approvedAmount < $principal) {
            // Разрешена/Усвоена сума: -10%
            return (
                $totalPointsStart
                + self::TOTAL_CREDIT_POINTS_PRINCIPAL_SMALLER_ALLOWED_AMOUNT
            );
        }

        // Разрешена/Усвоена сума: 5%
        return (
            $totalPointsStart
            + self::TOTAL_CREDIT_POINTS_PRINCIPAL_BIGGER_OR_EQUAL_ALLOWED_AMOUNT
        );
    }

    /**
     * [getTotalPointAccordingToRange description]
     *
     * @return float|int
     */
    private function getTotalPointAccordingToRange(int $totalCredits)
    {
        // Брой кредити (1-3): 4%
        if ($totalCredits >= 1 && $totalCredits <= 3) {
            return self::TOTAL_CREDIT_POINTS_FROM_1_TO_3;
        }

        // Брой кредити (4-7): 3%
        if ($totalCredits >= 4 && $totalCredits <= 7) {
            return self::TOTAL_CREDIT_POINTS_FROM_4_TO_7;
        }

        // Брой кредити (7-10): 2%
        if ($totalCredits >= 7 && $totalCredits <= 10) {
            return self::TOTAL_CREDIT_POINTS_FROM_7_TO_10;
        }

        // Брой кредити(>10): 0.5%
        if ($totalCredits > 10) {
            return self::TOTAL_CREDIT_POINTS_OVER_10;
        }

        return 0;
    }

    /**
     * [getTotalPercent description]
     *
     * @param array $bnbData
     * @param float|int $totalPoints
     * @param float|int $totalStartPercent
     *
     * @return float|int
     */
    private function getTotalPercent(
        array $bnbData,
        $totalPoints,
        $totalStartPercent
    ) {
        $totalPercent = $totalPoints + $totalStartPercent;
        $overduePrincipal = $this->getOverduePrincipal($bnbData);

        // Условие (главница) 0 просрочена главница +5%
        if ($overduePrincipal == 0) {
            return (
                $totalPercent
                + self::OVERDUE_PRINCIPAL_PERCENT_ZERO
            );
        }

        // Условие (главница) 1 просрочена главница -10%
        if (
            $overduePrincipal >= self::OVERDUE_PRINCIPAL_CONDITTION_1_FROM
            && $overduePrincipal <= self::OVERDUE_PRINCIPAL_CONDITTION_1_TO
        ) {
            return (
                $totalPercent
                + self::OVERDUE_PRINCIPAL_PERCENT_FROM_50_TO_400
            );
        }

        // Условие (главница) 2 просрочена главница -30%
        if ($overduePrincipal > self::OVERDUE_PRINCIPAL_CONDITTION_2_FROM) {
            return (
                $totalPercent
                + self::OVERDUE_PRINCIPAL_PERCENT_FROM_OVER_400
            );
        }

        return $totalPercent;
    }

    /**
     * [getApprovedAmount description]
     * Разрешена сума
     *
     * @param array $bnbData
     *
     * @return float
     */
    private function getApprovedAmount(array $bnbData): float
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        $total = 0;
        $bankRows = collect($bnbData['section']);

        if (!empty($bankRows['active-credits']['summaries'])) {
            foreach ($bankRows['active-credits']['summaries'] as $activecredit) {
                if (!empty($activecredit['summary'])) {
                    if ($activecredit['@grouping-attribute'] == 'overdue-payment-period') {
                        if (!empty($activecredit['summary']['@amount-approved'])) {
                            $total += $activecredit['summary']['@amount-approved'];
                        } else {
                            foreach ($activecredit['summary'] as $ac) {
                                if (!empty($ac['@amount-approved'])) {
                                    $total += $ac['@amount-approved'];
                                }
                            }
                        }
                    }
                }
            }

        } else {
            foreach ($bankRows as $bankRow) {
                if (!empty($bankRow['active-credits']['summaries'])) {
                    foreach ($bankRow['active-credits']['summaries'] as $activecredit) {
                        if (!empty($activecredit['summary'])) {
                            if ($activecredit['@grouping-attribute'] == 'overdue-payment-period') {
                                if (!empty($activecredit['summary']['@amount-approved'])) {
                                    $total += $activecredit['summary']['@amount-approved'];
                                } else {
                                    foreach ($activecredit['summary'] as $ac) {
                                        if (!empty($ac['@amount-approved'])) {
                                            $total += $ac['@amount-approved'];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $total;
    }

    /**
     * [getUtilizedAmount description]
     * Усвоена сума
     *
     * @param array $bnbData
     *
     * @return float
     */
    private function getUtilizedAmount(array $bnbData): float
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        $bankRows = collect($bnbData['section']);

        $total = 0;

        if (!empty($bankRows['active-credits']['summaries'])) {
            foreach ($bankRows['active-credits']['summaries'] as $activecredit) {
                if ($activecredit['@grouping-attribute'] != 'type') {
                    continue;
                }

                if (!empty($activecredit['summary']['@amount-drawn'])) {
                    //var_dump($activecredit['summary']['@amount-drawn']);
                    $total += $activecredit['summary']['@amount-drawn'];
                }

                if (!empty($activecredit['summary']) && is_array($activecredit['summary'])) {
                    foreach ($activecredit['summary'] as $summary) {
                        if (!empty($summary['@amount-drawn'])) {
                            $total += $summary['@amount-drawn'];
                        }
                    }
                }
            }
        } else {
            foreach ($bankRows as $bankRow) {
                if (!empty($bankRow['active-credits']['summaries'])) {
                    foreach ($bankRow['active-credits']['summaries'] as $activecredit) {
                        if ($activecredit['@grouping-attribute'] != 'type') {
                            continue;
                        }

                        if (!empty($activecredit['summary']['@amount-drawn'])) {
                            //var_dump($activecredit['summary']['@amount-drawn']);
                            $total += $activecredit['summary']['@amount-drawn'];
                        }

                        if (!empty($activecredit['summary']) && is_array($activecredit['summary'])) {
                            foreach ($activecredit['summary'] as $summary) {
                                if (!empty($summary['@amount-drawn'])) {
                                    $total += $summary['@amount-drawn'];
                                }
                            }
                        }
                    }
                }
            }
        }

        return $total;
    }

    /**
     * [getRemainingRegularPrincipal description]
     * Оставаща редовна главница
     *
     * @param array $bnbData
     *
     * @return float
     */
    private function getRemainingRegularPrincipal(array $bnbData): float
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        $bankRows = collect($bnbData['section']);

        $total = 0;

        if (!empty($bankRows['active-credits']['summaries'])) {
            foreach ($bankRows['active-credits']['summaries'] as $activecredit) {
                if (!empty($activecredit['summary']) && $activecredit['@grouping-attribute'] == 'type') {
                    if (!empty($activecredit['summary']['@outstanding-performing-principal'])) {
                        $total += $activecredit['summary']['@outstanding-performing-principal'];
                    }
                    if (is_array($activecredit['summary'])) {
                        foreach ($activecredit['summary'] as $summary) {
                            if (!empty($summary['@outstanding-performing-principal'])) {
                                $total += $summary['@outstanding-performing-principal'];
                            }
                        }
                    }
                }
            }
        } else {
            foreach ($bankRows as $bankRow) {
                if (!empty($bankRow['active-credits']['summaries'])) {
                    foreach ($bankRow['active-credits']['summaries'] as $activecredit) {
                        if (!empty($activecredit['summary']) && $activecredit['@grouping-attribute'] == 'type') {
                            if (!empty($activecredit['summary']['@outstanding-performing-principal'])) {
                                $total += $activecredit['summary']['@outstanding-performing-principal'];
                            }
                            if (is_array($activecredit['summary'])) {
                                foreach ($activecredit['summary'] as $summary) {
                                    if (!empty($summary['@outstanding-performing-principal'])) {
                                        $total += $summary['@outstanding-performing-principal'];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $total;
    }

    /**
     * [getOverduePrincipal description]
     * Просрочена главница
     *
     * @param array $bnbData
     *
     * @return float
     */
    private function getOverduePrincipal(array $bnbData): float
    {
        if (!isset($bnbData['section'])) {
            return 0;
        }

        $bankRows = collect($bnbData['section']);

        $total = 0;
        if (!empty($bankRows['active-credits']['summaries'])) {
            foreach ($bankRows['active-credits']['summaries'] as $activecredit) {
                if ($activecredit['@grouping-attribute'] != 'type') {
                    continue;
                }

                if (empty($activecredit['summary'])) {
                    continue;
                }

                if (is_array($activecredit['summary'])) {
                    foreach ($activecredit['summary'] as $key => $summary) {
                        if (!empty($summary['@outstanding-overdue-principal'])) {
                            $total += floatval($summary['@outstanding-overdue-principal']);
                        } elseif ($key === '@outstanding-overdue-principal') {
                            $total += floatval($summary);
                        }
                    }
                }
            }
        } else {
            foreach ($bankRows as $bankRow) {
                if (!empty($bankRow['active-credits']['summaries'])) {
                    foreach ($bankRow['active-credits']['summaries'] as $activecredit) {
                        if ($activecredit['@grouping-attribute'] != 'type') {
                            continue;
                        }

                        if (empty($activecredit['summary'])) {
                            continue;
                        }

                        if (is_array($activecredit['summary'])) {
                            foreach ($activecredit['summary'] as $key => $summary) {
                                if (!empty($summary['@outstanding-overdue-principal'])) {
                                    $total += floatval($summary['@outstanding-overdue-principal']);
                                } elseif ($key === '@outstanding-overdue-principal') {
                                    $total += floatval($summary);
                                }
                            }
                        }
                    }
                }
            }
        }

        return $total;
    }

    private function calcOverduePercent(
        $percent,
        $countIncrease,
        array $overdue
    ) {
        if (isset($overdue['@overdue-payment-period'])) {
            return (
                $percent
                + $this->calcOverduePaymentPeriod(
                    $overdue['@overdue-payment-period'],
                    $countIncrease
                )
            );
        }

        if (isset($overdue['@category'])) {
            return (
                $percent
                + $this->calcCategory($overdue['@category'], $countIncrease)
            );
        }

        throw new Exception("Unknown case", $overdue);
    }

    private function calcOverduePaymentPeriod(string $period, int $count)
    {
        if (isset(self::POSSIBLE_PERIODS_ASSOC[$period])) {
            return Calculator::multiply(self::POSSIBLE_PERIODS_ASSOC[$period], $count);
        }

        throw new Exception('Unknown overdue-payment-period: ' . $period);
    }

    private function calcCategory(string $cat, int $count)
    {
        $key = substr($cat, 0, 3);
        if (isset(self::CATEGORIES_ASSOC[$key])) {
            return Calculator::multiply(self::CATEGORIES_ASSOC[$key], $count);
        }

        throw new Exception('Unknown category: ' . $cat);
    }

    public function linkReport(int $ccrReportId, int $clientId, int $loanId)
    {
        return $this->getCcrRepo()->addPivot(
            $ccrReportId,
            $clientId,
            $loanId
        );
    }

    private function getBnb()
    {
        if (null === $this->bnb) {
            $this->bnb = new Bnb();
        }

        return $this->bnb;
    }

    private function getCcrRepo()
    {
        if (null === $this->ccrReportRepository) {
            $this->ccrReportRepository = new CcrReportRepository();
        }

        return $this->ccrReportRepository;
    }
}
