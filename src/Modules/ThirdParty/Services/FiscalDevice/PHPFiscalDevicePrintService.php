<?php

namespace Modules\ThirdParty\Services\FiscalDevice;

use Closure;
use Illuminate\Support\Facades\Log;
use Modules\CashDesk\Enums\TerminalLogStatusEnum;
use Modules\CashDesk\Events\TerminalLog\TerminalLogUpdateEvent;
use Modules\CashDesk\Events\Tremol\DTO\ProductDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolDailyReportDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolFiscalReceiptDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolServiceReceiptDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolStornoReceiptDTO;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Repositories\TerminalLogRepository;
use Modules\Common\Models\FiscalDevice;
use Modules\ThirdParty\Libraries\Tremol\FiscalPrinter;
use Modules\ThirdParty\Libraries\Tremol\FiscalReceipt;
use Modules\ThirdParty\Libraries\Tremol\TremolException;
use Throwable;

class PHPFiscalDevicePrintService
{

    public function __construct(
        public TerminalLogRepository $terminalLogRepository
    ) {}

    public function stornoReceipt(TremolStornoReceiptDTO $dto, ?FiscalDevice $fiscalDevice): void
    {
        $this->tryProcessFiscalDevice($dto, function () use ($fiscalDevice, $dto) {
            $fp = $this->setFp($fiscalDevice);

            $fpDelivery = [];
            if (!empty($dto->products)) {
                foreach ($dto->products as $product) {
                    $fpDelivery[] = [
                        'name' => $product->NamePLU,
                        'price' => $product->Price,
                        'qty' => $product->Quantity,
                        'vat_class' => $product->OptionVATClass,
                    ];

                }
            }

            $fp->makeStorno(
                $fpDelivery,
                $dto->frNum,
                $dto->frDateTime,
                $dto->fmNum,
                $dto->urn
            );
        });
    }

    /**
     * @param TremolFiscalReceiptDTO $dto
     * @param CashOperationalTransaction $cashOperationalTransaction
     * @return void
     */
    public function fiscalReceipt(
        TremolFiscalReceiptDTO     $dto,
        CashOperationalTransaction $cashOperationalTransaction
    ): void
    {
        $this->tryProcessFiscalDevice($dto, function () use ($dto, $cashOperationalTransaction) {
            $fiscalDevice = $cashOperationalTransaction->office->fiscalDevice;
            $fp = $this->setFp($fiscalDevice);
            $fp->setFiscalReceiptUniqueNumber();

            $fpDelivery = [];
            if (!empty($dto->products)) {
                /**
                 * @var ProductDTO $product
                 */
                foreach ($dto->products as $product) {
                    $fpDelivery[] = [
                        'name' => $product->NamePLU,
                        'price' => $product->Price,
                        'qty' => $product->Quantity,
                        'vat_class' => $product->OptionVATClass,
                    ];
                }
            }

            $fp->makePayment($fpDelivery, null);
            $fp->makeFiscalReceiptEntity($cashOperationalTransaction);
        });
    }

    /**
     * @param TremolServiceReceiptDTO $dto
     * @param FiscalDevice|null $fiscalDevice
     * @return void
     */
    public function serviceReceipt(TremolServiceReceiptDTO $dto, ?FiscalDevice $fiscalDevice): void
    {
        $this->tryProcessFiscalDevice($dto, function () use ($dto, $fiscalDevice) {
            $fp = $this->setFp($fiscalDevice);
            $fp->makeServiceReceipt($dto->amount, $dto->incoming, $dto->text);
        });
    }

    /**
     * @param TremolDailyReportDTO $dto
     * @param FiscalDevice|null $fiscalDevice
     * @return void
     */
    public function dailyReport(TremolDailyReportDTO $dto, ?FiscalDevice $fiscalDevice): void
    {
        $this->tryProcessFiscalDevice($dto, function () use ($fiscalDevice) {
            $fp = $this->setFp($fiscalDevice);
            $fp->makeDailyReport(true);
        });
    }


    public function tryProcessFiscalDevice(
        TremolStornoReceiptDTO|TremolFiscalReceiptDTO|TremolServiceReceiptDTO|TremolDailyReportDTO $dto,
        Closure                                                                                    $closure
    ): void
    {
        $dataLog = [];
        try {
            $closure();
            $dataLog['status'] = TerminalLogStatusEnum::handled;
            $dataLog = $this->terminalLogRepository->addConfirmFields($dataLog, getAdmin());
        } catch (Throwable $e) {
            // this error is hide because external lib error and did not stop process
            $dataLog['status'] = TerminalLogStatusEnum::failed;
            $dataLog['comment'] = $e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine();
//            $dataLog['trace_error'] = $e->getTrace();
            Log::channel('cashDeskModule')->debug($dataLog);
//            throw $e;
        } finally {
            TerminalLogUpdateEvent::dispatch($dto->terminal_log_id, $dataLog);
        }
    }

    /**
     * @throws TremolException
     */
    private function setFp(FiscalDevice $fiscalDevice): FiscalPrinter
    {
        $filePath = module_path("ThirdParty", "Libraries/Tremol/{$fiscalDevice->server_version}/FP.php");
        if (!file_exists($filePath)) {
            Log::debug('No lib for tremol version: ' . (!empty($fiscalDevice->server_version) ? $fiscalDevice->server_version : '#none'));
            throw new \Exception("Invalid TREMOL version: {$fiscalDevice->server_version}");
        }

        // Suppress deprecated warnings, because of old php in tremol lib
        $prevLevel = error_reporting(error_reporting() & ~E_DEPRECATED & ~E_USER_DEPRECATED);

        require_once($filePath);

        error_reporting($prevLevel); // Restore previous level

        $receipt = new FiscalReceipt();

        return new FiscalPrinter(
            $fiscalDevice,
            $receipt
        );
    }
}
