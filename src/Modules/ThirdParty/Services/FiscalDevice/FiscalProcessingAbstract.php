<?php

namespace Modules\ThirdParty\Services\FiscalDevice;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\CashDesk\Enums\TeminalPrintingTypeEnum;
use Modules\CashDesk\Enums\TremolProcessingStackEnum;
use Modules\CashDesk\Events\Tremol\DTO\ProductDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolDailyReportDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolFiscalReceiptDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolServiceReceiptDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolStornoReceiptDTO;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Models\TerminalLog;
use Modules\CashDesk\Repositories\TerminalLogRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\ThirdParty\Libraries\Tremol\TremolException;

abstract class FiscalProcessingAbstract implements FiscalProcessingInterface
{
    protected TremolProcessingStackEnum $stack;

    public function __construct(
        protected TerminalLogRepository $terminalLogRepository,
        protected LogEventService       $logEventService,
    ) {
    }

    protected function makeProductsCollection(?array $delivery = null): Collection
    {
        if (!$delivery) {
            return collect([]);
        }

        return collect($delivery)->map(function (array $item) {
            return new ProductDTO(
                __('payments::fiscalDevice.' . $item['column']) ?? 'N/A',
                'А', // vat_class -> fiscal device
                intToFloat($item['amount']),
                1
            );
        });
    }

    protected function logStornoReceipt(
        Payment $payment,
    ): TremolStornoReceiptDTO {

        $cashTransaction = $payment->cashOperationalTransaction;
        if (empty($cashTransaction->cash_operational_transaction_id)) {
            throw new TremolException(new NotFoundException(__('payments::fiscalDevice.noSuchCashTransaction')));
        }

        $relatedFiscalReceipt = $cashTransaction->fiscalReceipt;
//        if (empty($relatedFiscalReceipt)) {
//            throw new TremolException(new NotFoundException(__('payments::fiscalDevice.noSuchFiscalReceipt')));
//        }
        if ($relatedFiscalReceipt) {
            $payment->delivery = is_string($payment->delivery) ? json_decode($payment->delivery, true) : $payment->delivery;
            $products = $this->makeProductsCollection($payment->delivery);
            $dto = new TremolStornoReceiptDTO(
                (new TerminalLog)->newUniqueId(),
                $payment->office->fiscalDevice->fiscal_device_id,
                ProductDTO::collection($products),
                $relatedFiscalReceipt->fr_num,
                Carbon::parse($relatedFiscalReceipt->fr_date_time)->format('d-m-Y H:i:s'),
                $relatedFiscalReceipt->fm_num,
                $relatedFiscalReceipt->urn
            );
        } else {
            $dto = new TremolStornoReceiptDTO(
                terminal_log_id: (new TerminalLog)->newUniqueId(),
                fiscal_device_id: $payment->office->fiscalDevice->fiscal_device_id,
            );
        }

        $otherFields['loan_id'] = $payment->loan?->loan_id;
        $otherFields['client_id'] = $payment->client?->loan_id;
        $otherFields['cash_operational_transaction_id'] = $payment->cashOperationalTransactions->first()?->getKey();


        $this->logEventService->create(
            $this->stack,
            $dto,
            0,
            $this->stack,
            TeminalPrintingTypeEnum::stornoReceipt,
            $otherFields
        );

        return $dto;
    }

    protected function logFiscalReceipt(
        CashOperationalTransaction $cashOperationalTransaction,
    ): TremolFiscalReceiptDTO {
        $payment = $cashOperationalTransaction->payment;
        if (empty($payment)) {
            throw new \RuntimeException(__('payments::payments.paymentNotFound'));
        }

        $delivery = $payment->delivery;
        if (is_string($delivery)) {
            $delivery = json_decode($delivery, true);
        }

        $products = $this->makeProductsCollection($delivery);
        $dto = new TremolFiscalReceiptDTO(
            (new TerminalLog)->newUniqueId(),
            $cashOperationalTransaction->office->fiscalDevice->fiscal_device_id,
            ProductDTO::collection($products)
        );

        $otherFields['loan_id'] = $payment->loan?->loan_id;
        $otherFields['client_id'] = $payment->client?->loan_id;
        $otherFields['cash_operational_transaction_id'] = $cashOperationalTransaction->getKey();

        $this->logEventService->create(
            $this->stack,
            $dto,
            0,
            $this->stack,
            TeminalPrintingTypeEnum::fiscalReceipt,
            $otherFields
        );

        return $dto;
    }

    protected function logDailyReport(
        Office $office,
    ): TremolDailyReportDTO {
        $dto = new TremolDailyReportDTO(
            (new TerminalLog)->newUniqueId(),
            $office->fiscalDevice->fiscal_device_id,
            true
        );
        $this->logEventService->create($this->stack, $dto, 0, $this->stack, TeminalPrintingTypeEnum::dailyReport);

        return $dto;
    }

    protected function logServiceReceipt(
        CashOperationalTransaction $cashOperationalTransaction,
        string                     $additionalText
    ): TremolServiceReceiptDTO {
        $incoming = $cashOperationalTransaction->isIncoming();
        $amount = $cashOperationalTransaction->amount;
        $dto = new TremolServiceReceiptDTO(
            (new TerminalLog)->newUniqueId(),
            $cashOperationalTransaction->office->fiscalDevice->fiscal_device_id,
            //TODO: get rid of floats in CashDesk
            intToFloat($incoming ? $amount : -$amount),
            $additionalText,
            $incoming
        );

        $this->logEventService->create(
            $this->stack,
            $dto,
            $dto->amount,
            $this->stack,
            TeminalPrintingTypeEnum::serviceReceipt
        );

        return $dto;
    }
}
