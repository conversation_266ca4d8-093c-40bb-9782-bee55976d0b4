<?php

namespace Modules\ThirdParty\Services\FiscalDevice;

use Modules\CashDesk\Enums\TremolProcessingStackEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Repositories\TerminalLogRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\FiscalDevice;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\ThirdParty\Libraries\Tremol\TremolException;

class FiscalDeviceServiceFactory
{

    private FiscalProcessingInterface $processingClass;

    public function __construct(
        protected TerminalLogRepository $terminalLogRepository = new TerminalLogRepository(),
        protected LogEventService       $logEventService = new LogEventService,
    )
    {
        /**
         * @var TremolProcessingStackEnum $processing
         */
        $processing = config('cashdesk.tremolProcessing');
        $this->processingClass = $processing->getProcessingClass();
    }

    /**
     * @throws TremolException
     */
    public function makeServiceReceipt(
        CashOperationalTransaction $cashOperationalTransaction,
        ?FiscalDevice              $fiscalDevice,
        string                     $additionalText = ''
    ): void
    {
        $this->processingClass->makeServiceReceipt(
            $cashOperationalTransaction,
            $fiscalDevice,
            $additionalText
        );
    }

    /**
     * @throws TremolException
     */
    public function makeDailyReport(?FiscalDevice $fiscalDevice, Office $office): void
    {
        $this->processingClass->makeDailyReport($fiscalDevice, $office);
    }


    /**
     * @throws TremolException
     */
    public function makeFiscalReceipt(CashOperationalTransaction $cashOperationalTransaction): void
    {
        $this->processingClass->makeFiscalReceipt($cashOperationalTransaction);
    }

    /**
     * @throws TremolException
     * @throws NotFoundException
     */
    public function makeStornoReceipt(
        ?FiscalDevice $fiscalDevice,
        Payment       $payment
    ): void
    {
        $this->processingClass->makeStornoReceipt($fiscalDevice, $payment);
    }

}
