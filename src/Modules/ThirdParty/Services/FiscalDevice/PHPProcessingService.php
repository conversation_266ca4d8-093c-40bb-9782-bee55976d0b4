<?php

namespace Modules\ThirdParty\Services\FiscalDevice;

use Modules\CashDesk\Enums\TremolProcessingStackEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Repositories\TerminalLogRepository;
use Modules\Common\Models\FiscalDevice;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\ThirdParty\Libraries\Tremol\TremolException;

class PHPProcessingService extends FiscalProcessingAbstract
{
    public function __construct(
        protected TerminalLogRepository $terminalLogRepository,
        protected LogEventService $logEventService,
        protected PHPFiscalDevicePrintService $PHPFiscalDevicePrintService,
    ) {
        parent::__construct($terminalLogRepository, $logEventService);
    }

    protected TremolProcessingStackEnum $stack = TremolProcessingStackEnum::PHP;

    /**
     * @throws TremolException
     */
    private function checkIsNotNull(?FiscalDevice $fiscalDevice): void
    {
        if (empty($fiscalDevice)) {
            throw new TremolException(new \RuntimeException(__('payments::fiscalDevice.notSet')));
        }
    }

    /**
     * @throws TremolException
     */
    public function makeServiceReceipt(
        CashOperationalTransaction $cashOperationalTransaction,
        ?FiscalDevice $fiscalDevice,
        string $additionalText = ''
    ): void {
        $this->checkIsNotNull($fiscalDevice);
        $dto = $this->logServiceReceipt($cashOperationalTransaction, $additionalText);

        $this->PHPFiscalDevicePrintService->serviceReceipt($dto, $fiscalDevice);
    }

    /**
     * @throws TremolException
     */
    public function makeDailyReport(?FiscalDevice $fiscalDevice, Office $office): void
    {
        $this->checkIsNotNull($fiscalDevice);
        $dto = $this->logDailyReport($office);
        $this->PHPFiscalDevicePrintService->dailyReport($dto, $fiscalDevice);
    }

    public function makeFiscalReceipt(CashOperationalTransaction $cashOperationalTransaction): void
    {
        $dto = $this->logFiscalReceipt($cashOperationalTransaction);

        $this->PHPFiscalDevicePrintService->fiscalReceipt($dto, $cashOperationalTransaction);
    }


    /**
     * @throws TremolException
     */
    public function makeStornoReceipt(
        ?FiscalDevice $fiscalDevice,
        Payment $payment
    ): void
    {
        $this->checkIsNotNull($fiscalDevice);
        $dto = $this->logStornoReceipt($payment);

        $this->PHPFiscalDevicePrintService->stornoReceipt($dto, $fiscalDevice);
    }
}
