<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Services\LoanOfferMaker;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Head\Services\ClientService;
use Modules\ThirdParty\Enums\LoanOfferCompanyEnum;
use Modules\ThirdParty\Services\LoanOfferRequestLogService;
use Throwable;

final class SmartItkOfferMaker implements LoanOfferMaker
{
    const PERIOD_IN_DAYS = 180;

    public function __construct(
        private LoanOfferRequestLogService $logService,
        private ClientService $clientService
    ) {}

    private const SMART_ITK_COUNTRY_ID = 1;

    public function make(Loan $loan): bool
    {
        if (config('app.project') !== 'stikcredit') {
            return false;
        }

        $url = env('SMART_ITK_LEAD_CREATE_URL');
        $apiKey = env('SMART_ITK_API_KEY');

        if (!$url || !$apiKey) {
            Log::error('SmartItkOfferMaker: Missing SMART_ITK_LEAD_CREATE_URL or SMART_ITK_API_KEY');
            return false;
        }

        $client = $loan->client;
        if (!$client) {
            Log::error('SmartItkOfferMaker: Client is missing');
            return false;
        }

        /**
         * клиента няма кредит в статус активен или в статус изплатен създаден последните 180 дни.
         * Т.е. ако client ID има активен кредит ИЛИ има изплатен кредит с дата >= Днес минус 180 дни => не изпращай, иначе изпрати.
         */
        if ($this->clientService->hasActiveOrRepaidLoanForPeriod($client->client_id, self::PERIOD_IN_DAYS)) {
            $loan->addMeta(
                'smart_itk:hasActiveOrRepaidLoanForPeriod',
                'Client has active or repaid loan for last 180 days'
            );

            return false;
        }

        $params = [];
        try {
            $params = $this->getParams($client);
            $response = Http::withHeaders(['token' => $apiKey])->post($url, $params);
        } catch (Throwable $e) {
            Log::error('Error while sending request to SMART_ITK: ' . $e->getMessage());

            $this->logService->log(
                LoanOfferCompanyEnum::SMART_ITK,
                $loan->getKey(),
                $url,
                $params,
                $e->getMessage()
            );

            return false;
        }

        if (!$response->successful() || !$response->json('success')) {
            // Log::error('Unexpected response from SMART_ITK');

            $this->logService->log(
                LoanOfferCompanyEnum::SMART_ITK,
                $loan->getKey(),
                $url,
                $params,
                $response->body()
            );

            return false;
        }

        return true;
    }

    private function getParams(Client $client): array
    {
        $cityName = $client->clientLastAddressIdcard()?->city()->value('name') ?: 'Undefined';

        $phone = $client->phone;

        if ($phone && str_starts_with($phone, '0')) {
            $phone = '+359' . substr($phone, 1);
        }

        return [
            'country_id' => self::SMART_ITK_COUNTRY_ID,
            'firstname' => $client->first_name,
            'middlename' => $client->middle_name,
            'lastname' => $client->last_name,
            'phone' => $phone,
            'email' => $client->email,
            'city' => $cityName,
            'region' => '',
            'ein' => $client->pin,
            'additional_info' => '',
        ];
    }
}
