<?php

namespace Modules\ThirdParty\Services;

use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\NoiReport;
use Modules\ThirdParty\Libraries\Nsi;
use Modules\ThirdParty\Repositories\NoiReportRepository;

class ReportService
{
    const ADDITIONAL_NOI_PROVIDER = 'DateTechnology';

    public function __construct(
        private readonly NoiService $noiService = new NoiService,
        private readonly NoiReportRepository $noiReportRepository = new NoiReportRepository,
        private readonly DateTechnologyService $dateTechnologyService = new DateTechnologyService,
    ) {
    }

    public function isValidNoiCode(int $reportCode)
    {
        return in_array(
            $reportCode,
            [
                Nsi::REPORT_ID_SHORT,
                Nsi::REPORT_ID_FULL,
                Nsi::REPORT_ID_RETIRED,
            ]
        );
    }

    /**
     * Scenario:
     * - check in our DB for such report and it should be not older than 60 days
     * - check in D&A and it should be not older than 60 days
     * - check in NOI
     * @param bool $skipCaching - if true - always take fresh report
     */
    public function addNoiReport(
        Client $client,
        int $reportCode,
        string $source = 'job',
        ?Loan $loan = null,
        bool $skipCaching = false,
        ?int $attempt = 1
    ): array {

        // mandatory validations
        if (empty($client->pin)) {
            throw new \RuntimeException(__('table.WrongPin'));
        }
        if (empty($reportCode) || !$this->isValidNoiCode($reportCode)) {
            throw new \RuntimeException(__('table.ReportCodesNotFound'));
        }
        // optional validation if loan passed (always passed for job source)
        if (!is_null($loan) || $source == 'job') {
            // validate main params
            if (empty($loan->loan_id)) {
                throw new \RuntimeException(__('table.LoanNotFound'));
            }
        }

        $pin = $client->pin;

        // names\days mapping
        switch ($reportCode) {
            case Nsi::REPORT_ID_SHORT:
                $reportName = NoiReport::REPORT_NAME_SHORT;
                break;

            case Nsi::REPORT_ID_FULL:
                $reportName = NoiReport::REPORT_NAME_FULL;
                break;

            case Nsi::REPORT_ID_RETIRED:
                $reportName = NoiReport::REPORT_NAME_RETIRED;
                break;
        }
        $daysBack = (int) config('reports.' . Nsi::PREFIX . $reportCode . '.cache_days', 60);
        if (empty($daysBack) || empty($reportName)) {
            throw new \RuntimeException(__('table.ReportCodesNotMapped'));
        }
        if ('manual-report' == $source) {
            $daysBack = 0;
        }


        if (false === $skipCaching) {
            // check in our db first
            $noiReport = $this->getNoiReportFromOurDb(
                $pin,
                $reportName,
                $daysBack
            );

            /// if exists and report date is larger than 60 days
            if ($noiReport && $noiReport->hasValidReportDate()) {

                // save relation for the new loan
                if ($loan) {
                    $this->noiReportRepository->addPivot(
                        $noiReport->getKey(),
                        $loan->client_id,
                        $loan->getKey(),
                        $noiReport->name
                    );
                }

                return [$noiReport];
            }
        }


        // check in D&A
        $noiReportFromDt = $this->saveAndGetNoiReport(
            $client,
            $reportName,
            $source,
            $loan,
            $attempt
        );
        if (!empty($noiReportFromDt->noi_report_id)) {
            return [$noiReportFromDt];
        }


        // TMP STOPPED: since NOI is not connected properly, we just not find a report
        return [];


        // TMP STOPPED: since we have no NOI connection, we directly use DT
        // //ako ne e prazen dt, no datata na spravkata e po stara ot 60 dni vikame NOI api
        // if (Carbon::parse($dateTechnologyData['reportDate'])->gte(Carbon::now()->subDays($daysBack))) {
        //     //TODO: call noi api here
        //     return [$this->addNoiReportAndPivot($loan)];
        // } // ako datata na spravkata e v ramkite na 60 dni dobavqme v noi_report i noi_pivot tablicite
        // elseif (Carbon::parse($dateTechnologyData['reportDate'])->lt(Carbon::now()->subDays($daysBack))) {
        //     $data = [
        //         'pin' => $dateTechnology->pin,
        //         'reportName' => $dateTechnology->name,
        //         'report' => $dateTechnology->data,
        //         'execTime' => $dateTechnology->exec_time,
        //         'source' => self::ADDITIONAL_NOI_PROVIDER,
        //         'source_id' => $dateTechnology->getKey()
        //     ];
        //     $noiReport = $this->noiReportRepository->addReport($data);

        //     $this->noiReportRepository->addPivot(
        //         $noiReport->getKey(),
        //         $loan->client_id,
        //         $loan->getKey(),
        //         $noiReport->name
        //     );

        //     return [$noiReport];
        // }

        // if we are here then we call NOI api since could not find any valid report
        // return [$this->getAndSaveNoiReport($pin, $loan, $reportCode)];
    }

    // Used in:
    // - here
    // - CustomNoiUpdate
    public function saveAndGetNoiReport(
        Client $client,
        string $reportName,
        string $source = 'job',
        ?Loan $loan = null,
        ?int $attempt = 1
    ):?NoiReport {

        return $this->dateTechnologyService->addReportAndRelationsForLoan(
            $client,
            $reportName,
            $source,
            $loan,
            $attempt
        );
    }

    public function getNoiReportFromOurDb(
        string $pin,
        string $reportName = 'noi2',
        int $daysBefore = null
    ): ?NoiReport {
        $noiReport = $this->noiReportRepository->getNoiReport(
            $pin,
            $reportName,
            $daysBefore
        );
        if (!empty($noiReport->noi_report_id)) {
            return $noiReport;
        }

        return null;
    }

    /**
     * STOPPED FOR NOW, since we use DT for NOI
     * Use NOI api
     */
    public function getAndSaveNoiReport(
        string $pin,
        Loan $loan = null,
        int $reportCode = Nsi::REPORT_ID_SHORT
    ): ?NoiReport {
        $noiReport = $this->noiService->addReport(
            $pin,
            $reportCode
        );
        if (empty($noiReport->noi_report_id)) {
            throw new \RuntimeException('table.NoiReportNotCreated');
        }

        if (!empty($loan->loan_id)) {
            $this->noiReportRepository->addPivot(
                $noiReport->getKey(),
                $loan->client_id,
                $loan->getKey(),
                $noiReport->name
            );
        }

        return $noiReport;
    }
}
