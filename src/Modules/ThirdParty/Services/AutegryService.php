<?php

namespace Modules\ThirdParty\Services;

use Carbon\Carbon;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Models\AutegryReport;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Traits\PinTrait;

class AutegryService
{
    use PinTrait;

    const COMPANY = 'stick';
    const CHANEL_TO_COMPANY = 'google';
    const CHANEL_TO_CLIENT = 'web';

    private $ccrReportId = null;
    private $noi2ReportId = null;
    private $noi7ReportId = null;
    private $noi51ReportId = null;

    public function getScoringForLoan(
        Client $client,
        Loan $loan,
        string $source = 'job',
        int $attempt = 1
    ): ?AutegryReport {

        // prepare request data
        $request = $this->getRequestData($client, $loan);
        if (empty($request)) {
            return null;
        }

        // make prev reports for loan: last = 0
        AutegryReport::where('loan_id', $loan->loan_id)
            ->where('last', '1')
            ->update(['last' => 0]);

        // create new report
        $newReport = new AutegryReport();
        $newReport->client_id = $client->client_id;
        $newReport->loan_id = $loan->loan_id;
        $newReport->request = json_encode($request);
        $newReport->source = $source;
        $newReport->attempt = $attempt;
        $newReport->last = 1;
        $newReport->created_at = Carbon::now();
        $newReport->created_by = getAdminId();
        $newReport->save();

        // try to get Autegry API response
        $start = microtime(true);
        $responseData = self::getApiScoreResponse($request);
        $end = microtime(true);
        $execTime = round(($end - $start), 2);

        // write raw response before parse
        $newReport->response = ensureJsonEncoded($responseData);
        $newReport->save();

        // parse and add to entity parsed data
        $gb = null;
        $zone = null;
        $score = null;
        $badRate = null;

        $exg_bad_rate = null;
        $exg_zone = null;

        $new_bad_rate = null;
        $new_zone = null;

        if (empty($responseData['error']) && !empty($responseData['response'])) {
            try {
                $response = json_decode($responseData['response']);

                $mainData = null;
                if (!empty($response->score_app)) {
                    $mainData = $response->score_app;
                }
                if (!empty($response->{$loan->loan_id}->score_app)) {
                    $mainData = $response->{$loan->loan_id}->score_app;
                }

                if (!empty($mainData->score)) {
                    $score = $mainData->score;
                    $badRate = $mainData->bad_rate;
                    $zone = $mainData->risk_zone;
                    $gb = self::getRiskZone($zone);
                }

                if (!empty($response->score_exg)) {
                    $exg_bad_rate = $response->score_exg?->bad_rate ?? null;
                    $exg_zone = $response->score_exg?->risk_zone ?? null;
                }

                if (!empty($response->score_new)) {
                    $new_bad_rate = $response->score_new?->bad_rate ?? null;
                    $new_zone = $response->score_new?->risk_zone ?? null;
                }
            } catch (\Throwable $e) {}
        }

        // update new report with response data
        $newReport->gb = $gb;
        $newReport->zone = $zone;
        $newReport->score = $score;
        $newReport->bad_rate = $badRate;

        $newReport->exg_bad_rate = $exg_bad_rate;
        $newReport->exg_zone = $exg_zone;

        $newReport->new_bad_rate = $new_bad_rate;
        $newReport->new_zone = $new_zone;


        $newReport->updated_at = Carbon::now();
        $newReport->updated_by = getAdminId();
        $newReport->ccr_report_id = $this->ccrReportId;
        $newReport->noi2_report_id = $this->noi2ReportId;
        $newReport->noi7_report_id = $this->noi7ReportId;
        $newReport->noi51_report_id = $this->noi51ReportId;
        $newReport->save();

        return $newReport;
    }

    public static function getRiskZone(string $riskLetter): string
    {
        $mapping = [
            'A' => 'accept',
            'B' => 'low_risk',
            'C' => 'mid_risk',
            'D' => 'high_risk',
            'E' => 'reject'
        ];

        if (array_key_exists($riskLetter, $mapping)) {
            return $mapping[$riskLetter];
        }

        return 'unknown';
    }

    public function getRequestData2(
        Client $client,
        Loan $loan,
        bool $useCurrentLoan = false
    ): array {

        $data = [
            'info'  => [],
            'app'   => [],
            'ccr'   => 'None',
            'snssi' => 'None',
            'lnssi' => 'None',
            'pnssi' => 'None',
            'performance' => [],
        ];

        // --- info
        $data['info']['requested_date'] = $loan->created_at->format('Y-m-d H:i:s');
        $data['info']['request_id'] = $loan->loan_id;
        $data['info']['customer_id'] = $loan->client_id;
        $data['info']['company'] = self::COMPANY;

        // --- app
        $pinInfo = $this->getAgeAndSex($client->pin);
        $birthDate = Carbon::parse($pinInfo['birth_date']);
        $loanDate = Carbon::parse($loan->created_at);
        $age = $birthDate->diffInYears($loanDate);

        $data['app']['APP_Gender'] = (int) $pinInfo['sex']; // 2 = male, 1 = female
        $data['app']['APP_Age'] = $age;
        $data['app']['APP_BirthRegion'] = (string) $pinInfo['region'];

        $data['app']['APP_CreditType_Requested'] = $loan->product_type_id; // 1 = revolv, 2 = install
        $data['app']['APP_LoanAmount_Requested'] = intToFloat($loan->amount_approved);
        $data['app']['APP_LoanPeriod_Requested'] = (int) $loan->period_approved;
        $data['app']['APP_Installment_Requested'] = $loan->getFirstInstallmentPrimaryAmount();

        $data['app']['APP_AccessChannelToCustomer'] = self::CHANEL_TO_COMPANY;
        $data['app']['APP_AccessChannelToCompany'] = (int) $loan->payment_method_id; // 1 = bank, 2 = easypay

        $addrData = $loan->getAddressArrayForA4e();
        $data['app']['APP_AddressCity'] = $addrData['city'];
        $data['app']['APP_Address'] = $addrData['address'];
        $data['app']['APP_AddressPostCode'] = $addrData['postcode'];

        $refinancedId = $loan->getRefinancedId();
        $data['app']['APP_Refinancing'] = !empty($refinancedId) ? $refinancedId : 'None';

        // --- ccr
        $ccrData = $loan->getCcrArrayForA4e('ASC');
        if (!empty($ccrData['raw'])) {
            $data['ccr'] = $ccrData['raw'];
        } else if (!empty($ccrData['json'])) {
            $data['ccr'] = $ccrData['json'];
        }
        if (
            $data['ccr'] == '[]'
            || (empty($data['ccr']) && is_array($data['ccr']))
        ) {
            $data['ccr'] = 'None';
        }

        // --- NOI (snssi, lnssi, pnssi)
        $noiData = $loan->getNoiArrayForA4e('ASC');
        if (!empty($noiData['noi2']['raw'])) {
            $data['snssi'] = $noiData['noi2']['raw'];
        } else if (!empty($noiData['noi2']['json'])) {
            $data['snssi'] = $noiData['noi2']['json'];
        }
        if (!empty($noiData['noi7']['raw'])) {
            $data['lnssi'] = $noiData['noi7']['raw'];
        } else if (!empty($noiData['noi7']['json'])) {
            $data['lnssi'] = $noiData['noi7']['json'];
        }
        if (!empty($noiData['noi51']['raw'])) {
            $data['pnssi'] = $noiData['noi51']['raw'];
        } else if (!empty($noiData['noi51']['json'])) {
            $data['pnssi'] = $noiData['noi51']['json'];
        }

        // --- performance
        $data['performance'] = self::getPerfomanceData2($loan, $useCurrentLoan);


        $ccrReportId = null;
        $noi2ReportId = null;
        $noi7ReportId = null;
        $noi51ReportId = null;
        if (!empty($ccrData['ccr_report_id'])) { $ccrReportId = $ccrData['ccr_report_id']; }
        if (!empty($noiData['noi2']['noi_report_id'])) { $noi2ReportId = $noiData['noi2']['noi_report_id']; }
        if (!empty($noiData['noi7']['noi_report_id'])) { $noi7ReportId = $noiData['noi7']['noi_report_id']; }
        if (!empty($noiData['noi51']['noi_report_id'])) { $noi51ReportId = $noiData['noi51']['noi_report_id']; }
        $this->setReportsIds($ccrReportId, $noi2ReportId, $noi7ReportId, $noi51ReportId);

        return $data;
    }

    public function getRequestData(
        Client $client,
        Loan $loan,
        bool $useCurrentLoan = false
    ): array {

        $data = [
            'info'  => [],
            'app'   => [],
            'ccr'   => 'None',
            'snssi' => 'None',
            'lnssi' => 'None',
            'pnssi' => 'None',
            'performance' => [],
        ];

        // --- info
        $data['info']['requested_date'] = $loan->created_at->format('Y-m-d H:i:s');
        $data['info']['request_id'] = $loan->loan_id;
        $data['info']['customer_id'] = $loan->client_id;
        $data['info']['company'] = self::COMPANY;

        // --- app
        $pinInfo = $this->getAgeAndSex($client->pin);

        $data['app']['APP_Gender'] = (int) $pinInfo['sex']; // 2 = male, 1 = female
        $data['app']['APP_Age'] = (int) $pinInfo['age'];
        $data['app']['APP_BirthRegion'] = (string) $pinInfo['region'];

        $data['app']['APP_CreditType_Requested'] = $loan->product_type_id; // 1 = revolv, 2 = install
        $data['app']['APP_LoanAmount_Requested'] = intToFloat($loan->amount_approved);
        $data['app']['APP_LoanPeriod_Requested'] = (int) $loan->period_approved;
        $data['app']['APP_Installment_Requested'] = $loan->getFirstInstallmentPrimaryAmount();

        $data['app']['APP_AccessChannelToCustomer'] = self::CHANEL_TO_COMPANY;
        $data['app']['APP_AccessChannelToCompany'] = (int) $loan->payment_method_id; // 1 = bank, 2 = easypay

        $addrData = $loan->getAddressArrayForA4e();
        $data['app']['APP_AddressCity'] = $addrData['city'];
        $data['app']['APP_Address'] = $addrData['address'];
        $data['app']['APP_AddressPostCode'] = $addrData['postcode'];

        $refinancedId = $loan->getRefinancedId();
        $data['app']['APP_Refinancing'] = !empty($refinancedId) ? $refinancedId : 'None';

        // --- ccr
        $ccrData = $loan->getCcrArrayForA4e();
        if (!empty($ccrData['raw'])) {
            $data['ccr'] = $ccrData['raw'];
        } else if (!empty($ccrData['json'])) {
            $data['ccr'] = $ccrData['json'];
        }
        if (
            $data['ccr'] == '[]'
            || (empty($data['ccr']) && is_array($data['ccr']))
        ) {
            $data['ccr'] = 'None';
        }

        // --- NOI (snssi, lnssi, pnssi)
        $noiData = $loan->getNoiArrayForA4e();
        if (!empty($noiData['noi2']['raw'])) {
            $data['snssi'] = $noiData['noi2']['raw'];
        } else if (!empty($noiData['noi2']['json'])) {
            $data['snssi'] = $noiData['noi2']['json'];
        }
        if (!empty($noiData['noi7']['raw'])) {
            $data['lnssi'] = $noiData['noi7']['raw'];
        } else if (!empty($noiData['noi7']['json'])) {
            $data['lnssi'] = $noiData['noi7']['json'];
        }
        if (!empty($noiData['noi51']['raw'])) {
            $data['pnssi'] = $noiData['noi51']['raw'];
        } else if (!empty($noiData['noi51']['json'])) {
            $data['pnssi'] = $noiData['noi51']['json'];
        }

        // --- performance
        $data['performance'] = self::getPerfomanceData($loan, $useCurrentLoan);


        $ccrReportId = null;
        $noi2ReportId = null;
        $noi7ReportId = null;
        $noi51ReportId = null;
        if (!empty($ccrData['ccr_report_id'])) { $ccrReportId = $ccrData['ccr_report_id']; }
        if (!empty($noiData['noi2']['noi_report_id'])) { $noi2ReportId = $noiData['noi2']['noi_report_id']; }
        if (!empty($noiData['noi7']['noi_report_id'])) { $noi7ReportId = $noiData['noi7']['noi_report_id']; }
        if (!empty($noiData['noi51']['noi_report_id'])) { $noi51ReportId = $noiData['noi51']['noi_report_id']; }
        $this->setReportsIds($ccrReportId, $noi2ReportId, $noi7ReportId, $noi51ReportId);

        return $data;
    }

    public static function getApiScoreResponse(array $request)
    {
        // Prepare the user and JWT token
        $url = env('AUTEGRY_URL');
        if (empty($url)) {
            return ['response' => '', 'error' => 'no url set'];
        }
        $jsonData = json_encode($request);


        // Initialize cURL session
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

        // Set HTTP headers
        $headers = [
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $error = '';
        $response = curl_exec($ch);
        $cleanResponse = str_replace('NaN', 'null', $response);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
        }
        curl_close($ch);


        return [
            'response' => $cleanResponse,
            'error' => $error,
        ];
    }

    public static function getApiScoreResponseOuter(array $request)
    {
        // Prepare the user and JWT token
        $url = env('AUTEGRY_URL');
        $user = env('AUTEGRY_USER');
        $jwtToken = env('AUTEGRY_JWT');
        if (empty($url)) {
            return ['response' => '', 'error' => 'no url set'];
        }
        if (empty($user)) {
            return ['response' => '', 'error' => 'no user set'];
        }
        if (empty($jwtToken)) {
            return ['response' => '', 'error' => 'no jwt set'];
        }
        $jsonData = json_encode($request);


        // Initialize cURL session
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

        // Set HTTP headers
        $headers = [
            'Content-Type: application/json',
            'user: ' . $user,
            'Authentication: Bearer ' . $jwtToken,
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $error = '';
        $response = curl_exec($ch);
        $cleanResponse = str_replace('NaN', 'null', $response);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
        }
        curl_close($ch);


        return [
            'response' => $cleanResponse,
            'error' => $error,
        ];
    }

    public static function getApiFirstRegistration()
    {
        /*
        url: xxx/auth
        post data:
        {
            "password":"xxx", // pass provided from autegry
            "company":self::COMPANY,
            "identification":{
                "email":"", // our legit email
                "password":"xxx" // our custom pass
            },
            "first_sign" : 1
        }
        reponse: "First sign in is done"
        */
    }

    public static function getApiRsaKey()
    {
        /*
        url: xxx/auth
        post data:
        {
            "password":"xxx", // our custom pass
            "company":self::COMPANY,
            "first_sign" : 0
        }
        reponse: "RSA Key"
        */
    }

    public static function transformPrivateRsaKeyIntoPem()
    {
        /*
            take key string put and use this python script

            pemToJwt.py:
            import jwt
            private_key = """-----BEGIN PRIVATE KEY-----
            -----END PRIVATE KEY-----\n"""

            payload = {
                "user": "<EMAIL>",
                "company": "stikcredit"
            }

            auth_encoded = jwt.encode(payload, private_key, algorithm="RS256")
            print(auth_encoded)

            run: python3 pemToJwt.py
        */
    }

    public static function getPerfomanceData2(Loan $loan, bool $useCurrentLoan = false): array
    {
        $result = [];

        if ($useCurrentLoan) {
            $refinancingId = $loan->getRefinancingId();
            $r = [
                'request_id' => $loan->loan_id,
                'credit_type' => $loan->product_type_id, // 1 = revolv, 2 = install
                'amount' => intToFloat($loan->amount_approved),
                'period' => $loan->period_approved,
                'installment' => $loan->getFirstInstallmentPrimaryAmount(),
                'refinanced_id' => !empty($refinancingId) ? $refinancingId : 'None',
                'status' => self::getA4eLoanStatusForPerformance($loan, $refinancingId),
                'payment' => self::getPayments($loan, $useCurrentLoan),
                'payment_plan' => self::getPaymentPlan($loan),
            ];
            $result[$loan->loan_id] = $r;
        }

        $prevLoans = Loan::where('client_id', $loan->client_id)
            ->where('created_at', '<', $loan->created_at)
            ->where('office_id', 1)
            ->whereIn('loan_status_id', LoanStatus::getStatusesForA4e())
            ->orderBy('loan_id', 'DESC')
            ->limit(100)
            ->get();


        if ($prevLoans->count() < 1) {
            return $result;
        }


        // add prev loans
        /** @var \Modules\Common\Models\Loan $prevLoan **/
        foreach ($prevLoans as $prevLoan) {
            $refinancingId = $prevLoan->getRefinancingId();

            $r = [
                'request_id' => $prevLoan->loan_id,
                'credit_type' => $prevLoan->product_type_id, // 1 = revolv, 2 = install
                'amount' => intToFloat($prevLoan->amount_approved),
                'period' => $prevLoan->period_approved,
                'installment' => $prevLoan->getFirstInstallmentPrimaryAmount(),
                'refinanced_id' => !empty($refinancingId) ? $refinancingId : 'None',
                'status' => self::getA4eLoanStatusForPerformance($prevLoan, $refinancingId),
                'payment' => self::getPayments($prevLoan, $useCurrentLoan),
                'payment_plan' => self::getPaymentPlan($prevLoan),
            ];

            $result[$prevLoan->loan_id] = $r;
        }

        return $result;
    }

    public static function getPerfomanceData(Loan $loan, bool $useCurrentLoan = false): array
    {
        $result = [];

        if ($useCurrentLoan) {
            $refinancingId = $loan->getRefinancingId();
            $r = [
                'request_id' => $loan->loan_id,
                'credit_type' => $loan->product_type_id, // 1 = revolv, 2 = install
                'amount' => intToFloat($loan->amount_approved),
                'period' => $loan->period_approved,
                'installment' => $loan->getFirstInstallmentPrimaryAmount(),
                'refinanced_id' => !empty($refinancingId) ? $refinancingId : 'None',
                'status' => self::getA4eLoanStatusForPerformance($loan, $refinancingId),
                'payment' => self::getPayments($loan, $useCurrentLoan),
                'payment_plan' => self::getPaymentPlan($loan),
            ];
            $result[$loan->loan_id] = $r;
        }

        $prevLoans = Loan::where('client_id', $loan->client_id)
            ->where('created_at', '<', $loan->created_at)
            ->whereIn('loan_status_id', LoanStatus::getStatusesForA4e())
            ->orderBy('loan_id', 'DESC')
            ->limit(300)
            ->get();


        if ($prevLoans->count() < 1) {
            return $result;
        }


        // add prev loans
        /** @var \Modules\Common\Models\Loan $prevLoan **/
        foreach ($prevLoans as $prevLoan) {
            $refinancingId = $prevLoan->getRefinancingId();

            $r = [
                'request_id' => $prevLoan->loan_id,
                'credit_type' => $prevLoan->product_type_id, // 1 = revolv, 2 = install
                'amount' => intToFloat($prevLoan->amount_approved),
                'period' => $prevLoan->period_approved,
                'installment' => $prevLoan->getFirstInstallmentPrimaryAmount(),
                'refinanced_id' => !empty($refinancingId) ? $refinancingId : 'None',
                'status' => self::getA4eLoanStatusForPerformance($prevLoan, $refinancingId),
                'payment' => self::getPayments($prevLoan, $useCurrentLoan),
                'payment_plan' => self::getPaymentPlan($prevLoan),
            ];

            $result[$prevLoan->loan_id] = $r;
        }

        return $result;
    }

    public static function getA4eLoanStatusForPerformance(Loan $loan, $refinancingId): string
    {
        return match ($loan->loan_status_id) {
            LoanStatus::ACTIVE_STATUS_ID => $loan->juridical == 1 ? 'court' : 'active',
            LoanStatus::REPAID_STATUS_ID => !empty($refinancingId) ? 'refinanced' : 'paid_off',
            LoanStatus::CANCELLED_STATUS_ID => 'rejected',
            LoanStatus::WRITTEN_OF_STATUS_ID => 'cession',
            default => 'unknown',
        };
    }

    public static function getPaymentPlan(Loan $loan): array
    {
        $plan = [];

        $installments = $loan->getAllInstallments();
        foreach ($installments as $installment) {
            $plan[$installment->due_date->format('Y-m-d')] = [
                'payment' => ($installment->principal + $installment->interest + $installment->penalty),
                'principal' => $installment->principal,
                'interest' => $installment->interest,
                'penalty' => $installment->penalty,
                'taxes' => 0,
            ];
        }

        return $plan;
    }

    public static function getPayments(Loan $loan, bool $debug = false): array
    {
        $rows = [];

        $payments = $loan->getAllPayments();
        foreach ($payments as $payment) {

            if (!$payment->isIncoming()) {
                continue;
            }

            // normal distr
            if (!empty($payment->delivery) && empty($payment->migration_id)) {
                $pd = getExplainedPaymentDetails($payment->delivery);
            } else {

                // easypay refunded
                if ($payment->purpose == PaymentPurposeEnum::REFUND) {
                    $pd = ['principal' => intToFloat($payment->amount)];
                } else {

                    // migrated without delivery, based on distr table
                    $distr = $payment->getCalcDistribution();

                    $pd = [];
                    $pd['principal'] = (float) intToFloat($distr->principal);
                    $pd['interest'] = (float) intToFloat($distr->interest);
                    $pd['penalty'] = (float) intToFloat($distr->penalty);
                    $pd['late_interest'] = (float) intToFloat($distr->late_interest);
                    $pd['late_penalty'] = (float) intToFloat($distr->late_penalty);
                    $pd['taxes'] = (float) intToFloat($distr->amount);
                }
            }

            // if ($debug) {
            //     dd($pd, $payment->delivery, $payment->getCalcDistribution());
            // }

            $rows[$payment->created_at->format('Y-m-d H:i:s')] = [
                'payment'   => intToFloat($payment->amount),
                'principal' => $pd['principal'] ?? 0,
                'interest'  => $pd['interest'] ?? 0,
                'penalty'   => $pd['penalty'] ?? 0,
                'taxes'     => ($pd['late_penalty'] ?? 0) + ($pd['late_interest'] ?? 0) + ($pd['taxes'] ?? 0),
            ];
        }

        return $rows;
    }

    private function setReportsIds(
        ?int $ccrReportId = null,
        ?int $noi2ReportId = null,
        ?int $noi7ReportId = null,
        ?int $noi51ReportId = null,
    ): void {

        $this->nullateReportsIds();

        $this->ccrReportId = $ccrReportId;
        $this->noi2ReportId = $noi2ReportId;
        $this->noi7ReportId = $noi7ReportId;
        $this->noi51ReportId = $noi51ReportId;
    }

    private function nullateReportsIds(): void
    {
        $this->ccrReportId = null;
        $this->noi2ReportId = null;
        $this->noi7ReportId = null;
        $this->noi51ReportId = null;
    }
}
