<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Services\Veriff;

use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Enums\VeriffProviderStatusEnum;
use Modules\Common\Models\Client;
use Modules\Docs\Services\ClientDocumentService;

final class VeriffActionService
{
    public function __construct(private VeriffApiService $veriffApiService)
    {
    }

    public function setOffered(Client $client): void
    {
        $client->verif_offered = true;
        $client->verif_offered_at = now();
        $client->saveQuietly();
    }

    public function updateClientVerifProviderOnWaitingStatus(Client $client, $responseData): void
    {
        $client->verif_skipped = false;
        $client->verif_processed_at = now();
        $client->verif_processed_loan_id = $responseData['loan_id'];

        $client->verif_provider_session_id = $responseData['verification']['id'];
        $client->verif_provider_url = $responseData['verification']['url'];
        $client->verif_provider_status = VeriffProviderStatusEnum::VERIF_STATUS_WAITING;
        $client->verif_provider_status_updated_at = now();
        $client->saveQuietly();
    }

    public function updateClientVerifProviderOnStartedStatus(Client $client): void
    {
        $client->verif_provider_status = VeriffProviderStatusEnum::VERIF_STATUS_STARTED;
        $client->verif_provider_status_updated_at = now();
        $client->saveQuietly();
    }

    public function updateClientVerifProviderOnApprovedStatus(Client $client): void
    {
        $client->verif_provider_status = VeriffProviderStatusEnum::VERIF_STATUS_APPROVED;
        $client->verif_provider_status_updated_at = now();
        $client->verif_fails_count = 0;

        $client->docs_verified_at = now();

        /// when we have auto cancelled loan but client complete verification
        if (empty($client->verif_processed_at)) {
            $client->verif_skipped = false;
            $client->verif_processed_at = now();
        }

        $client->saveQuietly();
    }

    public function updateClientVerifProviderOnRejectedStatus(Client $client): void
    {
        $client->setAttribute('verif_provider_status', VeriffProviderStatusEnum::VERIF_STATUS_DECLINED);
        $client->setAttribute('verif_provider_status_updated_at', now());
        $client->setAttribute('verif_fails_count', ($client->verif_fails_count + 1));
        $client->saveQuietly();
    }

    public function saveAllMedia(Client $client): array
    {
        $mediaData = $this->veriffApiService->getUploadedMediaList($client);
        if (empty($mediaData['images']) && empty($mediaData['videos'])) {
            return [];
        }

        $images = !empty($mediaData['images']) ? $mediaData['images'] : [];
        $videos = !empty($mediaData['videos']) ? $mediaData['videos'] : [];

        $docs = [];
        if (!empty($images)) {
            $docs = array_merge($docs, $this->saveImages($client, $images));
        }
        if (!empty($videos)) {
            $docs = array_merge($docs, $this->saveVideos($client, $videos));
        }

        return $docs;
    }

    private function saveImages(Client $client, array $images): array
    {
        $documents = [];
        $addedDocType = [];

        $clientDocumentService = app(ClientDocumentService::class);
        foreach ($images as $image) {
            if (in_array($image['context'], $addedDocType)) {
                continue; // skip if already added this type, to not upload same shit many times.
            }

            if ($clientDocumentService->alreadyUploaded($client->client_id, $image['id'])) {
                continue; // skip if already added in our db
            }

            $type = $this->determineDocumentType($image['name']); // our document_type_id
            if ($type) {
                $stream = $this->veriffApiService->getImageStream($client, $image['id']);
                if (!empty($stream)) {
                    $doc = $clientDocumentService->uploadVerifDocuments(
                        $stream,
                        $client->client_id,
                        $type,
                        $client->verif_processed_loan_id,
                        $image['id'],
                        $this->getDocumentComment($image['name'])
                    );

                    if (!empty($doc->client_document_id)) {
                        $documents[] = $doc;

                        $addedDocType[] = $image['context'];
                    }
                }
            }
        }

        return $documents;
    }

    private function saveVideos(Client $client, array $videos): array
    {
        $documents = [];
        $clientDocumentService = app(ClientDocumentService::class);

        foreach ($videos as $video) {
            if ($clientDocumentService->alreadyUploaded($client->client_id, $video['id'])) {
                continue; // skip if already added in our db
            }

            $stream = $this->veriffApiService->getImageStream($client, $video['id']);
            if (!empty($stream)) {
                $doc = $clientDocumentService->uploadVeriffVideo(
                    $stream,
                    $client->client_id,
                    FileTypeEnum::CLIENT_DOC,
                    $client->verif_processed_loan_id,
                    $video['id'],
                    'Video from Veriff'
                );

                if (!empty($doc->client_document_id)) {
                    $documents[] = $doc;
                }
            }
        }

        return $documents;
    }

    private function getDocumentComment(string $documentName): string
    {
        if ($documentName === 'face') {
            return 'Veriff селфи';
        }

        if ($documentName === 'document-front') {
            return 'Veriff - Лична карта (front)';
        }

        if ($documentName === 'document-back') {
            return 'Veriff - Лична карта (back)';
        }

        return 'Veriff document';
    }

    private function determineDocumentType(string $imageName): ?FileTypeEnum
    {
        if (preg_match('/(-pre)/', $imageName)) {
            return null; // skip duplicates
        }

        if (preg_match('/(face)/', $imageName)) {
            return FileTypeEnum::ID_CARD; // DOCUMENT_TYPE_SELFIE_ID
        }

        if (preg_match('/(document)/', $imageName)) {
            return FileTypeEnum::ID_CARD; // DOCUMENT_TYPE_ID_IDCARD
        }

        return null;
    }
}
