<?php

namespace Modules\ThirdParty\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientPicture;
use Modules\Common\Models\IdCardIssued;
use Modules\Common\Models\MvrReport;
use Modules\Common\Models\MvrReportPivot;
use Modules\Head\Repositories\CityRepository;
use Modules\Head\Repositories\ClientPictureRepository;
use Modules\Sales\Exceptions\InvalidClientNames;
use Modules\ThirdParty\Exceptions\MvrException;
use Modules\ThirdParty\Libraries\Mvr;
use Modules\ThirdParty\Repositories\MvrReportRepository;
use Modules\ThirdParty\Traits\ReportTrait;

/**
 * Get reports from MVR
 * Save reports in db
 * Links reports to client & loan
 */
class MvrService
{
    use ReportTrait;

    const CODE_NO_RESPONSE = 1001;
    const CODE_WRONG_DATA = 1002;
    const CODE_WRONG_VALID_DATE = 1003;
    public const INCORRECT_PIN = '0502161260';

    private $mvrReportRepo = null;
    private $cityRepo = null;
    private ClientPictureRepository $clientPictureRepository;

    public function __construct(
        MvrReportRepository     $reportRepo,
        CityRepository          $cityRepo,
        ClientPictureRepository $clientPictureRepository
    ) {
        $this->mvrReportRepo = $reportRepo;
        $this->cityRepo = $cityRepo;
        $this->clientPictureRepository = $clientPictureRepository;
    }

    public function validateClientNames(array $data): bool
    {
        $mvrReport = $this->mvrReportRepo->get($data['client_idcard']['pin']);

        //// if we dont has mvr report skip check
        if (empty($mvrReport?->mvr_report_id)) {
            return true;
        }

        $mvrData = json_decode($mvrReport->parsed_data);
        if (
            empty($mvrData->client->first_name) ||
            empty($mvrData->client->last_name)
        ) {
            ///  We are skipping it because there is a possibility of an incorrect MVR report.
            return true;
        }

        /// validate client names
        if (
            strtolower(trim($data['client']['first_name'])) !== strtolower(trim($mvrData->client->first_name)) ||
            strtolower(trim($data['client']['last_name'])) !== strtolower(trim($mvrData->client->last_name))
        ) {
            throw new InvalidClientNames(
                $mvrData->client->first_name . ' ' . $mvrData->client->middle_name . ' ' . $mvrData->client->last_name,
                $data['client']['first_name'] . ' ' . $data['client']['middle_name'] . ' ' . $data['client']['last_name'],
            );
        }

        if (
            !empty($mvrData->client->middle_name) &&
            strtolower(trim($data['client']['middle_name'])) !== strtolower(trim($mvrData->client->middle_name))
        ) {
            throw new InvalidClientNames(
                $mvrData->client->first_name . ' ' . $mvrData->client->middle_name . ' ' . $mvrData->client->last_name,
                $data['client']['first_name'] . ' ' . $data['client']['middle_name'] . ' ' . $data['client']['last_name'],
            );
        }

        return true;
    }

    /**
     * Save mvr data, parse and return parsed array
     * Used in: new-application
     */
    public function getClientData(
        string $pin,
        string $idCardNumber,
        string $source = 'api'
    ): array {
        try {

            $report = $this->addMvrReport($pin, $idCardNumber, $source);
            if (!empty($report->parsed_data)) {
                return json_decode($report->parsed_data, true);
            }

            return [];

        } catch (MvrException $mvrException) {

            $error = [];
            $error['error'] = $mvrException->getMessage();

            return $error;
        }
    }

    /**
     * Save Mvr report to db
     * Used in: client card - manual request, new-application page, api
     * @throws MvrException
     */
    public function addMvrReport(
        string $pin,
        string $idCardNumber,
        string $source = 'api',
        int $attempt = 1
    ): ?MvrReport {

        if ($pin == self::INCORRECT_PIN) {
            return null;
        }

        $report = $this->reUseIfAlredyExists($pin, $idCardNumber);
        if (!empty($report)) {
            return $report;
        }

        if ($this->isRateLimitationExceeded($pin)) {
            return null;
        }

        $res = $this->getMvrData($pin, $idCardNumber);
        if (empty($res)) {
            throw new MvrException(
                __('head::clientCard.MvrNoResponse'),
                self::CODE_NO_RESPONSE
            );
        }

        // save report in db
        $data = $res['data'];
        $execTime = !empty($res['execTime']) ? $res['execTime'] : null;
        $report = $this->mvrReportRepo->add($pin, $data, $execTime);
        if (empty($report->mvr_report_id)) {
            throw new MvrException('Can not save mvr report to our DB');
        }

        // parse and save results
        if (empty($report->parsed_data)) {
            // when we save a report in repo, we already take parsed_data in observer
            $parsedData = $this->getParsedData($report);
            $report->parsed_data = json_encode($parsedData);
        }
        $report->attempt = $attempt;
        $report->source = $source;
        $report->saveQuietly();

        return $report;
    }

    private function reUseIfAlredyExists(string $pin, string $idCardNumber): ?MvrReport
    {
        $today = Carbon::today();
        $goodReport = MvrReport::where('pin', $pin)
            ->where('data', 'like', '%"ReturnCode":"0000"%')
            ->where('parsed_data', '!=', null)
            ->whereDate('created_at', $today)
            ->orderBy('mvr_report_id', 'DESC')
            ->first();

        if (!empty($goodReport->mvr_report_id)) {

            $pd = json_decode($goodReport->parsed_data);
            if (
                !empty($pd?->client_idcard?->idcard_number)
                && ($pd->client_idcard->idcard_number) != trim($idCardNumber)
            ) {
                return null;
            }

            return $goodReport;
        }

        return null;
    }

    private function isRateLimitationExceeded(string $pin): bool
    {
        $thirtyMinutesAgo = Carbon::now()->subMinutes(30);
        $reports = MvrReport::where('pin', $pin)
            ->where('created_at', '>=', $thirtyMinutesAgo)
            ->get();

        if ($reports->count() >= 7) {
            return true;
        }

        return false;
    }

    public function getClientByPin(string $pin)
    {
        $client = Client::where('pin', $pin)->first();
        if (empty($client->client_id)) {
            return false;
        }

        return $client;
    }

    /**
     * Used in:
     * - here@addMvrReport()
     *
     * Parsing mvr reponse
     * @param MvrReport $report
     * @return array|MvrException
     */
    public function getParsedData(MvrReport $report)
    {
        try {
            if (
                empty($report)
                || empty($report->mvr_report_id)
                || empty($report->data)
            ) {
                throw new MvrException(__('head::clientCard.NoResponseFromService'));
            }

            $obj = json_decode($report->data);
            if (empty($obj->EGN) || empty($obj->IdentityDocumentNumber)) {
                throw new MvrException(__('head::clientCard.NoDataForClient'));
            }


            // start with main params
            $clientData = [
                'client_idcard' => [
                    'pin' => $obj->EGN,
                    'idcard_number' => $obj->IdentityDocumentNumber,
                ],
            ];


            // cyrilic names
            if (!empty($obj->PersonNames->FirstName)) {
                $clientData['client']['first_name'] = mb_convert_case($obj->PersonNames->FirstName, MB_CASE_TITLE);
            }
            if (!empty($obj->PersonNames->Surname)) {
                $clientData['client']['middle_name'] = mb_convert_case($obj->PersonNames->Surname, MB_CASE_TITLE);
            }
            if (!empty($obj->PersonNames->FamilyName)) {
                $clientData['client']['last_name'] = mb_convert_case($obj->PersonNames->FamilyName, MB_CASE_TITLE);
            }


            // latin names
            if (!empty($obj->PersonNames->FirstNameLatin)) {
                $clientData['client']['first_name_latin'] = mb_convert_case($obj->PersonNames->FirstNameLatin, MB_CASE_TITLE);
            }
            if (!empty($obj->PersonNames->SurnameLatin)) {
                $clientData['client']['middle_name_latin'] = mb_convert_case($obj->PersonNames->SurnameLatin, MB_CASE_TITLE);
            }
            if (!empty($obj->PersonNames->LastNameLatin)) {
                $clientData['client']['last_name_latin'] = mb_convert_case($obj->PersonNames->LastNameLatin, MB_CASE_TITLE);
            }


            // id card
            if (!empty($obj->IssueDate)) {
                $clientData['client_idcard']['issue_date'] = Carbon::parse($obj->IssueDate)->format('Y-m-d');
            }
            if (!empty($obj->ValidDate)) {
                $clientData['client_idcard']['valid_date'] = Carbon::parse($obj->ValidDate)->format('Y-m-d');
            }
            if (!empty($obj->IssuerName)) {
                $clientData['client_idcard']['issue_by'] = mb_convert_case($obj->IssuerName, MB_CASE_TITLE);

                // try to find city id
                $mvrCity = $clientData['client_idcard']['issue_by'];

                if (preg_match('/сдвр/iu', $mvrCity)) {
                    $mvrCity = 'София';
                } else {
                    $mvrCity = trim(preg_replace('/(мвр|рдвр|одмвр|община)/iu', '', $mvrCity));
                }

                $city = IdCardIssued::where('name', 'like', '%' . $mvrCity . '%')->first();
                if (!empty($city->idcard_issued_id)) {
                    $clientData['client_idcard']['issue_by_city_id'] = $city->idcard_issued_id;

                }
                if (!empty($city->name)) {
                    $clientData['client_idcard']['issue_by_city_name'] = $city->name;
                } else {
                    $clientData['client_idcard']['issue_by_city_name'] = $mvrCity;
                }
            }

            // gender
            if (!empty($obj->GenderNameLatin)) {
                $sex = strtolower($obj->GenderNameLatin);
                $clientData['client_idcard']['sex'] = ($sex == 'man' || $sex == 'male' ? 'male' : 'female');
            }

            // get city
            $city = $this->getCityFromRawData($obj);
            if (!empty($city->city_id)) {
                $clientData['client_idcard']['city_id'] = $city->city_id;
            } else {
                \Log::channel('lost_cities')->info(
                    '#' . $report->mvr_report_id
                    . ', settlement code:' . (!empty($obj?->PermanentAddress?->SettlementCode) ? $obj->PermanentAddress->SettlementCode : 'none')
                    . ', settlement name:' . (!empty($obj?->PermanentAddress?->SettlementName) ? $obj->PermanentAddress->SettlementName : 'none')
                );
            }
            if (!empty($city->name)) {
                $clientData['client_idcard']['city_name'] = $city->name;
            }


            // build address(es)
            $address = [];
            $clientAddress = [];
            if (!empty($city->city_id)) {
                $clientAddress['city_id'] = $city->city_id;
                $clientAddress['post_code'] = '';
            }
            if (!empty($obj->PermanentAddress->SettlementName)) {
                $address[] = $obj->PermanentAddress->SettlementName;
            }
            if (!empty($obj->PermanentAddress->MunicipalityName)) {
                // $address[] = 'общ.' . $obj->PermanentAddress->MunicipalityName;
                $clientAddress['municipality'] = $obj->PermanentAddress->MunicipalityName;
            }
            if (!empty($obj->PermanentAddress->DistrictName)) {
                // $address[] = $obj->PermanentAddress->DistrictName;
                $clientAddress['district'] = $obj->PermanentAddress->DistrictName;
            }
            if (!empty($obj->PermanentAddress->LocationName)) {
                $adrString = $obj->PermanentAddress->LocationName;
                $adrString = str_replace(".", ". ", $adrString);
                $adrString = mb_convert_case($adrString, MB_CASE_TITLE);
                $adrString = str_replace(". ", ".", $adrString);
                $address[] = $adrString;

                $clientAddress['location'] = $obj->PermanentAddress->LocationName;
            }
            if (!empty($obj->PermanentAddress->BuildingNumber)) {
                $address[] = 'бл.' . $obj->PermanentAddress->BuildingNumber;

                $clientAddress['building_number'] = $obj->PermanentAddress->BuildingNumber;
            }
            if (!empty($obj->PermanentAddress->Entrance)) {
                $address[] = 'вх.' . $obj->PermanentAddress->Entrance;

                $clientAddress['building_entrance'] = $obj->PermanentAddress->Entrance;
            }
            if (!empty($obj->PermanentAddress->Floor)) {
                $address[] = 'ет.' . $obj->PermanentAddress->Floor;

                $clientAddress['building_floor'] = $obj->PermanentAddress->Floor;
            }
            if (!empty($obj->PermanentAddress->Apartment)) {
                $address[] = 'ап.' . $obj->PermanentAddress->Apartment;

                $clientAddress['building_apartment'] = $obj->PermanentAddress?->Floor ?? '';
            }
            if (!empty($address)) {
                $clientData['client_idcard']['address'] = implode(', ', $address);

                $clientAddress['address'] = implode(', ', $address);
            }

            // add settlement and location codes
            if (!empty($obj->PermanentAddress->SettlementCode)) {
                $clientAddress['settlement_code'] = $obj->PermanentAddress->SettlementCode;
            }
            if (!empty($obj->PermanentAddress->LocationCode)) {
                $clientAddress['location_code'] = $obj->PermanentAddress->LocationCode;
            }

            $clientData['client_address'] = $clientAddress;



            // pictures (foto & sign)
            $client = $this->getClientByPin($clientData['client_idcard']['pin']);
            $clientId = !empty($client->client_id) ? $client->client_id : null;
            if (!empty($obj->Picture)) {
                $clientData['client_idcard']['image'] = $obj->Picture;

                $this->prepareClientPictureData(
                    $clientData['client_idcard']['image'],
                    $clientData['client_idcard']['pin'],
                    ClientPicture::SOURCE_MVR_PICT,
                    $clientId
                );
            }
            if (!empty($obj->IdentitySignature)) {
                $clientData['client_idcard']['image_signature'] = $obj->IdentitySignature;

                $this->prepareClientPictureData(
                    $clientData['client_idcard']['image_signature'],
                    $clientData['client_idcard']['pin'],
                    ClientPicture::SOURCE_MVR_SIGN,
                    $clientId
                );
            }

            return $clientData;

        } catch (MvrException $e) {
            Log::channel('mvrErrors')->error(
                'msg:' . $e->getMessage()
                . ', file:' . $e->getFile()
                . ', line:' . $e->getLine()
            );

            return [];
        }
    }

    public function getCityFromRawData($obj)
    {
        // 1. attempt to search city by SettlementCode
        if (!empty($obj?->PermanentAddress?->SettlementCode)) {
            $code = $obj->PermanentAddress->SettlementCode;

            $city = $this->cityRepo->getCityByCode($code);
            if (!empty($city?->city_id)) {
                return $city;
            }

            if (strlen($code) < 5) {
                $code = mvrCodeToOurDbCode($code);
                $city = $this->cityRepo->getCityByCode($code);
                if (!empty($city?->city_id)) {
                    return $city;
                }
            }
        }

        // 2. attempt to search city by settlement name
        if (!empty($obj?->PermanentAddress?->SettlementName)) {

            $possibilities = $this->extractCityNames($obj->PermanentAddress->SettlementName);
            if (!empty($possibilities)) {

                foreach ($possibilities as $possibleCityName) {
                    $cities = $this->cityRepo->getCityByName($possibleCityName);

                    // we use, when only one match by name!
                    if ($cities->count() == 1) {
                        return $cities->first();
                    }
                }

            }
        }

        return null;
    }

    public function extractCityNames(string $input): array
    {
        if (empty(trim($input))) {
            return [];
        }

        // 1. Get part before 'Общ.'
        $beforeObstina = explode('Общ.', $input)[0] ?? '';
        $full = trim($beforeObstina);

        // Normalize casing
        $normalized = mb_convert_case($full, MB_CASE_TITLE, 'UTF-8'); // e.g. Гр.Чирпан Дол

        // Detect prefix
        if (preg_match('/^(гр|с)\\.?\\s?(.*)$/iu', $normalized, $matches)) {
            $prefix = trim(mb_strtolower($matches[1])); // 'гр' or 'с'
            $name = trim($matches[2]);            // 'Чирпан Дол'

            return [
                "{$prefix}. {$name}",  // e.g. "гр. Чирпан Дол"
                "{$prefix}.{$name}",   // e.g. "гр.Чирпан Дол"
                $name,                 // e.g. "Чирпан Дол"
            ];
        }

        // fallback if no prefix match
        return [trim($normalized)];
    }

    public function prepareClientPictureData(
        string $picture,
        string $pin,
        string $source,
        ?int $clientId = null
    ): ?ClientPicture {

        if (!empty($pin) && !empty($picture)) {
            return $this->mvrReportRepo->addClientPicture([
                'pin' => $pin,
                'client_id' => $clientId,
                'type' => ClientPicture::TYPE_MVR,
                'base64' => $picture,
                'source' => $source,
                'last' => 1
            ]);
        }

        return null;
    }

    /**
     * Used in:
     * - MvrReportsController - manual report
     *
     * This is very important method.
     *
     * Since our process worsk this way:
     * - 1st we get MVR report by PIN (no client id)
     * - Then we create client and loan
     * - Then we make links between MVR data and client/loan
     * - And we also need to update client id for client picture
     *   created from Mvr report by Pin
     *
     * @param Client $client
     * @return bool
     */
    public function updateClientPicture(Client $client): bool
    {
        $clientPictures = $this->clientPictureRepository->getClientPicturesByPinWithoutClientId(
            $client->pin
        );

        if (empty($clientPictures)) {
            return true;
        }

        foreach ($clientPictures as $clientPicture) {
            $clientPicture->client_id = $client->client_id;
            $clientPicture->save();
        }

        return true;
    }

    /**
     * Get data from the mvr api
     *
     * @param string $pin
     * @param string $idCardNumber
     *
     * @return array
     */
    public function getMvrData(string $pin, string $idCardNumber): array
    {
        $data = '';

        try {
            $start = $this->timer();

            if (!isProdOrStage() && config('app.skip_mvr_for_dev')) {
                $data = DummyService::get('Mvr');
                $end = $this->timer();
                $execTime = $this->calculateExecTime($start, $end, 5);

                $data = json_decode($data, true);
                $data['EGN'] = $pin;
                $data['IdentityDocumentNumber'] = $idCardNumber;
                $data = json_encode($data);

                return [
                    'data' => $data,
                    'execTime' => $execTime,
                ];
            }

            $data = (new Mvr)->getReport($pin, $idCardNumber);
            $end = $this->timer();
            $execTime = $this->calculateExecTime($start, $end, 5);

        } catch (\Exception $e) {
            Log::channel('mvrErrors')->error(
                'pin:' . $pin .
                ', idcard:' . $idCardNumber .
                ', msg:' . $e->getMessage()
            );
        }

        $result = ['data' => $data];

        if (isset($execTime)) {
            $result['execTime'] = $execTime;
        }

        return $result;
    }

    // Used in:
    // - New application - manual loan creation for new clients, to attached already prepared mvr data
    public function updateRelations(Client $client, int $loanId): bool
    {
        try{
            $report = MvrReport::where('pin', $client->pin)
                ->where('last', 1)
                ->whereNotExists(function ($query) {
                    $query->selectRaw(1)
                    ->from('mvr_report_pivot')
                    ->whereRaw('mvr_report_pivot.mvr_report_id = mvr_report.mvr_report_id');
                })
                ->first();

            if (!empty($report->mvr_report_id)) {

                $mvrParsedData = json_decode($report->parsed_data ?? '[]', true);

                $this->linkReport(
                    $report->mvr_report_id,
                    $client->client_id,
                    $loanId,
                    $mvrParsedData
                );
            }

            $this->updateClientPicture($client);

            return true;

        } catch (\Throwable $e) {

            $msg = 'Error MvrService@updateRelations(): ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();
            \Log::debug($msg);

            return false;
        }
    }

    /**
     * Add relation to report for client and loan - link mvr_report with loan
     */
    public function linkReport(
        int $mvrReportId,
        int $clientId,
        int $loanId,
        array $parsedData = []
    ): MvrReportPivot {

        return $this->mvrReportRepo->addPivot(
            $mvrReportId,
            $clientId,
            $loanId,
            $parsedData
        );
    }

    /**
     * Returns all clients mvr reports
     */
    public function getAllByPin(
        string $pin,
        array  $order = []
    ): Collection {
        return $this->mvrReportRepo->getAllByPin($pin, $order);
    }

    public function getLastReportByPin(
        string $pin
    ): ?MvrReport {
        return $this->mvrReportRepo->get($pin);
    }
}
