<?php

namespace Modules\ThirdParty\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\A4EReport;
use Modules\Common\Models\CcrReport;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Traits\PinTrait;
use Modules\ThirdParty\Libraries\A4E;
use Modules\ThirdParty\Repositories\A4EReportRepository;
use Modules\ThirdParty\Traits\ReportTrait;
use Throwable;

class A4EService
{
    use PinTrait, ReportTrait;

    // Existing of this key/value in 3 subkeys:  "applicantData", "noi2" & "ccr"
    // Make them fake for A4E and this way we simulate stage env,
    // And in fact using their prod with additional keys
    const STAGE_KEY = 'tc_commentary';
    const STAGE_VALUE = 'This is a test request from stikcredit(stage). Please ignore.';

    private $a4EReportRepository;

    private $ccrReportId = null;
    private $noi2ReportId = null;
    private $noi7ReportId = null;
    private $noi51ReportId = null;

    public function getScoringForLoan(
        Loan $loan,
        array $a4eData = [],
        string $source = 'job',
        ?int $attempt = 1
    ): ?A4EReport {

        try {

            $start = $this->timer();
            $addionalProperties = [];

            if (!isProdOrStage()) {
                $a4eData = [];
                $a4eResponse = json_decode(DummyService::get('A4E'));
            } else {

                if (empty($a4eData)) {
                    $a4eData = $this->getPreparedDataByLoan($loan);
                }

                $a4eResponse = (new A4E)->getScoreResponse($a4eData);
            }

            $gbr = $a4eResponse->content->scores[0]->ScorecardGBR_newClient->recommendation ?? '';
            if (!empty($gbr)) {
                $addionalProperties['gbr'] = $this->getCardKey($gbr);
            }
            $gbr2 = $a4eResponse->content->scores[0]->ScorecardGBR_oldClient->recommendation ?? '';
            if (empty($gbr) && !empty($gbr2)) {
                $addionalProperties['gbr'] = $this->getCardKey($gbr2);
            }
            $gb = $a4eResponse->content->scores[0]->ScorecardGB_newClient->recommendation ?? '';
            if (!empty($gb)) {
                $addionalProperties['gb'] = $this->getCardKey($gb);
            }
            $gb2 = $a4eResponse->content->scores[0]->ScorecardGB_oldClient->recommendation ?? '';
            if (empty($gb) && !empty($gb2)) {
                $addionalProperties['gb'] = $this->getCardKey($gb2);
            }

            $execTime = $this->calculateExecTime($start, $this->timer(), 5);

            $a4Rep =  $this->getA4ERepo()->addReportForLoan(
                $loan,
                json_encode($a4eResponse),
                $addionalProperties,
                $execTime,
                json_encode($a4eData)
            );

            $a4Rep->source = $source;
            $a4Rep->attempt = $attempt;
            $a4Rep->ccr_report_id = $this->ccrReportId;
            $a4Rep->noi2_report_id = $this->noi2ReportId;
            $a4Rep->noi7_report_id = $this->noi7ReportId;
            $a4Rep->noi51_report_id = $this->noi51ReportId;
            $a4Rep->save();

            return $a4Rep;

        } catch (Throwable $e) {

            Log::channel('a4eError')->error(
                'loan #' . $loan->getKey()
                . ', error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine()
            );
        }

        return null;
    }

    public function useOldReportFromProvision($loan, $a4eRequest, $a4eResponse, $createdAt = null): bool
    {
        $a4eResponseRaw = $a4eResponse;
        $a4eResponse = !empty($a4eResponse) ? json_decode($a4eResponse) : $a4eResponse;

        $addionalProperties = [];
        $gbr = $a4eResponse->content->scores[0]->ScorecardGBR_newClient->recommendation ?? '';
        if (!empty($gbr)) {
            $addionalProperties['gbr'] = $this->getCardKey($gbr);
        }
        $gbr2 = $a4eResponse->content->scores[0]->ScorecardGBR_oldClient->recommendation ?? '';
        if (empty($gbr) && !empty($gbr2)) {
            $addionalProperties['gbr'] = $this->getCardKey($gbr2);
        }
        $gb = $a4eResponse->content->scores[0]->ScorecardGB_newClient->recommendation ?? '';
        if (!empty($gb)) {
            $addionalProperties['gb'] = $this->getCardKey($gb);
        }
        $gb2 = $a4eResponse->content->scores[0]->ScorecardGB_oldClient->recommendation ?? '';
        if (empty($gb) && !empty($gb2)) {
            $addionalProperties['gb'] = $this->getCardKey($gb2);
        }

        // logic for old scoring format
        if (empty($addionalProperties)) {
            $ggg = $a4eResponse->content->scores[0]->Scorecard1->recommendation ?? '';
            if (!empty($ggg)) {
                $addionalProperties['gbr'] = $this->getCardKey($ggg);
            }
        }

        $report = $this->getA4ERepo()->addReportForLoan(
            $loan,
            $a4eResponseRaw,
            $addionalProperties,
            1,
            $a4eRequest,
            $createdAt
        );

        return true;
    }

    /**
     * Used in client card
     *
     * @param  array  $conditions
     * @param  array  $order
     * @return Collection
     */
    public function getReportsByCriteria(
        array $conditions = [],
        array $order = ['created_at' => 'DESC']
    ): Collection {
        return $this->getA4ERepo()->getReportsByCriteria($conditions, $order);
    }

    /**
     * return array in prepared format data for A4E
     * @param  Loan   $loan
     * @return array
     */
    public function getPreparedDataByLoan(Loan $loan): array
    {
        $client = $loan->client;
        $ccrReport = $client->getLastCcrReport();
        $noiReports = $client->getLastNoiReports(['noi2', 'noi51']);

        return $this->getPreparedData(
            $loan,
            $client,
            $ccrReport,
            $noiReports
        );
    }

    public function getPreparedData(
        Loan $loan,
        Client $client,
        ?CcrReport $ccrReport = null,
        array $noiReports = []
    ): array {

        $ccrReportId = null;
        $noi2ReportId = null;
        $noi7ReportId = null;
        $noi51ReportId = null;

        $pinInfo = $this->getAgeAndSex($client->pin);
        $currentAddress = $client->clientLastAddressIdcard();
        $cityObj = $currentAddress->city;
        $address = $currentAddress->address;
        $postCode = $cityObj->code;

        $city = $cityObj->name;
        if (!empty($currentAddress->municipality)) {
            $city .= ', общ. ' . $currentAddress->municipality;
        }
        if (!empty($currentAddress->district)) {
            $city .= ', обл. ' . $currentAddress->district;
        }


        $firstInstallment = $loan->orderedInstallments()->first();


        $data = [
            'records' => [
                [
                    'applicantData' => [
                        'credit_id' => (string) $loan->loan_id,
                        'created_at' => Carbon::parse($loan->created_at)->toDateTimeString(),
                        'sex_index' => (int) $pinInfo['sex'],
                        'age' => (int) $pinInfo['age'],
                        'region_text' => (string) $pinInfo['region'],
                        'credit_type' => (string) $loan->getMappedProductForA4E(),
                        'price' => (float) intToFloat($loan->amount_approved),
                        'price_old' => (float) intToFloat($loan->amount_requested),
                        'days' => (int) $loan->period_approved,
                        'days_old' => (int) $loan->period_requested,
                        'type_payment' => strtolower($loan->getPaymentMethod()),
                        'referrer' => 'google',
                        'input_channel' => (string) $loan->getSource(),
                        'user_id' => (string) $loan->client_id,

                        'installment' => (double) $firstInstallment->total_amount,
                        'address' => [
                            'city' => $city,
                            'address' => $address,
                            'post_code' => $postCode
                        ]
                    ],
                ],
            ],
            'scoreCards' => ['Scorecard1'],
        ];


        $loanIds = $loan->getPreviousLoanIds();
        if (!empty($loanIds)) {
            $data['records'][0]['applicantData']['history'] = $loanIds;
        }


        // noi2, noi51
        $validNoiReportTypes = [];
        if (!empty($noiReports)) {

            /** @var \Modules\Common\Models\NoiReport $noiReport **/
            foreach ($noiReports as $noiReport) {

                if (empty($noiReport->noi_report_id)) {
                    Log::channel('a4eError')->error('-- SKIPPED NOI (bad noi report), pin: ' . $client->pin);
                    continue;
                }

                if (!$noiReport->isValid()) {
                    Log::channel('a4eError')->error('-- SKIPPED NOI (!isValid) report #' . $noiReport->noi_report_id);
                    continue;
                }

                if (!empty($noiReport->name) && $noiReport->name == 'noi2') { $noi2ReportId = $noiReport->noi_report_id; }
                if (!empty($noiReport->name) && $noiReport->name == 'noi7') { $noi7ReportId = $noiReport->noi_report_id; }
                if (!empty($noiReport->name) && $noiReport->name == 'noi51') { $noi51ReportId = $noiReport->noi_report_id; }

                $noiData = json_decode(
                    str_ireplace(
                        $client->pin,
                        '',
                        $noiReport->data
                    )
                );

                $noiType = $noiReport->name;
                $validNoiReportTypes[$noiType] = $noiType;

                if (!empty($noiData->start)) { // come directly from noi
                    $data['records'][0][$noiType]['response'] = $noiData->start;
                    $data['records'][0][$noiType]['request_date'] = Carbon::parse($noiReport->created_at)->toDateString();
                } elseif (!empty($noiData->reports->{$noiType}->start)) { // come from DateTechnolgy
                    $data['records'][0][$noiType]['response'] = $noiData->reports->{$noiType}->start;
                    $data['records'][0][$noiType]['request_date'] = Carbon::parse($noiReport->created_at)->toDateString();
                }
            }
        }


        // ccr
        if (!empty($ccrReport->ccr_report_id)) {

            $ccrReportId = $ccrReport->ccr_report_id;

            $ccrData = json_decode(
                str_ireplace(
                    $ccrReport->pin,
                    '',
                    $ccrReport->data
                ),
                true
            );


            //////////////////////// SPECIAL FORMATING /////////////////////////
            if (
                isset($ccrData['section'])
                && !isset($ccrData['section'][0])
            ) {
                $row = $ccrData['section'];
                $ccrData['section'] = [];
                $ccrData['section'][0] = $row;
            }

            if (!empty($ccrData['section'] )) {
                foreach ($ccrData['section'] as $k => $section) {
                    foreach ($section as $sectionKey => $sectionVal) {
                        if (empty($sectionVal)) {
                            $ccrData['section'][$k][$sectionKey] = null;
                        }
                    }
                }
            }
            ////////////////////////////////////////////////////////////////////


            $data['records'][0]['ccr']['response'] = $ccrData;
            $data['records'][0]['ccr']['request_date'] = Carbon::parse($ccrReport->created_at)->toDateString();
            $data['records'][0]['applicantData']['ccr'] = (double) $ccrReport->total_percent;
        }


        // A4E Nikolay comment:
        // stage средата ни е спряна за неопределен период. За тестване на новата интеграция, можете да ползвате production средата, като:
        // пускате тестова заявка, в JSON payload-а на заявката, на нивото на "applicantData", "noi2" и "ccr", добавяте нова променлива, "tc_commentary", със стрингова стойност - кратък текст на тестовата дейност която правите.
        // Roman:
        // Ще попълваме "tc_commentary" със следния текст: "This is a test request from stikcredit(stage). Please ignore."

        if (!isProd()) {
            if (isset($data['records'][0]['applicantData'])) {
                $data['records'][0]['applicantData'][self::STAGE_KEY] = self::STAGE_VALUE;
            }
            if (isset($data['records'][0]['ccr'])) {
                $data['records'][0]['ccr'][self::STAGE_KEY] = self::STAGE_VALUE;
            }
            if (!empty($validNoiReportTypes)) {
                foreach ($validNoiReportTypes as $vrK => $vrV) {
                    if (isset($data['records'][0][$vrK])) {
                        $data['records'][0][$vrK][self::STAGE_KEY] = self::STAGE_VALUE;
                    }
                }
            }
        }


        $this->setReportsIds($ccrReportId, $noi2ReportId, $noi7ReportId, $noi51ReportId);


        return $data;
    }

    /**
     * @return A4EReportRepository|null
     */
    private function getA4ERepo(): ?A4EReportRepository
    {
        if (null === $this->a4EReportRepository) {
            $this->a4EReportRepository = new A4EReportRepository();
        }

        return $this->a4EReportRepository;
    }

    private function getCardKey(string $text): string
    {
        if (preg_match("/(low|mid|high)(\_|\s)?(risk)/i", $text, $m)) {
            return strtolower($m[1]) . '_' . strtolower($m[3]);
        }

        return strtolower($text);
    }

    private function setReportsIds(
        ?int $ccrReportId = null,
        ?int $noi2ReportId = null,
        ?int $noi7ReportId = null,
        ?int $noi51ReportId = null,
    ): void {

        $this->nullateReportsIds();

        $this->ccrReportId = $ccrReportId;
        $this->noi2ReportId = $noi2ReportId;
        $this->noi7ReportId = $noi7ReportId;
        $this->noi51ReportId = $noi51ReportId;
    }

    private function nullateReportsIds(): void
    {
        $this->ccrReportId = null;
        $this->noi2ReportId = null;
        $this->noi7ReportId = null;
        $this->noi51ReportId = null;
    }
}
