<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Services;

use RuntimeException;
use Modules\ThirdParty\Libraries\InsuranceClient;

class InsuranceService
{
    public function __construct(private InsuranceClient $client) {}

    /**
     * Create insurance offer (preview or create).
     *
     * Minimal payload keys (adapt to WSDL): insAmount, insDuration, insDurationType,
     * parameters (JSON string or array), brand, operationType ('preview'|'create'), insType
     *
     * @param array $payload
     * @return array parsed response
     */
    public function createInsuranceOffer(array $payload): array
    {
        // Normalize parameters per WSDL expectations
        $payload = $this->normalizeOfferPayload($payload);

        $resp = $this->client->createInsuranceOffer($payload);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('createInsuranceOffer failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /**
     * Updates offer details by id
     * @return array parsed response
     */
    public function updateInsuranceOffer(string $offerUuid, array $payload): array
    {
        $payload['offerUUID'] = $offerUuid;
        $payload = $this->normalizeOfferPayload($payload);

        $resp = $this->client->updateInsuranceOffer($offerUuid, $payload);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('updateInsuranceOffer failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /**
     * Create certificate from offer.
     *
     * Required: offerUUID, insuredNames (array of 3 strings), insuredPersonalIdn (EGN or LNCh),
     * optionally contact info, addresses etc. See WSDL docs.
     */
    public function createCertificate(string $offerUuid, array $insuredData): array
    {
        $params = ['offerUUID' => $offerUuid] + $insuredData;
        $this->validateInsuredData($params);

        $resp = $this->client->createCertificate($offerUuid, $params);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('createCertificate failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /**
     * Retrieve PDF binary for given certificate id.
     * Returns raw PDF binary string.
     */
    public function getCertificatePdf(int $certificateId): string
    {
        $pdf = $this->client->getCertificatePdf($certificateId);

        if (empty($pdf)) {
            throw new RuntimeException('Empty PDF returned for certificateId='.$certificateId);
        }

        return $pdf;
    }

    public function getInsuranceDocuments(): array
    {
        return $this->client->getInsuranceDocuments();
    }

    public function invalidateCertificate(int $certificateId): array
    {
        $resp = $this->client->invalidateCertificate($certificateId);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('invalidateCertificate failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /* ---------- helpers ---------- */

    private function normalizeOfferPayload(array $payload): array
    {
    	$this->validateOfferdData($payload);

        // If parameters passed as array, convert to JSON string if WS expects JSON string
        if (isset($payload['parameters']) && is_array($payload['parameters'])) {
            $payload['parameters'] = json_encode($payload['parameters'], JSON_UNESCAPED_UNICODE);
        }

        return $payload;
    }

    private function validateOfferdData(array $params): void
    {
        if (empty($params['insDurationType'])) {
            throw new RuntimeException('insDurationType is required');
        }

        if (empty($params['operationType'])) {
            throw new RuntimeException('operationType is required');
        }

        if (empty($params['insType'])) {
            throw new RuntimeException('insType is required');
        }
    }

    private function validateInsuredData(array $params): void
    {
        // Quick checks: insuredNames as array of 3 non-empty strings; insuredPersonalIdn present
        if (empty($params['insuredNames']) || !is_array($params['insuredNames']) || count($params['insuredNames']) < 1) {
            throw new RuntimeException('insuredNames is required and must be array of names');
        }
        if (empty($params['insuredPersonalIdn'])) {
            throw new RuntimeException('insuredPersonalIdn is required');
        }
    }

    /**
     * Try to extract numeric result code from response structure.
     * Different WS implementations use different fields: resultCode, result, responseCode etc.
     */
    private function extractResultCode($resp): int
    {
    	// different api calls, have different response format
        if (is_array($resp)) {
            if (isset($resp['resultCode'])) {
                return (int) $resp['resultCode'];
            }
            if (isset($resp['result'])) {
                return (int) $resp['result'];
            }
            if (isset($resp['responseCode'])) {
                return (int) $resp['responseCode'];
            }
        }

        // fallback to success
        return 0;
    }
}
