<?php

namespace Modules\ThirdParty\Services;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Noi2Stats;
use Modules\Common\Models\NoiReport;
use Modules\Common\Models\NoiReportPivot;
use Modules\ThirdParty\Libraries\Nsi;
use Modules\ThirdParty\ReportParsers\Noi2ReportParser;
use Modules\ThirdParty\ReportParsers\Noi51ReportParser;
use Modules\ThirdParty\ReportParsers\Noi7ReportParser;
use Modules\ThirdParty\Repositories\NoiReportRepository;
use Modules\ThirdParty\Services\NoiStatsService;
use Modules\ThirdParty\Traits\ReportTrait;

/**
 * НОЙ сървис
 * Може да тегли отчети от НСИ или от Date Technology
 */
class NoiService
{
    use ReportTrait;

    private $nsi;
    private $noiRepo;
    private Noi51ReportParser $noi51ReportParser;
    private Noi2ReportParser $noi2ReportParser;
    private Noi7ReportParser $noi7ReportParser;

    public function __construct(private readonly NoiReportRepository $noiReportRepository = new NoiReportRepository)
    {
        $this->noi51ReportParser = new Noi51ReportParser();
        $this->noi2ReportParser = new Noi2ReportParser($this);
        $this->noi7ReportParser = new Noi7ReportParser();
    }

    public function addReport(string $pin, int $reportCode): ?NoiReport
    {
        $response = $this->getNsiReport($pin, $reportCode);

        if (empty($response['data'])) {
            return null;
        }

        $data = [
            'pin' => $pin,
            'reportName' => $this->getReportName($reportCode),
            'report' => $response['data'],
            'execTime' => $response['execTime'],
        ];

        return $this->saveReport($data);
    }

    public function saveReport(array $repotData)
    {
        return $this->getNoiRepo()->addReport($repotData);
    }

    public function useOldReportFromProvision($pin, $loan, $noiReportName, $noiReportResponse, $createdAt = null): bool
    {
        $data = [
            'pin' => $pin,
            'reportName' => $noiReportName,
            'report' => $noiReportResponse,
            'execTime' => 1,
        ];
        if (!empty($createdAt)) {
            $data['created_at'] = $createdAt;
        }
        $report = $this->saveReport($data);

        $this->linkReport(
            $report->noi_report_id,
            $loan->client_id,
            $loan->loan_id,
            $noiReportName
        );

        return true;
    }

    public function getNsiReport(string $pin, int $reportCode): array
    {
        $start = $this->timer();

        try {
            if (!isProdOrStage()) {
                $data = DummyService::get(
                    'Nsi/' . $this->getReportName($reportCode)
                );
                $end = $this->timer();
                $execTime = $this->calculateExecTime($start, $end, 5);

                return [
                    'data' => $data,
                    'execTime' => $execTime
                ];
            }

            $report = $this->getNsi()->getReport($pin, $reportCode);
            $execTime = $this->calculateExecTime($start, $this->timer(), 5);
        } catch (Exception $e) {
            Log::channel('noiError')->info(
                'pin:' . $pin
                . ', report: noi' . $reportCode
                . ', error:' . $e->getMessage()
            );

            $report = [];
            $execTime = $this->calculateExecTime($start, $this->timer(), 5);
        }

        return [
            'data' => $report,
            'execTime' => $execTime
        ];
    }

    public function getReport(string $pin, int $reportCode): ?NoiReport
    {
        return $this->getNoiRepo()->get(
            $pin,
            $this->getReportName($reportCode)
        );
    }

    public function getReportName(int $reportCode): string
    {
        return Nsi::PREFIX . $reportCode;
    }

    /**
     * Add relation to report for client and loan
     *
     * @param int $noiReportId
     * @param int $clientId
     * @param int $loanId
     * @param string $reportName
     *
     * @return NoiReportPivot
     */
    public function linkReport(
        int $noiReportId,
        int $clientId,
        int $loanId,
        string $reportName
    ): NoiReportPivot {
        return $this->getNoiRepo()->addPivot(
            $noiReportId,
            $clientId,
            $loanId,
            $reportName
        );
    }

    public function getAllByPin(
        string $pin,
        array $order = ['created_at' => 'DESC'],
        array $moreConditions = []
    ): Collection {
        return $this->getNoiRepo()->getAllByPin(
            $pin,
            $moreConditions,
            $order
        );
    }

    public function getAllForLoan(
        int $loanId,
        int $clientId
    ): Collection {
        return $this->getNoiRepo()->getAllForLoan(
            $loanId,
            $clientId
        );
    }

    public function parseReport(NoiReport $report): ?NoiReport
    {
        return match ($report->name) {
            'noi2' => $this->parseReportNoi2($report),
            'noi7' => $this->parseReportNoi7($report),
            'noi51' => $this->parseReportNoi51($report),
            default => null
        };
    }

    public function parseReportNoi2(NoiReport $report): ?NoiReport
    {
        return $this->noi2ReportParser->run($report);
    }

    public function parseReportNoi51(NoiReport $report): ?NoiReport
    {
        return $this->noi51ReportParser->run($report);
    }

    public function parseReportNoi7(NoiReport $report): ?NoiReport
    {
        return $this->noi7ReportParser->run($report);
    }

    /**
     * @param mixed $employersData
     * @param array $parsedData
     * @return array
     */
    public function addEmployersFirst(mixed $employersData, array $parsedData): array
    {
        $employersPins = [];
        if (empty($employersData)) {
            return array($employersPins, $parsedData);
        }

        $key = 0;

        if (!empty($employersData['Bulstat'])) {
            $emp = [
                'pin' => trim($employersData['Bulstat']),
                'name' => !empty($employersData['Name']) ? $employersData['Name'] : '',
                'address' => !empty($employersData['address']) ? $employersData['address'] : '',
            ];

            $parsedData['data'][$key] = [
                'employer' => $emp,
                'stats' => [],
            ];

            $employersPins[$emp['pin']] = $key;
        } else {
            foreach ($employersData as $employerData) {
                if (empty($employerData['Bulstat'])) {
                    continue;
                }

                $emp = [
                    'pin' => trim($employerData['Bulstat']),
                    'name' => !empty($employerData['Name']) ? $employerData['Name'] : '',
                    'address' => !empty($employerData['address']) ? $employerData['address'] : '',
                ];

                $parsedData['data'][$key] = [
                    'employer' => $emp,
                    'stats' => [],
                ];

                $employersPins[$emp['pin']] = $key;

                $key++;
            }
        }
        return array($employersPins, $parsedData);
    }

    /**
     * @param array $employersPins
     * @param mixed $clientData
     * @param array $parsedData
     * @return array
     */
    public function addEmployersPins(array $employersPins, mixed $clientData, array $parsedData): array
    {
        if (empty($employersPins)) {
            return $parsedData;
        }

        if (isset($clientData['Bulstat'])) {

            foreach ($employersPins as $empPin => $empKey) {
                if ($empPin == trim($clientData['Bulstat'])) {
                    $parsedData['data'][$empKey]['stats'][] = [
                        'year' => $clientData['Year'],
                        'month' => $clientData['Month'],
                        'salary' => $clientData['Salary'],
                        'worked_days' => $clientData['Workdays'],
                        'declaration_date' => Carbon::parse(str_replace('/', '.', $clientData['inputdate']))->format(
                            'd.m.Y'
                        ),
                    ];
                }
            }

        } else {
            // i dont know what is this shit
            // but keep for happy long life
            foreach ($employersPins as $empPin => $empKey) {
                foreach ($clientData as $row) {
                    if ($empPin == trim($row['Bulstat'])) {
                        $parsedData['data'][$empKey]['stats'][] = [
                            'year' => $row['Year'],
                            'month' => $row['Month'],
                            'salary' => $row['Salary'],
                            'worked_days' => $row['Workdays'],
                            'declaration_date' => Carbon::parse(str_replace('/', '.', $row['inputdate']))->format(
                                'd.m.Y'
                            ),
                        ];
                    }
                }
            }
        }

        foreach (array_keys($parsedData['data']) as $k) {
            $parsedData['data'][$k]['stats'] = array_reverse($parsedData['data'][$k]['stats']);
        }

        return $parsedData;
    }

    public function saveNoiStats(NoiReport $report, int $clientId, int $loanId): ?Noi2Stats
    {
        // for now, only for noi2
        if (empty($report->name) || $report->name != NoiReport::REPORT_NAME_SHORT) {
            return null;
        }

        try {
            $data = array_merge([
                'created_at' => now(),
                'created_by' => getAdminId(),
                'noi_report_id' => $report->noi_report_id,
                'client_id' => $clientId,
                'loan_id' => $loanId,
            ], app(NoiStatsService::class)->extractnNoi2Vars($report));

            $stats = new Noi2Stats();
            $stats->fill($data);
            $stats->save();

            return $stats;

        } catch (\Throwable $e) {
            \Log::error(
                'Error saveNoiStats(' . ($report->noi_report_id ?? '') . '): '
                . $e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine()
            );

            return null;
        }
    }

    ////////////////////////////////////////////////////////////////////////////

    private function getNsi(): Nsi
    {
        if (null === $this->nsi) {
            $this->nsi = new Nsi();
        }

        return $this->nsi;
    }

    private function getNoiRepo(): NoiReportRepository
    {
        if (null === $this->noiRepo) {
            $this->noiRepo = new NoiReportRepository();
        }

        return $this->noiRepo;
    }
}
