<?php

namespace Modules\ThirdParty\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Common\Repositories\EasypayRequestRepo;

class CurlEasyPayService
{
    private $url = null;
    private $token = null;

    public function __construct()
    {
        if (app()->runningUnitTests() || app()->runningInConsole()) {
            return;
        }

        $env = app()->environment();
        $this->url = config("easypay.{$env}.EASYPAY_MS_URL");
        $this->token = config("easypay.{$env}.EASYPAY_MS_TOKEN");

        if (isProdOrStage() && empty($this->url)) {
            throw new \Exception('Invalid easy pay api endpoint');
        }

        if (isProdOrStage() && empty($this->token)) {
            throw new \Exception('Invalid authorization api key');
        }
    }

    private function execCurl(string $route, string $method, array $data): array
    {
        if (!$this->url) {
            $env = app()->environment();
            $this->url = config("easypay.{$env}.EASYPAY_MS_URL");
            $this->token = config("easypay.{$env}.EASYPAY_MS_TOKEN");
        }

        try {
            $request = Http::baseUrl($this->url)
                ->asForm()
                ->withHeaders(['authorization' => $this->token]);

            if ($method === 'POST') {
                $response = $request->post($route, $data);
            } else {
                $response = $request->get($route, $data);
            }

            return $response?->json() ?? [];

        } catch (\Exception $exception) {
            Log::channel('easypay')->debug('execCurl(): ' . $exception->getMessage());

            return [];
        }
    }

    public function sendMoney(
        Client  $client = null,
        Loan    $loan = null,
        Payment $payment = null,
        int     $amount = 0,
        ?int    $refinancedId = null
    ): array {

        $shouldSend = (int) config('company.easy_pay_auto_send_money');
        if (1 !== $shouldSend) {
            return [];
        }

        $data = [
            'admin_id' => getAdminId(),
            'client' => [
                'id' => $client->getKey(),
                'pin' => $client->pin,
                'names' => $client->getFullName(),
            ],
            'loan_id' => $loan->getKey(),
            'refinance_loan_id' => $refinancedId,
            'payment_id' => $payment->getKey(),
            'amount' => intToFloat($amount),
        ];

        return $this->execCurl('send-money', 'POST', $data);
    }

    public function refund(Payment $payment): array
    {
        $client = $payment->client;
        $loan = $payment->loan;

        $data = [
            'admin_id' => getAdminId(),
            'client' => [
                'id' => $client->getKey(),
                'pin' => $client->pin,
                'names' => $client->getFullName(),
            ],
            'loan_id' => $loan->getKey(),
            'refinance_loan_id' => $loan->getRefinancedId(),
            'payment_id' => $payment->getKey(),
            'amount' => intToFloat($payment->amount),
        ];

        return $this->execCurl('refund', 'POST', $data);
    }

    public function refundState(Payment $payment): array
    {
        $client = $payment->client;
        $loan = $payment->loan;

        $data = [
            'admin_id' => getAdminId(),
            'client' => [
                'id' => $client->getKey(),
                'pin' => $client->pin,
                'names' => $client->getFullName(),
            ],
            'loan_id' => $loan->getKey(),
            'refinance_loan_id' => $loan->getRefinancedId(),
            'payment_id' => $payment->getKey(),
            'amount' => intToFloat($payment->amount),
        ];

        return $this->execCurl('refund-state', 'POST', $data);
    }

    public function notification(string $encoded, string $checksum): array
    {
        $data = [
            'encoded' => $encoded,
            'checksum' => $checksum,
        ];

        return $this->execCurl('notification', 'POST', $data);
    }

    /**
     * @deprecated
     * used in deprecated logic
     */
    public function hasClientReceivedPayment(Payment $payment = null): bool
    {
        return false;
    }
}
