<?php

namespace Modules\ThirdParty\Services;

use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Client;
use Modules\Common\Models\DateTechnologyReport;
use Modules\Common\Models\DateTechnologyReportPivot;
use Modules\Common\Models\Loan;
use Modules\Common\Models\NoiReport;
use Modules\ThirdParty\Libraries\DateTechnology;
use Modules\ThirdParty\Repositories\DateTechnologyReportRepository;
use Modules\ThirdParty\Traits\ReportTrait;

class DateTechnologyService
{
    use ReportTrait;

    private $dt;
    private $dtRepo;
    private $noiService;

    /**
     * Get report from DT and save in DB
     * Parse report
     * Create duplicate in noi_reports
     * Create relations to loan
     */
    public function addReportAndRelationsForLoan(
        Client $client,
        string $reportName,
        string $source = 'job',
        ?Loan $loan = null,
        ?int $attempt = 1
    ): ?NoiReport {

        // mandatory checks
        if (empty($client->pin) || empty($client->idcard_number)) {
            Log::channel('dtError')->error('No client for loan #' . $loan->loan_id);
            return null;
        }
        if (empty($reportName)) {
            Log::channel('dtError')->error('Loan #' . $loan->loan_id . ' without provided reportName');
            return null;
        }
        // optional check
        if (!is_null($loan) || $source == 'job') {
            if (empty($loan->loan_id) || empty($loan->client_id)) {
                Log::channel('dtError')->error('Loan without id provided');

                return null;
            }
        }


        if ($this->isRateLimitationExceeded($client->pin, $reportName)) {
            Log::channel('dtError')->error('Rate limit skip for client: ' . $client->client_id . ', loan: ' . (!empty($loan->loan_id) ? $loan->loan_id : ''));
            return null;
        }


        // main params
        $pin = $client->pin;
        $idCardNum = $client->idcard_number;


        // get report and save into db
        $dtReport = $this->addReport(
            $pin,
            $idCardNum,
            $reportName
        );
        if (empty($dtReport->date_technology_report_id)) {
            Log::channel('dtError')->error(
                'Failed to save report: ' . $reportName
                . ' for loan #' . $loan->loan_id
            );
            return null;
        }
        $dtReport->source = $source;
        $dtReport->attempt = $attempt;
        $dtReport->save();

        // add dt relation to loan
        if ($loan) {
            $this->linkReport(
                $dtReport->date_technology_report_id,
                $loan->client_id,
                $loan->loan_id,
                $reportName
            );
        }


        // create noi report from dt report and parse it
        $noiReport = $this->distributeReport($dtReport);
        if (empty($noiReport->noi_report_id)) {
            Log::channel('dtError')->error(
                'Failed to distribute report: ' . $reportName
                . ', for loan #' . $loan->loan_id
                . ', from dt report #' . $dtReport->date_technology_report_id
            );
            return null;
        }
        $noiReport->source = $source;
        $noiReport->attempt = $attempt;
        $dtReport->save();

        // add noi relation to loan
        if ($loan) {
            $this->getNoiService()->linkReport(
                $noiReport->noi_report_id,
                $loan->client_id,
                $loan->loan_id,
                $reportName
            );

            $this->getNoiService()->saveNoiStats(
                $noiReport,
                $client->client_id,
                $loan->loan_id,
            );
        }


        return $noiReport;
    }

    /**
     * TODO LION: Used in test, should be deleted and used normal logic
     *
     * @param string $pin
     * @param string $idCardNum
     * @param array $reports - 'noi2', 'noi7', 'noi51', 'mvr'
     *
     * @return array of DateTechnologyReport objects
     */
    public function addReports(
        string $pin,
        string $idCardNum,
        array $reports
    ): array {

        if (empty($reports)) {
            return [];
        }

        // leave only allowed reports
        $allowedReports = DateTechnology::getAllowedReports();
        $reports = array_intersect($allowedReports, $reports);

        $start = $this->timer();
        $reportsResult = $this->getDateTechnologyReportsExtended(
            $pin,
            $idCardNum,
            $reports
        );
        $end = $this->timer();
        $execTime = $this->calculateExecTime($start, $end, 5);

        $reportsRawData = $reportsResult['raw'];
        $reportsData = $reportsResult['decoded'];
        if (empty($reportsData) || empty($reportsData['reports'])) {
            return [];
        }

        $data = [];
        foreach ($reportsData['reports'] as $reportName => $reportData) {
            $data[$reportName] = $this->getDTRepo()->addReport(
                $pin,
                $idCardNum,
                $reportName,
                json_encode($reportData),
                $execTime,
                $reportsRawData
            );
        }

        return $data;
    }

    private function isRateLimitationExceeded(string $pin, $name): bool
    {
        $hourAgo = Carbon::now()->subMinutes(60);

        $reports = DateTechnologyReport::where('pin', $pin)
            ->where('name', '=', $name)
            ->where('created_at', '>=', $hourAgo)
            ->get();

        if ($reports->count() >= 5) {
            return true;
        }

        $reports = NoiReport::where('pin', $pin)
            ->where('name', '=', $name)
            ->where('created_at', '>=', $hourAgo)
            ->get();

        if ($reports->count() >= 5) {
            return true;
        }

        return false;
    }

    /**
     * TODO LION: Used in test, should be deleted and used normal logic - from ReportService->addNoiReport()
     *
     * @param string $pin
     * @param string $idCardNum
     * @param string $report
     *
     * @return DateTechnologyReport|null
     */
    public function addReport(
        string $pin,
        string $idCardNum,
        string $report
    ) {
        $allowedReports = DateTechnology::getAllowedReports();
        if (!in_array($report, $allowedReports)) {
            return null;
        }

        $execTime = null;

        try {

            $start = $this->timer();

            $reportResult = $this->getDateTechnologyReportsExtended(
                $pin,
                $idCardNum,
                [$report]
            );
            $reportRawData = $reportResult['raw'];
            $reportData = $reportResult['decoded'];

            $end = $this->timer();
            $execTime = $this->calculateExecTime($start, $end, 5);

            if (empty($reportData)) {
                return null;
            }

            return $this->getDTRepo()->addReport(
                $pin,
                $idCardNum,
                $report,
                json_encode($reportData),
                $execTime,
                $reportRawData
            );

        } catch (Exception $e) {

            Log::channel('dtError')->error(
                'pin:' . $pin
                . ', idCardNum:' . $idCardNum
                . ', report:' . $report
                . ', error:' . $e->getMessage()
            );

            return null;
        }
    }

    /**
     * TODO LION: Used in test, should be deleted and used normal logic - from ReportService->addNoiReport()
     */
    public function getReports(
        string $pin,
        string $idCardNumber,
        array $reports
    ): array {
        if (empty($reports)) {
            return [];
        }

        $data = [];
        foreach ($reports as $reportName) {
            try {
                $report = $this->getReport($pin, $idCardNumber, $reportName);
                if ($report) {
                    $data[$reportName] = $report;
                }
            } catch (Exception $e) {
                Log::channel('dtError')->error(
                    'pin:' . $pin
                    . ', idCardNum:' . $idCardNumber
                    . ', reports:' . implode(',', $reports)
                    . ', error:' . $e->getMessage()
                );
                continue;
            }
        }

        return $data;
    }

    /**
     * TODO LION: Used in test, should be deleted and used normal logic - from ReportService->addNoiReport()
     */
    public function getReport(
        string $pin,
        string $idcardNumber,
        string $reportName
    ) {
        return $this->getDTRepo()->get(
            $pin,
            $idcardNumber,
            $reportName
        );
    }

    public function getDateTechnologyReport(
        string $pin,
        string $idCardNum,
        string $report
    ): array {

        try {

            if (!isProdOrStage()) {
                $data = DummyService::get(
                    'DateTechnology/' . $report
                );
                return json_decode($data, true);
            }

            $report = $this->getDT()->getReports($pin, $idCardNum, [$report]);

        } catch (Exception $e) {

            Log::channel('dtError')->error(
                'pin:' . $pin
                . ', idCardNum:' . $idCardNum
                . ', report:' . $report
                . ', error:' . $e->getMessage()
            );
            $report = [];
        }

        return $report;
    }

    public function getDummyDateTechnology(string $report)
    {
        if (rand(1, 0)) {
            $data = DummyService::get(
                'DateTechnology/' . $report
            );
            return json_decode($data, true);
        }

        return false;
    }

    public function getDateTechnologyReportsExtended(
        string $pin,
        string $idCardNum,
        array $reports
    ): array {

        try {
            if (!isProdOrStage()) {
                natsort($reports);
                $data = DummyService::get(
                    'DateTechnology/' . implode(',', $reports)
                );

                return [
                    'raw' => '',
                    'decoded' => json_decode($data, true),
                ];
            }

            $reportData = $this->getDT()->getReportsExtended(
                $pin,
                $idCardNum,
                $reports
            );

            return [
                'raw' => $reportData['raw'],
                'decoded' => $reportData['decoded'],
            ];

        } catch (Exception $e) {
            Log::channel('dtError')->error(
                'pin:' . $pin
                . ', idCardNum:' . $idCardNum
                . ', reports:' . implode(',', $reports)
                . ', error:' . $e->getMessage()
            );

            return [
                'raw' => '',
                'decoded' => '',
            ];
        }
    }

    /**
     * Add relation to report for client and loan
     */
    public function linkReport(
        int $dtReportId,
        int $clientId,
        int $loanId,
        string $reportName
    ): ?DateTechnologyReportPivot {
        return $this->getDTRepo()->addPivot(
            $dtReportId,
            $clientId,
            $loanId,
            $reportName
        );
    }

    public function distributeReport(DateTechnologyReport $report): ?NoiReport
    {
        if (empty($report->name) || empty($report->data)) {
            Log::channel('dtError')->error(
                'Failed to parse, bad object, #'
                . (
                    !empty($report->date_technology_report_id)
                    ? $report->date_technology_report_id
                    : 'undefined'
                )
            );
            return null;
        }

        if (
            !in_array(
                $report->name,
                [
                    NoiReport::REPORT_NAME_SHORT,
                    NoiReport::REPORT_NAME_FULL,
                    NoiReport::REPORT_NAME_RETIRED,
                ]
            )
        ) {
            Log::channel('dtError')->error(
                'Failed to parse, wrong type: ' . $report->name
                . ', #' . $report->date_technology_report_id
            );
            return null;
        }

        // after creation, will be parsed
        $noiReport = $this->getNoiService()->saveReport([
            'pin' => $report->pin,
            'source' => 'date_technology_report',
            'source_id' => $report->date_technology_report_id,
            'report' => $report->data,
            'reportName' => $report->name,
            'execTime' => $report->exec_time,
            'raw_data' => $report?->raw_data ?? '',
        ]);
        if (empty($noiReport->noi_report_id)) {
            Log::channel('dtError')->error(
                'Failed to create noi report from dt report #'
                . (
                    !empty($report->date_technology_report_id)
                    ? $report->date_technology_report_id
                    : 'undefined'
                )
            );
            return null;
        }

        return $noiReport;
    }

    private function getDT(): DateTechnology
    {
        if (null === $this->dt) {
            $this->dt = new DateTechnology();
        }

        return $this->dt;
    }

    private function getDTRepo(): DateTechnologyReportRepository
    {
        if (null === $this->dtRepo) {
            $this->dtRepo = new DateTechnologyReportRepository();
        }

        return $this->dtRepo;
    }

    private function getNoiService(): NoiService
    {
        if (null === $this->noiService) {
            $this->noiService = app(NoiService::class);
        }

        return $this->noiService;
    }
}
