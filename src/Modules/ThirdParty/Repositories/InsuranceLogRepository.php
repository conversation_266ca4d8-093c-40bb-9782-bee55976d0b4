<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Repositories\BaseRepository;
use Modules\ThirdParty\Models\InsuranceLog;

class InsuranceLogRepository extends BaseRepository
{
    public function getDbModel(): InsuranceLog
    {
        return new InsuranceLog();
    }

    /**
     * Get logs by action with pagination
     */
    public function getByAction(string $action, int $page = 1, int $perPage = 50): Collection
    {
        return InsuranceLog::where('action', $action)
            ->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();
    }

    /**
     * Get recent logs with pagination
     */
    public function getRecent(int $page = 1, int $perPage = 50): Collection
    {
        return InsuranceLog::orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();
    }

    /**
     * Get logs by date range
     */
    public function getByDateRange(\DateTime $from, \DateTime $to): Collection
    {
        return InsuranceLog::whereBetween('created_at', [$from, $to])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get statistics by action
     */
    public function getActionStats(): array
    {
        return InsuranceLog::selectRaw('action, COUNT(*) as count, MAX(created_at) as last_used')
            ->groupBy('action')
            ->orderBy('count', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Get error logs (responses with error codes)
     */
    public function getErrorLogs(int $limit = 100): Collection
    {
        return InsuranceLog::whereRaw("JSON_EXTRACT(response, '$.resultCode') != 0")
            ->orWhereRaw("JSON_EXTRACT(response, '$.code') != 0")
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean old logs
     */
    public function cleanOldLogs(int $daysToKeep = 30): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return InsuranceLog::where('created_at', '<', $cutoffDate)->delete();
    }

    /**
     * Get logs for specific certificate ID
     */
    public function getByCertificateId(int $certificateId): Collection
    {
        return InsuranceLog::where(function ($query) use ($certificateId) {
            $query->whereRaw("JSON_EXTRACT(request, '$.certificateId') = ?", [$certificateId])
                  ->orWhereRaw("JSON_EXTRACT(response, '$.certificateId') = ?", [$certificateId]);
        })
        ->orderBy('created_at', 'desc')
        ->get();
    }

    /**
     * Get logs for specific offer UUID
     */
    public function getByOfferUuid(string $offerUuid): Collection
    {
        return InsuranceLog::where(function ($query) use ($offerUuid) {
            $query->whereRaw("JSON_EXTRACT(request, '$.offerUUID') = ?", [$offerUuid])
                  ->orWhereRaw("JSON_EXTRACT(response, '$.offerUUID') = ?", [$offerUuid]);
        })
        ->orderBy('created_at', 'desc')
        ->get();
    }
}
