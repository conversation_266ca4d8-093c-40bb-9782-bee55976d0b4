<?php

namespace Modules\ThirdParty\Repositories;

use Carbon\Carbon as CarbonEx;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\CcrReport;
use Modules\Common\Models\CcrReportPivot;
use Modules\Common\Repositories\BaseRepository;

class CcrReportRepository extends BaseRepository
{
    /**
     * Get last report by pin
     *
     * @return CcrReport
     */
    public function getByPin(string $pin): ?CcrReport
    {
        return CcrReport::where(
            [
                'pin' => $pin,
                'last' => 1,
            ]
        )->orderBy('ccr_report_id', 'DESC')
            ->first();
    }

    /**
     * [getClientReportAfterDate description]
     *
     * @param string $pin
     * @param Carbon $bnbLastSyncDate
     *
     * @return CcrReport|null
     */
    public function getClientReportAfterDate(
        string $pin,
        Carbon $bnbLastSyncDate
    ): ?CcrReport
    {

        $lastClientReport = $this->getByPin($pin);
        if (!$lastClientReport) {
            return null;
        }

        $lastClientReportDate = Carbon::parse($lastClientReport->created_at);
        if ($lastClientReportDate->lt($bnbLastSyncDate)) {
            return null;
        }

        return $lastClientReport;
    }

    /**
     * @param string $pin
     * @param string $bnbData
     * @param array $stats
     * @param float $execTime
     *
     * @return CcrReport
     */
    public function addReport(
        string $pin,
        float  $execTime,
        string $bnbData = '',
        array  $stats = [],
        array  $parsedData = [],
        string $bnbRaw = '',
        $createdAt = null
    ): CcrReport {
        $previousReport = $this->getByPin($pin);

        $ccrReport = new CcrReport();
        if (!empty($stats)) {
            $ccrReport->fill($stats);
        }
        $ccrReport->last = 1;
        $ccrReport->pin = $pin;
        $ccrReport->data = $bnbData;
        $ccrReport->parsed_data = (!empty($parsedData) ? json_encode($parsedData) : '');
        $ccrReport->exec_time = $execTime;
        $ccrReport->raw_data = $bnbRaw;
        if (!empty($createdAt)) {
            $ccrReport->created_at = $createdAt;
        }
        $ccrReport->save();

        if (!empty($previousReport->ccr_report_id)) {
            $previousReport->last = 0;
            $previousReport->save();
        }

        return $ccrReport;
    }

    /**
     * [getPivot description]
     *
     * @param int $clientId
     * @param int $loanId
     *
     * @return CcrReportPivot|null
     */
    public function getPivot(int $clientId, int $loanId): ?CcrReportPivot
    {
        return CcrReportPivot::where(
            [
                'client_id' => $clientId,
                'loan_id' => $loanId,
                'last' => 1,
            ]
        )->first();
    }

    /**
     * Add new report pivot
     *
     * @param int $ccrReportId
     * @param int $clientId
     * @param int $loanId
     *
     * @return CcrReportPivot
     */
    public function addPivot(
        int $ccrReportId,
        int $clientId,
        int $loanId
    ): CcrReportPivot
    {

        $previousReportPivot = $this->getPivot($clientId, $loanId);

        $newReportPivot = new CcrReportPivot();
        $newReportPivot->ccr_report_id = $ccrReportId;
        $newReportPivot->client_id = $clientId;
        $newReportPivot->loan_id = $loanId;
        $newReportPivot->last = 1;
        $newReportPivot->save();

        if (!empty($previousReportPivot->ccr_report_pivot_id)) {
            $previousReportPivot->last = 0;
            $previousReportPivot->save();
        }

        return $newReportPivot;
    }

    public function getAllByPin(
        string $pin,
        array  $order = ['ccr_report_id' => 'DESC']
    ): Collection {
        return app(CcrReport::class)
            ->where('pin', $pin)
            ->orderBy('ccr_report_id', 'DESC')
            ->get();
    }

    public function getAllForLoan(
        int $loanId,
        int $clientId
    ): Collection {
        return CcrReport::whereHas('pivots', function($query) use ($clientId, $loanId) {
            $query->where('client_id', $clientId)
                ->where('loan_id', $loanId);
        })->orderBy('created_at', 'DESC')->get();
    }

    public function getReport(string $pin, int $daysBefore = 0)
    {
        $where = [
            'pin' => $pin,
            'active' => 1,
            'last' => 1,
            'deleted' => 0
        ];

        $today = \Carbon\Carbon::now()->startOfDay();
        if ($daysBefore > 0) {
            $today = $today->subDays($daysBefore);
        }
        $where[] = ['created_at', '>=', $today];

        return CcrReport::where($where)
            ->orderBy('ccr_report_id', 'DESC')
            ->first();
    }

    public function getLatestUnparsed(CarbonEx $dateFrom, CarbonEx $dateTo): Collection
    {
        return DB::table('ccr_report')
            ->where(function (Builder $query) {
                $query->where('parsed_data', '=', '')->orWhereNull('parsed_data');
            })
            ->where('created_at', '>=', $dateFrom->toDateTimeString())
            ->where('created_at', '<=', $dateTo->toDateTimeString())
            ->get();
    }
}
