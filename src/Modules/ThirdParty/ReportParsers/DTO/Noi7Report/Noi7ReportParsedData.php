<?php

namespace Modules\ThirdParty\ReportParsers\DTO\Noi7Report;

use <PERSON><PERSON>\LaravelData\Attributes\WithTransformer;
use <PERSON><PERSON>\LaravelData\Data;

/**
 * https://spatie.be/docs/laravel-data/v2/as-a-resource/transformers
 * Example of SlashToDotTransformer from 'dasdas/asdasd/asdasd' to 'dasdas.asdasd.asdasd'
 */
class Noi7ReportParsedData extends Data
{
    public function __construct(
        #[WithTransformer(SlashToDotTransformer::class)]
        public string $report_date,
        public string $name,
        public string $bulstat,
        public string $address,
        public string $contract_type,
        #[WithTransformer(SlashToDotTransformer::class)]
        public string $start_date,
        public string $end_date,
        public string $expire_date,
        public string $salary,
        public string $status,
        public string $contract_doc_type,
        public string $contract_type_num,
        #[WithTransformer(SlashToDotTransformer::class)]
        public ?string $contract_end_date = null,
        public ?int $timestamp = null,
        public ?string $phone = null,
    ) {
    }
}
