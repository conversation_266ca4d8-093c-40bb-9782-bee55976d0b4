<?php

namespace Modules\ThirdParty\ReportParsers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\ThirdParty\Services\NoiService;
use Modules\Common\Models\NoiReport;

class Noi2ReportParser
{

    public function __construct(private readonly NoiService $noiService = new NoiService)
    {
    }

    private function getReportData(NoiReport $report)
    {
        $data = json_decode($report->data, JSON_UNESCAPED_SLASHES);

        if (!empty($data['reports'][$report->name])) {
            return $data['reports'][$report->name];
        }

        return $data;
    }

    public function run(NoiReport $report)
    {
        $data = $this->getReportData($report);

        if (
            empty($data['start'])
            || empty($data['reportDate'])
            || empty($data['start']['PersonalInfo'])
            || (empty($data['start']['BulstatInfo']) && empty($data['start']['EgnInfo']))
        ) {
            Log::channel('noiError')->debug($data);

            return;
        }

        $clientData = $data['start']['PersonalInfo'];


        $employersData = [];
        if (!empty($data['start']['BulstatInfo'])) {
            $employersData = $data['start']['BulstatInfo'];
        }

        $parsedData = [
            'date' => Carbon::parse($data['reportDate'])->format('Y-m-d'),
            'data' => [],
        ];

        list($employersPins, $parsedData) = $this->noiService->addEmployersFirst($employersData, $parsedData);

        if (!empty($employersPins)) {
            $parsedData = $this->noiService->addEmployersPins($employersPins, $clientData, $parsedData);
        } else {
            // костъль, когда у нас нет масива - BulstatInfo
            foreach ($clientData as $empKey => $row) {
                $parsedData['data'][$empKey]['stats'][] = [
                    'year' => $row['Year'],
                    'month' => $row['Month'],
                    'salary' => $row['Salary'],
                    'worked_days' => $row['Workdays'],
                    'declaration_date' => Carbon::parse(str_replace('/', '.', $row['inputdate']))->format(
                        'd.m.Y'
                    ),
                ];
            }
        }

        $report->parsed_data = json_encode($parsedData);
        $report->save();

        return $report;
    }
}
