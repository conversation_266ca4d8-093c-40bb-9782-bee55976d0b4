<?php

namespace Modules\ThirdParty\Tests\Unit;

use Exception;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\ThirdParty\Services\A4EService;
use Modules\Common\Models\Loan;
use Tests\TestCase;

/**
 * USAGE: php artisan test --filter A4EServiceTest
 */
class A4EServiceTest extends TestCase
{
    use WithoutMiddleware;

    private $a4eService;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->checkEnvironment();
        parent::setUp();

        $this->a4eService = new A4EService;
    }

    public function testSuccess()
    {
     //    $loan = Loan::where('loan_status_id', 2)->first();
    	// $data = $this->a4eService->getScoringForLoan($loan);
        // $this->assertNotEmpty($data);
    }
}
