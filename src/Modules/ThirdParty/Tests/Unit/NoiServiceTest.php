<?php

namespace Modules\ThirdParty\Tests\Unit;

use Exception;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\ThirdParty\Libraries\Nsi;
use Modules\ThirdParty\Repositories\NoiReportRepository;
use Modules\ThirdParty\Services\NoiService;
use Tests\TestCase;
use Tests\Unit\Traits\FakeDataTrait;

/**
 * USAGE: php artisan test --filter NoiServiceTest
 */
class NoiServiceTest extends TestCase
{
    use WithoutMiddleware;
    use FakeDataTrait;

    private $testPin;
    private $testLoanId;
    private $testClientId;
    private $noiService;
    private NoiReportRepository $noiReportRepository;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->checkEnvironment();
        parent::setUp();
        $this->testPin = '8809090159';
        $this->testLoanId = '5';
        $this->testClientId = '5';
        $this->noiService = app(NoiService::class);
        $this->noiReportRepository = app(NoiReportRepository::class);
    }

    /*
    public function testNsiReportCode2Success()
    {
    	if (empty($this->testPin)) {
            $this->assertEquals(1, 1);
    		return ;
    	}

        $nsi = new Nsi();
    	$data = $nsi->getReport(
    		$this->testPin,
            Nsi::REPORT_ID_SHORT
    	);

        $this->assertNotEmpty($data);
    }
    */

    public function testNoiServiceAddReportSuccess()
    {
        $report = $this->noiService->addReport($this->testPin, Nsi::REPORT_ID_SHORT);
        $this->checkMainFieldsInReports($report, Nsi::REPORT_ID_SHORT);

        $parsedData = $report->getData();
        $this->assertNotEmpty($parsedData['EgnInfo']['EGN']);
        $this->assertNotEmpty($parsedData['EgnInfo']['familyname']);
        $this->assertNotEmpty($parsedData['EgnInfo']['Address']);
        $this->assertNotEmpty($parsedData['PersonalInfo']);
    }

    // public function testNoiServiceAddReportsSuccess()
    // {
    //     $reports = $this->noiService->addReports(
    //         $this->testPin,
    //         [
    //             Nsi::REPORT_ID_SHORT,
    //             Nsi::REPORT_ID_FULL,
    //             Nsi::REPORT_ID_RETIRED,
    //         ]
    //     );
    //     $this->assertNotEmpty($reports['noi' . Nsi::REPORT_ID_SHORT]);
    //     $this->assertNotEmpty($reports['noi' . Nsi::REPORT_ID_FULL]);
    //     $this->assertNotEmpty($reports['noi' . Nsi::REPORT_ID_RETIRED]);

    //     $report1 = $reports['noi' . Nsi::REPORT_ID_SHORT];
    //     $this->assertNotEmpty($report1['exec_time']);
    //     $this->checkMainFieldsInReports($report1, Nsi::REPORT_ID_SHORT);
    //     $parsedData1 = $report1->getData();
    //     $this->assertNotEmpty($parsedData1['EgnInfo']['EGN']);
    //     $this->assertNotEmpty($parsedData1['EgnInfo']['familyname']);
    //     $this->assertNotEmpty($parsedData1['EgnInfo']['Address']);
    //     $this->assertNotEmpty($parsedData1['PersonalInfo']);

    //     $report2 = $reports['noi' . Nsi::REPORT_ID_FULL];
    //     $this->assertNotEmpty($report2['exec_time']);
    //     $this->checkMainFieldsInReports($report2, Nsi::REPORT_ID_FULL);
    //     $parsedData2 = $report2->getData();
    //     $this->assertNotEmpty($parsedData2['DataForEGNContract']);
    //     $element = current($parsedData2['DataForEGNContract']);
    //     $this->assertNotEmpty($element['EGN']);
    //     $this->assertNotEmpty($element['FirstName']);
    //     $this->assertNotEmpty($element['FamilyName']);
    //     $this->assertNotEmpty($element['Bulstat']);
    //     $this->assertNotEmpty($element['EGNBaseSalary']);
    //     $this->assertNotEmpty($element['EmployerName']);
    //     $this->assertNotEmpty($element['EmployerAddress']);

    //     $report3 = $reports['noi' . Nsi::REPORT_ID_RETIRED];
    //     $this->assertNotEmpty($report3['exec_time']);
    //     $this->checkMainFieldsInReports($report3, Nsi::REPORT_ID_RETIRED);
    //     $parsedData3 = $report3->getData();
    //     $this->assertNotEmpty($parsedData3['Pensioner']);
    //     $this->assertNotEmpty($parsedData3['Addition_or_Debt']);
    //     $this->assertNotEmpty($parsedData3['Pensions']);
    // }

    // public function testNoiServiceGetReportSuccess()
    // {
    //     $report = $this->noiService->getReport($this->testPin, Nsi::REPORT_ID_SHORT);
    //     $this->checkMainFieldsInReports($report, Nsi::REPORT_ID_SHORT);
    // }

    // public function testNoiServiceGetReportsSuccess()
    // {
    //     $reports = $this->noiService->getReports(
    //         $this->testPin,
    //         [
    //             Nsi::REPORT_ID_SHORT,
    //             Nsi::REPORT_ID_FULL,
    //             Nsi::REPORT_ID_RETIRED,
    //         ]
    //     );
    //     $this->assertNotEmpty($reports);

    //     foreach ($reports as $reportName => $report) {
    //         $this->checkMainFieldsInReports(
    //             $report,
    //             $this->noiService->getReportCode($reportName)
    //         );
    //     }
    // }

    // public function testReportPivotLinkSuccess()
    // {
    //     $report = $this->noiService->getReport($this->testPin, Nsi::REPORT_ID_SHORT);
    //     $this->assertEquals($report->pin, $this->testPin);
    //     $this->assertEquals($report->last, 1);
    //     $this->assertNotEmpty($report->exec_time);

    //     $reportPivot = $this->noiService->linkReport(
    //         $report->noi_report_id,
    //         $this->testClientId,
    //         $this->testLoanId,
    //         $report->name
    //     );

    //     $this->assertNotEmpty($reportPivot);
    //     $this->assertNotEmpty($reportPivot->noi_report_id, $report->noi_report_id);
    //     $this->assertNotEmpty($reportPivot->client_id, $this->testClientId);
    //     $this->assertNotEmpty($reportPivot->loan_id, $this->testLoanId);
    //     $this->assertNotEmpty($reportPivot->last, 1);

    //     $report2 = $this->noiService->addReport($this->testPin, Nsi::REPORT_ID_SHORT);
    //     $this->assertEquals($report2->pin, $this->testPin);
    //     $this->assertEquals($report2->last, 1);
    //     $this->assertNotEmpty($report2->exec_time);

    //     $reportPivot2 = $this->noiService->linkReport(
    //         $report2->noi_report_id,
    //         $this->testClientId,
    //         $this->testLoanId,
    //         $report2->name
    //     );

    //     $this->assertNotEmpty($reportPivot2);
    //     $this->assertNotEmpty($reportPivot2->noi_report_id, $report2->noi_report_id);
    //     $this->assertNotEmpty($reportPivot2->client_id, $this->testClientId);
    //     $this->assertNotEmpty($reportPivot2->loan_id, $this->testLoanId);
    //     $this->assertNotEmpty($reportPivot2->last, 1);

    //     $report->refresh();
    //     $this->assertEquals($report->last, 0);

    //     $reportPivot->refresh();
    //     $this->assertEquals($reportPivot->last, 0);
    // }

    // private function checkMainFieldsInReports($report, $reportCode)
    // {
    //     $this->assertNotEmpty($report->noi_report_id);
    //     $this->assertNotEmpty($report->noi_report_id);
    //     $this->assertNotEmpty($report->exec_time);
    //     $this->assertNotEmpty($report->exec_time);
    //     $this->assertEquals($report->name, 'noi' . $reportCode);
    //     $this->assertEquals($report->pin, $this->testPin);

    //     $data = json_decode($report->data, true);
    //     $this->assertNotEmpty($data['reportDate']);
    //     $this->assertRegExp('/(20[0-9]{2}\-[0,1][0-9]\-[0-9]{2})/i', $data['reportDate']);
    // }

    // public function testNoiReport()
    // {
    //     $DbData = $this->getClientData();
    //     $client = $this->clientService->create($DbData);
    //     $reportCode = Nsi::REPORT_ID_FULL;

    //     $noiReport = $this->noiService->addReport($client->pin, $reportCode);
    //     $noiReportArray = json_decode($noiReport->data, JSON_UNESCAPED_SLASHES);

    //     foreach ($noiReportArray['start']['DataForEGNContract'] as $noiReportData) {
    //         $DbData = $noiReportData;
    //     }

    //     $employer = $this->noiReportRepository->getNoiEmployerByNoiReportId($noiReport->noi_report_id);

    //     $this->assertEquals($client->pin, $noiReport->pin);
    //     $this->assertEquals($DbData['EmployerName'], $employer[0]->employer_name);
    //     $this->assertEquals($DbData['ContractReason'], $employer[0]->contract_reason);
    //     $this->assertEquals($DbData['ContractDocumentType'], $employer[0]->contract_document_type);
    //     $this->assertEquals($DbData['EGNBaseSalary'], $employer[0]->salary);
    //     $this->assertEquals($DbData['ContractFoundationDate'],
    //         !is_null($employer[0]->contract_foundation_date)
    //             ? $employer[0]->contract_foundation_date->format('d/m/Y')
    //             : null);
    //     $this->assertEquals($DbData['ContractExpirationDate'],
    //         !is_null($employer[0]->contract_end_date)
    //             ? $employer[0]->contract_foundation_date->format('d/m/Y')
    //             : null);
    //     $this->assertEquals($DbData['ContractTerminationDate'],
    //         !is_null($employer[0]->contract_termination_date)
    //             ? $employer[0]->contract_termination_date->format('d/m/Y')
    //             : null);
    // }
}
