<?php

namespace Modules\ThirdParty\Tests\Unit;

use Exception;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\Common\Models\City;
use Modules\Head\Repositories\CityRepository;
use Modules\ThirdParty\Repositories\MvrReportRepository;
use Modules\ThirdParty\Services\MvrService;
use Modules\Common\Models\MvrReport;
use Modules\Common\Models\MvrReportPivot;
use Tests\TestCase;

class MvrServiceTest extends TestCase
{
    use WithoutMiddleware;

    private $mvrService;
    private $testPin;
    private $testIdcardNumber;
    private $testClientId;
    private $testLoanId;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->checkEnvironment();
        parent::setUp();

        // setup service(s)
        $mvrReportRepo = new MvrReportRepository(
            new MvrReport,
            new MvrReportPivot
        );
        $cityRepo = new CityRepository(new City);
        $this->mvrService = new MvrService($mvrReportRepo, $cityRepo);

        // since we use dummy data, send unexisting credentials
        $this->testPin = 1111;
        $this->testIdcardNumber = 2222;
        $this->testLoanId = 5;
        $this->testClientId = 2;
    }

    // public function testReal()
    // {
    //     // $data = $this->mvrService->getMvrData(
    //     //     11111,
    //     //     00000
    //     // );

    //     // dd('testReal', $data);
    // }

    public function testGetMvrDataSuccess()
    {
        if (empty($this->testPin) || empty($this->testIdcardNumber)) {
            return;
        }

        $res = $this->mvrService->getMvrData(
            $this->testPin,
            $this->testIdcardNumber
        );

        $obj = json_decode($res['data']);
        $this->assertNotEmpty($obj->EGN);
        $this->assertNotEmpty($obj->IdentityDocumentNumber);
        $this->assertNotEmpty($obj->PersonNames->FirstName);
        $this->assertNotEmpty($obj->PersonNames->FamilyName);
        $this->assertNotEmpty($obj->IssueDate);
        $this->assertNotEmpty($obj->ValidDate);
        $this->assertNotEmpty($obj->GenderNameLatin);
        $this->assertNotEmpty($obj->PermanentAddress->DistrictName);
        $this->assertNotEmpty($obj->PermanentAddress->MunicipalityName);
        $this->assertNotEmpty($obj->PermanentAddress->LocationName);
        $this->assertNotEmpty($obj->PermanentAddress->BuildingNumber);
        $this->assertNotEmpty($obj->PermanentAddress->Entrance);
        $this->assertNotEmpty($obj->PermanentAddress->Floor);
        $this->assertNotEmpty($obj->PermanentAddress->Apartment);
    }

    public function testAddMvrReportSuccess()
    {
        if (empty($this->testPin) || empty($this->testIdcardNumber)) {
            return;
        }

        $report = $this->mvrService->addMvrReport(
            $this->testPin,
            $this->testIdcardNumber
        );
        $report->refresh();

        $this->assertNotEmpty($report);
        $this->assertNotEmpty($report->mvr_report_id);
        $this->assertEquals($report->pin, $this->testPin);
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($report->last, 1);
    }

    public function testAddTwoMvrReportsSuccess()
    {
        if (empty($this->testPin) || empty($this->testIdcardNumber)) {
            return;
        }

        $report1 = $this->mvrService->addMvrReport(
            $this->testPin,
            $this->testIdcardNumber
        );
        $report1->refresh();
        $this->assertNotEmpty($report1);
        $this->assertNotEmpty($report1->mvr_report_id);
        $this->assertEquals($report1->pin, $this->testPin);
        $this->assertNotEmpty($report1->exec_time);
        $this->assertEquals($report1->last, 1);

        $report2 = $this->mvrService->addMvrReport(
            $this->testPin,
            $this->testIdcardNumber
        );
        $report2->refresh();
        $this->assertNotEmpty($report2);
        $this->assertNotEmpty($report2->mvr_report_id);
        $this->assertEquals($report2->pin, $this->testPin);
        $this->assertNotEmpty($report2->exec_time);
        $this->assertEquals($report2->last, 1);

        $report1->refresh();
        $this->assertEquals($report1->last, 0);
    }

    public function testGetClientDataSuccess()
    {
        if (empty($this->testPin) || empty($this->testIdcardNumber)) {
            return;
        }

        $clientData = $this->mvrService->getClientData(
            $this->testPin,
            $this->testIdcardNumber
        );

        $this->assertNotEmpty($clientData);
        $this->assertEquals(is_array($clientData), true);

        $this->assertNotEmpty($clientData['client']);
        $this->assertNotEmpty($clientData['client']['first_name']);
        $this->assertNotEmpty($clientData['client']['middle_name']);
        $this->assertNotEmpty($clientData['client']['last_name']);

        $this->assertNotEmpty($clientData['client_idcard']);
        $this->assertNotEmpty($clientData['client_idcard']['issue_date']);
        $this->assertNotEmpty($clientData['client_idcard']['valid_date']);
        $this->assertNotEmpty($clientData['client_idcard']['issue_by']);
        $this->assertNotEmpty($clientData['client_idcard']['sex']);
        $this->assertNotEmpty($clientData['client_idcard']['city_id']);
        $this->assertNotEmpty($clientData['client_idcard']['address']);
    }

    public function testReportPivotLinkSuccess()
    {
        if (empty($this->testPin) || empty($this->testIdcardNumber)) {
            return;
        }

        $report = $this->mvrService->addMvrReport(
            $this->testPin,
            $this->testIdcardNumber
        );

        $reportPivot = $this->mvrService->linkReport(
            $report->mvr_report_id,
            $this->testClientId,
            $this->testLoanId
        );

        $this->assertNotEmpty($reportPivot);
        $this->assertNotEmpty($reportPivot->mvr_report_id, $report->mvr_report_id);
        $this->assertNotEmpty($reportPivot->client_id, $this->testClientId);
        $this->assertNotEmpty($reportPivot->loan_id, $this->testLoanId);
        $this->assertNotEmpty($reportPivot->last, 1);

        $reportPivot2 = $this->mvrService->linkReport(
            $report->mvr_report_id,
            $this->testClientId,
            $this->testLoanId
        );

        $this->assertNotEmpty($reportPivot2);
        $this->assertNotEmpty($reportPivot2->mvr_report_id, $report->mvr_report_id);
        $this->assertNotEmpty($reportPivot2->client_id, $this->testClientId);
        $this->assertNotEmpty($reportPivot2->loan_id, $this->testLoanId);
        $this->assertNotEmpty($reportPivot2->last, 1);

        $reportPivot->refresh();
        $this->assertEquals($reportPivot->last, 0);
    }
}
