<?php

namespace Modules\ThirdParty\Tests\Unit;

use Exception;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\ThirdParty\Services\CcrService;
use Tests\TestCase;

/**
 * USAGE: php artisan test --filter CcrServiceTest
 */
class CcrServiceTest extends TestCase
{
    use WithoutMiddleware;

    private $testPin;
    private $testLoanId;
    private $testClientId;
    private $ccrService;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->checkEnvironment();
        parent::setUp();
        $this->testPin = '**********'; // **********, **********, **********
        $this->testLoanId = '5';
        $this->testClientId = '5';
        $this->ccrService = new CcrService;
    }

    public function testBnbReportSuccess()
    {
    	$data = $this->ccrService->getBnbReport(
    		$this->testPin
    	);

        $this->assertNotEmpty($data);
    }

    public function testParsing()
    {
        $bnbData = '{"@code":"**********","@name":"\u0421\u0412\u0415\u0422\u041b\u0418\u041d \u041d\u0418\u041a\u041e\u041b\u041e\u0412 \u0421\u042a\u0411\u0415\u0412","section":{"@entity-type":"banks","active-credits":{"@cred-count":"2","@source-entity-count":"1","summaries":[{"@grouping-attribute":"type","summary":{"@date-from":"2021-11-30","@type":"\u041a\u0440\u0435\u0434\u0438\u0442\u043d\u0430 \u043a\u0430\u0440\u0442\u0430","@amount-approved":"25000","@amount-drawn":"859.95","@monthly-installment":"0","@outstanding-performing-principal":"859.95","@outstanding-overdue-principal":"0","@balance-sheet-value":"859.95","@off-balance-sheet-value":"24140.05","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"24140.05"}},{"@grouping-attribute":"overdue-payment-period","summary":{"@date-from":"2021-11-30","@overdue-payment-period":"\u043e\u0442 0 \u0434\u043e 30 \u0434\u043d\u0438","@amount-approved":"25000","@amount-drawn":"859.95","@monthly-installment":"0","@outstanding-performing-principal":"859.95","@outstanding-overdue-principal":"0","@balance-sheet-value":"859.95","@off-balance-sheet-value":"24140.05","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"24140.05"}},{"@grouping-attribute":"rest","summary":{"@date-from":"2021-11-30","@rest":"\u0414\u043e \u0435\u0434\u043d\u0430 \u0433\u043e\u0434\u0438\u043d\u0430","@amount-approved":"25000","@amount-drawn":"859.95","@monthly-installment":"0","@outstanding-performing-principal":"859.95","@outstanding-overdue-principal":"0","@balance-sheet-value":"859.95","@off-balance-sheet-value":"24140.05","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"24140.05"}},{"@grouping-attribute":"term","summary":{"@date-from":"2021-11-30","@term":"\u0414\u043e \u0435\u0434\u043d\u0430 \u0433\u043e\u0434\u0438\u043d\u0430","@amount-approved":"25000","@amount-drawn":"859.95","@monthly-installment":"0","@outstanding-performing-principal":"859.95","@outstanding-overdue-principal":"0","@balance-sheet-value":"859.95","@off-balance-sheet-value":"24140.05","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"24140.05"}}]},"overdue-history":[],"new-credits":[],"related-active-credits":{"credits-set":[{"@person-role":"\u0421\u044a\u0434\u043b\u044a\u0436\u043d\u0438\u043a"},{"@person-role":"\u041f\u043e\u0440\u044a\u0447\u0438\u0442\u0435\u043b"}]}}}';
        $x = $this->ccrService->getStats($bnbData);

        dd('---- tuk ----');
    }

    public function testCcrServiceGetBnbReportSuccess()
    {
        $json = $this->ccrService->getBnbReport($this->testPin);

        $this->assertNotEmpty($json);

        $data = json_decode($json, true);
        $this->assertNotEmpty($data['@code']);
        $this->assertEquals($data['@code'], $this->testPin); // since it dummy data
        // $this->assertEquals($data['@code'], '**********'); // since it dummy data
    }

    public function testCcrServiceStatsSuccess()
    {
        $json = $this->ccrService->getBnbReport($this->testPin);
        $stats = $this->ccrService->getStats($json);
        $this->assertNotEmpty($stats);
    }

    // public function testCcrServiceStatsSuccess()
    // {
    //     $json = $this->ccrService->getBnbReport($this->testPin);
    //     $stats = $this->ccrService->getStats($json);
    //     $this->assertEquals($stats['active_percent'], '2.45');
    //     $this->assertEquals($stats['not_active_percent'], '1.39');
    //     $this->assertEquals($stats['total_percent'], '-19.16');
    //     $this->assertEquals($stats['total_points'], 7);
    // }

    public function testCcrServiceSaveNewReportForceSuccess()
    {
        $report = $this->ccrService->getAndSaveNewReport($this->testPin, true);
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($report->pin, $this->testPin);
        // $this->assertEquals($report->active_percent, '2.45');
        // $this->assertEquals($report->not_active_percent, '1.39');
        // $this->assertEquals($report->total_percent, '-19.16');
        // $this->assertEquals($report->total_points, 7);
        $this->assertEquals($report->last, 1);
    }

    public function testCcrServiceSaveNewReportNoStatsSuccess()
    {
        $report = $this->ccrService->getAndSaveNewReport($this->testPin, false);
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($report->pin, $this->testPin);
        $this->assertEmpty($report->active_percent);
        $this->assertEmpty($report->not_active_percent);
        $this->assertEmpty($report->total_percent);
        $this->assertEmpty($report->total_points);
        $this->assertEquals($report->last, 1);
    }

    public function testCcrServiceSaveTwoReportsSuccess()
    {
        $report1 = $this->ccrService->getAndSaveNewReport($this->testPin, true);
        $this->assertEquals($report1->pin, $this->testPin);
        $this->assertEquals($report1->last, 1);
        $this->assertNotEmpty($report1->total_percent);
        $this->assertNotEmpty($report1->total_points);
        $this->assertNotEmpty($report1->exec_time);

        $report2 = $this->ccrService->getAndSaveNewReport($this->testPin, false);
        $this->assertEquals($report2->pin, $this->testPin);
        $this->assertEquals($report2->last, 1);
        $this->assertEmpty($report2->total_percent);
        $this->assertEmpty($report2->total_points);
        $this->assertNotEmpty($report2->exec_time);

        $report1->refresh();
        $this->assertEquals($report1->last, 0);
    }

    public function testCcrServiceGetReportSuccess()
    {
        // force
        $reportForce = $this->ccrService->getReport($this->testPin, true);
        $this->assertNotEmpty($reportForce->exec_time);
        $this->assertEquals($reportForce->pin, $this->testPin);
        $this->assertEquals($reportForce->last, 1);

        // get existing if exist
        $reportExisting = $this->ccrService->getReport($this->testPin);
        $this->assertNotEmpty($reportForce->exec_time);
        $this->assertEquals($reportExisting->pin, $this->testPin);
        $this->assertEquals($reportExisting->last, 1);
        $this->assertEquals($reportExisting->ccr_report_id, $reportForce->ccr_report_id);
    }

    public function testReportPivotLinkSuccess()
    {
        $report1 = $this->ccrService->getReport($this->testPin, true);
        $this->assertEquals($report1->pin, $this->testPin);
        $this->assertNotEmpty($report1->exec_time);

        $reportPivot = $this->ccrService->linkReport(
            $report1->ccr_report_id,
            $this->testClientId,
            $this->testLoanId
        );

        $this->assertNotEmpty($reportPivot);
        $this->assertNotEmpty($reportPivot->ccr_report_id, $report1->ccr_report_id);
        $this->assertNotEmpty($reportPivot->client_id, $this->testClientId);
        $this->assertNotEmpty($reportPivot->loan_id, $this->testLoanId);
        $this->assertNotEmpty($reportPivot->last, 1);


        $report2 = $this->ccrService->getReport($this->testPin, true);
        $this->assertEquals($report2->pin, $this->testPin);
        $this->assertNotEmpty($report2->exec_time);

        $reportPivot2 = $this->ccrService->linkReport(
            $report2->ccr_report_id,
            $this->testClientId,
            $this->testLoanId
        );

        $this->assertNotEmpty($reportPivot2);
        $this->assertNotEmpty($reportPivot2->ccr_report_id, $report2->ccr_report_id);
        $this->assertNotEmpty($reportPivot2->client_id, $this->testClientId);
        $this->assertNotEmpty($reportPivot2->loan_id, $this->testLoanId);
        $this->assertNotEmpty($reportPivot2->last, 1);

        $reportPivot->refresh();
        $this->assertEquals($reportPivot->last, 0);
    }
}
