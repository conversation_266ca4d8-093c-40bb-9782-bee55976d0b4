<?php

namespace Modules\ThirdParty\Tests\Unit;

use Exception;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\ThirdParty\Libraries\Nsi;
use Modules\ThirdParty\Services\DateTechnologyService;
use Tests\TestCase;

/**
 * USAGE: php artisan test --filter DateTechnologyServiceTest
 */
class DateTechnologyServiceTest extends TestCase
{
    use WithoutMiddleware;

    private $testPin;
    private $testLoanId;
    private $testClientId;
    private $noiService;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->checkEnvironment();
        parent::setUp();
        $this->testPin = '8007036057';
        $this->testIdCArdNumber = '613128631';
        $this->testLoanId = '5';
        $this->testClientId = '5';
        $this->dtService = new DateTechnologyService;
    }

    /*
    public function testDateTechnologyReportsSuccess()
    {
    	if (empty($this->testPin)) {
            $this->assertEquals(1, 1);
    		return ;
    	}

        $dt = new DateTechnology();
    	$data = $dt->getReports(
            $this->testPin,
    		$this->testIdCArdNumber,
            ['noi7']
    	);
        $this->assertNotEmpty($data);
    }
    */

    public function testDateTechnologyServiceAddReportsSuccess()
    {
        $reportNames = [
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT, // noi2
            Nsi::PREFIX . Nsi::REPORT_ID_FULL, // noi7
            Nsi::PREFIX . Nsi::REPORT_ID_RETIRED, // noi51
        ];

        $reports = $this->dtService->addReports(
            $this->testPin,
            $this->testIdCArdNumber,
            $reportNames
        );

        $this->assertNotEmpty($reports);
        $this->assertEquals(count($reportNames), count($reports));

        foreach ($reports as $reportName => $obj) {
            $this->assertNotEmpty($obj->exec_time);

            $this->assertEquals($this->testPin, $obj->pin);
            $this->assertEquals($this->testIdCArdNumber, $obj->idcard_number);
            $this->assertEquals(1, $obj->last);

            $data = $obj->getData();
            if (Nsi::PREFIX . Nsi::REPORT_ID_SHORT == $reportName) {
                $this->assertNotEmpty($data['EgnInfo']['EGN']);
                $this->assertNotEmpty($data['EgnInfo']['familyname']);
                $this->assertNotEmpty($data['EgnInfo']['City']);
                $this->assertNotEmpty($data['EgnInfo']['Address']);

                $this->assertNotEmpty($data['BulstatInfo']['Bulstat']);
                $this->assertNotEmpty($data['BulstatInfo']['Name']);
                $this->assertNotEmpty($data['BulstatInfo']['address']);

                $this->assertNotEmpty($data['PersonalInfo']);
                $this->assertNotEmpty($data['PersonalInfo'][0]['Bulstat']);
                $this->assertNotEmpty($data['PersonalInfo'][0]['Year']);
                $this->assertNotEmpty($data['PersonalInfo'][0]['Month']);
                $this->assertNotEmpty($data['PersonalInfo'][0]['Typeofisured']);
                $this->assertNotEmpty($data['PersonalInfo'][0]['Salary']);
                $this->assertNotEmpty($data['PersonalInfo'][0]['inputdate']);
            }

            if (Nsi::PREFIX . Nsi::REPORT_ID_FULL == $reportName) {
                $this->assertNotEmpty($data['DataForEGNContract']);
                $lastRow = end($data['DataForEGNContract']);
                $this->assertNotEmpty($lastRow['EGN']);
                $this->assertNotEmpty($lastRow['FamilyName']);
                $this->assertNotEmpty($lastRow['SurName']);
                $this->assertNotEmpty($lastRow['FirstName']);
                $this->assertNotEmpty($lastRow['DateForEGNPersonalData']);
                $this->assertNotEmpty($lastRow['ContractDocumentType']);
                $this->assertNotEmpty($lastRow['Bulstat']);
                $this->assertNotEmpty($lastRow['EGNBaseSalary']);
                $this->assertNotEmpty($lastRow['InputNumber']);
                $this->assertNotEmpty($lastRow['EmployerName']);
                $this->assertNotEmpty($lastRow['EmployerAddress']);
            }

            if (Nsi::PREFIX . Nsi::REPORT_ID_RETIRED == $reportName) {
                $this->assertNotEmpty($data);
            }
        }
    }

    public function testDateTechnologyServiceAddReportSuccess()
    {
        return true;

        $report = $this->dtService->addReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT
        );

        $this->assertNotEmpty($report->date_technology_report_id);
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($this->testPin, $report->pin);
        $this->assertEquals($this->testIdCArdNumber, $report->idcard_number);
        $this->assertEquals(Nsi::PREFIX . Nsi::REPORT_ID_SHORT, $report->name);
        $this->assertEquals(1, $report->last);

        $data = $report->getData();
        $this->assertNotEmpty($data['EgnInfo']['EGN']);
        $this->assertNotEmpty($data['EgnInfo']['familyname']);
        $this->assertNotEmpty($data['EgnInfo']['City']);
        $this->assertNotEmpty($data['EgnInfo']['Address']);
        $this->assertNotEmpty($data['BulstatInfo']['Bulstat']);
        $this->assertNotEmpty($data['BulstatInfo']['Name']);
        $this->assertNotEmpty($data['BulstatInfo']['address']);
        $this->assertNotEmpty($data['PersonalInfo']);
        $this->assertNotEmpty($data['PersonalInfo'][0]['Bulstat']);
        $this->assertNotEmpty($data['PersonalInfo'][0]['Year']);
        $this->assertNotEmpty($data['PersonalInfo'][0]['Month']);
        $this->assertNotEmpty($data['PersonalInfo'][0]['Typeofisured']);
        $this->assertNotEmpty($data['PersonalInfo'][0]['Salary']);
        $this->assertNotEmpty($data['PersonalInfo'][0]['inputdate']);


        $report2 = $this->dtService->addReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_FULL
        );

        $this->assertNotEmpty($report2->date_technology_report_id);
        $this->assertNotEmpty($report2->exec_time);
        $this->assertEquals($this->testPin, $report2->pin);
        $this->assertEquals($this->testIdCArdNumber, $report2->idcard_number);
        $this->assertEquals(Nsi::PREFIX . Nsi::REPORT_ID_FULL, $report2->name);
        $this->assertEquals(1, $report2->last);

        $data = $report2->getData();
        $this->assertNotEmpty($data['DataForEGNContract']);
        $lastRow = end($data['DataForEGNContract']);
        $this->assertNotEmpty($lastRow['EGN']);
        $this->assertNotEmpty($lastRow['FamilyName']);
        $this->assertNotEmpty($lastRow['SurName']);
        $this->assertNotEmpty($lastRow['FirstName']);
        $this->assertNotEmpty($lastRow['DateForEGNPersonalData']);
        $this->assertNotEmpty($lastRow['ContractDocumentType']);
        $this->assertNotEmpty($lastRow['Bulstat']);
        $this->assertNotEmpty($lastRow['EGNBaseSalary']);
        $this->assertNotEmpty($lastRow['InputNumber']);
        $this->assertNotEmpty($lastRow['EmployerName']);
        $this->assertNotEmpty($lastRow['EmployerAddress']);
    }

    public function testDateTechnologyServiceTwoSimilarAddReportsSuccess()
    {
        return true;

        $report = $this->dtService->addReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT
        );
        $this->assertNotEmpty($report->date_technology_report_id);
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($this->testPin, $report->pin);
        $this->assertEquals($this->testIdCArdNumber, $report->idcard_number);
        $this->assertEquals(Nsi::PREFIX . Nsi::REPORT_ID_SHORT, $report->name);
        $this->assertEquals(1, $report->last);

        $report2 = $this->dtService->addReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT
        );
        $this->assertNotEmpty($report2->date_technology_report_id);
        $this->assertNotEmpty($report2->exec_time);
        $this->assertEquals($this->testPin, $report2->pin);
        $this->assertEquals($this->testIdCArdNumber, $report2->idcard_number);
        $this->assertEquals(Nsi::PREFIX . Nsi::REPORT_ID_SHORT, $report2->name);
        $this->assertEquals(1, $report2->last);

        $report->refresh();
        $this->assertEquals(0, $report->last);
    }

    public function testDateTechnologyServiceGetReportsSuccess()
    {
        return true;

        $reportNames = [
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT,
            Nsi::PREFIX . Nsi::REPORT_ID_FULL,
        ];

        $reports = $this->dtService->getReports(
            $this->testPin,
            $this->testIdCArdNumber,
            $reportNames
        );
        $this->assertNotEmpty($reports);
        $this->assertEquals(count($reportNames), count($reports));

        foreach ($reports as $reportName => $obj) {
            $this->assertEquals($this->testPin, $obj->pin);
            $this->assertEquals($this->testIdCArdNumber, $obj->idcard_number);
            $this->assertEquals(1, $obj->last);
        }
    }

    public function testDateTechnologyServiceGetReportSuccess()
    {
        return true;

        $report = $this->dtService->getReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT
        );
        $this->assertNotEmpty($report->date_technology_report_id);
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($this->testPin, $report->pin);
        $this->assertEquals($this->testIdCArdNumber, $report->idcard_number);
        $this->assertEquals(Nsi::PREFIX . Nsi::REPORT_ID_SHORT, $report->name);
        $this->assertEquals(1, $report->last);
    }

    public function testReportPivotLinkSuccess()
    {
        return true;

        $report = $this->dtService->addReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT
        );
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($report->pin, $this->testPin);
        $this->assertEquals($report->last, 1);

        $reportPivot = $this->dtService->linkReport(
            $report->date_technology_report_id,
            $this->testClientId,
            $this->testLoanId,
            $report->name
        );

        $this->assertNotEmpty($reportPivot);
        $this->assertNotEmpty($reportPivot->date_technology_report_id, $report->date_technology_report_id);
        $this->assertNotEmpty($reportPivot->client_id, $this->testClientId);
        $this->assertNotEmpty($reportPivot->loan_id, $this->testLoanId);
        $this->assertNotEmpty($reportPivot->last, 1);


        $report2 = $this->dtService->addReport(
            $this->testPin,
            $this->testIdCArdNumber,
            Nsi::PREFIX . Nsi::REPORT_ID_SHORT
        );
        $this->assertNotEmpty($report->exec_time);
        $this->assertEquals($report2->pin, $this->testPin);
        $this->assertEquals($report2->last, 1);

        $reportPivot2 = $this->dtService->linkReport(
            $report2->date_technology_report_id,
            $this->testClientId,
            $this->testLoanId,
            $report2->name
        );

        $this->assertNotEmpty($reportPivot2);
        $this->assertNotEmpty($reportPivot2->date_technology_report_id, $report2->date_technology_report_id);
        $this->assertNotEmpty($reportPivot2->client_id, $this->testClientId);
        $this->assertNotEmpty($reportPivot2->loan_id, $this->testLoanId);
        $this->assertNotEmpty($reportPivot2->last, 1);

        $report->refresh();
        $this->assertEquals($report->last, 0);

        $reportPivot->refresh();
        $this->assertEquals($reportPivot->last, 0);
    }
}
