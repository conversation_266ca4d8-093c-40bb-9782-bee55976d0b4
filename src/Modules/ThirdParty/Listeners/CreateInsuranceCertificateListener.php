<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Listeners;

use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Common\Models\Loan;
use Modules\ThirdParty\Jobs\InsuranceCertificateJob;

class CreateInsuranceCertificateListener
{
    public function handle(LoanWasApproved $event): void
    {
        $loan = $event->loan;

        // Check if loan needs insurance
        if (!$this->shouldCreateInsuranceCertificate($loan)) {
            return;
        }

        // Dispatch the insurance certificate
        InsuranceCertificateJob::dispatch($loan);
    }

    /**
     * Determine if insurance certificate should be created for this loan
     */
    private function shouldCreateInsuranceCertificate(Loan $loan): bool
    {
        if (!$loan->isOnlineLoan()) {
            return false;
        }

        if (!$loan?->insurance?->isTrue()) {
            return false;
        }

        return true;
    }
}
