<?php

namespace Modules\ThirdParty\Libraries;

use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;
use Modules\ThirdParty\Exceptions\MvrException;

class Mvr
{
    private static ?string $apiUrl;
    private static ?string $token;

    public function __construct()
    {
        self::$apiUrl = config('app.mvr_service_url');
        if (empty(self::$apiUrl)) {
            throw new MvrException('No api url defined');
        }

        self::$token = config('app.mvr_service_token');
        if (empty(self::$token)) {
            throw new MvrException('No api token defined');
        }
    }

    /**
     * Get raw data (json) from MVR
     */
    public function getReport(string $pin, string $idCardNumber): string
    {
        Log::channel('mvrExec')->info('pin:' . $pin . ', idcard:' . $idCardNumber);

        try {
            $parameters = [
                'pin' => $pin,
                'num' => $idCardNumber,
                'token' => self::$token,
            ];

            $jsonResponse = Curl::to(self::$apiUrl)
                ->withData($parameters)
                ->post();

            if (empty($jsonResponse)) {
                throw new MvrException(__('head::clientCard.MvrNoResponse'));
            }

            return $jsonResponse;

        } catch (\Exception $e) {
            Log::channel('mvrErrors')->info('pin:' . $pin . ', idcard:' . $idCardNumber . ', msg: ' . $e->getMessage());
            return '';
        }
    }
}
