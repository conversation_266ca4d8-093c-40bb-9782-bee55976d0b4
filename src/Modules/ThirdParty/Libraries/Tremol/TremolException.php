<?php

namespace Modules\ThirdParty\Libraries\Tremol;

use Exception;
use Throwable;
use Tremol\ServerErrorType;
use Tremol\SException;

class TremolException extends Exception
{
    public function __construct(Throwable $throwable)
    {
        $msg = $this->parseMessage($throwable);
        $code = $this->parseCode($throwable);

        parent::__construct($msg, $code, $throwable);
    }

    private function parseMessage(Throwable $throwable): string
    {
        $msg = "Грешка!  " . $throwable->getMessage();
        if(!$throwable instanceof SException) {
            return $msg;
        }

        $code = $throwable->getCode();
        // Fiscal device error
        if($throwable->isFpException()) {
            $ste1 = $throwable->getSte1();
            $ste2 = $throwable->getSte2();

            /**
             *   Possible reasons:
             *  ste1 =                                                    ste2 =
             *  0x30 OK                                                   0x30 OK
             *  0x31 Out of paper, printer failure                        0x31 Invalid command
             *  0x32 Registers overflow                                   0x32 Illegal command
             *  0x33 Clock failure or incorrect date&time                 0x33 Z daily report is not zero
             *  0x34 Opened fiscal receipt                                0x34 Syntax error
             *  0x35 Payment residue account                              0x35 Input registers overflow
             *  0x36 Opened non-fiscal receipt                            0x36 Zero input registers
             *  0x37 Registered payment but receipt is not closed         0x37 Unavailable transaction for correction
             *  0x38 Fiscal memory failure                                0x38 Insufficient amount on hand
             *  0x39 Incorrect password                                   0x3A No access
             *  0x3a Missing external display
             *  0x3b 24hours block – missing Z report
             *  0x3c Overheated printer thermal head.
             *  0x3d Interrupt power supply in fiscal receipt (one time until status is read)
             *  0x3e Overflow EJ
             *  0x3f Insufficient conditions
             */
            switch (true) {
                case $ste1 == 0x30 && $ste2 == 0x32:
                    $msg = 'Грешка!  ste1 == 0x30 - Командата е ОК и ste2 == 0x32 - Командата е непозволена в текущото състояние на ФУ';
                    break;
                case $ste1 == 0x30 && $ste2 == 0x33:
                    $msg = 'Грешка!  ste1 == 0x30 - Командата е ОК и ste2 == 0x33 - Направете Z отчет';
                    break;
                case $ste1 == 0x34 && $ste2 == 0x32:
                    $msg = 'Грешка!  ste1 == 0x34 - Отворен фискален бон и ste2 == 0x32 - Командата е непозволена в текущото състояние на ФУ';
                    break;
                case $ste1 == 0x39 && $ste2 == 0x32:
                    $msg = 'Грешка!  ste1 == 0x39 - Грешна парола и ste2 == 0x32 - Командата е непозволена';
                    break;
                default:
                    $msg = $msg."\nste1=".$ste1.", ste2=".$ste2;
                    break;
            }
        } else { // Connection between server and fiscal device error
            switch ($code) {
                case ServerErrorType::ServerDefsMismatch:
                    $msg = 'Грешка!  Текущата версия на библиотеката и сървърните дефиниции се различават.';
                break;
                case ServerErrorType::ServMismatchBetweenDefinitionAndFPResult:
                    $msg = 'Грешка!  Текущата версия на библиотеката и фърмуера на ФУ са несъвместими';
                break;
                case ServerErrorType::ServerAddressNotSet:
                    $msg = 'Грешка!  Не е зададен адрес на сървъра!';
                break;
                case ServerErrorType::ServerConnectionError:
                    $msg = 'Грешка!  Не може да се осъществи връзка със ZfpLab сървъра';
                break;
                case ServerErrorType::ServSockConnectionFailed:
                    $msg = 'Грешка!  Сървъра не може да се свърже с ФУ';
                break;
                case ServerErrorType::ServTCPAuth:
                    $msg = 'Грешка!  Грешна TCP парола на устройството';
                break;
                case ServerErrorType::ServWaitOtherClientCmdProcessingTimeOut:
                    $msg = 'Грешка!  Обработката на другите клиенти на сървъра отнема много време';
                break;
            }
        }

        return $msg;
    }

    private function parseCode(Throwable $throwable)
    {
        return ($throwable instanceof SException || $throwable instanceof \RuntimeException)
            ? $throwable->getCode()
            : 500;
    }
}
