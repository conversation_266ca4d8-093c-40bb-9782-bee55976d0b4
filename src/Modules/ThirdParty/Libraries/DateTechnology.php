<?php

namespace Modules\ThirdParty\Libraries;

use \DOMDocument;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\ThirdParty\Exceptions\DateTechnologyException;
use Modules\ThirdParty\Traits\ParseTrait;

class DateTechnology
{
    use ParseTrait;

    const REPORT_SHORT = 'noi2';
    const REPORT_FULL = 'noi7';
    const REPORT_RETIRED = 'noi51';
    const REPORT_MVR = 'mvr';

    private static $uid;
    private static $salt;
    private static $apiUrl;

    public function __construct($uid = null, $salt = null, $url = null)
    {
        self::$uid = env('DATE_TECHNOLOGY_UID', null);
        self::$salt = env('DATE_TECHNOLOGY_SALT', null);
        self::$apiUrl = env('DATE_TECHNOLOGY_API_URL', null);


        $uid = env('DATE_TECHNOLOGY_LENDIVO_UID', null);
        $salt = env('DATE_TECHNOLOGY_LENDIVO_SALT', null);

        $project = env('PROJECT');
        if (!empty($project) && $project == 'lendivo') {
            $uid = env('DATE_TECHNOLOGY_LENDIVO_UID', null);
            $salt = env('DATE_TECHNOLOGY_LENDIVO_SALT', null);
        }

        if (!empty($uid)) {
            self::$uid = $uid;
        }
        if (!empty($salt)) {
            self::$salt = $salt;
        }
        if (!empty($url)) {
            self::$apiUrl = $url;
        }

        if (
            empty(self::$uid)
            || empty(self::$salt)
            || empty(self::$apiUrl)
        ) {
            throw new DateTechnologyException('No credentials');
        }
    }

    public static function getAllowedReports(): array
    {
        return [
            self::REPORT_RETIRED,
            self::REPORT_FULL,
            self::REPORT_SHORT,
            self::REPORT_MVR,
        ];
    }

    public function getReports(
        string $pin,
        string $idcardNumber,
        array $reports
    ): array {

        Log::channel('dtExec')->info(
            'pin:' . $pin
            . ', idcard: ' . $idcardNumber
            . ', reports: ' . implode(', ', $reports)
        );

        $parameters = [
            'ID' => $pin,
            'IDN' => $idcardNumber,
            'reports' => implode(',', $reports), // ccr,noi2,noi7,noi51,idcn,mvr
        ];
        $postData = http_build_query($parameters, '', '&');
        $sign = hash_hmac("sha512", $postData, self::$salt);
        $headers = [
            'Sign: ' . $sign,
            'Key: ' . self::$uid,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, self::$apiUrl . '?' . $postData);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Add timeouts
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // seconds to wait while trying to connect
        curl_setopt($ch, CURLOPT_TIMEOUT, 15); // max seconds to allow cURL to execute

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new DateTechnologyException('Curl Error: ' . curl_error($ch));
        }
        curl_close($ch);

        try {
            $dom = new DOMDocument();
            $dom->recover = true;
            $dom->encoding = 'windows-1251';
            $dom->loadXML($response, LIBXML_NOERROR);
            $payload = str_replace('Start', 'start', $dom->saveXML());
            $result = $this->parseXML($payload);
        } catch(\Exception $e) {
            throw new DateTechnologyException('Failed to parse DT data');
        }

        return $result;
    }

    public function getReportsExtended(
        string $pin,
        string $idcardNumber,
        array $reports
    ): array {

        Log::channel('dtExec')->info(
            'pin:' . $pin
            . ', idcard: ' . $idcardNumber
            . ', reports: ' . implode(', ', $reports)
        );

        $parameters = [
            'ID' => $pin,
            'IDN' => $idcardNumber,
            'reports' => implode(',', $reports), // ccr,noi2,noi7,noi51,idcn,mvr
        ];
        $postData = http_build_query($parameters, '', '&');
        $sign = hash_hmac("sha512", $postData, self::$salt);
        $headers = [
            'Sign: ' . $sign,
            'Key: ' . self::$uid,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, self::$apiUrl . '?' . $postData);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $responseRaw = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new DateTechnologyException('Curl Error: ' . curl_error($ch));
        }
        curl_close($ch);

        try {
            $dom = new DOMDocument();
            $dom->recover = true;
            $dom->encoding = 'windows-1251';
            $dom->loadXML($responseRaw, LIBXML_NOERROR);
            $payload = str_replace('Start', 'start', $dom->saveXML());
            $result = $this->parseXML($payload);

            return [
                'raw' => iconv('UTF-8', 'UTF-8//IGNORE', $responseRaw),
                'decoded' => $result,
            ];

        } catch(\Exception $e) {
            throw new DateTechnologyException('Failed to parse DT data');
        }
    }
}
