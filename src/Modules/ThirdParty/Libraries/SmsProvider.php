<?php

namespace Modules\ThirdParty\Libraries;

use Exception;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;
use Modules\Common\Models\Sms;
use RuntimeException;
use Throwable;

class SmsProvider
{
    private static $testPhone;
    private static $smsServiceUrl;
    private static $smsServiceUser;
    private static $smsServicePass;

    public function __construct()
    {
        self::init();
    }

    public static function init()
    {
        self::$smsServiceUrl = env('SMS_SERVICE_MOBICA_URL', null);
        if (empty(self::$smsServiceUrl)) {
            throw new Exception('No api sms url defined!');
        }

        self::$smsServiceUser = env('SMS_SERVICE_MOBICA_USER', null);
        if (empty(self::$smsServiceUser)) {
            throw new Exception('No api sms user defined!');
        }

        self::$smsServicePass = env('SMS_SERVICE_MOBICA_PASS', null);
        if (empty(self::$smsServicePass)) {
            throw new Exception('No api sms password defined!');
        }

        self::$testPhone = env('SMS_SERVICE_TEST_PHONE', null);
    }

    /**
     * @param string $number
     * @param string $text
     *
     * @return mixed:stdClass|string
     */
    public static function send(string $number, string $text)
    {
        self::init();

        if (empty(self::$testPhone)) {
            throw new RuntimeException('No test phone provided!');
        }

        // overirte email on test env and if test email provided
        // $number = self::$testPhone;
        if (!isProd()) {
            $number = self::$testPhone;
        }


        if (empty($number)) {
            Log::channel('smsErrors')->error('No number provided, text: ' . $text); // logs/sms/errors.log
            return Sms::NEGATIVE_RESPONSE;
        }

        if (empty($text)) {
            Log::channel('smsErrors')->error('No text provided, number=' . $number); // logs/sms/errors.log
            return Sms::NEGATIVE_RESPONSE;
        }

        // add 359 to number
        if (substr($number, 0, 1) == '0') {
            $number = '359' . substr($number, 1);
        }


        // save money, send only live
        // if (!isProdOrStage()) {
        if (!isProd()) {
            return Sms::POSITIVE_RESPONSE;
        }


        try {

            $smsParams = [
                'phone'   => $number,
                'user'    => self::$smsServiceUser,
                'pass'    => self::$smsServicePass,
                'message' => $text,
            ];

            $message = '-BEGIN: number=' . $number . ', text: ' . $text;
            Log::channel('smsExec')->info($message); // logs/sms/exec.log


            $response = Curl::to(self::$smsServiceUrl)
                ->asJson()
                ->withHeader('Accept: application/json')
                ->withData($smsParams)
                //->enableDebug(storage_path('logs/smsLogs.txt'))
                ->post();

            // dd(
            //     'URL: ' . self::$smsServiceUrl,
            //     $smsParams = [
            //         'nmb_to' => $number,
            //         'user' => self::$smsServiceUser,
            //         'pass' => self::$smsServicePass,
            //         'text' => $text,
            //     ],
            //     '$response', json_encode($response)
            // );

            $message = '-END: number=' . $number . ', response: ' . json_encode($response);
            Log::channel('smsExec')->info($message); // logs/sms/exec.log

            if (empty($response)) {
                throw new Exception('No response from SMS api');
            }

            return json_encode($response);

        } catch (Throwable $e) {

            $message = 'number: ' . $number
                . ', text: ' . $text
                . '| error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            Log::channel('smsErrors')->error($message); // logs/sms/errors.log
        }

        return Sms::NEGATIVE_RESPONSE;
    }

    public static function sendOld(string $number, string $text)
    {
        self::init();

        if (empty(self::$testPhone)) {
            throw new RuntimeException('No test phone provided!');
        }

        if (empty($number)) {
            Log::channel('smsErrors')->error('No number provided, text: ' . $text); // logs/sms/errors.log
            return 'KO';
        }

        if (empty($text)) {
            Log::channel('smsErrors')->error('No text provided, number=' . $number); // logs/sms/errors.log
            return 'KO';
        }


        // save money, send only live
        // if (!isProdOrStage()) {
        // if (!isProd()) {
        //     return 'OK';
        // }


        // overirte number on test env and if test number provided
        // if (!isProdOrStage()) {
        // if (!isProd()) {
        //     if (empty(self::$testPhone)) {
        //         throw new Exception('No test phone provided!');
        //     }

        //     $number = self::$testPhone;
        // }


        try {

            $smsParams = [
                'nmb_to' => $number,
                'user' => self::$smsServiceUser,
                'pass' => self::$smsServicePass,
                'text' => $text,
            ];

            $message = '-BEGIN: number=' . $number . ', text: ' . $text;
            Log::channel('smsExec')->info($message); // logs/sms/exec.log

            $response = Curl::to(self::$smsServiceUrl)
                ->withData($smsParams)
                // In case a request fails, it might be useful to get debug the request.
                // ->enableDebug(storage_path('logs/sms/smsLogs.txt'))
                ->post();

        // dd(
        //     'URL: ' . self::$smsServiceUrl,
        //     $smsParams = [
        //         'nmb_to' => $number,
        //         'user' => self::$smsServiceUser,
        //         'pass' => self::$smsServicePass,
        //         'text' => $text,
        //     ],
        //     '$response', $response
        // );

            $message = '-END: number=' . $number . ', response: ' . $response;
            Log::channel('smsExec')->info($message); // logs/sms/exec.log

            if (empty($response)) {
                throw new Exception('No response from SMS api');
            }

            return $response;
        } catch (Throwable $e) {

            $message = 'number: ' . $number
                . ', text: ' . $text
                . '| error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            Log::channel('smsErrors')->error($message); // logs/sms/errors.log
        }

        return 'KO';
    }
}
