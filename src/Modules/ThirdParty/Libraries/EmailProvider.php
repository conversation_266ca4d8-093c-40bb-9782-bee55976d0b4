<?php

namespace Modules\ThirdParty\Libraries;

use \Exception;
use Illuminate\Mail\SentMessage;
use \Throwable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

final class EmailProvider
{
    private static $testEmail;
    private static $senderName;

    public function __construct()
    {
        self::$testEmail = env('MAIL_TEST_EMAIL', null);
        self::$senderName = env('MAIL_SENDER_NAME', '');
    }

    /**
     * Send email via mail provider
     *
     * @param string $from
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param array $attachments - format [['path' => xxx, 'name' => 'yyy', 'mime' => 'zzz']]
     * @return SentMessage|null
     * @throws Exception
     */
    public function sendEmail(
        string $from,
        string $to,
        string $subject,
        string $body,
        array  $attachments = []
    ): ?SentMessage {
        if (empty(self::$testEmail)) {
            throw new Exception('No test email provided!');
        }

        // overirte email on test env and if test email provided
        // $to = self::$testEmail;
        if (!isProd()) {
            $to = self::$testEmail;
        }

        if (empty($from)) {
            Log::channel('emailErrors')->error('No sender provided');
            return null;
        }

        if (empty($to)) {
            Log::channel('emailErrors')->error('No receiver provided');
            return null;
        }

        if (empty($subject)) {
            Log::channel('emailErrors')->error('No subject provided');
            return null;
        }

        if (empty($subject)) {
            Log::channel('emailErrors')->error('No body provided');
            return null;
        }

        // save money, send only live
        if (!isProdOrStage()) {
        // if (!isProd()) {
            return null;
        }

        try {
            $message = '-BEGIN: email=' . $to . ', subject: ' . $subject;
            Log::channel('emailExec')->info($message);

            /** @var SentMessage $response */
            $sentMessage = Mail::send([], [], function ($message) use ($from, $to, $subject, $body, $attachments) {

                // trackers
                $message->getHeaders()->addTextHeader('X-Mailgun-Track', 'yes');

                $message->from($from, self::$senderName);
                $message->to($to);
                $message->subject($subject);

                foreach ($attachments as $at) {
                    $message->attach($at['path'], ['as' => $at['name'], 'mime' => $at['mime']]);
                }

                $message->html($body);
            });

            Log::channel('emailExec')->info('-END: email=' . $to);

            return $sentMessage;
        } catch (Throwable $e) {
            $message = 'to: ' . $to
                . ', subject: ' . $subject
                . '| error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            Log::channel('emailErrors')->error($message);
        }

        return null;
    }
}
