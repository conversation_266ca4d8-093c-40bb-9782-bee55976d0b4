<?php

namespace Modules\ThirdParty\Libraries;

use \DOMDocument;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;
use Modules\ThirdParty\Exceptions\BnbException;
use Modules\ThirdParty\Services\PathService;
use Modules\ThirdParty\Traits\ParseTrait;

class Bnb
{
    use ParseTrait;

    const CERTIFICATE_FILE = 'key.pem';
    const CERTIFICATE_FODLER = 'Bnb';
    const DEFAULT_UPDATE_PERIOD = 15;

    private static $url;
    private static $username;
    private static $password;
    private static $udpatePeriodInDays;
    private static $certificatePassword;
    private static $certificatePath;

    public function __construct()
    {
        self::$url = env('CCR_URL', null);
        self::$username = env('CCR_USERNAME', null);
        self::$password = env('CCR_PASSWORD', null);
        self::$certificatePassword = env('CCR_CERTIFICATE_PASSWORD', null);

        if (
            empty(self::$url)
            || empty(self::$username)
            || empty(self::$password)
            || empty(self::$certificatePassword)
        ) {
            throw new BnbException('No credentials');
        }

        self::$certificatePath = PathService::getCertificatePath(
            self::CERTIFICATE_FODLER,
            self::CERTIFICATE_FILE
        );
        if (!file_exists(self::$certificatePath)) {
            throw new BnbException('No cerificate file');
        }
    }

    /**
     * [getDataByPin description]
     * @param  string $pin
     * @return array
     */
    public function getDataByPin(string $pin): array
    {
        Log::channel('bnbExec')->info('pin:' . $pin);

        $parameters = [
            'user' => self::$username,
            'pass' => self::$password,
            'identifier' => $pin,
        ];

        $response = Curl::to(self::$url)
            ->withData($parameters)
            ->withOption('SSLCERT', self::$certificatePath)
            ->get();

        if (!$response) {
            throw new BnbException('No response from BNB');
        }

        $result = [];
        try {
            $response = mb_convert_encoding($response, 'UTF-8', 'windows-1251');
            $dom = new DOMDocument();
            $dom->recover = true;
            $dom->encoding = 'windows-1251';
            $dom->loadXML($response, LIBXML_NOERROR);
            $payload = $dom->saveXML();
            $result = $this->parseXML($payload);
        } catch(\Exception $e) {
            throw new BnbException('Failed to parse BNB data');
        }

        return $result;
    }

    public function getDataByPinExtended(string $pin): array
    {
        Log::channel('bnbExec')->info('pin:' . $pin);

        $parameters = [
            'user' => self::$username,
            'pass' => self::$password,
            'identifier' => $pin,
        ];

        $responseRaw = Curl::to(self::$url)
            ->withData($parameters)
            ->withOption('SSLCERT', self::$certificatePath)
            ->get();

        if (!$responseRaw) {
            throw new BnbException('No response from BNB');
        }

        $result = [];
        try {

            $response = mb_convert_encoding($responseRaw, 'UTF-8', 'windows-1251');
            $dom = new DOMDocument();
            $dom->recover = true;
            $dom->encoding = 'windows-1251';
            $dom->loadXML($response, LIBXML_NOERROR);
            $payload = $dom->saveXML();
            $result = $this->parseXML($payload);

        } catch(\Exception $e) {
            throw new BnbException('Failed to parse BNB data');
        }

        return [
            'raw' => iconv('UTF-8', 'UTF-8//IGNORE', $responseRaw),
            'decoded' => $result,
        ];
    }

    /**
     * [getLastUpdateDate description]
     * @return Carbon
     */
    public static function getLastUpdateDate(): Carbon
    {
        $lastSyncDate = self::getNearestSyncDate();
        $lastSyncDate->hour(0)->minute(0)->second(0);
        return $lastSyncDate;
    }

    /**
     * Get last sync date
     * @return Carbon
     */
    public static function getNearestSyncDate(): Carbon
    {
        $now = Carbon::now();
        $updatePeriod = self::getUpdatePeriod();
        $currentMonthPeriod = Carbon::now()->day($updatePeriod);

        // if sync date is passed for current month we use current sync date
        if ($now->gt($currentMonthPeriod)) {
            return $currentMonthPeriod;
        }

        // else we use last month sync date
        return Carbon::now()->subMonth()->day($updatePeriod);
    }

     /**
     * [getUpdatePeriod description]
     * @return int
     */
    public static function getUpdatePeriod(): int
    {
        if (empty(self::$udpatePeriodInDays)) {
            self::$udpatePeriodInDays = env(
                'CCR_UPDATE_DATE',
                self::DEFAULT_UPDATE_PERIOD
            );
        }

        return self::$udpatePeriodInDays;
    }
}
