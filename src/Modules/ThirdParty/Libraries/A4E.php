<?php

namespace Modules\ThirdParty\Libraries;

use \stdClass;
use \Exception;
use \Throwable;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;
use Modules\ThirdParty\Exceptions\A4EException;

class A4E
{
    const API_HEADER_MONTHLY_PERFORMANCE = 'a4lending.monthlyBehUpload';
    const API_HEADER_APPLICATION_SCORE = 'a4lending.scoreApplicant';
    const API_HEADER_ORGANIZATION = 'stikcredit';
    const API_HEADER_LOCATION = 'BG';
    const ERROR_NO_CREDENTIALS = 'No credentials';

    private static $apiUrl;
    private static $apiToken;
    private static $apiUrlPerformance;

    public function __construct()
    {
        self::$apiUrl = env('A4_URL', null);
        self::$apiToken = env('A4_JWT_TOKEN', null);
        self::$apiUrlPerformance = env('A4_URL_PERFORMANCE', null);

        if (
            empty(self::$apiUrl)
            || empty(self::$apiToken)
            || empty(self::$apiUrlPerformance)
        ) {
            throw new A4EException(self::ERROR_NO_CREDENTIALS);
        }
    }

    public function getScoreResponse(array $data)
    {
        if (empty($data)) {
            Log::channel('a4eError')->error('Empty data provided');
            return '';
        }

        // $str = '{"records":[{"applicantData":{"credit_id":"1009845","created_at":"2023-11-02 17:57:23","sex_index":2,"age":20,"region_text":"\u0413\u0430\u0431\u0440\u043e\u0432\u043e","type_of_employment":"permanent-employment-contract","credit_type":"installment-credit","price_old":1400,"days_old":6,"installment":392.36,"referrer":"google","input_channel":"web","type_payment":"easypay","user_id":"145245","address":{"city":"\u0413\u0420.\u0421\u0415\u0412\u041b\u0418\u0415\u0412\u041e \u041e\u0431\u0449.\u0421\u0415\u0412\u041b\u0418\u0415\u0412\u041e \u041e\u0431\u043b.\u0413\u0410\u0411\u0420\u041e\u0412\u041e","address":"\u0423\u041b.\u0425\u0410\u041d \u0410\u0421\u041f\u0410\u0420\u0423\u0425 (\u0418\u0412\u0410\u041d \u041a\u0418\u0428\u041c\u0415\u0420\u041e\u0412), \u21163, \u0435\u0442\u0430\u0436 2, \u0430\u043f. 7","post_code":""},"history":{"788559":{"id":"788559","price":100,"days":30,"credit_type":"loan-to-salary","datetime":"2022-02-04 14:33:35","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":280.48,"max_overdue_days":96,"payments_count":1,"installments_paid":1},"930346":{"id":"930346","price":200,"days":30,"credit_type":"loan-to-salary","datetime":"2023-02-16 14:48:38","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":310.97,"max_overdue_days":23,"payments_count":1,"installments_paid":1},"944805":{"id":"944805","price":300,"days":30,"credit_type":"loan-to-salary","datetime":"2023-04-11 17:45:30","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":1,"installments_paid":1},"945424":{"id":"945424","price":600,"days":30,"credit_type":"loan-to-salary","datetime":"2023-04-14 06:53:51","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":1,"installments_paid":1},"952636":{"id":"952636","price":600,"days":3,"credit_type":"installment-credit","datetime":"2023-05-14 14:08:21","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":1402.82,"max_overdue_days":91,"payments_count":3,"installments_paid":3},"955796":{"id":"955796","price":800,"days":5,"credit_type":"installment-credit","datetime":"2023-05-24 17:54:18","status":"cancel","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":0,"installments_paid":0},"993338":{"id":"993338","price":200,"days":30,"credit_type":"loan-to-salary","datetime":"2023-09-16 11:36:32","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":1,"installments_paid":1},"994403":{"id":"994403","price":500,"days":30,"credit_type":"loan-to-salary","datetime":"2023-09-19 15:19:31","status":"refund","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":1,"installments_paid":1},"996263":{"id":"996263","price":1000,"days":7,"credit_type":"installment-credit","datetime":"2023-09-25 17:01:38","status":"cancel","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":0,"installments_paid":0},"1007067":{"id":"1007067","price":500,"days":30,"credit_type":"loan-to-salary","datetime":"2023-10-26 11:50:15","status":"cancel","remaining_amount":0,"overdue_days":0,"overdue_amount":0,"max_overdue_amount":0,"max_overdue_days":0,"payments_count":0,"installments_paid":0}},"ccr":17.94},"noi2":{"response":{"EgnInfo":{"EGN":"0","familyname":"\u0425\u0420\u0418\u0421\u0422\u041e\u0412","initials":"\u0418\u0414","City":"\u0413\u0420.\u0421\u0415\u0412\u041b\u0418\u0415\u0412\u041e","Address":"\u0423\u041b.\u0425\u0410\u041d \u0410\u0421\u041f\u0410\u0420\u0423\u0425\/\u0418\u0412\u0410\u041d \u041a\u0418\u0428\u041c\u0415\u0420\u041e\u0412\/,003, \u0415\u0422.02, \u0410\u041f.007","PostalCode":null},"PersonalInfo":[{"correctionflag":"0","Bulstat":"*********    ","Year":"2022","Month":"11  ","Typeofisured":"01","Workdays":"22","Salary":"1190.00","inputdate":"20\/12\/2022"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2022","Month":"12  ","Typeofisured":"01","Workdays":"19","Salary":"1127.36","inputdate":"16\/01\/2023"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2023","Month":"1   ","Typeofisured":"01","Workdays":"21","Salary":"1409.01","inputdate":"20\/02\/2023"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2023","Month":"2   ","Typeofisured":"01","Workdays":"20","Salary":"1275.90","inputdate":"17\/03\/2023"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2023","Month":"3   ","Typeofisured":"01","Workdays":"22","Salary":"1508.00","inputdate":"26\/04\/2023"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2023","Month":"5   ","Typeofisured":"01","Workdays":"20","Salary":"1296.20","inputdate":"27\/06\/2023"},{"correctionflag":"1","Bulstat":"*********    ","Year":"2023","Month":"4   ","Typeofisured":"01","Workdays":"18","Salary":"1277.74","inputdate":"24\/07\/2023"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2023","Month":"6   ","Typeofisured":"01","Workdays":"22","Salary":"2083.53","inputdate":"25\/07\/2023"},{"correctionflag":"0","Bulstat":"*********    ","Year":"2023","Month":"7   ","Typeofisured":"01","Workdays":"02","Salary":"138.09","inputdate":"28\/08\/2023"},{"correctionflag":"1","Bulstat":"*********    ","Year":"2023","Month":"5   ","Typeofisured":"01","Workdays":null,"Salary":"43.25","inputdate":"09\/06\/2023"}],"BulstatInfo":{"Bulstat":"*********    ","Name":"\u0425\u0438\u0442\u0430\u0447\u0438 \u0415\u043d\u0435\u0440\u0434\u0436\u0438 \u0411\u044a\u043b\u0433\u0430\u0440\u0438\u044f","address":"\u0433\u0440.\u0421\u0415\u0412\u041b\u0418\u0415\u0412\u041e,  \u0443\u043b. \u201c\u041d\u0438\u043a\u043e\u043b\u0430 \u041f\u0435\u0442\u043a\u043e\u0432\"  \u211632"}},"request_date":"2023-11-02"},"ccr":{"response":{"@code":"","@name":"\u0418\u0412\u0410\u041d \u0414\u041e\u041d\u0427\u0415\u0412 \u0425\u0420\u0418\u0421\u0422\u041e\u0412","section":[{"@entity-type":"nonbanks","active-credits":{"@cred-count":"7","@source-entity-count":"7","summaries":[{"@grouping-attribute":"type","summary":[{"@date-from":"2023-09-30","@type":"\u0413\u0430\u0440\u0430\u043d\u0446\u0438\u043e\u043d\u043d\u0438 \u0441\u0434\u0435\u043b\u043a\u0438 ","@amount-approved":"977","@amount-drawn":"0","@monthly-installment":"0","@outstanding-performing-principal":"0","@outstanding-overdue-principal":"0","@balance-sheet-value":"0","@off-balance-sheet-value":"1002","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"1002","@off-balance-sheet-unused":"0"},{"@date-from":"2023-09-30","@type":"\u041a\u0440\u0435\u0434\u0438\u0442\u043d\u0430 \u043b\u0438\u043d\u0438\u044f\/\u041a\u0440\u0435\u0434\u0438\u0442 \u0437\u0430 \u043e\u0431\u043e\u0440\u043e\u0442\u043d\u0438 \u0441\u0440\u0435\u0434\u0441\u0442\u0432\u0430","@amount-approved":"500","@amount-drawn":"0","@monthly-installment":"0","@outstanding-performing-principal":"0","@outstanding-overdue-principal":"0","@balance-sheet-value":"0","@off-balance-sheet-value":"500","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"500"},{"@date-from":"2023-09-30","@type":"\u041f\u043e\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u0441\u043a\u0438 \u043a\u0440\u0435\u0434\u0438\u0442","@amount-approved":"2227","@amount-drawn":"2227","@monthly-installment":"522.84","@outstanding-performing-principal":"1740","@outstanding-overdue-principal":"150","@balance-sheet-value":"2013.67","@off-balance-sheet-value":"0","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"0"}]},{"@grouping-attribute":"overdue-payment-period","summary":[{"@date-from":"2023-09-30","@overdue-payment-period":"\u043e\u0442 0 \u0434\u043e 30 \u0434\u043d\u0438","@amount-approved":"3554","@amount-drawn":"2077","@monthly-installment":"369","@outstanding-performing-principal":"1740","@outstanding-overdue-principal":"0","@balance-sheet-value":"1823.5","@off-balance-sheet-value":"1502","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"1002","@off-balance-sheet-unused":"500"},{"@date-from":"2023-09-30","@overdue-payment-period":"\u043e\u0442 61 \u0434\u043e 90 \u0434\u043d\u0438","@amount-approved":"150","@amount-drawn":"150","@monthly-installment":"153.84","@outstanding-performing-principal":"0","@outstanding-overdue-principal":"150","@balance-sheet-value":"190.17","@off-balance-sheet-value":"0","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"0"}]},{"@grouping-attribute":"rest","summary":[{"@date-from":"2023-09-30","@rest":"\u0421 \u0438\u0437\u0442\u0435\u043a\u044a\u043b \u0441\u0440\u043e\u043a \u0434\u043e \u043f\u0430\u0434\u0435\u0436\u0430","@amount-approved":"150","@amount-drawn":"150","@monthly-installment":"153.84","@outstanding-performing-principal":"0","@outstanding-overdue-principal":"150","@balance-sheet-value":"190.17","@off-balance-sheet-value":"0","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"0"},{"@date-from":"2023-09-30","@rest":"\u0414\u043e \u0435\u0434\u043d\u0430 \u0433\u043e\u0434\u0438\u043d\u0430","@amount-approved":"3054","@amount-drawn":"2077","@monthly-installment":"369","@outstanding-performing-principal":"1740","@outstanding-overdue-principal":"0","@balance-sheet-value":"1823.5","@off-balance-sheet-value":"1002","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"1002","@off-balance-sheet-unused":"0"},{"@date-from":"2023-09-30","@rest":"\u041d\u0430\u0434 \u0435\u0434\u043d\u0430 \u0433\u043e\u0434\u0438\u043d\u0430","@amount-approved":"500","@amount-drawn":"0","@monthly-installment":"0","@outstanding-performing-principal":"0","@outstanding-overdue-principal":"0","@balance-sheet-value":"0","@off-balance-sheet-value":"500","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"500"}]},{"@grouping-attribute":"term","summary":[{"@date-from":"2023-09-30","@term":"\u0414\u043e \u0435\u0434\u043d\u0430 \u0433\u043e\u0434\u0438\u043d\u0430","@amount-approved":"3204","@amount-drawn":"2227","@monthly-installment":"522.84","@outstanding-performing-principal":"1740","@outstanding-overdue-principal":"150","@balance-sheet-value":"2013.67","@off-balance-sheet-value":"1002","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"1002","@off-balance-sheet-unused":"0"},{"@date-from":"2023-09-30","@term":"\u041d\u0430\u0434 \u0435\u0434\u043d\u0430 \u0433\u043e\u0434\u0438\u043d\u0430","@amount-approved":"500","@amount-drawn":"0","@monthly-installment":"0","@outstanding-performing-principal":"0","@outstanding-overdue-principal":"0","@balance-sheet-value":"0","@off-balance-sheet-value":"500","@off-balance-sheet-dues":"0","@off-balance-sheet-conditional":"0","@off-balance-sheet-unused":"500"}]}]},"overdue-history":{"summary":[{"@active":"true","@year":"2023","@overdue-payment-period":"\u043e\u0442 31 \u0434\u043e 60 \u0434\u043d\u0438","@months-count":"2","@max-cred-count":"1","@max-outstanding-overdue-principal":"150","@max-outstanding-overdue-interest-and-others":"13","@max-off-balance-sheet-dues":"0"},{"@active":"true","@year":"2023","@overdue-payment-period":"\u043e\u0442 61 \u0434\u043e 90 \u0434\u043d\u0438","@months-count":"2","@max-cred-count":"1","@max-outstanding-overdue-principal":"150","@max-outstanding-overdue-interest-and-others":"20","@max-off-balance-sheet-dues":"0"},{"@active":"false","@year":"2022","@overdue-payment-period":"\u043e\u0442 31 \u0434\u043e 60 \u0434\u043d\u0438","@months-count":"1","@max-cred-count":"1","@max-outstanding-overdue-principal":"100","@max-outstanding-overdue-interest-and-others":"5","@max-off-balance-sheet-dues":"0"},{"@active":"false","@year":"2022","@overdue-payment-period":"\u043e\u0442 61 \u0434\u043e 90 \u0434\u043d\u0438","@months-count":"1","@max-cred-count":"1","@max-outstanding-overdue-principal":"100","@max-outstanding-overdue-interest-and-others":"6","@max-off-balance-sheet-dues":"0"},{"@active":"false","@year":"2023","@overdue-payment-period":"\u043e\u0442 31 \u0434\u043e 60 \u0434\u043d\u0438","@months-count":"2","@max-cred-count":"3","@max-outstanding-overdue-principal":"480.77","@max-outstanding-overdue-interest-and-others":"52.25","@max-off-balance-sheet-dues":"131.02"},{"@active":"false","@year":"2023","@overdue-payment-period":"\u043e\u0442 61 \u0434\u043e 90 \u0434\u043d\u0438","@months-count":"1","@max-cred-count":"3","@max-outstanding-overdue-principal":"774.66","@max-outstanding-overdue-interest-and-others":"70.87","@max-off-balance-sheet-dues":"196.53"}]},"new-credits":null,"related-active-credits":{"credits-set":[{"@person-role":"\u0421\u044a\u0434\u043b\u044a\u0436\u043d\u0438\u043a"},{"@person-role":"\u041f\u043e\u0440\u044a\u0447\u0438\u0442\u0435\u043b"}]}}]},"request_date":"2023-11-02"}}],"scoreCards":["Scorecard1"]}';

        // $data = json_decode($str, true);

        try {
            $response = Curl::to(self::$apiUrl)
                ->withHeaders([
                    'organization: ' . self::API_HEADER_ORGANIZATION,
                    'location: ' . self::API_HEADER_LOCATION,
                    'application: ' . self::API_HEADER_APPLICATION_SCORE,
                    'Authorization: Bearer ' . self::$apiToken
                ])
                ->withOption('TIMEOUT', 15)
                ->withData($data)
                ->asJson()
                ->returnResponseObject()
                ->withResponseHeaders()
                ->post();

// dump('request data', $data);
// dd('url=' . self::$apiUrl, 'token=' . self::$apiToken,  '$response', $response);

            if (empty($response)) {
                throw new Exception('A4E returned nothing');
            }

            return $response;

        } catch (Throwable $t) {
            Log::channel('a4eError')->error(
                'Error: ' . $t->getMessage()
                . ', file: ' . $t->getFile()
                . ', line: ' . $t->getLine()
            );
        }

        return '';
    }

    public function sendMonthlyPerformance(
        string $absoluteFilePath,
        bool $test = false
    ) {

        if (empty($absoluteFilePath)) {
            Log::channel('a4eError')->error('Error performance: Empty file path');
            return null;
        }

        if (!file_exists($absoluteFilePath)) {
            Log::channel('a4eError')->error('Error performance: Unexisting file: ' . $absoluteFilePath);
            return null;
        }

        $data = [];
        if ($test) {
            $data = ['testUpload' => true];
        }

        try {

            $response = Curl::to(self::$apiUrlPerformance)
            ->withHeaders([
                'organization: ' . self::API_HEADER_ORGANIZATION,
                'location: ' . self::API_HEADER_LOCATION,
                'application: ' . self::API_HEADER_MONTHLY_PERFORMANCE,
                'Authorization: Bearer ' . self::$apiToken,
                'Content-Type: multipart/form-data',
            ])
            ->withData($data)
            ->withFile('file', $absoluteFilePath)
            ->returnResponseObject()
            ->withResponseHeaders()
            ->post();

            if (empty($response)) {
                throw new Exception('A4E.Performance returned nothing');
            }

            return $response;

        } catch (Throwable $t) {
            Log::channel('a4eError')->error(
                'Error performance: ' . $t->getMessage()
                . ', file: ' . $t->getFile()
                . ', line: ' . $t->getLine()
            );
        }

        return null;
    }
}
