<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Libraries;

use SoapClient;
use SoapHeader;
use SoapFault;

class InsuranceClient
{
    private SoapClient $soap;
    private array $config = [];

    public function __construct(array $config)
    {
        $this->config = $config;

        $wsdlOrNull = $config['wsdl'] ?? null;
        $options = $config['soap_options'] ?? [];

        // Recommended options:
        $defaultOptions = [
            'trace' => true,
            'exceptions' => true,
            'cache_wsdl' => WSDL_CACHE_NONE,
            'connection_timeout' => 30,
        ];

        // Override service location if provided
        if (!empty($config['service_url'])) {
            $defaultOptions['location'] = $config['service_url'];
        }

        $options = $options + $defaultOptions;

        try {
            $this->soap = new SoapClient($wsdlOrNull, $options);
        } catch (SoapFault $e) {
            throw new \RuntimeException('Failed to initialize SOAP client: ' . $e->getMessage(), 0, $e);
        }

        // attach authentication header if provided
        if (!empty($config['username']) && array_key_exists('password', $config)) {
            $this->setAuthHeader($config['username'], $config['password'], $config['auth_namespace'] ?? null);
        }
    }

    public function setAuthHeader(string $username, string $password, ?string $namespace = null): void
    {
        $ns = $namespace ?? ($this->config['auth_namespace'] ?? 'urn:Auth');
        $auth = [
            'username' => $username,
            'password' => $password,
        ];
        $header = new SoapHeader($ns, 'Auth', $auth);
        $this->soap->__setSoapHeaders([$header]);
    }

    public function createInsuranceOffer(array $payload): array
    {
        $resp = $this->soapCall('createInsuranceOfferWService', $payload);
        return $this->toArray($resp);
    }

    public function updateInsuranceOffer(string $offerUuid, array $payload): array
    {
        $params = $payload + ['offerUUID' => $offerUuid];
        $resp = $this->soapCall('updateInsuranceOfferWService', $params);
        return $this->toArray($resp);
    }

    public function createCertificate(string $offerUuid, array $insuredData): array
    {
        $params = ['offerUUID' => $offerUuid] + $insuredData;
        $resp = $this->soapCall('createCertificateWService', $params);
        return $this->toArray($resp);
    }

    public function getCertificatePdf(int $certificateId): string
    {
        $resp = $this->soapCall('getCertificatePDFWService', ['certificateId' => $certificateId]);
        $arr = $this->toArray($resp);

        // Response field name depends on WS; commonly certificateFileAsPDF (base64)
        $keyCandidates = ['certificateFileAsPDF', 'certificateFile', 'fileAsPDF', 'pdf'];
        foreach ($keyCandidates as $k) {
            if (isset($arr[$k]) && !empty($arr[$k])) {
                // Some implementations return raw binary or base64 string
                $data = $arr[$k];
                // detect base64
                if ($this->looksLikeBase64($data)) {
                    return base64_decode($data);
                }
                return $data;
            }
        }

        throw new \RuntimeException('PDF not present in response');
    }

    public function getInsuranceDocuments(): array
    {
        $resp = $this->soapCall('getInsuranceDocuments', []);
        return $this->toArray($resp);
    }

    public function invalidateCertificate(int $certificateId): array
    {
        $resp = $this->soapCall('doInvalidateCertficateWService', ['certificateId' => $certificateId]);
        return $this->toArray($resp);
    }

    private function looksLikeBase64(string $s): bool
    {
        // crude check: long string and valid base64 characters
        if (strlen($s) < 100) {
            return false;
        }
        return preg_match('#^[A-Za-z0-9+\\/\\r\\n]+={0,2}$#', trim($s)) === 1;
    }

    private function soapCall(string $action, array $params = [])
    {
        return $this->soap->__soapCall($action, [$params]);
    }

    private function getLastResponse(): string
    {
        try {
            return $this->soap->__getLastResponse() ?? '';
        } catch (\Throwable $e) {
            return '';
        }
    }

    private function toArray($obj): array
    {
        if ($obj instanceof \stdClass) {
            return json_decode(json_encode($obj), true);
        }
        if (is_array($obj)) {
            return $obj;
        }
        return ['result' => $obj];
    }
}
