<?xml version='1.0' encoding='UTF-8'?>
<wsdl:definitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://risk.uni.wsgw.scli.ee.eil.bg/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" name="RISKCertificateFactoryWS" targetNamespace="http://risk.uni.wsgw.scli.ee.eil.bg/">
  <wsdl:types>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://risk.uni.wsgw.scli.ee.eil.bg/" elementFormDefault="unqualified" targetNamespace="http://risk.uni.wsgw.scli.ee.eil.bg/" version="1.0">
      <xs:element name="createCertificateWService" type="tns:createCertificateWService"/>
      <xs:element name="createCertificateWServiceResponse" type="tns:createCertificateWServiceResponse"/>
      <xs:element name="createInsuranceOfferWService" type="tns:createInsuranceOfferWService"/>
      <xs:element name="createInsuranceOfferWServiceResponse" type="tns:createInsuranceOfferWServiceResponse"/>
      <xs:element name="doInvalidateCertficateWService" type="tns:doInvalidateCertficateWService"/>
      <xs:element name="doInvalidateCertficateWServiceResponse" type="tns:doInvalidateCertficateWServiceResponse"/>
      <xs:element name="getCertificatePDFWService" type="tns:getCertificatePDFWService"/>
      <xs:element name="getCertificatePDFWServiceResponse" type="tns:getCertificatePDFWServiceResponse"/>
      <xs:element name="getInsuranceDocuments" type="tns:getInsuranceDocuments"/>
      <xs:element name="getInsuranceDocumentsResponse" type="tns:getInsuranceDocumentsResponse"/>
      <xs:complexType name="getInsuranceDocuments">
        <xs:sequence/>
      </xs:complexType>
      <xs:complexType name="getInsuranceDocumentsResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="return" type="tns:insProductsDocumentsVO"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="insProductsDocumentsVO">
        <xs:complexContent>
          <xs:extension base="tns:baseResponseVO">
            <xs:sequence>
              <xs:element minOccurs="0" name="jsonDocsData" type="xs:string"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="baseResponseVO">
        <xs:sequence>
          <xs:element name="resultCode" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="createInsuranceOfferWService">
        <xs:sequence>
          <xs:element name="insAmount" type="xs:int"/>
          <xs:element name="insDuration" type="xs:int"/>
          <xs:element minOccurs="0" name="insDurationType" type="xs:string"/>
          <xs:element name="otherParameters" type="xs:string"/>
          <xs:element name="operationType" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="createInsuranceOfferWServiceResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="return" type="tns:offerResultVO"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="offerResultVO">
        <xs:complexContent>
          <xs:extension base="tns:baseResponseVO">
            <xs:sequence>
              <xs:element minOccurs="0" name="offerUUID" type="xs:string"/>
              <xs:element name="premiumAmount" type="xs:double"/>
              <xs:element name="premiumAmountVal" type="xs:string"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="getCertificatePDFWService">
        <xs:sequence>
          <xs:element name="certificateId" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="getCertificatePDFWServiceResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="return" type="tns:certificatePDFResultVO"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="certificatePDFResultVO">
        <xs:complexContent>
          <xs:extension base="tns:baseResponseVO">
            <xs:sequence>
              <xs:element minOccurs="0" name="certificateFileAsPDF" type="xs:base64Binary"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="createCertificateWService">
        <xs:sequence>
          <xs:element name="offerUUID" type="xs:string"/>
          <xs:element name="insuredNames" type="xs:string"/>
          <xs:element name="insuredPersonalIdn" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="createCertificateWServiceResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="return" type="tns:createCertificateResultVO"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="createCertificateResultVO">
        <xs:complexContent>
          <xs:extension base="tns:baseResponseVO">
            <xs:sequence>
              <xs:element minOccurs="0" name="certificateId" type="xs:int"/>
              <xs:element minOccurs="0" name="certificateNo" type="xs:string"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="doInvalidateCertficateWService">
        <xs:sequence>
          <xs:element name="certificateId" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="doInvalidateCertficateWServiceResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="return" type="tns:baseResponseVO"/>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="getInsuranceDocuments">
    <wsdl:part element="tns:getInsuranceDocuments" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="getCertificatePDFWService">
    <wsdl:part element="tns:getCertificatePDFWService" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="getCertificatePDFWServiceResponse">
    <wsdl:part element="tns:getCertificatePDFWServiceResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="createCertificateWServiceResponse">
    <wsdl:part element="tns:createCertificateWServiceResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="doInvalidateCertficateWServiceResponse">
    <wsdl:part element="tns:doInvalidateCertficateWServiceResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="getInsuranceDocumentsResponse">
    <wsdl:part element="tns:getInsuranceDocumentsResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="createInsuranceOfferWService">
    <wsdl:part element="tns:createInsuranceOfferWService" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="createInsuranceOfferWServiceResponse">
    <wsdl:part element="tns:createInsuranceOfferWServiceResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="createCertificateWService">
    <wsdl:part element="tns:createCertificateWService" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="doInvalidateCertficateWService">
    <wsdl:part element="tns:doInvalidateCertficateWService" name="parameters"/>
  </wsdl:message>
  <wsdl:portType name="IRISKCertificateUniFactoryWS">
    <wsdl:operation name="getInsuranceDocuments">
      <wsdl:input message="tns:getInsuranceDocuments" name="getInsuranceDocuments"/>
      <wsdl:output message="tns:getInsuranceDocumentsResponse" name="getInsuranceDocumentsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="createInsuranceOfferWService">
      <wsdl:input message="tns:createInsuranceOfferWService" name="createInsuranceOfferWService"/>
      <wsdl:output message="tns:createInsuranceOfferWServiceResponse" name="createInsuranceOfferWServiceResponse"/>
    </wsdl:operation>
    <wsdl:operation name="getCertificatePDFWService">
      <wsdl:input message="tns:getCertificatePDFWService" name="getCertificatePDFWService"/>
      <wsdl:output message="tns:getCertificatePDFWServiceResponse" name="getCertificatePDFWServiceResponse"/>
    </wsdl:operation>
    <wsdl:operation name="createCertificateWService">
      <wsdl:input message="tns:createCertificateWService" name="createCertificateWService"/>
      <wsdl:output message="tns:createCertificateWServiceResponse" name="createCertificateWServiceResponse"/>
    </wsdl:operation>
    <wsdl:operation name="doInvalidateCertficateWService">
      <wsdl:input message="tns:doInvalidateCertficateWService" name="doInvalidateCertficateWService"/>
      <wsdl:output message="tns:doInvalidateCertficateWServiceResponse" name="doInvalidateCertficateWServiceResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="RISKCertificateFactoryWSSoapBinding" type="tns:IRISKCertificateUniFactoryWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getInsuranceDocuments">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getInsuranceDocuments">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getInsuranceDocumentsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createInsuranceOfferWService">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="createInsuranceOfferWService">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createInsuranceOfferWServiceResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCertificatePDFWService">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getCertificatePDFWService">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getCertificatePDFWServiceResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createCertificateWService">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="createCertificateWService">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createCertificateWServiceResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="doInvalidateCertficateWService">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="doInvalidateCertficateWService">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="doInvalidateCertficateWServiceResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="RISKCertificateFactoryWS">
    <wsdl:port binding="tns:RISKCertificateFactoryWSSoapBinding" name="RISKCertificateFactoryWSPort">
      <soap:address location="http://localhost:8080/MVInsWS/ws/RISKCertificate"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
