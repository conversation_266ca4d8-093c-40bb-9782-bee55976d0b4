<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;
use Modules\ThirdParty\Http\Controllers\ReportStatsController;
use Modules\ThirdParty\Http\Controllers\A4eReportsCheckController;

Route::prefix('thirdparty')->group(function() {
    // Third Party Checks
    Route::get('/stats', [ReportStatsController::class, 'index'])
        ->name('thirdparty.stats')
        ->defaults('description', 'View reports page')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'External Services Checks')
        ->defaults('info_bubble', 'Вижда справка Проверки в служби');

    Route::get('/stats/refresh', [ReportStatsController::class, 'refresh'])
        ->name('thirdparty.stats.refresh');

    Route::get('/stats/export', [ReportStatsController::class, 'export'])
        ->name('thirdparty.stats.export')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'External Services Checks')
        ->defaults('description', 'Export data');


    Route::get('/a4e-reports-check', [A4eReportsCheckController::class, 'index'])
        ->name('thirdparty.a4e-reports-check')
        ->defaults('description', 'View scoring JSON check report')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Scoring Check Report')
        ->defaults('info_bubble', 'Може да вижда справка Скоринг проверка. В справката се вижда изпратени/получени данни от/към А4Е.');

});
