<?php

namespace Modules\ThirdParty\Jobs;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Models\Loan;
use Modules\Common\Traits\ReportsChainTrait;
use Modules\ThirdParty\Libraries\Nsi;
use Modules\ThirdParty\Services\ReportService;

/**
 * Getting NOI reports on backgoround
 *
 * Scenario:
 * 1. Logic depends on office:
 * - if online always run report
 * - if office, run report depends on office settings
 *
 * 2. Logic for getting a report
 * - check in our DB for such report and it should be not older than 60 days
 * - check in D&A and it should be not older than 60 days
 * - check in NOI
 *
 * Automatic report is searching only for NOI2 report, other reports are for manual handling
 *
 * Allow getting reports for client and for loan(create relations)
 *
 * For now we're getting noi reports from Date Technology
 *
 */
class NoiReportsJob extends CommonJob
{
    use ReportsChainTrait;

    const META_KEY = 'NoiReportsJob';

    public $tries = 3; // Increase this number as needed
    public $timeout = 120; // Timeout in seconds

    public $queue = 'reports';

    private array $reportNumbers = []; // TODO

    protected $logChannel = 'noiError';
    protected $queueName = 'reports';

    private int $agePensioner = 65;

    public function __construct(
        private readonly ?string $pin = null,
        private readonly ?Loan $loan = null,
        private readonly ?int $reportNumber = null,
        private readonly int $try = 1,
        private readonly ReportService $reportService = new ReportService
    ) {
    }

    public function handle(): void
    {
        $loanId = ($this->loan->loan_id ?? null);
        $this->startLog($loanId, $this->try, $this->getClassName());

        if (!$loanId) {
            $this->release();
            return;
        }

        if (!$this->pin && !$this->reportNumber) {
            $this->release();
            return;
        }

        // Avoiding parallel processes
        $lockKey = "noi-job-lock-loan-{$loanId}";
        $lock = Cache::lock($lockKey, 10);
        if (!$lock->get()) {
            $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Skip: processing with another job');

            $errorMsg = "Skipped NoiReportsJob: another job is processing loan #{$loanId}";
            $this->log($errorMsg);
            $this->finishedLog(0, $errorMsg);

            return ;
        }


        try {
            $reportArray = $this->reportService->addNoiReport(
                $this->loan->client,
                $this->reportNumber,
                'job',
                $this->loan,
                false,
                $this->try
            );

            if (empty($reportArray[0]->noi_report_id)) {
                $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Fail: bad report');
                $this->release((int) config('reports.noi' . $this->reportNumber . '.tries.' . $this->try, 0));

            } else {
                $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Success: received data');
            }

        } catch (\Throwable $e) {
            $reportArray = [];

            $msg = 'Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();
            $this->log($msg);

            $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', getMetaException($msg));

        } finally {
            $lock->release();
        }
    }

    /**
     * Used in: NoiReportListener - when we create loan from the office
     *
     * @param Loan $loan
     * @param int $reportNumber
     * @return bool
     */
    public function pushLoanToQueue(
        Loan $loan,
        int $reportNumber = Nsi::REPORT_ID_SHORT,
        int $delayInSec = 0
    ): bool {

        $client = $loan->client;
        if (empty($client->pin)) {
            return false;
        }

        if ($reportNumber === Nsi::REPORT_ID_SHORT) {
            try {
                $age = (int) $client->getAgeAndSex($client->pin)['age'];
                if ($age > $this->agePensioner) {
                    $reportNumber = Nsi::REPORT_ID_RETIRED;
                }
            } catch (\Throwable $e) {
                $this->log(
                    $e->getMessage()
                    . ', ' . $e->getFile()
                    . ': ' . $e->getLine()
                );
            }
        }

        if ($delayInSec > 0) {
            $now = Carbon::now();
            self::dispatch($client->pin, $loan, $reportNumber)->delay($now->addSeconds($delayInSec));
        } else {
            self::dispatch($client->pin, $loan, $reportNumber);
        }

        return true;
    }
}
