<?php

namespace Modules\ThirdParty\Jobs;

use \Throwable;
use Carbon\Carbon;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Traits\ReportsChainTrait;
use Modules\ThirdParty\Services\AutegryService;
use Modules\Common\Models\AutegryReport;
use Modules\Common\Models\Loan;

class AutegryReportJob extends CommonJob
{
    use ReportsChainTrait;

    const MAX_TRY = 3;

    private $try = 1;
    private $loan = null;
    private $service = null;

    protected $logChannel = 'autegryError';
    protected $queueName  = 'reports';

    public function __construct(Loan $loan = null, int $try = 1)
    {
        $this->try = $try;
        $this->loan = $loan;
    }

    public function handle()
    {
        $this->startLog(
            ($this->loan->loan_id ?? null),
            $this->try,
            $this->getClassName()
        );


        if ($this->try > self::MAX_TRY) {
            $msg = 'Too many bad tryies, loan #' . ($this->loan->loan_id ?? '');

            $this->log($msg);
            $this->finishedLog(0, $msg);

            return false;
        }


        if (empty($this->loan->loan_id)) {
            $msg = 'There are no mandatory params';

            $this->log($msg);
            $this->finishedLog(0, $msg);

            return false;
        }


        try {

            $newTry = 1 + $this->try;

            $autegryReport = app(AutegryService::class)->getScoringForLoan(
                $this->loan->client,
                $this->loan,
                'job',
                $this->try
            );

            if (
                empty($autegryReport->id)
                // || empty($autegryReport->zone)
            ) {
                $msg = 'Bad report, loan #' . ($this->loan->loan_id ?? '');

                if ($newTry <= self::MAX_TRY) {
                    $waitSeconds = (int) config('reports.autegry.tries.' . $newTry, 0);
                    $this->pushLoanToQueue(
                        $this->loan,
                        $newTry,
                        $waitSeconds
                    );

                    $msg .= '. Send next try, with delay: ' . $waitSeconds;
                }

                $this->log($msg);
                $this->finishedLog(1, $msg);

                return false;
            }



            $msg = 'Get autegry report #' . $autegryReport->id;
            $this->log($msg);
            $this->finishedLog(1, $msg);

            return true;

        } catch (Throwable $e) {
            $msg = 'Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            $this->log($msg);
            $this->finishedLog(0, $msg);

            return false;
        }
    }

    /**
     * Used in: ReportJobsDispatcher
     *
     * @param  Loan  $loan
     * @param  int   $try
     * @param  int   $delayInSec
     *
     * @return bool
     */
    public function pushLoanToQueue(
        Loan $loan,
        int $try = 1,
        int $delayInSec = 0
    ): bool {

        $queueName = $this->getQueueName();

        if ($delayInSec > 0) {
            $now = Carbon::now();

            self::dispatch($loan, $try)
                ->onQueue($queueName)
                ->delay($now->addSeconds($delayInSec));

            return true;
        }

        self::dispatch($loan, $try)->onQueue($queueName);

        return true;
    }
}
