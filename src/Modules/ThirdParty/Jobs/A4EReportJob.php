<?php

namespace Modules\ThirdParty\Jobs;

use \Throwable;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Traits\ReportsChainTrait;
use Modules\Head\Jobs\CreditLimitJob;
use Modules\ThirdParty\Services\A4EService;
use Modules\Common\Models\A4EReport;
use Modules\Common\Models\Loan;

/**
 * Used in: A4EReportListener
 * Getting A4E reports on background
 *
 * Logic:
 * 1. separately we are getting Noi & Ckr report, wo if we don't have them till the time, we re-try(3 times: 0-30-50 sec)
 * 2. send what we have to A4E and get scoring, if no result just simulate it with Empty Not-Save A4e report
 * 3. run CreditLimitJob
 */
class A4EReportJob extends CommonJob
{
    use ReportsChainTrait;

    private const MAX_TRY  = 3;
    private const META_KEY = 'A4EReportJob';

    private $try = 1;
    private $loan = null;

    private $a4eReport = null;
    private $ccrReport = null;
    private $noiReports = [];

    protected $logChannel = 'a4eError';
    protected $queueName  = 'reports';

    public function __construct(Loan $loan = null, int $try = 1)
    {
        $this->try = $try;
        $this->loan = $loan;
    }

    public function pushLoanToQueue(
        Loan $loan,
        int $try = 1,
        int $delayInSec = 0
    ): bool {

        $queueName = $this->getQueueName();

        if ($delayInSec > 0) {
            $now = Carbon::now();

            self::dispatch($loan, $try)
                ->onQueue($queueName)
                ->delay($now->addSeconds($delayInSec));

            return true;
        }

        self::dispatch($loan, $try)->onQueue($queueName);

        return true;
    }

    public function handle(): bool
    {
        $loanId = $this->loan->loan_id ?? null;
        $this->startLog($loanId, $this->try, $this->getClassName());

        if ($this->shouldStopJob()) {
            return false;
        }

        // Avoiding parallel processes
        $lockKey = "a4e-job-lock-loan-{$loanId}";
        $lock = Cache::lock($lockKey, 10);
        if (!$lock->get()) {
            $this->loan->addMeta(self::META_KEY . "({$this->try})", 'Skip: processing with another job');

            $errorMsg = "Skipped A4EReportJob: another job is processing loan #{$loanId}";
            $this->log($errorMsg);
            $this->finishedLog(0, $errorMsg);

            return false;
        }

        try {
            $client = $this->loan->client;

            if (!$this->hasCcrReport($client)) {
                return false;
            }

            if (!$this->hasNoiReports($client)) {
                return false;
            }

            if (!$this->hasA4eReport($client)) {
                return false;
            }

            $this->loan->addMeta(self::META_KEY . "({$this->try})", 'Success: run CreditLimitJob');
            (new CreditLimitJob)->pushLoanToQueue($this->loan, $this->a4eReport);

            $msg = "Get report #{$this->a4eReport->a4e_report_id}. Run: credit limits, auto process";
            $this->log($msg);
            $this->finishedLog(1, $msg);

            return true;

        } catch (Throwable $e) {
            $msg = "Error: {$e->getMessage()}, file: {$e->getFile()}, line: {$e->getLine()}";

            $this->log($msg);
            $this->finishedLog(0, $msg);
            $this->loan->addMeta(self::META_KEY . "({$this->try})", getMetaException($msg));

            return false;

        } finally {
            $lock->release();
        }
    }

    private function shouldStopJob(): bool
    {
        if (empty($this->loan->loan_id)) {
            $msg = "There are no mandatory params";
            $this->logFailure($msg, 'Fail: no mandatory params');

            return true;
        }

        if ($this->try > self::MAX_TRY) {
            $msg = "Too many bad attempts, loan #{$this->loan->loan_id}";
            $this->logFailure($msg, 'Fail: too many bad attempts');

            return true;
        }

        return false;
    }

    private function logFailure(string $msg, string $meta): void
    {
        $this->loan->addMeta(self::META_KEY . "({$this->try})", $meta);

        $this->log($msg);

        $this->finishedLog(0, $msg);
    }

    private function hasCcrReport($client): bool
    {
        $this->ccrReport = $client->getLastCcrReport();
        if (!empty($this->ccrReport->ccr_report_id)) {
            return true;
        }

        $this->loan->addMeta(self::META_KEY . "({$this->try})", 'Fail: no CCR report');

        if ($this->try < self::MAX_TRY) {
            $wait = (int) config("reports.ccr.tries.$this->try", 5);
            $this->pushLoanToQueue($this->loan, ($this->try + 1), $wait);
            $this->finishedLog(0, "No ccr report. Wait: $wait");

            return false;
        }

        // Final try — log but continue
        $this->log("No CCR report on final try, continuing with fallback.");

        return true;
    }

    private function hasNoiReports($client): bool
    {
        $this->noiReports = $client->getLastNoiReports(['noi2', 'noi51']);
        if (!empty($this->noiReports)) {
            return true;
        }

        $this->loan->addMeta(self::META_KEY . "({$this->try})", 'Fail: no NOI reports');

        // Allow fallback on last try
        if ($this->try < self::MAX_TRY) {
            $wait = (int) config("reports.noi2.tries.$this->try", 5);
            $this->pushLoanToQueue($this->loan, ($this->try  +1), $wait);
            $this->finishedLog(0, "No noi report. Wait: $wait");

            return false;
        }

        // Final try — log but continue
        $this->log("No NOI reports on final try, continuing with fallback.");

        return true;
    }

    private function hasA4eReport($client): bool
    {
        try {

            $service = app(A4EService::class);

            $data = $service->getPreparedData(
                $this->loan,
                $client,
                $this->ccrReport,
                $this->noiReports
            );

            $this->a4eReport = $service->getScoringForLoan(
                $this->loan,
                $data,
                'job',
                $this->try
            );

            if (empty($this->a4eReport->gb) && empty($this->a4eReport->gbr)) {
                $this->loan->addMeta(self::META_KEY . "({$this->try})", 'Fail: bad report');

                if ($this->try < self::MAX_TRY) {
                    $wait = (int) config("reports.a4e.tries.$this->try", 0);
                    $this->pushLoanToQueue($this->loan, ($this->try + 1), $wait);

                    $msg = "Bad report, loan #{$this->loan->loan_id}. Send next try, with delay: $wait";
                    $this->log($msg);
                    $this->finishedLog(0, $msg);

                    return false;
                }

                $this->loan->addMeta(self::META_KEY . "({$this->try})", 'Info: emulate report');
                $this->a4eReport = new A4EReport;
                $this->a4eReport->a4e_report_id = 1;
            }

            return true;

        } catch (Throwable $e) {
            $msg = "Error: {$e->getMessage()}, file: {$e->getFile()}, line: {$e->getLine()}";

            $this->log($msg);
            $this->finishedLog(0, $msg);

            $this->loan->addMeta(self::META_KEY . "({$this->try})", getMetaException($msg));

            return false;
        }
    }
}
