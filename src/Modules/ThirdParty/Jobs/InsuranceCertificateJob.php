<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\Loan;
use Modules\Docs\Services\ClientDocumentService;
use Modules\ThirdParty\Libraries\InsuranceClient;
use Modules\ThirdParty\Services\InsuranceService;
use RuntimeException;

class InsuranceCertificateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const META_KEY = 'InsuranceCertificateJob';
    public const MAX_TRIES = 1;

    public int $tries = self::MAX_TRIES;
    public int $timeout = 120; // 2 minutes

    public function __construct(public Loan $loan, public int $try = 1)
    {
        $this->onQueue('commands');
    }

    public function handle(): void
    {
        $loanId = $this->loan->loan_id ?? null;
        if (!$loanId) {
            \Log::error('Error InsuranceCertificateJob: no loan_id provided');
            return;
        }

        try {
            $this->processInsuranceCertificate();
        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error InsuranceCertificateJob: ' . $msg);
        }
    }

    private function processInsuranceCertificate(): void
    {
        // Create insurance service instance
        $config = config('thirdparty.insurance', []);
        if (empty($config['wsdl'])) {
            $config['wsdl'] = base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl');
        }

        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);

        // Step 1: Create offer
        $offer = $service->createInsuranceOffer([
            'insAmount' => $this->loan->amount_approved,
            'insDuration' => $this->loan->period_approved,
            'insDurationType' => $this->loan->isPaydayLoan() ? 'd' : 'm',
            'operationType' => 'create',
            // 'insType' => 'LIFE', // Default insurance type
            // 'parameters' => json_encode([
            //     'loan_id' => $this->loan->loan_id,
            //     'client_id' => $this->loan->client_id,
            // ]),
        ]);

        $offerUuid = $offer['offerUUID'] ?? null;
        if (!$offerUuid) {
            $this->loan->addMeta(self::META_KEY . '_offer', 'failed to create offer');
            throw new RuntimeException('No offer UUID returned');
        }

        $this->loan->addMeta(self::META_KEY . '_offer', $offerUuid);

        // Step 2: Create certificate
        $loanClient = $this->loan->client;
        $certificate = $service->createCertificate($offerUuid, [
            'insuredNames' => [
                $loanClient->first_name ?? '',
                $loanClient->middle_name ?? '',
                $loanClient->last_name ?? ''
            ],
            'insuredPersonalIdn' => $loanClient->pin ?? '',
            // 'insuredEmail' => $loanClient->email ?? '',
            // 'insuredPhone' => $loanClient->phone ?? '',
        ]);

        $certificateId = $certificate['certificateId'] ?? null;
        if (!$certificateId) {
            $this->loan->addMeta(self::META_KEY . '_cert', 'failed to create certificate');
            throw new RuntimeException('No certificate ID returned');
        }

        $this->loan->addMeta(self::META_KEY . '_cert', (string)$certificateId);

        // Step 3: Download certificate PDF
        $certData = $service->getCertificatePdf($certificateId);
        $certPdfBinary = $certData['certificateFileAsPDF'] ?? ''; // byte array
        if (empty($certPdfBinary)) {
            $this->loan->addMeta(self::META_KEY . '_get_pdf', 'failed to get certificate PDF');
            throw new RuntimeException('No PDF data returned');
        }

        $this->loan->addMeta(self::META_KEY . '_get_pdf', 'OK');

        // Step 4: Save certificate as client document
        $clientDocumentService = app(ClientDocumentService::class);
        $fileName = "insurance_certificate_{$certificateId}_" . time() . ".pdf";

        $clientDoc = $clientDocumentService->uploadBinaryData(
            $certPdfBinary,
            $fileName,
            'application/pdf',
            $loanClient->client_id,
            FileTypeEnum::INSURANCE_CERTIFICATE,
            $this->loan->loan_id,
            "Insurance certificate for loan #{$this->loan->loan_id}"
        );

        if (!$clientDoc) {
            $this->loan->addMeta(self::META_KEY . '_save_pdf', 'failed to save certificate');
            throw new RuntimeException('Failed to save certificate file');
        }

        $this->loan->addMeta(self::META_KEY . '_save_pdf', $clientDoc?->client_document_id);
    }

    public static function dispatch(Loan $loan, int $delaySeconds = 0): void
    {
        $job = new self($loan);

        if ($delaySeconds > 0) {
            $job->delay($delaySeconds);
        }

        dispatch($job);
    }
}
