<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\ClientDocument;
use Modules\Common\Models\Document;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Loan;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Services\EmailService;
use Modules\Docs\Services\ClientDocumentService;

class SendApprovalEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const MAX_TRIES = 3;

    public int $tries = self::MAX_TRIES;
    public int $timeout = 30;

    public function __construct(
        public Loan $loan,
        public int $try = 1,
        private EmailService $emailService
    ) {
        $this->onQueue('email');
    }

    public function handle(): void
    {
        $loanId = $this->loan->loan_id ?? null;
        if (!$loanId) {
            \Log::error('Error SendApprovalEmailJob: no loan_id provided');
            return;
        }

        try {
            $this->sendEmailForLoan();
        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error($msg, [
                'loan_id' => $loanId,
                'try' => $this->try,
                'exception' => $e
            ]);
        }
    }

    public function sendEmailForLoan(Loan $dbLoan): void
    {
        // send email only for online offices, docs for approve are generated on creation!
        // for physical offices we don't have email-address
        if (!$dbLoan->isOnlineLoan()) {
            return ;
        }

        // Specific logic related to Insurance certificate
        if ($dbLoan?->insurance?->isTrue()) {

            // Check if insurance certificate exists
            $insuranceCertificate = app(ClientDocumentService::class)->getInsuranceCertificate($dbLoan);

            if ($insuranceCertificate) {
                // Insurance certificate exists, send email immediately with it
                $this->sendEmailWithInsuranceCertificate($dbLoan, $insuranceCertificate);
            } else {
                // Insurance certificate doesn't exist yet, dispatch delayed job to retry
                SendApprovalEmailJob::dispatch($dbLoan, 30); // 30 seconds delay
            }

            return;
        }

        // No insurance needed, send email normally
        $this->sendStandardApprovalEmail($dbLoan);
    }

    private function sendEmailWithInsuranceCertificate(Loan $dbLoan, ClientDocument $insuranceCertificate): void
    {
        $attachments = $this->getStandardAttachments($dbLoan);

        // Add insurance certificate to attachments
        $mimeContentType = mime_content_type($insuranceCertificate->file->filepath());

        $attachments[] = [
            'file_id' => $insuranceCertificate->file->getKey(),
            'path' => $insuranceCertificate->file->filepath(),
            'name' => 'Insurance_Certificate.pdf',
            'mime' => $mimeContentType,
        ];

        $this->emailService->sendByTemplateKeyAndLoanId(
            EmailTemplateKeyEnum::LOAN_APPROVED->value,
            $dbLoan->getKey(),
            attachments: $attachments
        );
    }

    private function sendStandardApprovalEmail(Loan $dbLoan): void
    {
        $attachments = $this->getStandardAttachments($dbLoan);

        $this->emailService->sendByTemplateKeyAndLoanId(
            EmailTemplateKeyEnum::LOAN_APPROVED->value,
            $dbLoan->getKey(),
            attachments: $attachments
        );
    }

    private function getStandardAttachments(Loan $dbLoan): array
    {
        $docTemplateIds = DocumentTemplate::whereIn('type', [
            DocumentTemplate::TPL_CONTRACT,
            DocumentTemplate::TPL_GEN_TERMS,
            DocumentTemplate::TPL_PLAN
        ])->pluck('document_template_id')->toArray();

        $attachments = [];
        $documents = $dbLoan->documents->whereIn('document_template_id', $docTemplateIds);
        $documents->each(function (Document $document) use (&$attachments) {
            $mimeContentType = mime_content_type($document->file->filepath());

            $attachments[] = [
                'file_id' => $document->file->getKey(),
                'path' => $document->file->filepath(),
                'name' => $document->getHumanFileName(),
                'mime' => $mimeContentType,
            ];
        });

        return $attachments;
    }

    public static function dispatch(Loan $loan, int $delaySeconds = 30): void
    {
        $job = new self($loan);

        if ($delaySeconds > 0) {
            $job->delay($delaySeconds);
        }

        dispatch($job);
    }
}
