<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Examples;

use Modules\ThirdParty\Services\InsuranceService;
use Modules\ThirdParty\Libraries\InsuranceClient;

/**
 * Simple usage examples for RISKCertificateFactoryWS.wsdl
 * No service provider modifications needed
 */
class SimpleInsuranceUsage
{
    /**
     * Example 1: Basic usage with config file credentials
     */
    public static function basicUsage(): array
    {
        // Get config from thirdparty.insurance
        $config = config('thirdparty.insurance', [
            'wsdl' => base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl'),
            'service_url' => 'http://localhost:8080/MVInsWS/ws/RISKCertificate',
            'username' => env('INSURANCE_USERNAME'),
            'password' => env('INSURANCE_PASSWORD'),
        ]);

        // Create client and service
        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);

        // Create offer
        $offer = $service->createInsuranceOffer([
            'insAmount' => 5000,
            'insDuration' => 12,
            'insDurationType' => 'm',
            'operationType' => 'preview',
            'parameters' => json_encode(['clientAge' => 35])
        ]);

        return $offer;
    }

    /**
     * Example 2: Usage with runtime credentials
     */
    public static function withRuntimeCredentials(string $username, string $password): array
    {
        $config = [
            'wsdl' => base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl'),
            'service_url' => 'https://production-service.com/ws/RISKCertificate',
            'username' => $username,
            'password' => $password,
            'auth_namespace' => 'urn:Auth',
            'soap_options' => [
                'connection_timeout' => 30,
                'trace' => true,
            ]
        ];

        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);

        return $service->createInsuranceOffer([
            'insAmount' => 3000,
            'insDuration' => 6,
            'insDurationType' => 'm',
            'operationType' => 'create',
        ]);
    }

    /**
     * Example 3: Complete workflow - offer to certificate
     */
    public static function completeWorkflow(string $username, string $password): array
    {
        $config = [
            'wsdl' => base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl'),
            'username' => $username,
            'password' => $password,
        ];

        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);

        // Step 1: Create offer
        $offer = $service->createInsuranceOffer([
            'insAmount' => 5000,
            'insDuration' => 12,
            'insDurationType' => 'm',
            'operationType' => 'create',
        ]);

        $offerUuid = $offer['offerUUID'] ?? null;
        if (!$offerUuid) {
            throw new \RuntimeException('No offer UUID returned');
        }

        // Step 2: Create certificate
        $certificate = $service->createCertificate($offerUuid, [
            'insuredNames' => ['John', 'Doe', 'Smith'],
            'insuredPersonalIdn' => '1234567890',
            'insuredEmail' => '<EMAIL>',
            'insuredPhone' => '+************'
        ]);

        return [
            'offer' => $offer,
            'certificate' => $certificate
        ];
    }

    /**
     * Example 4: Usage in a Laravel Controller
     */
    public static function controllerExample(): string
    {
        return '
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Modules\ThirdParty\Services\InsuranceService;
use Modules\ThirdParty\Libraries\InsuranceClient;

class InsuranceController extends Controller
{
    public function createOffer(Request $request)
    {
        // Create insurance service instance
        $config = [
            "wsdl" => base_path("src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl"),
            "username" => env("INSURANCE_USERNAME"),
            "password" => env("INSURANCE_PASSWORD"),
            "service_url" => env("INSURANCE_SERVICE_URL")
        ];
        
        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);
        
        try {
            $offer = $service->createInsuranceOffer([
                "insAmount" => $request->input("amount", 5000),
                "insDuration" => $request->input("duration", 12),
                "insDurationType" => "m",
                "operationType" => "preview",
            ]);
            
            return response()->json($offer);
        } catch (\Exception $e) {
            return response()->json(["error" => $e->getMessage()], 500);
        }
    }
}
        ';
    }

    /**
     * Example 5: Usage in a Service Class
     */
    public static function serviceClassExample(): string
    {
        return '
<?php

namespace App\Services;

use Modules\ThirdParty\Services\InsuranceService;
use Modules\ThirdParty\Libraries\InsuranceClient;

class LoanInsuranceService
{
    private InsuranceService $insuranceService;
    
    public function __construct()
    {
        $config = config("thirdparty.insurance");
        $client = new InsuranceClient($config);
        $this->insuranceService = new InsuranceService($client);
    }
    
    public function createInsuranceForLoan(int $loanAmount, int $duration): array
    {
        return $this->insuranceService->createInsuranceOffer([
            "insAmount" => $loanAmount,
            "insDuration" => $duration,
            "insDurationType" => "m",
            "operationType" => "create",
        ]);
    }
    
    public function getCertificatePdf(int $certificateId): string
    {
        return $this->insuranceService->getCertificatePdf($certificateId);
    }
}
        ';
    }
}
