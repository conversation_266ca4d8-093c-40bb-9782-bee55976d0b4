# Insurance Log Implementation

## Overview
This implementation adds comprehensive logging to the InsuranceService, tracking all requests and responses to the insurance WSDL service.

## Database Schema

### insurance_log Table
```sql
CREATE TABLE insurance_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    action VARCHAR(255) NOT NULL,
    request JSON NOT NULL,
    response JSON NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT UNSIGNED NULL,
    updated_by BIGINT UNSIGNED NULL,
    
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);
```

## Components

### 1. InsuranceLog Model
**File**: `src/Modules/ThirdParty/Models/InsuranceLog.php`

**Features**:
- Automatic `created_by` tracking when user is authenticated
- JSON casting for request/response fields
- Static methods for common operations
- Built-in cleanup functionality

**Usage**:
```php
// Log an action
InsuranceLog::logAction('createOffer', $request, $response);

// Get recent logs
$logs = InsuranceLog::getRecent(100);

// Get logs by action
$logs = InsuranceLog::getByAction('createInsuranceOffer', 50);

// Clean old logs
$deleted = InsuranceLog::cleanOldLogs(30); // Keep 30 days
```

### 2. Enhanced InsuranceService
**File**: `src/Modules/ThirdParty/Services/InsuranceService.php`

**Logged Methods**:
- `createInsuranceOffer()` - Logs offer creation requests
- `updateInsuranceOffer()` - Logs offer update requests  
- `createCertificate()` - Logs certificate creation requests
- `getCertificatePdf()` - Logs PDF download requests (with size info)
- `getInsuranceDocuments()` - Logs document retrieval requests
- `invalidateCertificate()` - Logs certificate invalidation requests

**Special Handling**:
- PDF responses are logged with size and preview info (not full binary data)
- All requests and responses are automatically logged after each call
- Logging happens after successful SOAP calls but before error checking

### 3. InsuranceLogRepository
**File**: `src/Modules/ThirdParty/Repositories/InsuranceLogRepository.php`

**Advanced Queries**:
- Get logs by date range
- Get error logs (non-zero result codes)
- Get logs by certificate ID
- Get logs by offer UUID
- Get action statistics

### 4. Console Command
**File**: `src/Modules/ThirdParty/Console/CleanInsuranceLogsCommand.php`

**Usage**:
```bash
# Clean logs older than 30 days (default)
php artisan insurance:clean-logs

# Clean logs older than 60 days
php artisan insurance:clean-logs --days=60
```

## Logged Data Examples

### createInsuranceOffer
```json
{
    "action": "createInsuranceOffer",
    "request": {
        "insAmount": 5000,
        "insDuration": 12,
        "insDurationType": "m",
        "operationType": "create",
        "insType": "LIFE"
    },
    "response": {
        "resultCode": 0,
        "offerUUID": "abc-123-def-456",
        "premiumAmount": 150.00
    }
}
```

### getCertificatePdf
```json
{
    "action": "getCertificatePdf",
    "request": {
        "certificateId": 12345
    },
    "response": {
        "pdf_size": 245760,
        "pdf_available": true,
        "pdf_preview": "JVBERi0xLjQKJcOkw7zDtsO..."
    }
}
```

## Usage Examples

### View Recent Activity
```php
use Modules\ThirdParty\Models\InsuranceLog;

$recentLogs = InsuranceLog::getRecent(50);
foreach ($recentLogs as $log) {
    echo "{$log->created_at}: {$log->action}\n";
}
```

### Track Certificate Lifecycle
```php
use Modules\ThirdParty\Repositories\InsuranceLogRepository;

$repo = new InsuranceLogRepository();
$logs = $repo->getByCertificateId(12345);

// Shows: offer creation → certificate creation → PDF download → etc.
```

### Monitor Errors
```php
$repo = new InsuranceLogRepository();
$errorLogs = $repo->getErrorLogs(20);

foreach ($errorLogs as $log) {
    $error = $log->response['message'] ?? 'Unknown error';
    echo "Error in {$log->action}: {$error}\n";
}
```

### Get Statistics
```php
$repo = new InsuranceLogRepository();
$stats = $repo->getActionStats();

// Returns:
// [
//     ['action' => 'createInsuranceOffer', 'count' => 150, 'last_used' => '2024-01-15 10:30:00'],
//     ['action' => 'createCertificate', 'count' => 145, 'last_used' => '2024-01-15 10:32:00'],
//     ...
// ]
```

## Maintenance

### Automatic Cleanup
Add to your scheduler in `app/Console/Kernel.php`:
```php
protected function schedule(Schedule $schedule)
{
    // Clean insurance logs older than 30 days, daily at 2 AM
    $schedule->command('insurance:clean-logs --days=30')
             ->dailyAt('02:00');
}
```

### Manual Cleanup
```bash
# Clean logs older than 7 days
php artisan insurance:clean-logs --days=7

# Clean logs older than 90 days  
php artisan insurance:clean-logs --days=90
```

## Monitoring & Debugging

### Find Failed Operations
```php
// Get all failed certificate creations
$failedCerts = InsuranceLog::where('action', 'createCertificate')
    ->whereRaw("JSON_EXTRACT(response, '$.resultCode') != 0")
    ->get();
```

### Track Specific Loan's Insurance Journey
```php
// Find all operations for a specific offer
$repo = new InsuranceLogRepository();
$offerLogs = $repo->getByOfferUuid('abc-123-def-456');

// Timeline: offer creation → certificate creation → PDF download
```

### Performance Monitoring
```php
// Get average response times by action (if you add timing)
$stats = InsuranceLog::selectRaw('
    action, 
    COUNT(*) as total_calls,
    AVG(JSON_EXTRACT(response, "$.processing_time")) as avg_time
')
->groupBy('action')
->get();
```

## Security Considerations

1. **Sensitive Data**: The logging excludes sensitive personal data from requests
2. **PDF Data**: Only logs PDF size and preview, not full binary content
3. **Retention**: Automatic cleanup prevents indefinite data growth
4. **Access Control**: Consider adding access controls for viewing logs

## Benefits

1. **Debugging**: Complete audit trail of all insurance operations
2. **Monitoring**: Track success/failure rates and performance
3. **Compliance**: Full request/response logging for audit purposes
4. **Analytics**: Usage patterns and statistics
5. **Troubleshooting**: Easy to trace issues through the entire workflow
