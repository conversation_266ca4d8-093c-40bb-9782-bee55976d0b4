<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Examples;

use Modules\ThirdParty\Models\InsuranceLog;
use Modules\ThirdParty\Repositories\InsuranceLogRepository;

/**
 * Examples of how to use InsuranceLog
 */
class InsuranceLogUsage
{
    /**
     * Example: View recent insurance logs
     */
    public function viewRecentLogs(): array
    {
        $logs = InsuranceLog::getRecent(50);
        
        return $logs->map(function ($log) {
            return [
                'id' => $log->id,
                'action' => $log->action,
                'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                'request_summary' => $this->summarizeRequest($log->request),
                'response_summary' => $this->summarizeResponse($log->response),
            ];
        })->toArray();
    }

    /**
     * Example: View logs for specific action
     */
    public function viewActionLogs(string $action): array
    {
        $logs = InsuranceLog::getByAction($action, 100);
        
        return $logs->map(function ($log) {
            return [
                'id' => $log->id,
                'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                'request' => $log->request,
                'response' => $log->response,
            ];
        })->toArray();
    }

    /**
     * Example: Get statistics
     */
    public function getStatistics(): array
    {
        $repo = new InsuranceLogRepository();
        
        return [
            'action_stats' => $repo->getActionStats(),
            'total_logs' => InsuranceLog::count(),
            'recent_errors' => $repo->getErrorLogs(10)->map(function ($log) {
                return [
                    'id' => $log->id,
                    'action' => $log->action,
                    'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    'error' => $this->extractError($log->response),
                ];
            })->toArray(),
        ];
    }

    /**
     * Example: Track specific certificate
     */
    public function trackCertificate(int $certificateId): array
    {
        $repo = new InsuranceLogRepository();
        $logs = $repo->getByCertificateId($certificateId);
        
        return [
            'certificate_id' => $certificateId,
            'total_operations' => $logs->count(),
            'operations' => $logs->map(function ($log) {
                return [
                    'action' => $log->action,
                    'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    'success' => $this->isSuccessResponse($log->response),
                ];
            })->toArray(),
        ];
    }

    /**
     * Example: Track specific offer
     */
    public function trackOffer(string $offerUuid): array
    {
        $repo = new InsuranceLogRepository();
        $logs = $repo->getByOfferUuid($offerUuid);
        
        return [
            'offer_uuid' => $offerUuid,
            'total_operations' => $logs->count(),
            'operations' => $logs->map(function ($log) {
                return [
                    'action' => $log->action,
                    'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    'success' => $this->isSuccessResponse($log->response),
                ];
            })->toArray(),
        ];
    }

    /**
     * Example: Manual logging (if needed)
     */
    public function manualLog(): void
    {
        InsuranceLog::logAction('manual_test', [
            'test_param' => 'test_value'
        ], [
            'resultCode' => 0,
            'message' => 'Test successful'
        ]);
    }

    /**
     * Example: Clean old logs
     */
    public function cleanOldLogs(int $days = 30): int
    {
        return InsuranceLog::cleanOldLogs($days);
    }

    /**
     * Helper: Summarize request for display
     */
    private function summarizeRequest(array $request): string
    {
        $summary = [];
        
        if (isset($request['insAmount'])) {
            $summary[] = "Amount: {$request['insAmount']}";
        }
        
        if (isset($request['offerUUID'])) {
            $summary[] = "Offer: " . substr($request['offerUUID'], 0, 8) . '...';
        }
        
        if (isset($request['certificateId'])) {
            $summary[] = "Cert ID: {$request['certificateId']}";
        }
        
        return implode(', ', $summary) ?: 'No summary available';
    }

    /**
     * Helper: Summarize response for display
     */
    private function summarizeResponse(array $response): string
    {
        $code = $response['resultCode'] ?? $response['code'] ?? 'unknown';
        $success = $this->isSuccessResponse($response);
        
        return $success ? "Success (Code: {$code})" : "Error (Code: {$code})";
    }

    /**
     * Helper: Check if response indicates success
     */
    private function isSuccessResponse(array $response): bool
    {
        return ($response['resultCode'] ?? $response['code'] ?? 1) == 0;
    }

    /**
     * Helper: Extract error message from response
     */
    private function extractError(array $response): ?string
    {
        return $response['message'] ?? $response['error'] ?? null;
    }
}
