<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Examples;

use Modules\ThirdParty\Services\InsuranceService;
use Modules\ThirdParty\Libraries\InsuranceClient;

/**
 * Example usage of InsuranceService with RISKCertificateFactoryWS.wsdl
 * Direct instantiation without service provider bindings
 */
class InsuranceServiceExample
{
    /**
     * Create InsuranceService instance with RISKCertificateFactoryWS.wsdl
     */
    public static function createInsuranceService(string $username = null, string $password = null): InsuranceService
    {
        // Get configuration from config file or use defaults
        $config = config('thirdparty.insurance', []);

        // Override with runtime credentials if provided
        if ($username !== null) {
            $config['username'] = $username;
        }
        if ($password !== null) {
            $config['password'] = $password;
        }

        // Ensure WSDL path is set
        if (empty($config['wsdl'])) {
            $config['wsdl'] = base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl');
        }

        // Create client and service
        $client = new InsuranceClient($config);
        return new InsuranceService($client);
    }

    /**
     * Example: Create an insurance offer using direct instantiation
     */
    public static function createOfferExample(string $username = null, string $password = null): array
    {
        // Create service instance
        $insuranceService = self::createInsuranceService($username, $password);

        $payload = [
            'insAmount' => 5000,           // Insurance amount
            'insDuration' => 12,           // Duration in months
            'insDurationType' => 'm', // Duration type
            'operationType' => 'preview',  // 'preview' or 'create'
            'insType' => 'LIFE',          // Insurance type
            'parameters' => [             // Additional parameters as array (will be JSON encoded)
                'clientAge' => 35,
                'riskCategory' => 'LOW',
                'additionalInfo' => 'Standard life insurance'
            ]
        ];

        try {
            $response = $insuranceService->createInsuranceOffer($payload);
            return $response;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to create insurance offer: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Create a certificate from an offer
     */
    public function createCertificateExample(string $offerUuid): array
    {
        $insuredData = [
            'insuredNames' => ['John', 'Doe', 'Smith'],  // Array of 3 names
            'insuredPersonalIdn' => '1234567890',        // EGN or LNCh
            'insuredEmail' => '<EMAIL>',
            'insuredPhone' => '+************',
            'insuredAddress' => 'Sofia, Bulgaria'
        ];

        try {
            $response = $this->insuranceService->createCertificate($offerUuid, $insuredData);
            return $response;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to create certificate: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Get certificate PDF
     */
    public function getCertificatePdfExample(int $certificateId): string
    {
        try {
            $pdfBinary = $this->insuranceService->getCertificatePdf($certificateId);

            // You can save it to a file or return as response
            // file_put_contents('certificate_' . $certificateId . '.pdf', $pdfBinary);

            return $pdfBinary;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to get certificate PDF: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Get insurance documents
     */
    public function getDocumentsExample(): array
    {
        try {
            return $this->insuranceService->getInsuranceDocuments();
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to get insurance documents: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Invalidate a certificate
     */
    public function invalidateCertificateExample(int $certificateId): array
    {
        try {
            return $this->insuranceService->invalidateCertificate($certificateId);
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to invalidate certificate: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Direct instantiation in your controller or service
     */
    public static function directUsageExample(): array
    {
        // Method 1: Using config file credentials
        $config = config('thirdparty.insurance');
        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);

        // Method 2: Using runtime credentials
        $config = [
            'wsdl' => base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl'),
            'service_url' => 'https://your-service.com/ws/RISKCertificate',
            'username' => 'your_username',
            'password' => 'your_password',
            'auth_namespace' => 'urn:Auth'
        ];
        $client = new InsuranceClient($config);
        $service = new InsuranceService($client);

        $payload = [
            'insAmount' => 3000,
            'insDuration' => 6,
            'insDurationType' => 'm',
            'operationType' => 'preview',
            'insType' => 'ACCIDENT',
            'parameters' => json_encode(['riskLevel' => 'MEDIUM'])
        ];

        return $service->createInsuranceOffer($payload);
    }

    /**
     * Example: Direct SOAP client usage for custom operations
     */
    public static function directSoapClientExample(): void
    {
        $config = [
            'wsdl' => base_path('src/Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl'),
            'username' => 'your_username',
            'password' => 'your_password'
        ];

        $client = new InsuranceClient($config);

        // You can call any WSDL operation directly
        // This is useful for operations not wrapped in InsuranceService

        // Example: Call WSDL operations directly if needed
        // $response = $client->soapCall('someCustomOperation', $params);
    }
}
