<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Examples;

use Modules\ThirdParty\Services\InsuranceService;
use Modules\ThirdParty\Libraries\InsuranceClient;

/**
 * Example usage of InsuranceService with RISKCertificateFactoryWS.wsdl
 */
class InsuranceServiceExample
{
    private InsuranceService $insuranceService;
    private InsuranceClient $insuranceClient;

    public function __construct(InsuranceService $insuranceService, InsuranceClient $insuranceClient)
    {
        $this->insuranceService = $insuranceService;
        $this->insuranceClient = $insuranceClient;
    }

    /**
     * Example: Create an insurance offer
     */
    public function createOfferExample(): array
    {
        $payload = [
            'insAmount' => 5000,           // Insurance amount
            'insDuration' => 12,           // Duration in months
            'insDurationType' => 'months', // Duration type
            'operationType' => 'preview',  // 'preview' or 'create'
            'insType' => 'LIFE',          // Insurance type
            'parameters' => [             // Additional parameters as array (will be JSON encoded)
                'clientAge' => 35,
                'riskCategory' => 'LOW',
                'additionalInfo' => 'Standard life insurance'
            ]
        ];

        try {
            $response = $this->insuranceService->createInsuranceOffer($payload);
            return $response;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to create insurance offer: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Create a certificate from an offer
     */
    public function createCertificateExample(string $offerUuid): array
    {
        $insuredData = [
            'insuredNames' => ['John', 'Doe', 'Smith'],  // Array of 3 names
            'insuredPersonalIdn' => '1234567890',        // EGN or LNCh
            'insuredEmail' => '<EMAIL>',
            'insuredPhone' => '+************',
            'insuredAddress' => 'Sofia, Bulgaria'
        ];

        try {
            $response = $this->insuranceService->createCertificate($offerUuid, $insuredData);
            return $response;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to create certificate: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Get certificate PDF
     */
    public function getCertificatePdfExample(int $certificateId): string
    {
        try {
            $pdfBinary = $this->insuranceService->getCertificatePdf($certificateId);

            // You can save it to a file or return as response
            // file_put_contents('certificate_' . $certificateId . '.pdf', $pdfBinary);

            return $pdfBinary;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to get certificate PDF: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Get insurance documents
     */
    public function getDocumentsExample(): array
    {
        try {
            return $this->insuranceService->getInsuranceDocuments();
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to get insurance documents: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Invalidate a certificate
     */
    public function invalidateCertificateExample(int $certificateId): array
    {
        try {
            return $this->insuranceService->invalidateCertificate($certificateId);
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to invalidate certificate: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Example: Using helper functions
     */
    public function usingHelperFunctionsExample(): array
    {
        // Using global helper functions
        $service = insuranceService();
        $client = insuranceClient();

        $payload = [
            'insAmount' => 3000,
            'insDuration' => 6,
            'insDurationType' => 'months',
            'operationType' => 'preview',
            'insType' => 'ACCIDENT',
            'parameters' => json_encode(['riskLevel' => 'MEDIUM'])
        ];

        return $service->createInsuranceOffer($payload);
    }

    /**
     * Example: Direct SOAP client usage for custom operations
     */
    public function directSoapClientExample(): array
    {
        // Access the underlying SOAP client for custom operations
        $soapClient = $this->insuranceClient;

        // You can call any WSDL operation directly
        // This is useful for operations not wrapped in InsuranceService

        return [];
    }
}
