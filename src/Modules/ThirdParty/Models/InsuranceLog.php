<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

/**
 * @property int $id
 * @property string $action
 * @property array $request
 * @property array $response
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 */
class InsuranceLog extends Model
{
    protected $table = 'insurance_log';
    
    protected $fillable = [
        'action',
        'request',
        'response',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'request' => 'array',
        'response' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model and set created_by automatically
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (Auth::check()) {
                $model->created_by = Auth::id();
            }
        });

        static::updating(function ($model) {
            if (Auth::check()) {
                $model->updated_by = Auth::id();
            }
        });
    }

    /**
     * Create a new insurance log entry
     */
    public static function logAction(string $action, array $request, array $response): self
    {
        return self::create([
            'action' => $action,
            'request' => $request,
            'response' => $response,
        ]);
    }

    /**
     * Get logs for a specific action
     */
    public static function getByAction(string $action, int $limit = 100): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('action', $action)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent logs
     */
    public static function getRecent(int $limit = 100): \Illuminate\Database\Eloquent\Collection
    {
        return self::orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean old logs (older than specified days)
     */
    public static function cleanOldLogs(int $daysToKeep = 30): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return self::where('created_at', '<', $cutoffDate)->delete();
    }
}
