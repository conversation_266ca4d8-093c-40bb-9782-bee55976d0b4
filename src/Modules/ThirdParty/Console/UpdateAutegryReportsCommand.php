<?php

namespace Modules\ThirdParty\Console;

use Carbon\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\AutegryReport;
use Modules\ThirdParty\Services\AutegryService;

// php artisan script:third-party:update-autegry-reports 2024-10-01
class UpdateAutegryReportsCommand extends CommonCommand
{
    protected $name = 'script:third-party:update-autegry-reports';
    protected $signature = 'script:third-party:update-autegry-reports {from_date}';
    protected $description = 'Update autegry reports';

    public function handle()
    {
        // $this->processOld();
        $this->processNewScore();
    }

    private function processNewScore()
    {
        $this->startLog($this->description);

        $fromDate = $this->argument('from_date');
        if (empty($fromDate)) {
            throw new \RuntimeException('Error from_date not provided');
        }
        $fromDate = Carbon::parse($fromDate)->format('Y-m-d');

        $rows = AutegryReport::query()
            ->where('created_at', '>=', $fromDate)
            ->where('created_at', '<=', '2025-04-28 11:45:00')
            ->where('loan_id', '>=', 1339564)
            ->whereNotNull('loan_id')
            ->whereNotNull('request')
            ->orderBy('id');

        $processed = 0;
        $updated = 0;

        $rows->chunkById(100, function ($reports) use (&$processed, &$updated) {
            /** @var AutegryReport $report * */
            foreach ($reports as $report) {
                $processed++;

                $loan = $report->loan;
                $client = $loan->client;
                $autegryReport = app(AutegryService::class)->getScoringForLoan(
                    $client,
                    $loan,
                    'manual',
                    1
                );
                if (!empty($autegryReport->id)) {
                    $updated++;
                }
            }
        });

        $this->finishLog([
            'Processed: ' . $processed,
            'Updated: ' . $updated,
        ]);
    }

    private function processOld()
    {
        $this->startLog($this->description);

        $fromDate = $this->argument('from_date');
        if (empty($fromDate)) {
            throw new \RuntimeException('Error from_date not provided');
        }
        $fromDate = Carbon::parse($fromDate)->format('Y-m-d');

        $rows = AutegryReport::query()
            ->where('created_at', '>=', $fromDate)
            ->whereNull('exg_zone')
            ->whereNull('exg_bad_rate')
            ->whereNull('new_zone')
            ->whereNull('new_bad_rate')
            ->orderBy('id');

        $processed = 0;
        $updated = 0;

        $rows->chunkById(100, function ($reports) use (&$processed, &$updated) {
            /** @var AutegryReport $report * */
            foreach ($reports as $report) {
                $processed++;
                $update = false;

                $response = ensureJsonDecoded($report->response);
                if (!empty($response['response'])) {
                    $response2 = ensureJsonDecoded($response['response']);
                }

                if (isset($response2) && is_array($response2)) {
                    $response = (object) $response2;
                } else if (is_array($response)) {
                    $response = (object) $response;
                }


                if (!empty($response->score_exg)) {

                    if (is_array($response->score_exg)) {
                        $response->score_exg = (object) $response->score_exg;
                    }

                    $update = true;

                    $report->setAttribute('exg_bad_rate', $response->score_exg?->bad_rate ?? null);
                    $report->setAttribute('exg_zone', $response->score_exg?->risk_zone ?? null);
                }

                if (!empty($response->score_new)) {

                    if (is_array($response->score_new)) {
                        $response->score_new = (object) $response->score_new;
                    }

                    $update = true;

                    $report->setAttribute('new_bad_rate', $response->score_new?->bad_rate ?? null);
                    $report->setAttribute('new_zone', $response->score_new?->risk_zone ?? null);
                }

                if ($update) {
                    $updated++;
                    $report->saveQuietly();
                }
            }
        });

        $this->finishLog([
            'Processed: ' . $processed,
            'Updated: ' . $updated,
        ]);
    }
}
