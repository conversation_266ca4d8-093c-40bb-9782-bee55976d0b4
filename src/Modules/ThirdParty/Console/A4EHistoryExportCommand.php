<?php

namespace Modules\ThirdParty\Console;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Services\StorageService;
use Modules\ThirdParty\Services\AutegryService;

class A4EHistoryExportCommand extends CommonCommand
{
    protected $name = 'script:a4e-history';
    protected $signature = 'script:a4e-history {fromDate} {toDate} {loanId?}';
    protected $description = 'A4EScoring history data export';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): bool
    {
        $totalHandled = 0;
        $this->startLog();


        // prepare params
        $loanId = (int) $this->argument('loanId');
        $fromDateBase = $this->argument('fromDate');
        $toDateBse = $this->argument('toDate');
        $fromDate = Carbon::parse($fromDateBase)->startOfDay();
        $toDate = Carbon::parse($toDateBse)->endOfDay();


        // create folder for collecting jsons
        if (!Storage::exists(StorageService::PATH_TO_A4E_PERFORMANCE_TMP)) {
            Storage::disk('local')->makeDirectory(
                StorageService::PATH_TO_A4E_PERFORMANCE_TMP,
                0777,
                true,
                true
            );
        }


        // define full path to new export file
        if (!empty($loanId)) {
            $filename = 'data_loan_' . $loanId . '.json';
        } else {
            $filename = 'data_' . $fromDateBase . '_' . $toDateBse . '.json';
        }

        $outputFile = storage_path(StorageService::PATH_TO_A4E_PERFORMANCE_TMP . '/' . $filename);
        dump("File: " . $outputFile);

        // remove such file if already exists
        if (Storage::exists($outputFile)) {
            Storage::delete($outputFile);
        }


        // Open the file for writing
        $fileHandle = fopen($outputFile, 'w+');

        // Start the JSON array
        fwrite($fileHandle, '[');
        $firstRecord = true;


        // if (!empty($loanId)) {
        //     $loanIds = [$loanId];
        //     $builder = Loan::whereIn('loan_id', $loanIds);
        // } else {
        //     $builder = Loan::where('created_at', '>=', $fromDate)
        //         ->where('created_at', '<=', $toDate)
        //         ->where('office_id', '=', 1)
        //         ->whereIn('loan_status_id', LoanStatus::getStatusesForA4e());
        // }

        // 5 credits from last 5 years
        // $loanIds = [103965, 103967, 103968, 103969, 103970, 103972, 103973, 103985, 103986, 103987, 625366, 626056, 105207, 652745, 105242, 1140776, 1182640, 1218288, 1226056, 1238688, 1276201, 1289252, 1289276, 1289255, 1289287];

        // credits by Svetlin request - to check vars
        // $loanIds = [69982, 69983, 77267];

        // credits by Svetlin request - to check APP_Installment_Requested
        // $loanIds = [626235, 626264, 626953];



        $loanIds = Loan::whereYear('created_at', 2024)
            ->whereIn('loan_status_id', [6, 7])
            ->inRandomOrder()
            ->limit(200)
            ->pluck('loan_id')
            ->toArray();

        $builder = Loan::whereIn('loan_id', $loanIds);

        dump('Total: ' . $builder->count());

        $i = 1;
        $chunk = 10000;

        // Query and chunk the data
        $builder
            ->orderBy('created_at')
            ->chunkById(
                $chunk,
                function ($loans) use ($fileHandle, &$firstRecord, &$totalHandled, &$i, $chunk) {

                    dump('-- chunk(' . $i . '): ' . ($i * $chunk));

                    foreach ($loans as $loan) {
                        if (!$firstRecord) {
                            fwrite($fileHandle, ',');
                        }

                        $res = app(AutegryService::class)->getRequestData2(
                            $loan->client,
                            $loan,
                            true
                        );

                        $loanData = json_encode([$loan->loan_id => $res]);
                        fwrite($fileHandle, $loanData);

                        // After the first record, this is set to false
                        $firstRecord = false;

                        $totalHandled++;
                    }

                    $i++;
                },
                'loan.loan_id',
                'loan_id'
            );

        // Write a closing bracket
        fwrite($fileHandle, ']');

        // Close the file handle
        fclose($fileHandle);


        $this->finishLog([$this->executionTimeString()],$totalHandled, $totalHandled, 'OK');
        return true;
    }

    // @deprecated - need for now, will be deleted later
    private function customExport()
    {
        $builder = Loan::leftJoin('autegry_report as ar', 'ar.loan_id', '=', 'loan.loan_id')
            ->where('loan.created_at', '>=', '2024-06-01 00:00:00')
            ->where('loan.created_at', '<', '2024-07-04 00:00:00')
            ->where('loan.office_id', '=', 1)
            ->whereNull('ar.id')
            ->orderBy('loan.loan_id')
            ->select('loan.*');

        $builder
            ->chunkById(
                200,
                function ($loans) use (&$totalHandled) {
                    dump('-- chunk = ' . $totalHandled/200);

                    foreach ($loans as $loan) {

                        $autegryReport = app(AutegryService::class)->getScoringForLoan(
                            $loan->client,
                            $loan,
                            'manual',
                            1
                        );

                        $totalHandled++;
                    }
                },
                'loan.loan_id',
                'loan_id'
            );

        dump('------------------------');
        dump('Total handled: ' . $totalHandled);

        return true;
    }
}
