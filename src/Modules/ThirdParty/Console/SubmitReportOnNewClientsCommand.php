<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Console;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\Office;

final class SubmitReportOnNewClientsCommand extends CommonCommand
{
    private const MAX_CHUNK_SIZE = 100;

    protected $signature = 'script:third-party:submit-report-on-new-clients';
    protected $description = 'Send unique clients for last 24h to Ads service';

    public function handle(): void
    {
        $this->startLog($this->description);

        $url = env('MAKE_ITEGROMAT_NEW_CLIENTS_URL');
        if (!$url) {
            $message = 'Error: MAKE_ITEGROMAT_NEW_CLIENTS_URL is not set';
            \Log::error($message);
            return;
        }

        $data = $this->prepareData();

        $this->submitData($url, $data);

        $this->finishLog(['Total: ' . count($data)]);
    }

    private function prepareData(): array
    {
        $data = [];

        Client::query()
            ->select('client.client_id', 'email', 'first_name', 'last_name', 'phone', 'client_address.post_code')
            ->distinct()
            ->join('loan', function (JoinClause $join) {
                $join->on('loan.client_id', '=', 'client.client_id')
                    ->where('loan.created_at', '>=', now()->subDay())
                    ->where('loan.office_id', Office::OFFICE_ID_WEB);
            })
            ->join('client_address', function (JoinClause $join) {
                $join->on('client.client_id', '=', 'client_address.client_id')
                    ->where('client_address.type', ClientAddress::TYPE_CURRENT)
                    ->where('client_address.last', true)
                    ->where('client_address.active', true);
            }, type: 'left')
            ->chunk(self::MAX_CHUNK_SIZE, static function (Collection $clients) use (&$data) {
                foreach ($clients as $client) {
                    $data[] = [
                        'Email' => $client->email,
                        'First Name' => $client->first_name,
                        'Last Name' => $client->last_name,
                        'Country' => 'Bulgaria',
                        'Zip' => $client->post_code,
                        'Phone' => $client->phone,
                    ];
                }
            });


        return $data;
    }

    private function submitData(string $url, array $data): void
    {
        $response = Http::post($url, $data);
        if (!$response->successful()) {
            $response->throw();
        }
    }
}
