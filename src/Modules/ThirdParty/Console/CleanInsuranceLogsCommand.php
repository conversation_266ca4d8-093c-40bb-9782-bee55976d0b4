<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Console;

use Illuminate\Console\Command;
use Modules\ThirdParty\Models\InsuranceLog;

class CleanInsuranceLogsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'insurance:clean-logs {--days=30 : Number of days to keep logs}';

    /**
     * The console command description.
     */
    protected $description = 'Clean old insurance logs';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        
        if ($days < 1) {
            $this->error('Days must be a positive number');
            return 1;
        }

        $this->info("Cleaning insurance logs older than {$days} days...");
        
        $deletedCount = InsuranceLog::cleanOldLogs($days);
        
        $this->info("Deleted {$deletedCount} old insurance log entries.");
        
        return 0;
    }
}
