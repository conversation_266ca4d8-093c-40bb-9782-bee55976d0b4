<?php

namespace Modules\Collect\Http\Controllers;

use Illuminate\Contracts\View\View;
use Illuminate\Http\Response;
use Modules\Collect\Application\Action\ExportInstallmentsInDueAction;
use Modules\Collect\Application\Action\InstallmentInDueListingAction;
use Modules\Collect\Http\Requests\InstallmentsInDueSearchRequest;
use Modules\Common\Http\Controllers\BaseController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

final class InstallmentsInDueController extends BaseController
{

    public function list(InstallmentsInDueSearchRequest $request, InstallmentInDueListingAction $action): View
    {
        return view(
            'collect::installments-in-due.list',
            $action->execute($request->validated(), $this->getPaginationLimit())
        );
    }

    public function export(
        InstallmentsInDueSearchRequest $request,
        ExportInstallmentsInDueAction  $action,
    ): Response
    {
        $exportData = $action->execute($request->validated());

        // Check if export data is available
        if (empty($exportData) || !isset($exportData['file_path'])) {
            return response('No data available for export', 404);
        }

        // Create a response with the file content
        $fileContent = file_get_contents($exportData['file_path']);

        // Clean up the temporary file
        unlink($exportData['file_path']);

        return response($fileContent)
            ->header('Content-Type', $exportData['content_type'])
            ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
            ->header('Cache-Control', 'max-age=0');
    }
}
