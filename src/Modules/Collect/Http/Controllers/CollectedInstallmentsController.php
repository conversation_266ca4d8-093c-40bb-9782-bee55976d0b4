<?php

namespace Modules\Collect\Http\Controllers;

use Illuminate\Http\Response;
use Illuminate\View\View;
use Modules\Collect\Application\Action\CollectedInstallmentListAction;
use Modules\Collect\Application\Action\CollectedInstallmentsExportAction;
use Modules\Collect\Http\Requests\CollectedInstallmentFilterRequest;
use Modules\Common\Http\Controllers\BaseController;

class CollectedInstallmentsController extends BaseController
{
    public function list(CollectedInstallmentFilterRequest $request, CollectedInstallmentListAction $action): View
    {
        return view(
            'collect::collected-installments.list',
            $action->execute($request->validated(), $this->getPaginationLimit())
        );
    }

    public function export(
        CollectedInstallmentFilterRequest $request,
        CollectedInstallmentsExportAction $action
    ): Response {
        $exportData = $action->execute($request->validated());

        // Check if export data is available (when saveToFile is false, it returns an array)
        if (is_array($exportData) && isset($exportData['file_path'])) {
            // Create a response with the file content
            $fileContent = file_get_contents($exportData['file_path']);

            // Clean up the temporary file
            unlink($exportData['file_path']);

            return response($fileContent)
                ->header('Content-Type', $exportData['content_type'])
                ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
                ->header('Cache-Control', 'max-age=0');
        }

        // This shouldn't happen with the current implementation, but just in case
        return response('No data available for export', 404);
    }
}