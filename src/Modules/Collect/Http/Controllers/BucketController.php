<?php

namespace Modules\Collect\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Collect\Application\Action\CreateBucketAction;
use Modules\Collect\Application\Action\UpdateBucketAction;
use Modules\Collect\Http\Requests\BucketCrudRequest;
use Modules\Collect\Http\Requests\BucketSearchRequest;
use Modules\Collect\Repositories\BucketRepository;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Bucket;
use Modules\Common\Services\BaseService;
use RuntimeException;
use Throwable;

class BucketController extends BaseController
{
    public string $pageTitle = 'Bucket list';
    public string $indexRoute = 'collect.buckets.list';

    public function __construct(private BucketRepository $bucketRepository)
    {
        parent::__construct();
    }

    /**
     * @param BucketSearchRequest $request
     *
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function list(BucketSearchRequest $request)
    {
        $this->checkForRequestParams($request);

        return view(
            'collect::buckets.list',
            [
                'buckets' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
                'bucketTypes' => $this->bucketRepository->getTypes(),
            ]
        );
    }

    /**
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function create()
    {
        return view('collect::buckets.crud', [
            'bucketTypes' => $this->bucketRepository->getTypes()
        ]);
    }

    /**
     * @param Bucket $bucket
     *
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function edit(Bucket $bucket)
    {
        return view('collect::buckets.crud', [
            'bucket' => $bucket,
            'bucketTypes' => $this->bucketRepository->getTypes()
        ]);
    }

    /**
     * @param BucketCrudRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function store(CreateBucketAction $action, BucketCrudRequest $request): RedirectResponse
    {
        $action->execute($request->asDto());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('collect::bucketCrud.CreatedSuccessfully'));
    }


    public function update(UpdateBucketAction $action, BucketCrudRequest $request): RedirectResponse
    {
        $action->execute($request->asDto());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('collect::bucketCrud.UpdatedSuccessfully'));
    }

    /**
     * @param Bucket $bucket
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function delete(Bucket $bucket): RedirectResponse
    {
        try {
            $this->bucketRepository->delete($bucket);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('bucketCrud.DeletionFailed'),
                $exception
            );
        }

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('collect::bucketCrud.DeletedSuccessfully'));
    }

    /**
     * @param Bucket $bucket
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function disable(Bucket $bucket): RedirectResponse
    {
        //todo remove this method
        $this->delete($bucket);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('collect::bucketCrud.DisabledSuccessfully'));
    }

    /**
     * @param Bucket $bucket
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function enable(Bucket $bucket): RedirectResponse
    {
        //todo remove this method

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('collect::bucketCrud.EnabledSuccessfully'));
    }

    /**
     * @param BucketSearchRequest $request
     *
     * @return bool|RedirectResponse
     *
     * @throws Exception
     */
    public function setFilters(BucketSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    /**
     * @param BucketSearchRequest $request
     *
     * @return RedirectResponse|void
     *
     * @throws Exception
     */
    protected function checkForRequestParams(BucketSearchRequest $request)
    {
        if ($request->exists(
            [
                'name',
                'active',
                'createdAt',
                'updatedAt',
            ]
        )
        ) {
            $this->cleanFilters();
            $this->setFilters($request);
        }
    }

    /**
     * @return mixed
     *
     * @throws Exception
     */
    public function getTableData()
    {
        return $this->bucketRepository->getAll(
            parent::getTableLength(),
            [],
            app()->make(BaseService::class)->whereCond(session($this->cacheKey, [])),
            ['bucket_id' => 'DESC']
        );
    }

    /**
     * @param BucketSearchRequest $request
     *
     * @return array|string
     * @throws Exception|Throwable
     */
    public function refresh(BucketSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'collect::buckets.list-table',
            [
                'buckets' => $this->getTableData(),
            ]
        )->render();
    }
}
