<?php

namespace Modules\Collect\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Modules\Collect\Application\Action\CollectSendEmailMessageAction;
use Modules\Collect\Application\Action\CollectSendSmsMessageAction;
use Modules\Collect\Application\Action\LoanBucketAction;
use Modules\Collect\Application\Action\MassMoveLoanBucketAction;
use Modules\Collect\Http\Requests\LoanBucketSearchRequest;
use Modules\Collect\Http\Requests\MassLoanBucketRequest;
use Modules\Collect\Repositories\LoanBucketRepository;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Collect\Application\Action\GenerateChosenLawDocsAction;
use Modules\Collect\Http\Requests\LegalDocumentsByChoiceRequest;
use Modules\Common\Models\LoanBucket;
use Modules\Head\Http\Requests\ClientsWithoutActive\EmailMessageRequest;
use Modules\Head\Http\Requests\ClientsWithoutActive\SmsMessageRequest;
use Modules\Head\Services\SpreadsheetHelper;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

final class LoanBucketController extends BaseController
{
    public function list(
        LoanBucketSearchRequest $request,
        LoanBucketAction $loanBucketAction
    ): View {
        $filters = $request->validated();
        $data = $loanBucketAction->execute($filters);

        return view('collect::loan-buckets.list', $data);
    }

    public function sendSmsMessage(SmsMessageRequest $request, CollectSendSmsMessageAction $action): RedirectResponse
    {
        ['recipientsCount' => $recipientsCount, 'templateId' => $templateId]
            = $action->execute($request->validated());

        $link = route('communication.manual-mailings-stats', [
            'type' => 'sms', 'templatable_id' => $templateId
        ]);

        return back()->with(
            'success',
            __(
                'communication::messages.successManualMessageSend',
                compact('recipientsCount', 'link')
            )
        );
    }

    public function sendEmailMessage(
        EmailMessageRequest $request,
        CollectSendEmailMessageAction $action
    ): RedirectResponse {
        ['recipientsCount' => $recipientsCount, 'templateId' => $templateId]
            = $action->execute($request->validated());

        $link = route('communication.manual-mailings-stats', [
            'type' => 'email', 'templatable_id' => $templateId
        ]);

        return back()->with(
            'success',
            __(
                'communication::messages.successManualMessageSend',
                compact('recipientsCount', 'link')
            )
        );
    }

    public function storeBucket(
        MassLoanBucketRequest $request,
        MassMoveLoanBucketAction $massMoveLoanBucketAction
    ): RedirectResponse {
        $massMoveLoanBucketAction->execute($request->asDtoArray());

        return to_route('collect.loan-buckets.list')
            ->with('success', __('collect::bucketCrud.UpdatedSuccessfully'));
    }

    public function export(
        LoanBucketSearchRequest $request
    ): Response {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            __('table.Bucket'),
            __('table.Client'),
            __('table.Pin'),
            __('table.ContractNumber'),
            __('table.StartDate'),
            __('table.FinalRepaidDate'),
            __('table.AmountApproved'),
            __('table.OverdueAmount'),
            __('table.OverdueDays'),
            __('table.InstallmentsOverdue'),
            __('table.Phone'),
            __('table.Address'),
            __('table.Comment'),
            __('table.Guarant'),
            __('table.Consultant'),
            __('table.Office'),
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;

        $filters = $request->validated();
        $builder = app(LoanBucketRepository::class)
            ->getBuilderByFilters($filters)
            ->orderBy('loan_bucket.loan_id', 'ASC');

        $builder->chunkById(
            100,
            function ($rows) use (&$sheet, &$rowIndex) {
                /** @var LoanBucket $loanBucket ** */
                foreach ($rows as $loanBucket) {
                    $r = [
                        __('collect::buckets.bucket_' . $loanBucket->bucket_id),
                        $loanBucket->client_name,
                        $loanBucket->pin,
                        $loanBucket->loan_id,
                        \Carbon\Carbon::parse($loanBucket->start_date)->format('d.m.Y'),
                        \Carbon\Carbon::parse($loanBucket->end_date)->format('d.m.Y'),
                        intToFloat($loanBucket->amount_withdraw),
                        $loanBucket->amount_overdue,
                        $loanBucket->days_overdue,
                        $loanBucket->installments_overdue,
                        $loanBucket->phone,
                        $loanBucket->address,
                        $loanBucket->comment,
                        $loanBucket->getGuarantorsLabel(false),
                        $loanBucket->loan?->getConsultantName(),
                        getOfficeName($loanBucket->loan?->office_id)
                    ];

                    // Populate data from the array
                    $rowIndex++;
                    foreach ($r as $columnIndex => $value) {
                        $sheet->setCellValue(
                            [$columnIndex + 1, $rowIndex],
                            $value
                        );
                    }
                }
            },
            'loan_bucket.loan_id',
            'loan_id'
        );

        /// set autosize columns
        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'loan_bucket_export_' . time() . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Create a response with the file content
        $fileContent = file_get_contents($tempFile);

        // Clean up the temporary file
        unlink($tempFile);

        return response($fileContent)
            ->header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"')
            ->header('Cache-Control', 'max-age=0');
    }

    public function generateChosenLawDocs(
        LegalDocumentsByChoiceRequest $request,
        GenerateChosenLawDocsAction $action
    ): BinaryFileResponse {
        $validatedData = $request->validated();
        $loans = $validatedData['loans'];

        return response()->download(
            $action->execute($loans)
        );
    }
}
