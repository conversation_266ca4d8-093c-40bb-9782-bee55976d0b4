<?php

namespace Modules\Collect\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Modules\Collect\Application\Action\OuterCollectorDataAction;
use Modules\Collect\Http\Requests\OuterCollectorFilterRequest;
use Modules\Collect\Http\Requests\StoreOuterCollectorRequest;
use Modules\Collect\Imports\OuterCollectorLoanIdsImport;
use Modules\Collect\Services\MarkAsOuterCollectorService;
use Modules\Common\Http\Controllers\BaseController;

class OuterCollectorController extends BaseController
{
    public function list(
        OuterCollectorFilterRequest $filterRequest,
        OuterCollectorDataAction $action
    ): View {
        return view(
            'collect::outer-collector.list',
            $action->execute($filterRequest->validated(), $this->getPaginationLimit())
        );
    }


    /**
     * @throws Exception
     */
    public function storeOuterCollector(StoreOuterCollectorRequest $request): RedirectResponse
    {
        $data = $request->validated();

        try {
            $importLoanIds = app(OuterCollectorLoanIdsImport::class);
            \Excel::import($importLoanIds, $request->file('importLoans'));

            app(MarkAsOuterCollectorService::class)->markForCollector(
                $importLoanIds->loanIds()->toArray(),
                $data['consultant_id']
            );
        } catch (Exception $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return back()->with('fail', $e->getMessage());
        }

        return back()->with('success', __('messages.storeOuterCollectorSuccess'));
    }
}