<?php

namespace Modules\Collect\Exports;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Modules\Common\Models\BucketTask;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class LoanBucketExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    private Collection $rows;
    public ?Builder $builder = null;

    public function __construct(Collection $rows)
    {
        $this->rows = $rows;
    }

    public function formatRow(BucketTask $task): array
    {
        $currentLoan = $task->loan;
        $currentClient = $currentLoan->client;
        $currentAddress = $currentClient->clientLastAddressCurrent();
        $currentAddressStr = $currentAddress?->addressToString($currentClient->isBlocked());
        $lastClientComment = $currentClient->getLastComment();
        $loansStats = $currentLoan->loanActualStats;

        $comment = (string)$currentLoan->bucketTask?->getLoanBucketProcessInfo();
        $comment .= (!empty($comment) ? '<br/>' : '') . (
                (!empty($task->comment)
                    ? 'Бъкет коментар: ' . $task->comment . '<br/>'
                    : ''
                ) . (
                !empty($lastClientComment)
                    ? 'Коментар клиент: ' . $lastClientComment
                    : ''
                )
            );

        return [
            'name' => $currentClient->getFullName(),
            'pin' => $currentClient->pin,
            'loan_id' => $task->loan_id,
            'start_date' => $currentLoan->created_at->format('d.m.Y'),
            'end_date' => $currentLoan->repaymentDate()->format('d.m.Y'),
            'amount_withdraw' => intToFloat($currentLoan->amount_approved),
            'amount_overdue' => $loansStats->current_overdue_amount,
            'days_overdue' => $loansStats->current_overdue_days,
            'installments_overdue' => $loansStats->overdue_installments,
            'phone' => $currentClient->phone,
            'address' => str_replace('<br/>', "\n", $currentAddressStr),
            'comment' => str_replace('<br/>', "\n", $comment),
        ];
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->rows;
    }

    public function headings(): array
    {
        return [
            'Клиент',
            'Егн',
            'No. Договор',
            'Начало',
            'Край',
            'Усвоена сума',
            'Дължима сума',
            'Проср. дни',
            'Проср. вн.',
            'Телефон',
            'Адрес',
            //'Доп. инф.'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 15,
            'C' => 10,
            'D' => 12,
            'E' => 12,
            'F' => 15,
            'G' => 15,
            'H' => 10,
            'J' => 15,
            'K' => 30,
            'L' => 130,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        foreach ($sheet->getColumnIterator('K', 'L') as $column) {
            $column->getWorksheet()
                ->getStyle($column->getColumnIndex().'1:'.$column->getColumnIndex().$sheet->getHighestDataRow())
                ->getAlignment()
                ->setWrapText(true);
        }
    }
}
