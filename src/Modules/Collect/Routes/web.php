<?php

use Illuminate\Support\Facades\Route;
use Modules\Collect\Http\Controllers\BucketController;
use Modules\Collect\Http\Controllers\BucketTaskController;
use Modules\Collect\Http\Controllers\CollectorAttemptController;
use Modules\Collect\Http\Controllers\CompletedCollectTasksController;
use Modules\Collect\Http\Controllers\InstallmentsInDueController;
use Modules\Collect\Http\Controllers\LoanBucketController;
use Modules\Collect\Http\Controllers\BucketTaskSkipController;
use Modules\Collect\Http\Controllers\OuterCollectorController;
use Modules\Collect\Http\Controllers\CollectedInstallmentsController;

Route::prefix('collect')->group(function () {
    $idPattern = '[1-9][0-9]{0,5}';

    /// CollectedInstallmentsController::class
    Route::get('/collected-installments', [CollectedInstallmentsController::class, 'list'])
        ->name('collect.collected-installments.list')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Consultants Collection')
        ->defaults('description', 'View reports page');

    Route::get('/collected-installments/export', [CollectedInstallmentsController::class, 'export'])
        ->name('collect.collected-installments.export')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Consultants Collection')
        ->defaults('description', 'Export data');

    // Collector skip bucket task till date
    Route::post('/skip-bucket-task', [BucketTaskSkipController::class, 'storeSkipBucketTask'])
        ->name('collect.skip-bucket.storeSkipBucketTask')
        ->defaults('description', 'Postpone collection task for x days')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'General')
        ->defaults('info_bubble', 'Може да отлага създаването на задача за събиране от клиентска карта');

    /// OuterCollectorController
    Route::get('/outer-collector', [OuterCollectorController::class, 'list'])
        ->name('collect.outer-collector.list')
        ->defaults('description', 'View External collection page')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'External Collection')
        ->defaults('info_bubble', 'Вижда страница Външно събиране');

    Route::post('/outer-collector', [OuterCollectorController::class, 'storeOuterCollector'])
        ->name('collect.outer-collector.storeOuterCollector')
        ->defaults('description', 'Add loans for external collection')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'External Collection')
        ->defaults('info_bubble', 'Може да добави кредити за външно събиране');

    /// Buckets crud
    Route::get('/buckets', [BucketController::class, 'list'])
        ->name('collect.buckets.list');

    Route::get('/buckets/create', [BucketController::class, 'create'])
        ->name('collect.buckets.create');

    Route::post('/buckets/store', [BucketController::class, 'store'])
        ->name('collect.buckets.store');

    Route::get('/buckets/edit/{bucket}', [BucketController::class, 'edit'])
        ->name('collect.buckets.edit')
        ->where('id', $idPattern);

    Route::post('/buckets/update/{bucket_id}', [BucketController::class, 'update'])
        ->name('collect.buckets.update')
        ->where('id', $idPattern);

    Route::get('/buckets/delete/{bucket}', [BucketController::class, 'delete'])
        ->name('collect.buckets.delete')
        ->where('id', $idPattern);

    Route::get('/buckets/enable/{bucket}', [BucketController::class, 'enable'])
        ->name('collect.buckets.enable')
        ->where('id', $idPattern);

    Route::get('/buckets/disable/{bucket}', [BucketController::class, 'disable'])
        ->name('collect.buckets.disable')
        ->where('id', $idPattern);

    // ajax load of table with buckets
    Route::get(
        '/buckets/refresh',
        [BucketController::class, 'refresh']
    )->name('collect.buckets.refresh')
        ->defaults('description', 'Ajax refresh bucket table');

    // session
    Route::get('/buckets/filters', [BucketController::class, 'getFilters'])
        ->name('collect.buckets.getFilters')
        ->defaults('description', 'Get bucket filters');
    Route::put('/buckets/filters', [BucketController::class, 'setFilters'])
        ->name('collect.buckets.setFilters')
        ->defaults('description', 'Update bucket filters');
    Route::delete('/buckets/filters', [BucketController::class, 'cleanFilters'])
        ->name('collect.buckets.cleanFilters')
        ->defaults('description', 'Cleanup filters');

    // Bucket tasks
    Route::get('/bucket-tasks', [BucketTaskController::class, 'list'])
        ->name('collect.bucket-tasks.list')
        ->defaults('description', 'View tasks list')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'General')
        ->defaults('info_bubble', 'Може да вижда списък със задачи за събиране');

    Route::get('/bucket-tasks/export', [BucketTaskController::class, 'export'])
        ->name('collect.bucket-tasks.export')
        ->defaults('description', 'Export Call list')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'General')
        ->defaults('info_bubble', 'Може да експортва справка Списък обаждане');

    Route::get(
        '/bucket-tasks/process/{bucketTask}',
        [BucketTaskController::class, 'processTask']
    )->name('collect.bucket-tasks.process');

    Route::post('/bucket-tasks/collector-attempt', [CollectorAttemptController::class, 'storeAttempt'])
        ->name('collect.collector-attempt.create');

    // LOAN BUCKETS
    Route::get('/loan-buckets', [LoanBucketController::class, 'list'])
        ->name('collect.loan-buckets.list')
        ->defaults('description', 'View loans page')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('info_bubble', 'Вижда страница Събиране -> Кредити');

    Route::get('/loan-buckets/create', [LoanBucketController::class, 'create'])
        ->name('collect.loan-buckets.create');

    Route::get('/loan-buckets/store', [LoanBucketController::class, 'storeBucket'])
        ->name('collect.loan-buckets.store')
        ->defaults('description', 'Move loans to selected bucket')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('info_bubble', 'Може да мести кредити от един бъкет в друг');

    Route::get('/loan-buckets/export', [LoanBucketController::class, 'export'])
        ->name('collect.loan-buckets.export')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('description', 'Export data');

    Route::post('/loan-buckets/send-sms-message', [LoanBucketController::class, 'sendSmsMessage'])
        ->name('collect.loan-buckets.send-sms-message')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('description', 'Send sms');

    Route::post('/loan-buckets/send-email-message', [LoanBucketController::class, 'sendEmailMessage'])
        ->name('collect.loan-buckets.send-email-message')
        ->defaults('description', 'Send email')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('info_bubble', 'Може да изпраща емейл до клиенти през страница Събиране -> Кредити');

    Route::get('/bucket-tasks/legal-docs-by-choice', [LoanBucketController::class, 'generateChosenLawDocs'])
        ->name('collect.legal-docs.choose')
        ->defaults('description', 'Generate legal documents')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('info_bubble', 'Може да генерира съдебни документи');


    // Installments In Due
    Route::get('/installments-in-due', [InstallmentsInDueController::class, 'list'])
        ->name('collect.installments-in-due.list')
        ->defaults('description', 'View due installments page')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Due Installments Report')
        ->defaults('info_bubble', 'Вижда страница В падеж. Показва всички предстоящи вноски');

    Route::get('/installments-in-due/export', [InstallmentsInDueController::class, 'export'])
        ->name('collect.installments-in-due.export')
        ->defaults('description', 'Export data')
        ->defaults('module_name', 'Collection')
        ->defaults('controller_name', 'Due Installments Report')
        ->defaults('info_bubble', 'Може да експортва данни');

    // Completed collect tasks
    Route::get('/completed-tasks', [CompletedCollectTasksController::class, 'index'])
        ->name('collect.completed-tasks')
        ->defaults('description', 'View reports page')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Collection Processed Tasks')
        ->defaults('info_bubble', 'Вижда репорт Събиране обработени задачи');

    Route::get('/completed-tasks/export', [CompletedCollectTasksController::class, 'export'])
        ->name('collect.completed-tasks.export')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Collection Processed Tasks')
        ->defaults('description', 'Export data');
});
