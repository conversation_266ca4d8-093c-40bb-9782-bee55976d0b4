<?php

namespace Modules\Collect\Application\Console;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Collect\Application\Console\DailyInstallmentRefresh;
use Modules\Collect\Application\Console\DispatchNewDayForActiveLoans;
use Modules\Collect\Application\Console\GraceLoanResetCommand;
use Modules\Collect\Repositories\BucketTaskRepository;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Console\CreateCollectorFeeWatcher;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\SaleTaskType;
use Modules\Communication\Application\Enums\OverdueDaysEnum;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Repositories\SmsRepository;
use Modules\Head\Repositories\ClientRepository;
use Modules\Payments\Console\EasyPayRefundTasksCommand;
use Modules\Payments\Repositories\PaymentTaskRepository;
use Modules\Product\Services\ProductSettingsService;
use Modules\ThirdParty\Libraries\EmailProvider;
use Symfony\Component\Console\Command\Command;


// php artisan script:daily-checker-command
class DailyCheckerCommand extends CommonCommand
{
    protected $name = 'script:daily-checker-command';
    protected $signature = 'script:daily-checker-command';
    protected $description = 'Monitors cron activities and send a report';

    public function __construct(
        private ClientRepository $clientRepository,
        private ProductSettingsService $productSettingsService,
        private readonly SmsRepository $smsRepo,
        private readonly BucketTaskRepository $btRepo,
        private readonly PaymentTaskRepository $ptRepo
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->startLog();

        $today = Carbon::now()->format('Y-m-d');
        $beforeDate = getLoanStartDate(
            $today,
            $today,
            '+1 month'
        );

        $logMessages = [
            'DispatchNewDayForActiveLoans' => [
                'loan_stats_to_be_updated' => DispatchNewDayForActiveLoans::getTotalCountForHandling(),
                'loan_stats_updated' => $this->getUpdatedLoansStatsCount(),
            ],
            'DispatchNewDayForClients' => [
                'client_stats_to_be_updated' => $this->clientRepository->getActiveOldClientsBuilder()->count(),
                'client_stats_updated' => $this->getUpdatedClientStatsCount(),
            ],
            'DailyInstallmentRefresh' => [
                'installments_to_be_updated' => DailyInstallmentRefresh::getTotalCountForHandling($beforeDate),
                'installments_updated' => $this->getUpdatedInstallmentCount(),
            ],
            'CreateCollectorFeeWatcher' => [
                'collector_tax_to_be_created' => $this->getToBeCreatedCollectorTaxCount(),
                'collector_tax_created' => $this->getCreatedCollectorTaxCount(),
            ],
            'ComingDueDateReminder' => [
                'comming_due_date_count' => $this->getUpcomingInstallmentsDueDateCount(),
                'comming_due_date_to_be_sent' => $this->getUpcomingDueDateSmsToBeSentCount(),
                'comming_due_date_sent' => $this->getUpcomingDueDateSmsSentCount(),
            ],
            'OverdueNotification' => [
                'overdue_due_date_count' => $this->getOverdueInstallmentsCount(),
                'overdue_due_date_to_be_sent' => $this->getOverdueSmsToBeSentCount(),
                'overdue_due_date_sent' => $this->getOverdueSmsSentCount(),
            ],
            'LoanToBucketPlacer' => [
                'loan_in_buckets' => $this->getLoanBucketsCount(),
                'bucket_tasks_created' => $this->getBucketTasksCreated(),
            ],
            'EasyPayRefundTasksCommand' => [
                'epay_payments_for_refund_count' => $this->getEpayPaymentsForRefundCount(),
                'epay_payments_task_for_refund_count' => $this->getEpayPaymentTasksForRefundCount(),
                'epay_sale_task_for_refund_count' => $this->getEpaySaleTasksForRefundCount(),
            ],
            'EasyPayAttemptHealthCheck' => [
                'epay_incoming_payments_total' => $this->getIncomiongEpayPaymentsCount(),
                'epay_incoming_payments_undelivered' => $this->getIncomiongUndeliveredEpayPaymentsCount(),
            ],
            'TmpRequestCommand' => [
                'uncompletted_tmp_request_sale_tasks_count' => $this->getUnfinishedTmpRequestSaleTaskCount(),
                'uncompletted_tmp_request_handled_sale_tasks_count' => $this->getUnfinishedTmpRequestHandledSaleTaskCount(),
            ],
            'UnsignedLoansCommand' => [
                'unsigned_loan_sale_tasks_count' => $this->getUnsignedLoanSaleTaskCount(),
                'unsigned_loan_handled_sale_tasks_count' => $this->getUnsignedLoanHandledSaleTaskCount(),
            ],
            'GeneratePromoSaleTasks' => [
                'promo_interest_fee_sale_tasks_count' => $this->getPromoInterestFeeSaleTaskCount(),
                'promo_preapproved_sale_tasks_count' => $this->getPromoPreapprovedSaleTaskCount(),
            ],
            'GraceLoanResetCommand' => [
                'grace_loans_to_be_reset_count' => GraceLoanResetCommand::getLoans()->count(),
                'grace_loans_reset_count' => $this->getResetGraceLoanCount(),
            ],
        ];

        // CcrReportOut (monthly)
        $currentDate = Carbon::now();
        if ($currentDate->day == 1) {
            $logMessages['CcrReportOut']['ccr_cucr_created'] = (
                $this->getCcrCucReportCount() > 0
                ? 'yes'
                : 'NOT CREATED!'
            );
        }

        $this->send($logMessages);


        $newLogMessages = [];
        foreach ($logMessages as $key => $value) {
            foreach ($value as $subKey => $subValue) {
                $newLogMessages[] = $subKey . ' = ' . $subValue;
            }
        }
        $this->finishLog($newLogMessages, count($newLogMessages), count($newLogMessages), 'Checked command execution.');

        return Command::SUCCESS;
    }

    public function getUpdatedLoansStatsCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(las.loan_stats_id) as count
            FROM loan_actual_stats as las
            WHERE
                las.date = current_date
                AND las.daily_update_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getUpdatedClientStatsCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(cas.client_actual_stats_id) as count
            FROM client_actual_stats as cas
            WHERE cas.date = current_date
        "));

        return $res->count ?? 0;
    }

    public function getUpdatedInstallmentCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(i.installment_id) as count
            FROM installment as i
            WHERE
                i.accrued_updated_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
                OR i.late_updated_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getToBeCreatedCollectorTaxCount(): int
    {
        // format: product_id => days
        $settingsDays = $this->productSettingsService->getProductSettingsForCollectorDays();
        if (empty($settingsDays)) {
            return 0;
        }

        $productIds = array_keys($settingsDays);

        // format: product_id => percent
        $settingsPercents = $this->productSettingsService->getProductSettingsForCollectorPercents($productIds);
        if (empty($settingsPercents)) {
            return 0;
        }

        $installments = CreateCollectorFeeWatcher::getInstallmentsForUpdate($productIds, $settingsDays);

        $todo = 0;
        foreach ($installments as $installment) {
            // since we take multipple overdues days we need to check the proper one of our product
            $productId = (int) $installment->product_id;
            $productOverdueDays = $settingsDays[$productId];
            if ($productOverdueDays != $installment->overdue_days) {
                continue;
            }

            $todo++;
        }

        return $todo;
    }

    public function getCreatedCollectorTaxCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.tax_id) as count
            FROM tax as t
            WHERE
                t.type = 'collector'
                AND t.created_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getUpcomingInstallmentsDueDateCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(i.installment_id) as count
            FROM installment as i
            JOIN loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
            WHERE
                i.due_date = '" . Carbon::now()->addDays(2)->format('Y-m-d') . "'
                AND i.paid = 0
        "));

        return $res->count ?? 0;
    }

    public function getUpcomingDueDateSmsToBeSentCount(): int
    {
        return $this->smsRepo->getByTemplateIdsAndDateRange(
                [SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_COMING_DUE_DATE->value)['id']],
                now()->startOfDay(),
                now(),
                false
            )
            ->count();
    }

    public function getUpcomingDueDateSmsSentCount(): int
    {
        return $this->smsRepo->getByTemplateIdsAndDateRange(
                [SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_COMING_DUE_DATE->value)['id']],
                now()->startOfDay(),
                now(),
                true
            )
            ->count();
    }

    public function getOverdueInstallmentsCount(): int
    {
        $where = ["i.paid = 0"];
        foreach (OverdueDaysEnum::possibleDaysPhysicalOffice() as $dc) {
            $where[] = "i.due_date = '" . Carbon::now()->subDays($dc)->format('Y-m-d') . "'";
        }

        $res = DB::selectOne(DB::raw("
            SELECT count(i.installment_id) as count
            FROM installment as i
            JOIN loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
            WHERE " . implode(' AND ', $where) . "
        "));

        return $res->count ?? 0;
    }

    public function getOverdueSmsToBeSentCount(): int
    {
        $templateIds = [
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_3->value)['id'],
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_23->value)['id'],
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_60->value)['id'],
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_90->value)['id'],
        ];

        return $this->smsRepo->getByTemplateIdsAndDateRange(
                $templateIds,
                now()->startOfDay(),
                now(),
                false
            )
            ->count();
    }

    public function getOverdueSmsSentCount(): int
    {
        $templateIds = [
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_3->value)['id'],
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_23->value)['id'],
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_60->value)['id'],
            SmsTemplate::getTemplateByKey(SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_90->value)['id'],
        ];

        return $this->smsRepo->getByTemplateIdsAndDateRange(
                $templateIds,
                now()->startOfDay(),
                now(),
                true
            )
            ->count();
    }

    public function getLoanBucketsCount(): int
    {
        return $this->btRepo->getLoansBucketCount();
    }

    public function getBucketTasksCreated(): int
    {
        return $this->btRepo->getCreatedCount(now()->startOfDay());
    }

    public function getEpayPaymentsForRefundCount(): int
    {
        $command = app(EasyPayRefundTasksCommand::class);
        $payments = $command->getRefundablePayments();
        return $payments->count();
    }

    public function getEpayPaymentTasksForRefundCount(): int
    {
        return $this->ptRepo->getCreatedEasyPayRefundTaskCount(now()->startOfDay());
    }

    public function getEpaySaleTasksForRefundCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_UNRECEIVED_MONEY . "'
                AND t.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND t.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getIncomiongEpayPaymentsCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(p.payment_id) as count
            FROM payment as p
            WHERE
                p.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND p.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
                AND p.direction = 'in'
                AND p.payment_method_id = '" . PaymentMethod::PAYMENT_METHOD_EASYPAY . "'
        "));

        return $res->count ?? 0;
    }

    public function getIncomiongUndeliveredEpayPaymentsCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(p.payment_id) as count
            FROM payment as p
            WHERE
                p.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND p.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
                AND p.direction = 'in'
                AND p.payment_method_id = '" . PaymentMethod::PAYMENT_METHOD_EASYPAY . "'
                AND p.status != 'delivered'
        "));

        return $res->count ?? 0;
    }

    public function getUnfinishedTmpRequestSaleTaskCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_PRODUCT_REQUEST . "'
                AND t.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND t.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getUnfinishedTmpRequestHandledSaleTaskCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_PRODUCT_REQUEST . "'
                AND t.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND t.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
                AND t.status = 'done'
        "));

        return $res->count ?? 0;
    }

    public function getUnsignedLoanSaleTaskCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_APPLICATION . "'
                AND t.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND t.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getUnsignedLoanHandledSaleTaskCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_APPLICATION . "'
                AND t.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
                AND t.created_at < '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
                AND t.status = 'done'
        "));

        return $res->count ?? 0;
    }

    public function getPromoInterestFeeSaleTaskCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_NEW_INTEREST_FREE_LOAN . "'
                AND t.created_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getPromoPreapprovedSaleTaskCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.sale_task_id) as count
            FROM sale_task as t
            WHERE
                t.sale_task_type_id = '" . SaleTaskType::SALE_TASK_TYPE_ID_PRE_APPROVED . "'
                AND t.created_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getCcrCucReportCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(t.report_id) as count
            FROM ccr_report_out as t
            WHERE
                t.type = 'cucr'
                AND t.created_at >= '" . Carbon::now()->subDays(1)->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    public function getResetGraceLoanCount(): int
    {
        $res = DB::selectOne(DB::raw("
            SELECT count(l.loan_id) as count
            FROM loan as l
            WHERE l.grace_period_finished_at >= '" . Carbon::now()->format('Y-m-d 00:00:00') . "'
        "));

        return $res->count ?? 0;
    }

    private function send(array $data)
    {
        $html = $this->prepareHtml($data);
        $env = strtoupper(env('APP_ENV'));
        $prj = strtoupper(env('PROJECT'));

        $senderData = config('mail.log_monitor')['sender'];
        $receivers = config('mail.log_monitor')['receivers'];
        $title = $prj . ' - Daily Checker Credit-Hunter (' . $env . '): ' . Carbon::now()->format('Y-m-d');

        foreach ($receivers as $receiver) {
            (new EmailProvider)->sendEmail(
                $senderData['from'],
                $receiver,
                $title,
                $html
            );
        }
    }

    private function prepareHtml(array $data)
    {
        $html = '
            <html>
            <table>
                <tr>
                    <td>Command</td>
                    <td>Type</td>
                    <td>Count</td>
                </tr>
        ';

        foreach ($data as $command => $commandStats) {
            foreach ($commandStats as $type => $count) {
                $html .= '
                    <tr>
                        <td>' . $command . '</td>
                        <td>' . $type . '</td>
                        <td>' . $count . '</td>
                    </tr>
                ';
            }
        }

        $html .= '
            </table>
            </html>
        ';

        return $html;
    }
}
