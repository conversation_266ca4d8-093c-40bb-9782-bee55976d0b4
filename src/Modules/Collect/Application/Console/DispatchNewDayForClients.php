<?php

namespace Modules\Collect\Application\Console;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Domain\Events\DayHasPassedForClient;
use Throwable;

final class DispatchNewDayForClients extends CommonCommand
{
    private const MAX_CHUNK_SIZE = 100;

    protected $name = 'script:new-day-active-client';
    protected $signature = 'script:new-day-active-client {clientId?}';
    protected $description = 'New day event for client. Can be used for stats or messaging';

    public function __construct(
        private readonly ClientRepository $clientRepository,
    ) {
        parent::__construct();
    }

    public function handle(): void
    {
        $this->startLog();

        $clientId = (int) $this->argument('clientId');
        $builder = $this->clientRepository->getActiveOldClientsBuilder($clientId);

        $total = $builder->count();
        $processedClients = 0;

        $this->info('total = ' . $total);

        $builder
            ->chunkById(self::MAX_CHUNK_SIZE, function ($clients) use (&$processedClients) {
                /**
                 * @var Collection|Client[] $clients
                 */
                try {
                   $clients->each(function (Client $client) use (&$processedClients) {
                       DayHasPassedForClient::dispatch($client);
                       $processedClients++;

                   });

                   $this->info('-- done: ' . $processedClients);

                } catch (Throwable $t) {
                    $this->logError('Stats update failed', $t);
                }
            });

        $msg = "Finished daily client stats refresh. Refreshed: $processedClients";
        $logMessages = [
            'Processed client(s): ' . $processedClients,
            "Finished daily client stats refresh. Refreshed: $processedClients",
            $this->executionTimeString()
        ];
        $this->finishLog($logMessages, $total, $processedClients, $msg);
    }

    private function logError(string $startMessage, Throwable $e)
    {
        $msg = sprintf(
            $startMessage . ' Error %s, file: %s, line: %s',
            $e->getMessage(),
            $e->getFile(),
            $e->getLine()
        );
        $this->log($msg);
    }
}
