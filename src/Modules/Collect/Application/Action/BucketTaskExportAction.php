<?php

namespace Modules\Collect\Application\Action;

ini_set('max_execution_time', 9000);
ini_set('memory_limit', '2536M');

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Collect\Repositories\BucketTaskRepository;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Common\Enums\BucketTaskStatusEnum;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\BucketTaskHistory;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Contact;
use Modules\Common\Models\Loan;
use Modules\Communication\Repositories\CommunicationCommentRepository;
use Modules\Head\Services\SpreadsheetHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class BucketTaskExportAction
{
    private $collectorDecisionOptions = [];

    public function __construct(
        public BucketTaskRepository $bucketTaskRepository
    ) {
    }

    public function execute(array $filters)
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $filters['show_after'] = Carbon::now();
        if (empty($filters['bucketOfficeId'])) {
            $adminOfficeIds = auth()->user()->offices->pluck('office_id')->toArray();
            $filters['bucketOfficeIds'] = $adminOfficeIds;
        }
        if (empty($filters['bucket_task_status'])) {
            $filters['bucket_task_status_not'] = BucketTaskStatusEnum::STATUS_DONE->value;
        }

        $rows = $this->bucketTaskRepository->getBuilderByFilters($filters);

        $sheet->fromArray([$this->getHeaders()], null, 'A1');
        $rowIndex = 2; // Start from row 2 since row 1 has headers

        $this->collectorDecisionOptions = CollectorDecision::where('active', 1)
            ->pluck('name', 'collector_decision_id')
            ->toArray();

        $uniquIds = [];
        $rows->chunk(5000, function (Collection $collection) use (&$sheet, &$rowIndex, &$uniquIds) {
            foreach ($collection as $row) {

                if (isset($uniquIds[$row->loan_id])) {
                    continue;
                }

                $uniquIds[$row->loan_id] = $row->loan_id;

                $loan = $row->loan;
                $lastPayment = $loan->getLastPayment();
                $loanContacts = $this->getLoanContacts($loan);

                $rowData = [
                    preg_replace('/^0/', '359', $row->phone),
                    $row->client_id,
                    __('collect::buckets.bucket_' . $row->bucket_id),
                    $row->client_full_name,
                    $row->loan_id,
                    route('collect.bucket-tasks.process', $row->getKey()),
                    $this->getLastCommentsToExport($row->loan_id),
                    $this->getLastAttempts($loan),
                    $row->days_overdue,
                    $loan->getOverduedInstallments()->count(),
                    $row->amount_overdue,
                    $lastPayment?->created_at->format('d.m.Y'),
                    intToFloat($lastPayment?->amount),
                    intToFloat($row->loan_amount),
                    $row->product_type_id == 1 ? 1 : $row->loan_period,
                    $row->first_installment_date,
                    $loan->installments->first()->total_amount,
                    $loanContacts
                ];

                // Write single row to the correct position
                $sheet->fromArray([$rowData], null, 'A' . $rowIndex);
                $rowIndex++;
            }
        });
        unset($uniquIds);

        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Set the headers to force download the file
        $fileName = 'list_for_call_' . time() . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');

        // Write the spreadsheet to the output
        $writer->save('php://output');
        return ;
    }

    private function getLoanContacts(Loan $loan): string
    {
        $loanContacts = $loan->getLastContacts(3);

        if (empty($loanContacts)) {
            return '';
        }

        $str = [];
        foreach ($loanContacts as $contact) {
            $str[] = $contact->name . ': ' . preg_replace('/^0/', '359', $contact->phone);
        }

        return implode("\n", $str);
    }

    private function getLastAttempts(Loan $loan): string
    {
        $lastAttempts = app(BucketTaskRepository::class)->getLastBucketTasks($loan, 5);

        return $lastAttempts->map(function (BucketTaskHistory $bucketTaskHistory) {
            $decision = $this->collectorDecisionOptions[$bucketTaskHistory->collector_decision_id] ?? '';
            return Carbon::parse($bucketTaskHistory->created_at)->format('Y-m-d H:i:s') . ' ' . __($decision);
        })->implode("\n");
    }

    private function getLastCommentsToExport(int $loanId): string
    {
        $comments = app(CommunicationCommentRepository::class)->getPreparedByLoanId($loanId);
        if (empty($comments)) {
            return '';
        }

        $str = [];
        foreach ($comments as $comment) {
            $str[] = Carbon::parse($comment->created_at)->format('Y-m-d H:i:s') . ' ' . $comment->text;
        }

        return implode("\n", $str);
    }

    private function getHeaders(): array
    {
        return [
            __('table.Phone'),
            __('table.ClientId'),
            __('table.Bucket'),
            __('table.clientNames'),
            __('table.LoanId'),
            __('table.Task'),
            __('table.Comment'),
            __('head::clientCard.lastBucketTasks'),
            __('table.overdueDays'),
            __('head::clientCard.overdueInstallmentsCount'),
            __('table.OverdueAmount'),
            __('table.LastPayment'),
            __('head::clientCard.lastPaidSum'),
            __('table.LoanAmount'),
            __('table.CountInstallments'),
            __('head::clientCard.dateOfFirstInstallment'),
            __('head::installment.totalAmount'),
            __('table.Contact')
        ];
    }
}
