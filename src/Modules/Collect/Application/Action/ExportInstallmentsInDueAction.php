<?php

namespace Modules\Collect\Application\Action;

use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Modules\Collect\Repositories\InstallmentsInDueRepository;
use Modules\Common\Models\Installment;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

final readonly class ExportInstallmentsInDueAction
{
    public function __construct(private InstallmentsInDueRepository $repository)
    {
    }

    public function execute(array $filters): array
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            __('table.LoanId'),
            __('table.ClientFullName'),
            __('table.Phone'),
            __('table.Installment'),
            __('table.DueDate'),
            __('table.LoanAmountApproved'),
            __('table.PrimaryTotalRestAmount'),
            __('table.AccruedAmountTotal'),
            __('table.Fee'),
            __('table.OutstandingAmountTotal'),
            __('table.OverdueDays1'),
            __('table.OfficeName'),
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;

        ///// write xls here

        $this->repository->getCollection($filters)->chunkById(
            100,
            function (Collection $collection) use (&$sheet, &$rowIndex) {

                /**
                 * @var Installment $row
                 */
                foreach ($collection as $row) {
                    $carton = $row->loan->getCartonDb();

                    $r = [
                        $row->loan_id,
                        $row->client->getFullName(),
                        $row->client->phone,
                        $row->seq_num . '/' . $row->loan->installments_approved,
                        CarbonImmutable::parse($row->due_date)->format('d/m/Y'),
                        intToFloat($row->loan->amount_approved),
                        $row->getPrimaryTotalRestAmount(),
                        intToFloat($carton['accrued_amount_total']),
                        intToFloat($row->getSumTaxesRestAmount()),
                        intToFloat($carton['outstanding_amount_total']),
                        (string)$row->overdue_days,
                        getOfficeName($row->loan->office_id),
                    ];
                    // Populate data from the array
                    $rowIndex++;
                    foreach ($r as $columnIndex => $value) {
                        $sheet->setCellValue(
                            [$columnIndex + 1, $rowIndex],
                            $value
                        );
                    }
                }
            });

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'installments_in_due_export_' . time() . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
    }
}
