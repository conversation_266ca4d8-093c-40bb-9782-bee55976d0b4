<?php

namespace Modules\Collect\Application\Action;

use Modules\Common\Services\ZipArchiveService;
use Modules\Docs\Application\Actions\GenerateLegalDocsForLoansAction;
use Modules\Docs\Services\DocumentService;
use Modules\Head\Repositories\LoanRepository;
use RuntimeException;

class GenerateChosenLawDocsAction
{
    public function __construct(
        private LoanRepository $loanRepository,
        private ZipArchiveService $zipArchiveService,
        private GenerateLegalDocsForLoansAction $legalDocAction
    ) {}

    public function execute(array $loanIds): string
    {
        $loans = $this->loanRepository->getByIds($loanIds);
        $diff = array_diff($loanIds, $loans->pluck('loan_id')->toArray());
        if (count($diff)) {
            throw new RuntimeException('Failed to find loan with id #' . $diff[0]);
        }

        $filePathArr = [];
        foreach ($loans as $loan) {
            $filePathArr[$loan->loan_id] = $this->legalDocAction->execute($loan);
        }

        return $this->zipArchiveService->makeZipFile(
            $filePathArr,
            DocumentService::LEGAL_INFO_EXPORT_FILE_NAME
        );
    }
}
