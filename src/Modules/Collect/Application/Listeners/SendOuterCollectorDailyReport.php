<?php

declare(strict_types=1);

namespace Modules\Collect\Application\Listeners;

use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\SentMessage;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Modules\Collect\Events\OuterCollectorReport\OuterCollectorReportWasCreated;
use Modules\Communication\Emails\OuterCollector\OuterCollectorDailyReportMail;
use RuntimeException;

final class SendOuterCollectorDailyReport implements ShouldQueue
{
    use SerializesModels;

    public $queue = 'email';
    public int $tries = 3;
    public array $backoff = [1, 5, 10]; // Retry after 1 second, then 5 seconds, then 10 seconds.

    public function handle(OuterCollectorReportWasCreated $event): void
    {
        try {
            $this->sendReport($event->outerCollectorReport); // send report after create
        } catch (Exception $e) {
            report($e);
            throw new RuntimeException(
                'Failed to send OuterCollectorReport with id ' . $event->outerCollectorReport->getKey(),
                previous: $e
            );
        }
    }

    public function sendReport($outerCollectorReport): ?SentMessage
    {
        $sent = null;
        foreach ($outerCollectorReport->send_to as $to) {
            $sent = Mail::to($to)->send(
                new OuterCollectorDailyReportMail(
                    $outerCollectorReport, $outerCollectorReport->report_type->value
                )
            );
            sleep(1);
        }

        return $sent;
    }
}
