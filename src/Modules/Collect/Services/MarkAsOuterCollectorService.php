<?php

namespace Modules\Collect\Services;

use Exception;
use Modules\Common\Enums\BooleanCasesEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\LoanRepository;

class MarkAsOuterCollectorService extends BaseService
{

    /**
     * @throws Exception
     */
    public function markForCollector(array $loanIds, int $consultantId): void
    {
        $loans = app(LoanRepository::class)->getDbModel()->whereIn('loan_id', $loanIds)->get();
        if (!$loans->count()) {
            throw new Exception(__('messages.ErrorNoAvailableLoansToImport'));
        }

        $now = now();
        $adminId = getAdminId();

        $loans->each(function (Loan $loan) use ($consultantId, $now, $adminId) {
            $loan->setAttribute('consultant_id', $consultantId);
            $loan->setAttribute('outer_collector', BooleanCasesEnum::YES);
            $loan->setAttribute('outer_collector_from_date', $now);
            $loan->setAttribute('marked_as_outer_collector_by', $adminId);
            $loan->saveQuietly();
        });
    }
}
