<?php

namespace Modules\Collect\Services;

use Modules\Collect\Repositories\CollectorDecisionRepository;
use Modules\Common\Services\BaseService;

class CollectorDecisionService extends BaseService
{
    public function __construct(private readonly CollectorDecisionRepository $collectorDecisionRepository)
    {
        parent::__construct();
    }

    public function getByFilters(?int $limit, array $data, array $order)
    {
        $whereConditions = $this->getWhereConditions($data);

        return $this->collectorDecisionRepository->getAll(
            $limit,
            [],
            $whereConditions,
            $order
        );
    }
}
