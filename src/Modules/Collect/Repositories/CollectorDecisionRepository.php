<?php

namespace Modules\Collect\Repositories;

use Modules\Common\Models\CollectorDecision;
use Modules\Common\Repositories\BaseRepository;

class CollectorDecisionRepository extends BaseRepository
{
    public function getAll(
        ?int $limit,
        array $joins = [],
        array $where = [],
        array $order = [],
        bool $showDeleted = false
    ) {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = CollectorDecision::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );
        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return is_null($limit) ? $builder->get() : $builder->paginate($limit);
    }

    public function getById(int $id): ?CollectorDecision
    {
        return CollectorDecision::where(['collector_decision_id' => $id])->first();
    }
}
