<?php

namespace Modules\Collect\Repositories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Bucket;
use Modules\Common\Models\BucketHistory;
use Modules\Common\Repositories\BaseRepository;

class BucketRepository extends BaseRepository
{
    public function __construct(
        private readonly CurrentDate $currentDate,
        protected ?Bucket $bucket = null,
    ) {
        $this->bucket = $bucket ?: new Bucket();
    }

    public function getAll(
        ?int  $limit,
        array $joins = [],
        array $where = [],
        array $order = []
    ) {
        $builder = $this->bucket::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );
        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return !$limit ? $builder->get() : $builder->paginate($limit);
    }

    public function getById(int $bucketId): ?Bucket
    {
        return $this->bucket::where(['bucket_id' => $bucketId])->first();
    }

    public function create(array $data): Bucket
    {
        $bucket = new Bucket();
        $bucket->fill($data);
        $bucket->save();

        return $bucket;
    }

    public function update(Bucket $bucket, array $data): Bucket
    {
        $bucket->fill($data);
        $bucket->save();

        return $bucket;
    }

    public function getTypes()
    {
        return [
            $this->bucket::TYPE_STATIC,
            $this->bucket::TYPE_DYNAMIC,
        ];
    }

    public function getDynamicBuckets(): Collection
    {
        return Bucket::where([
            'type' => Bucket::TYPE_DYNAMIC,
        ])
            ->orderBy('overdue_days_from', 'DESC')
            ->get();
    }

    public function save(Bucket $bucket): ?Bucket
    {
        if ($bucket->save()) {
            $this->addHistoryEntry($bucket);
            return $bucket;
        }
        return null;
    }

    public function delete(Bucket $bucket): void
    {
        $this->addHistoryEntry($bucket);
        $bucket->delete();
    }

    public function addHistoryEntry(Bucket $bucket): void
    {
        $history = new BucketHistory;
        $history->fill($bucket->attributesToArray());
        $history->created_at = $this->currentDate->now();
        $history->save();
    }

    /**
    SELECT
        l.loan_id,
        c.client_id,
        l.product_id,
        l.amount_approved as amount,
        l.period_approved as period,
        cas.repaid_loans_count as repaid_credits_count,
        CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) as client_full_name,
        c.pin,
        c.phone,
        c.email,
        ls.current_overdue_amount as overdue_amount,
        ls.current_overdue_days as overdue_days_count,
        lb.loan_bucket_id,
        (select bt.bucket_task_id FROM bucket_task bt WHERE bt.loan_id = l.loan_id LIMIT 1) as bucket_task_id
    FROM loan l
    JOIN loan_actual_stats ls on (l.loan_id = ls.loan_id and ls.current_overdue_days >= 3 and ls.current_overdue_days <= 10)
    LEFT JOIN loan_bucket lb on lb.loan_id = ls.loan_id
    JOIN client c ON c.client_id = l.client_id
    JOIN client_actual_stats cas ON cas.client_id = c.client_id
    WHERE
        l.loan_status_id = 6
        and l.outer_collector != 1
        and l.juridical = 0
        and l.cession = 0
        and l.fraud = 0
        and (lb.loan_bucket_id is null or lb.placed_manually != true)
    */
    public function getBuilderLoansForBucketBasedOnStats(Bucket $bucket)
    {
        /// if bucket 1 set overdue_days_from to 1 because by default it is 3
        $overdue_days_from = $bucket->overdue_days_from;
        if ($bucket->getKey() === 1) {
            $overdue_days_from = 1; // we need, to show loans with overdue in: /collect/loan-buckets
        }

        return Loan::join('loan_actual_stats as ls', function ($join) use ($bucket, $overdue_days_from) {
                $join->on('loan.loan_id', '=', 'ls.loan_id')
                    ->whereBetween('ls.current_overdue_days', [$overdue_days_from, $bucket->overdue_days_to]);
            })
            ->leftJoin('loan_bucket as lb', 'lb.loan_id', '=', 'loan.loan_id')
            ->join('client as c', 'c.client_id', '=', 'loan.client_id')
            ->join('client_actual_stats as cas', 'c.client_id', '=', 'cas.client_id')
            ->where('loan.loan_status_id', '=', LoanStatus::ACTIVE_STATUS_ID)
            ->where('loan.juridical', '=', 0)
            ->where('loan.cession', '=', 0)
            ->where('loan.fraud', '=', 0)
            // ->where('loan.outer_collector', '!=', 1)
            ->where(function ($query) {
                $query->whereNull('lb.loan_bucket_id')
                    ->orWhere('lb.placed_manually', '!=', true);
            })
            ->where(function ($query) {
                $query
                    ->whereHas('activeBucketTaskSkipRule', function ($query) {
                        $query->where('till_date', '<=', Carbon::now()->format('Y-m-d'));
                    })
                    ->orWhereDoesntHave('activeBucketTaskSkipRule');
            })
            ->select(
                "loan.loan_id",
                "c.client_id",
                "loan.product_id",
                "loan.product_type_id",
                "loan.outer_collector",
                "loan.amount_approved as amount",
                "loan.period_approved as period",
                "cas.repaid_loans_count as repaid_credits_count",
                DB::raw("CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) as client_full_name"),
                "c.pin",
                "c.phone",
                "c.email",
                "ls.current_overdue_amount as overdue_amount",
                "ls.current_overdue_days as overdue_days_count",
                "lb.loan_bucket_id",
                "lb.bucket_id as current_bucket_id",
                DB::raw("(
                    SELECT bt.bucket_task_id
                    FROM bucket_task bt
                    WHERE
                        bt.loan_id = loan.loan_id
                        AND bt.status != 'done'
                    LIMIT 1
                ) as bucket_task_id")
            );
    }

    public function getBuilderLoansForBucketBasedOnCommingOverdue()
    {
        return Loan::join('loan_actual_stats as ls', function ($join) {
                $join->on('loan.loan_id', '=', 'ls.loan_id')
                    ->where('ls.current_overdue_days', 0);
            })
            ->join('installment as i', function ($join) {
                $join->on('loan.loan_id', '=', 'i.loan_id')
                    ->where('i.paid', 0)
                    ->where('i.due_date', Carbon::today()->addDays(1)->format('Y-m-d'));
            })
            ->leftJoin('loan_bucket as lb', 'lb.loan_id', '=', 'loan.loan_id')
            ->join('client as c', 'c.client_id', '=', 'loan.client_id')
            ->join('client_actual_stats as cas', 'c.client_id', '=', 'cas.client_id')
            ->where('loan.loan_status_id', '=', LoanStatus::ACTIVE_STATUS_ID)
            ->where('loan.juridical', '=', 0)
            ->where('loan.cession', '=', 0)
            ->where('loan.fraud', '=', 0)
            ->where('loan.outer_collector', '!=', 1)
            ->where(function ($query) {
                $query->whereNull('lb.loan_bucket_id')
                    ->orWhere('lb.placed_manually', '!=', true);
            })
            ->where(function ($query) {
                $query
                    ->whereHas('activeBucketTaskSkipRule', function ($query) {
                        $query->where('till_date', '<=', Carbon::now()->format('Y-m-d'));
                    })
                    ->orWhereDoesntHave('activeBucketTaskSkipRule');
            })
            ->select(
                "loan.loan_id",
                "c.client_id",
                "loan.product_id",
                "loan.amount_approved as amount",
                "loan.period_approved as period",
                "cas.repaid_loans_count as repaid_credits_count",
                DB::raw("CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) as client_full_name"),
                "c.pin",
                "c.phone",
                "c.email",
                "i.total_amount as overdue_amount",
                "lb.loan_bucket_id"
            );
    }

    /**
        delete from loan_bucket;
        delete from loan_bucket_history;
        delete from bucket_task;
        delete from bucket_task_history;
     */
    public function clearActiveLoansOutOfOverdue(?int $loanId = null)
    {
        DB::statement("
            INSERT INTO loan_bucket_history (
                loan_bucket_id,
                loan_id,
                bucket_id,
                placed_manually,
                comment,
                created_at,
                created_by,
                archived_at,
                archived_by
            )
            SELECT
                loan_bucket_id,
                loan_id,
                bucket_id,
                placed_manually,
                comment,
                created_at,
                created_by,
                NOW() as archived_at,
                1 as archived_by
            FROM
                loan_bucket
            WHERE loan_bucket.loan_id IN (
                SELECT
                    l.loan_id
                FROM loan l
                JOIN loan_actual_stats ls on (l.loan_id = ls.loan_id and ls.current_overdue_days < 3)
                LEFT JOIN loan_bucket lb on lb.loan_id = ls.loan_id
                WHERE
                    l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and (lb.loan_bucket_id is null or lb.placed_manually != true)
                    " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            DELETE FROM loan_bucket
            WHERE loan_bucket.loan_id IN (
                SELECT
                    l.loan_id
                FROM loan l
                JOIN loan_actual_stats ls on (l.loan_id = ls.loan_id and ls.current_overdue_days < 3)
                LEFT JOIN loan_bucket lb on lb.loan_id = ls.loan_id
                WHERE
                    l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and (lb.loan_bucket_id is null or lb.placed_manually != true)
                    " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            INSERT INTO bucket_task_history (
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            )
            SELECT
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                COALESCE(details, ' - removed by script - out from overdue 1' ) as details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            FROM
                bucket_task
            WHERE bucket_task.loan_id IN (
                SELECT
                    l.loan_id
                FROM loan l
                JOIN loan_actual_stats ls on (l.loan_id = ls.loan_id and ls.current_overdue_days < 3)
                LEFT JOIN loan_bucket lb on lb.loan_id = ls.loan_id
                WHERE
                    l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and (lb.loan_bucket_id is null or lb.placed_manually != true)
                    " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            DELETE FROM bucket_task
            WHERE bucket_task.loan_id IN (
                SELECT
                    l.loan_id
                FROM loan l
                JOIN loan_actual_stats ls on (l.loan_id = ls.loan_id and ls.current_overdue_days < 3)
                LEFT JOIN loan_bucket lb on lb.loan_id = ls.loan_id
                WHERE
                    l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and (lb.loan_bucket_id is null or lb.placed_manually != true)
                    " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");
    }

    public function clearNotActiveLoans(?int $loanId = null)
    {
        DB::statement("
            INSERT INTO loan_bucket_history (
                loan_bucket_id,
                loan_id,
                bucket_id,
                placed_manually,
                comment,
                created_at,
                created_by,
                archived_at,
                archived_by
            )
            SELECT
                loan_bucket_id,
                loan_id,
                bucket_id,
                placed_manually,
                comment,
                created_at,
                created_by,
                NOW() as archived_at,
                1 as archived_by
            FROM
                loan_bucket
            WHERE loan_bucket.loan_id IN (
                SELECT l.loan_id
                FROM loan l
                WHERE l.loan_status_id != " . LoanStatus::ACTIVE_STATUS_ID . "
                " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            DELETE FROM loan_bucket
            WHERE loan_bucket.loan_id IN (
                SELECT l.loan_id
                FROM loan l
                WHERE l.loan_status_id != " . LoanStatus::ACTIVE_STATUS_ID . "
                " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            INSERT INTO bucket_task_history (
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            )
            SELECT
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                COALESCE(details, ' - removed by script - out from overdue 2' ) as details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            FROM
                bucket_task
            WHERE bucket_task.loan_id IN (
                SELECT l.loan_id
                FROM loan l
                WHERE l.loan_status_id != " . LoanStatus::ACTIVE_STATUS_ID . "
                " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            DELETE FROM bucket_task
            WHERE bucket_task.loan_id IN (
                SELECT l.loan_id
                FROM loan l
                WHERE l.loan_status_id != " . LoanStatus::ACTIVE_STATUS_ID . "
                " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");
    }

    public function clearOuterCollector(?int $loanId = null)
    {
        // DB::statement("
        //     INSERT INTO loan_bucket_history (
        //         loan_bucket_id,
        //         loan_id,
        //         bucket_id,
        //         placed_manually,
        //         comment,
        //         created_at,
        //         created_by,
        //         archived_at,
        //         archived_by
        //     )
        //     SELECT
        //         loan_bucket_id,
        //         loan_id,
        //         bucket_id,
        //         placed_manually,
        //         comment,
        //         created_at,
        //         created_by,
        //         NOW() as archived_at,
        //         1 as archived_by
        //     FROM
        //         loan_bucket
        //     WHERE loan_bucket.loan_id IN (
        //         SELECT
        //             l.loan_id
        //         FROM loan l
        //         WHERE
        //             l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
        //             and l.outer_collector = 1
        //             " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
        //     )
        // ");

        // DB::statement("
        //     DELETE FROM loan_bucket
        //     WHERE loan_bucket.loan_id IN (
        //         SELECT
        //             l.loan_id
        //         FROM loan l
        //         WHERE
        //             l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
        //             and l.outer_collector = 1
        //             " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
        //     )
        // ");

        DB::statement("
            INSERT INTO bucket_task_history (
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            )
            SELECT
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                COALESCE(details, ' - removed by script - out from overdue 3' ) as details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            FROM
                bucket_task
            WHERE bucket_task.loan_id IN (
                SELECT
                    l.loan_id
                FROM loan l
                WHERE
                    l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and l.outer_collector = 1
                    " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");

        DB::statement("
            DELETE FROM bucket_task
            WHERE bucket_task.loan_id IN (
                SELECT
                    l.loan_id
                FROM loan l
                WHERE
                    l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and l.outer_collector = 1
                    " . (!empty($loanId) ? " and l.loan_id = " . $loanId : '') . "
            )
        ");
    }

    public function clearNotActualTasks(?int $loanId = null)
    {
        DB::statement("
            INSERT INTO bucket_task_history (
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            )
            SELECT
                bucket_task_id,
                bucket_id,
                loan_id,
                client_id,
                product_id,
                amount,
                promised_amount,
                COALESCE(details, ' - removed by script - out from overdue 4' ) as details,
                period,
                repaid_credits_count,
                client_full_name,
                pin,
                phone,
                email,
                overdue_amount,
                overdue_days_count,
                show_after,
                started_at,
                finished_at,
                processed_by,
                created_at,
                status,
                collector_decision_id,
                prev_decision_id,
                prev_promised_date,
                prev_promised_amount,
                prev_agent_id,
                parent_id
            FROM
                bucket_task
            WHERE bucket_task_id IN (
                select bt2.bucket_task_id
                from bucket_task bt2
                left join loan_bucket lb2 on lb2.loan_id = bt2.loan_id
                where
                    lb2.loan_bucket_id is null
                    OR (lb2.bucket_id != bt2.bucket_id)
            )
        ");

        DB::statement("
            DELETE FROM bucket_task
            WHERE bucket_task_id IN (
                select bt2.bucket_task_id
                from bucket_task bt2
                left join loan_bucket lb2 on lb2.loan_id = bt2.loan_id
                where
                    lb2.loan_bucket_id is null
                    OR (lb2.bucket_id != bt2.bucket_id)
            )
        ");
    }
}

