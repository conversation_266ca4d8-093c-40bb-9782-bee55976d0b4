<?php

namespace Modules\Collect\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Installment;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Repositories\BaseRepository;

final class InstallmentsInDueRepository extends BaseRepository
{
    public function __construct(private Installment $installment)
    {
    }

    public function getPaginator(
        array $filters = [],
        int   $perPage = 10
    ): LengthAwarePaginator {
        $builder = $this->builder($filters);
        // @phpstan-ignore-next-line
        return $builder->sortable()->paginate($perPage);
    }

    private function builder(array $filters = []): Builder
    {
        if (isset($filters['end_due_date'])) {
            unset($filters['end_due_date']);
        }

        if (!isset($filters['due_date'])) {
            $filters['due_date'] = Carbon::now()->format('Y-m-d') . ' - ' . Carbon::now()->format('Y-m-d');
        }

        if (!isset($filters['instalmentOfficeId'])) {
            $filters['instalmentOfficeId'] = getAdminOfficeIds();
        }

        return $this->installment->with(['loan', 'client'])
            ->where('paid', 0)
            ->whereHas('loan', fn($q) => $q->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID))
            ->filterBy($filters);
    }

    public function getCollection(array $filters = []): Builder
    {
        // @phpstan-ignore-next-line
        return $this->builder($filters)->sortable();
    }
}
