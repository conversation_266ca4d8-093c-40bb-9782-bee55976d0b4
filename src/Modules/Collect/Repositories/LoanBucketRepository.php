<?php

namespace Modules\Collect\Repositories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\LoanBucket;
use Modules\Common\Models\LoanBucketHistory;
use Modules\Common\Models\LoanContactActual;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Repositories\BaseRepository;

final class LoanBucketRepository extends BaseRepository
{
    public function __construct(private CurrentDate $currentDate)
    {
    }

    public function getByLoanId(int $loanId): ?LoanBucket
    {
        return LoanBucket::where(['loan_id' => $loanId])->first();
    }

    public function addHistoryEntry(LoanBucket $loanBucket): void
    {
        $history = new LoanBucketHistory;
        $history->fill($loanBucket->attributesToArray());
        $history->created_at = $this->currentDate->now();
        $history->save();
    }

    public function save(LoanBucket $loanBucket): ?LoanBucket
    {
        if ($loanBucket->save()) {
            // $this->addHistoryEntry($loanBucket);
            return $loanBucket;
        }
        return null;
    }

    public function delete(LoanBucket $loanBucket): void
    {
        // $this->addHistoryEntry($loanBucket); // TODO
        $loanBucket->delete();
    }

    public function getByFiltersPaginated(array $filters = []): LengthAwarePaginator
    {
        $builder = $this->getBuilderByFilters($filters);
        // @phpstan-ignore-next-line
        return $builder->sortable('loan_id')->paginate($this->getPaginationLimit());
    }

    public function getBuilderByFilters(array $filters): Builder
    {
        $builder = LoanBucket::query();
        $builder->with(['loan', 'loan.consultant', 'loan.relLoanGuarantors.guarant']);

        $builder->select(DB::raw("
            loan_bucket.bucket_id as bucket_id,
            client.client_id,
            CONCAT_WS(' ', client.first_name, client.middle_name, client.last_name) as client_name,
            client.pin,
            loan_bucket.loan_id as loan_id,
            loan.activated_at as start_date,
            loan_actual_stats.contract_end_date as end_date,
            loan.amount_approved as amount_withdraw,
            loan_actual_stats.current_overdue_amount as amount_overdue,
            loan_actual_stats.current_overdue_days as days_overdue,
            loan_actual_stats.overdue_installments as installments_overdue,
            loan_actual_stats.last_paid_date as last_paid_date,
            client.phone as phone,
            CONCAT_WS(' ', city.name, client_address.address) as address,
            (
                SELECT STRING_AGG(cc.text, '. ') AS concatenated_comments
                FROM communication_comment cc
                WHERE cc.loan_id = loan.loan_id
            ) as comment
        "));


        $builder
            ->join('loan', 'loan.loan_id', '=', 'loan_bucket.loan_id')
            ->join('client', 'client.client_id', '=', 'loan.client_id')
            ->join('loan_actual_stats', 'loan_actual_stats.loan_id', '=', 'loan_bucket.loan_id')
            ->leftJoin('client_address', function ($query) {
                $query->on('client.client_id', '=', 'client_address.client_id')
                    ->where('client_address.type', 'current')
                    ->where('client_address.last', '1')
                    ->whereRaw('client_address.client_address_id = (SELECT MAX(client_address_id) FROM client_address WHERE client_id = client.client_id)');
            })
            ->leftJoin('city', 'client_address.city_id', '=', 'city.city_id');

        // if (!empty($filters['payment_days_from']) || !empty($filters['payment_days_to'])) {
        //     $builder->whereRaw('loan_actual_stats.last_paid_date is not null');
        // }
        if (!empty($filters['payment_days_from'])) {
            $customDateFrom = Carbon::today()->subDays($filters['payment_days_from'])->format('Y-m-d');
            $builder->whereDate('loan_actual_stats.last_paid_date', '<=', $customDateFrom);
        }
        if (!empty($filters['payment_days_to'])) {
            $customDateTo = Carbon::today()->subDays($filters['payment_days_to'])->format('Y-m-d');
            $builder->whereDate('loan_actual_stats.last_paid_date', '>=', $customDateTo);
        }

        if (!empty($filters['pin'])) {
            $builder->where('client.pin', '=', $filters['pin']);
        }
        if (!empty($filters['loan_id'])) {
            $builder->where('loan_bucket.loan_id', $filters['loan_id']);
        }
        if (!empty($filters['clientNames'])) {
            $filters['clientNames'] = str_replace(' ', "%", trim($filters['clientNames']));

            $builder
                ->whereRaw("CONCAT_WS(' ',first_name,middle_name,last_name) ILIKE '%{$filters['clientNames']}%'")
                ->orWhereRaw("CONCAT_WS(' ',first_name,last_name,middle_name) ILIKE '%{$filters['clientNames']}%'")
                ->orWhereRaw("CONCAT_WS(' ',first_name,last_name) ILIKE '%{$filters['clientNames']}%'")
                ->orWhereRaw("CONCAT_WS(' ',first_name,middle_name) ILIKE '%{$filters['clientNames']}%'")
                ->orWhere('first_name', 'ILIKE', "%{$filters['clientNames']}%")
                ->orWhere('middle_name', 'ILIKE', "%{$filters['clientNames']}%")
                ->orWhere('last_name', 'ILIKE', "%{$filters['clientNames']}%");
        }
        if (!empty($filters['clientPhone'])) {
            $builder->where('phone', $filters['clientPhone']);
        }
        if (!empty($filters['bucket_ids'])) {
            $builder->whereIn('loan_bucket.bucket_id', $filters['bucket_ids']);
        }
        if (isset($filters['outer_collector']) && is_numeric($filters['outer_collector'])) {
            $builder->where('loan.outer_collector', $filters['outer_collector']);
        }
        if (!empty($filters['amount_from'])) {
            $builder->where('loan.amount_approved', '>=', floatToInt($filters['amount_from']));
        }
        if (!empty($filters['office_id'])) {
            $builder->where('loan.office_id', '=', $filters['office_id']);
        }
        if (!empty($filters['consultant_id'])) {
            $builder->whereIn('loan.consultant_id', $filters['consultant_id']);
        }
        if (!empty($filters['amount_to'])) {
            $builder->where('loan.amount_approved', '<=', floatToInt($filters['amount_to']));
        }
        if (!empty($filters['overdue_amount_from'])) {
            $builder->where(function ($query) use (&$filters) {
                $query->where('loan_actual_stats.current_overdue_amount', '>=', $filters['overdue_amount_from']);
            });
        }
        if (!empty($filters['overdue_amount_to'])) {
            $builder->where(function ($query) use (&$filters) {
                $query->where('loan_actual_stats.current_overdue_amount', '<=', $filters['overdue_amount_to']);
            });
        }
        if (!empty($filters['overdue_days_from'])) {
            $builder->where(function ($query) use (&$filters) {
                $query->where('loan_actual_stats.current_overdue_days', '>=', $filters['overdue_days_from']);
            });
        }
        if (!empty($filters['overdue_days_to'])) {
            $builder->where(function ($query) use (&$filters) {
                $query->where('loan_actual_stats.current_overdue_days', '<=', $filters['overdue_days_to']);
            });
        }

        if (!empty($filters['loanStatusId'])) {
            $builder->whereIn('loan.loan_status_id', $filters['loanStatusId']);
        }

        if (!empty($filters['collect_contact_phone'])) {
            $contactPersonLoanIds = LoanContactActual::query()
                ->distinct('loan_id')
                ->select('loan_id')
                ->whereHas('contact', function ($query) use ($filters) {
                    $query->where('phone', $filters['collect_contact_phone']);
                })
                ->pluck('loan_id')
                ->toArray();

            $builder->whereIn('loan.loan_id', $contactPersonLoanIds)
                ->where('loan.loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
                ->where('loan_actual_stats.current_overdue_days', '>', 0);
        }

        $builder->whereRaw('loan.office_id IN (select ao.office_id from administrator_office ao where ao.administrator_id = ' . getAdminId() . ')');

        return $builder;
    }
}
