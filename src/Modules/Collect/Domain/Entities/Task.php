<?php

namespace Modules\Collect\Domain\Entities;

use Illuminate\Support\Carbon;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Collect\Domain\Events\CollectTaskFinished;
use Modules\Collect\Domain\Exceptions\AdminDoesntBelongToOffice;
use Modules\Collect\Domain\Exceptions\AdminNotAuthenticated;
use Modules\Collect\Domain\Exceptions\BucketTaskNotFoundById;
use Modules\Collect\Domain\Exceptions\CollectorDecisionNotFound;
use Modules\Collect\Domain\Exceptions\ProcessedByDifferentAdmin;
use Modules\Collect\Domain\Exceptions\TaskOwnerNotFound;
use Modules\Collect\Http\Dto\CloseBucketTaskDto;
use Modules\Collect\Repositories\BucketTaskRepository as Repo;
use Modules\Collect\Repositories\CollectorDecisionRepository;
use Modules\Common\Domain\AggregateRootInterface;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BucketTask as DbModel;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanMeta;
use Modules\Communication\Services\CommunicationCommentService;

class Task extends DomainModel implements AggregateRootInterface
{
    private ?DbModel $dbModel;
    private ?DbModel $newDbModel;
    private $bucketObj = null;

    public function __construct(
        private readonly Repo                        $repo,
        private readonly CurrentDate                 $currentDate,
        private readonly CollectorDecisionRepository $decRepo,
        private readonly TaskShowAfter               $taskShowAfter,
        private readonly AdministratorRepository     $adminRepo,
        private readonly CommunicationCommentService $communicationCommentService
    ) {
    }

    /*************************** BUILDING *************************************/

    public static function loadSelf(int $id): self
    {
        return app()->make(self::class)->loadById($id);
    }

    public function loadById(int $id): AggregateRootInterface
    {
        $dbModel = $this->repo->getById($id);
        if (!$dbModel) {
            throw new BucketTaskNotFoundById($id);
        }
        return $this->buildFromExisting($dbModel);
    }

    // used in: LoanForBucket->closeTask()
    public function buildFromDbLoan(DbLoan $dbLoan): self
    {
        return $this->setDbModel($dbLoan->bucketTask);
    }

    // used in: ProcessBucketTaskAction
    public function buildFromExisting(DbModel $dbModel): self
    {
        $this->setDbModel($dbModel);

        return $this;
    }

    private function setDbModel(?DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    /************************************* INTERFACE *********************************/

    // used in: ProcessBucketTaskAction
    public function process(?int $adminId): self
    {
        // if already process such task -> do nothing
        if (
            $this->dbModel->status == DbModel::STATUS_PROCESSING
            && $this->dbModel->processed_by == getAdminId()
        ) {
            return $this;
        }

        $this
            ->setAdminId($adminId)
            ->setStatus(DbModel::STATUS_PROCESSING)
            ->setStartTime()
            ->save($this->dbModel);

        return $this;
    }

    // used in: LoanForBucket->closeTask()
    public function close(CloseBucketTaskDto $dto): self
    {
        if (!$this->dbModel) {
            throw new \Exception('No bucket task set for finilazing');
        }

        return $this
            ->setAdminId($dto->administrator_id)
            ->setStatus(DbModel::STATUS_DONE)

            ->setCollectorDecision($dto->collector_decision_id)
            ->setPromisedAmount($dto->promise_amount)
            ->setPromisedDate($dto->show_after)
            ->setDetails($dto)

            ->closeParent()
            ->createChildIfNeeded($dto)
            ->createMetaForExternalPaymentAgreement($dto);
    }

    /****************************************INTERNAL*******************************/

    private function createMetaForExternalPaymentAgreement(CloseBucketTaskDto $dto): self
    {
        if (0 === $dto->special_payment_agreement) {
            return $this;
        }

        /// add meta
        $this->dbModel->loan->addMeta(LoanMeta::LOAN_SPECIAL_PAYMENT_AGREEMENT, $dto->show_after);

        return $this;
    }

    private function setAdminId(?int $adminId): self
    {
        if (!$adminId) {
            throw new AdminNotAuthenticated();
        }

        //expiring or processing existing model
        if ($this->dbModel->status === DbModel::STATUS_NEW) {
            $officeId = $this->dbModel->loan->office_id;
            $adminId !== Administrator::SYSTEM_ADMINISTRATOR_ID;

            if (!$this->adminRepo->isAdminFromOffice($adminId, $officeId)) {
                throw new AdminDoesntBelongToOffice($adminId, $officeId);
            }

            $this->dbModel->processed_by = $adminId;
            return $this;
        }

        if (!$this->dbModel->processed_by) {
            throw new TaskOwnerNotFound($this->dbModel->getKey());
        }

        if ($this->dbModel->processed_by !== $adminId) {
            throw new ProcessedByDifferentAdmin($this->dbModel->processed_by);
        }

        return $this;
    }

    private function setStartTime(): self
    {
        $this->dbModel->started_at = $this->currentDate->now();

        return $this;
    }

    private function setStatus(string $newStatus): self
    {
        $this->dbModel->status = $newStatus;

        return $this;
    }

    private function setCollectorDecision(int $decisionId): self
    {
        if (!$this->dbModel) {
            return $this;
        }

        $cd = $this->decRepo->getById($decisionId);
        if (!$cd) {
            throw new CollectorDecisionNotFound($decisionId);
        }

        $this->dbModel->collector_decision_id = $cd->getKey();
        $this->dbModel->setRelation(CollectorDecision::class, $cd);

        return $this;
    }

    private function setPromisedAmount(?float $amount = null): self
    {
        $this->dbModel->promised_amount = $amount;

        return $this;
    }

    private function setPromisedDate(?Carbon $datetime = null): self
    {
        $this->dbModel->promised_date = $datetime;

        return $this;
    }

    private function createCommunicationComment(string $text): void
    {
        $this->communicationCommentService->create([
            'client_id' => $this->dbModel->loan->client_id,
            'loan_id' => $this->dbModel->loan->loan_id,
            'text' => 'Събиране: ' . $text,
            'administrator_id' => getAdminId(),
        ]);
    }

    private function setDetails(CloseBucketTaskDto $dto): self
    {
        $details = '';

        if ($dto->collector_decision_id === CollectorDecision::PROMISE) {

            $details = $dto->show_after ? 'Ще плати на: ' . Carbon::parse($dto->show_after)->format('d.m.Y H:i') : '';

            if ($dto->details) {
                $this->createCommunicationComment($dto->details);
                $details .= ' Коментар на агента: ' . $dto->details;
            }

        } else if ($dto->collector_decision_id === CollectorDecision::CALL_LATER) {

            if (!empty($dto->show_after) && preg_match('/[0-9]{4}-[0-9]{2}-[0-9]{2}/', $dto->show_after)) {
                $details = 'Обаждане на ' . Carbon::parse($dto->show_after)->format('d.m.Y H:i');
            }

        } else {
            $details = $dto->details;

            if ($details) {
                $this->createCommunicationComment($details);
            }
        }

        $this->dbModel->details = mb_strcut($details, 0, 255);

        return $this;
    }

    private function closeParent(): self
    {
        $this->dbModel->finished_at = Carbon::now();
        $this->dbModel->duration = $this->dbModel->finished_at->diffInSeconds(Carbon::parse($this->dbModel->started_at));

        $oldBT = $this->save($this->dbModel);
        if (empty($oldBT->bucket_task_id)) {
            throw new \Exception('Failed to save parent bucket task');
        }

        // SendEmailAboutWrongPhoneListener - send email for wrong phones
        CollectTaskFinished::dispatchIf($this->dbModel->status === DbModel::STATUS_DONE, $this->dbModel);

        return $this;
    }

    private function createChildIfNeeded($dto): self
    {
        if (empty($this->dbModel->collector_decision->type)) {
            throw new \Exception('Can not close bucket task, no collector decision found');
        }

        if ($this->dbModel->collector_decision->type === CollectorDecision::TYPE_FINAL) {

            // move parent to history
            $this->repo->moveTaskToHistoryAndDeleteOriginal($this->dbModel->bucket_task_id, $dto);

            return $this;
        }

        // prepare data based on parent
        $this->newDbModel = new DbModel();
        $this->newDbModel->bucket_id = $this->dbModel->bucket_id;
        $this->newDbModel->client_id = $this->dbModel->client_id;
        $this->newDbModel->loan_id = $this->dbModel->loan_id;
        $this->newDbModel->product_id = $this->dbModel->product_id;
        $this->newDbModel->amount = $this->dbModel->amount;
        $this->newDbModel->period = $this->dbModel->period;
        $this->newDbModel->repaid_credits_count = $this->dbModel->repaid_credits_count;
        $this->newDbModel->client_full_name = $this->dbModel->client_full_name;
        $this->newDbModel->pin = $this->dbModel->pin;
        $this->newDbModel->phone = $this->dbModel->phone;
        $this->newDbModel->email = $this->dbModel->email;
        $this->newDbModel->overdue_amount = $this->dbModel->overdue_amount;
        $this->newDbModel->overdue_days_count = $this->dbModel->overdue_days_count;
        $this->newDbModel->created_by = getAdminId();
        $this->newDbModel->status = DbModel::STATUS_NEW;
        $this->newDbModel->created_at =  now();

        // set decision data from parent task
        $this->newDbModel->prev_decision_id = $this->dbModel->collector_decision_id;
        $this->newDbModel->prev_promised_date = $this->dbModel->promised_date;
        $this->newDbModel->prev_promised_amount = $this->dbModel->promised_amount;
        $this->newDbModel->prev_agent_id = $this->dbModel->processed_by;
        $this->newDbModel->parent_id = $this->dbModel->bucket_task_id;

        // set show after based on decision from closing task
        $this->setShowAfter($dto->show_after);

        // save child task
        $newBT = $this->save($this->newDbModel);
        if (empty($newBT->bucket_task_id)) {
            throw new \Exception('Failed to save child bucket task');
        }

        // move parent to history
        $this->repo->moveTaskToHistoryAndDeleteOriginal($this->dbModel->bucket_task_id, $dto);

        return $this;
    }

    private function setShowAfter(?Carbon $showAfter = null): self
    {
        $this->newDbModel->show_after = $this->taskShowAfter
            ->buildFromTask($this->dbModel)
            ->setShowAfter($showAfter)
            ->showAfter;

        return $this;
    }

    private function save(DbModel $dbModel): ?DbModel
    {
        return $this->repo->save($dbModel);
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
