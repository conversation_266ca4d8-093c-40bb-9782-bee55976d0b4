<?php

namespace Modules\Collect\Forms;

use Modules\Common\FilterForms\BaseForm;
use Modules\Common\Models\CollectorDecision;

class CollectorDecisionForm extends BaseForm
{
    public function buildForm(): void
    {
        $collectorDecisionOptions = CollectorDecision::where('active', 1)
            ->pluck('name', 'collector_decision_id')
            ->toArray();

        foreach ($collectorDecisionOptions as $key => $val) {
            $collectorDecisionOptions[$key] = __($val);
        }

        $this->add('collector_decision_id', 'select', [
            'label' => __('head::clientCard.Choose'),
            'choices' => $collectorDecisionOptions,
            'empty_value' => __('table.SelectOption'),
            'attr' => [
                'required' => 'required'
            ]
        ]);


        $this->add('details', 'textarea', [
            'label' => __('head::clientCard.WriteComment'),
            'attr' => [
                'rows' => 5
            ]
        ]);

        $this->add('promise_amount', 'text', [
            'label' => __('head::clientCard.amount'),
            'attr' => [
                'data-amount-formatter' => 'true',
            ]
        ]);

        $this->add('show_after', 'text', [
            'label' => __('head::clientCard.ChooseDateTime'),
            'attr' => []
        ]);
    }
}
