<?php

namespace Modules\CashDesk\Console\Commands;

use Exception;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\CashDesk\Services\CashOperationalTransactionService;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Exceptions\NotFoundException;

class CashClosingTransaction extends CommonCommand
{

    protected $signature = 'script:cash-closing:daily';



    protected $description = "If cash closing transaction isn't made and initial balance is we need to close the cashDesk";

    public function __construct(
        private CashOperationalTransactionService $cashOperationalTransactionService
    ) {
        parent::__construct();
    }

    /**
     * @throws NotFoundException
     * @throws Exception
     */
    public function handle(): true
    {
        return true; // TEMPORARY STOPPED

        //Vot tak nado
//        foreach (Office::all() as $office) {
//            $dto = new TransactionDto(
//                $office->getKey(),
//                0,
//                CashOperationalTransactionTypeEnum::CASH_CLOSING,
//                'script',
//                'automatic closing',
//                CashOperationalTransactionDirectionEnum::OUT
//            );
//            $closing = app(MakeTransAction::class)->execute($dto);
//        }


//        $this->log('---Starting ' . $this->getClassName() . '---');
//
//        $start = microtime(true);
//        $cronLog = $this->logService->createCronLog($this->getClassName());
//
//        $offices = $this->officeService->getAllOffices(false);
//        $total = count($offices);
//        try {
//            $processed = 0;
//
//            foreach ($offices as $office) {
//                if ($office->isWeb()) {
//                    $total--;
//                    continue;
//                }
//                if ($this->processRow($office)) {
//                    $processed++;
//                }
//            }
//
//            $end = microtime(true);
//            $this->info('Time spent: ' . Calculator::round($end - $start, 3) . ' seconds');
//
//            $msg = 'Offices processed: ' . $processed;
//            $cronLog->finish($start, $total, $processed, $msg);
//            $this->log($msg);
//            $this->log('---Finish ' . $this->getClassName() . '---');
//        } catch (Throwable $e) {
//            $msg = 'Process error: ' . $e->getMessage();
//            $cronLog->finish($start, null, $total, $msg);
//            $this->log($msg);
//            $this->log('---Finish ' . $this->getClassName() . '---');
//            throw $e;
//        }
//
//        return true;
    }

    public function processRow(mixed $office): bool
    {
        // TODO: get office phones and alert to them and to Central office
        return true;
    }

    /**
     * @param mixed $office
     * @return void
     */
    public function closeOffice(mixed $office): bool
    {
        if (!$this->cashOperationalTransactionService->isInitialBalanceTransactionMade($office->office_id)) {
            return false;
        }

        if ($this->cashOperationalTransactionService->isDailyReportTransactionMade($office->office_id)) {
            return false;
        }

        $closingTransaction = [
            'office_id' => $office->office_id,
            'transaction_type' => CashOperationalTransactionTypeEnum::CASH_CLOSING->value,
            'direction' => CashOperationalTransactionDirectionEnum::OUT->value,
            'amount' => $this->cashOperationalTransactionService->getDailyBalance(
                $office->office_id,
                CashOperationalTransactionTypeEnum::CASH_CLOSING
            ),
        ];
        app(CashOperationalTransactionRepository::class)->create($closingTransaction);

        return true;
    }
}
