<?php

namespace Modules\CashDesk\Services;

use Modules\CashDesk\Enums\CashOperationalTransactionModalEnum;

readonly class CashDeskService
{
    public function __construct(private CashOperationalTransactionService $cashOperationalTransactionService) {}

    public function checkDailyTransactions(string $modalName, int $officeId): void
    {
        if (
            $modalName === CashOperationalTransactionModalEnum::INITIAL_BALANCE->value
            && $this->cashOperationalTransactionService->isInitialBalanceTransactionMade($officeId)
        ) {
            throw new \RuntimeException(__('table.initialBalanceAlreadyDone'));
        }

        if (
            $modalName === CashOperationalTransactionModalEnum::DAILY_REPORT->value
            && $this->cashOperationalTransactionService->isDailyReportTransactionMade($officeId)
        ) {
            throw new \RuntimeException(__('table.dailyReportAlreadyDone'));
        }
    }
}