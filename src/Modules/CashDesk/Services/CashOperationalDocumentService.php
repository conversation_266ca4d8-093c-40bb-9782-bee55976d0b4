<?php

namespace Modules\CashDesk\Services;

use Modules\CashDesk\Repositories\CashOperationalDocumentRepository;
use Modules\Common\Services\BaseService;

class CashOperationalDocumentService extends BaseService
{
    public function __construct(
        private readonly CashOperationalDocumentRepository $cashOperationalDocumentRepository,
    ) {
        parent::__construct();
    }

    public function getById(int $id): mixed
    {
        if (!$cashOperational = $this->cashOperationalDocumentRepository->getById($id)) {
            throw new \RuntimeException(__('cashdesk::cashDesk.WrongID'));
        }

        return $cashOperational;
    }
}
