<?php

namespace Modules\CashDesk\Services;

use Modules\CashDesk\Exports\OfficeBalanceExport;
use Modules\CashDesk\Http\Requests\DTO\CashDeskSearchRequest;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Office;

class CashBalanceExportFileService
{
    private string $fileName;
    private OfficeBalanceExport $officeBalanceExport;

    /**
     * @param CashOperationalTransactionService $cashOperationalTransactionService
     */
    public function __construct(
        protected CashOperationalTransactionService $cashOperationalTransactionService,
        protected CashOperationalTransactionRepository $cashOperationalTransactionRepository
    ) {
    }


    public function execute(
        CashDeskSearchRequest $request,
        Administrator $administrator,
        ?Office $office,
    ): void {
        $filterWhere = $request->setDefaults()->all();

        if ($office) {
            $filterWhere['office_id'] = $office->getKey();
        }
        $tableData = $this->cashOperationalTransactionRepository->filter($filterWhere)->get();

        $officeBalanceExport = new OfficeBalanceExport($tableData);

        if ($officeBalanceExport->getTransactions()->isEmpty()) {
            throw new \RuntimeException(
                __('cashdesk::cashDesk.TransactionListEmpty'),
                '',
                400
            );
        }

        $this->officeBalanceExport = $officeBalanceExport;
        $this->fileName = $administrator->getKey() . '/' . date('Ymd') . '-office-balance-export';
    }

    /**
     * @return OfficeBalanceExport
     */
    public function getOfficeBalanceExport(): OfficeBalanceExport
    {
        return $this->officeBalanceExport;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }
}