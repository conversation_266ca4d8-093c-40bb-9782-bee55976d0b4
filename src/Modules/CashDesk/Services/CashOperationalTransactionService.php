<?php

namespace Modules\CashDesk\Services;

use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\Common\Services\BaseService;

class CashOperationalTransactionService extends BaseService
{
    public function __construct(
        private readonly CashOperationalTransactionRepository $cashOperationalTransactionRepository,
    ) {
        parent::__construct();
    }

    public function update(int $id, array $data): CashOperationalTransaction
    {
        $this->checkAmountDifference($id, $data);

        // $data['amount'] = floatToInt($data['amount']);
        return $this->cashOperationalTransactionRepository->update($id, $data);
    }

    public function checkAmountDifference($id, $data)
    {
        if ($data['direction'] !== CashOperationalTransactionDirectionEnum::OUT) {
            return null;
        }

        $balance = $this->getBalance([
            'office_id' => $data['office_id'],
            'created_at' => Carbon::now()->format('Y-m-d')
        ]);

        $oldData = $this->cashOperationalTransactionRepository->getById($id);

        $amount = $oldData->amount - $data['amount'];
        $difference = $balance->difference - $amount;

        if ($oldData->amount < $data['amount']) {
            $amount = $data['amount'] - $oldData->amount;
            $difference = $balance->difference - $amount;
        }

        if ($difference < 0) {
            throw new \RuntimeException(__('cashdesk::cashDesk.SmallAmountCashDesk'));
        }
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return array|LengthAwarePaginator
     */
    public function getByWhereConditions(array $data, int $limit = null): array|LengthAwarePaginator
    {
        $cashTransactions = $this->cashOperationalTransactionRepository->listing($limit, $data);

        $cashTransactions->uneditable = false;


        if ($cashTransactions->firstWhere(
            'transaction_type',
            '=',
            CashOperationalTransactionTypeEnum::CASH_CLOSING->value
        )
        ) {
            $cashTransactions->uneditable = true;
        }

        return $cashTransactions;
    }

    /**
     * @param array $data
     * @return object
     */
    public function getBalance(array $data): object
    {
        $cashTransactions = $this->cashOperationalTransactionRepository->filter($data);

        return $this
            ->cashOperationalTransactionRepository
            ->getCashBalance($cashTransactions);
    }


    /**
     * TODO: Оптимизировать перед сдачей
     */
    public function isAvailableTransaction(int $officeId, ?CashOperationalTransactionTypeEnum $type): bool
    {
        $transactions = $this
            ->cashOperationalTransactionRepository
            ->getDailyOpeningAndClosingTransactionTypes($officeId);

        return in_array($type, $transactions);
    }


    public function getDailyBalance(
        int                                 $office_id,
        ?CashOperationalTransactionTypeEnum $transactionType
    ): int
    {

        $dailyBalance = 0;

        if ($transactionType === CashOperationalTransactionTypeEnum::INITIAL_BALANCE) {

            $dailyBalance = $this
                ->cashOperationalTransactionRepository
                ->getLastClosingBalance($office_id);

            $dailyBalance = $dailyBalance ?: 0;

        } elseif ($transactionType === CashOperationalTransactionTypeEnum::CASH_CLOSING) {

            $cashTransactions = $this->cashOperationalTransactionRepository->filter([
                'office_id' => $office_id,
                'created_at' => Carbon::now()->format('Y-m-d'),
            ]);

            $dailyBalance = $this
                ->cashOperationalTransactionRepository
                ->getCashBalance($cashTransactions)->initialBalance;
        }

        return $dailyBalance;
    }


    private function isDailyTransactionMade(
        $officeId,
        CashOperationalTransactionTypeEnum $cashOperationalTransactionType
    ): bool
    {
        $transactions = $this->cashOperationalTransactionRepository->getDailyOpeningAndClosingTransactionTypes($officeId);
        if (in_array($cashOperationalTransactionType, $transactions)) {
            return true;
        }

        return false;
    }

    public function isInitialBalanceTransactionMade(int $officeId): bool
    {
        return $this->isDailyTransactionMade($officeId, CashOperationalTransactionTypeEnum::INITIAL_BALANCE);
    }

    public function isDailyReportTransactionMade(int $officeId): bool
    {
        return $this->isDailyTransactionMade($officeId, CashOperationalTransactionTypeEnum::CASH_CLOSING);
    }

}
