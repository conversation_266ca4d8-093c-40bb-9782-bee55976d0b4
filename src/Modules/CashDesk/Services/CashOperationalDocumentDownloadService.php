<?php

namespace Modules\CashDesk\Services;

use Illuminate\Support\Facades\Storage;
use Modules\CashDesk\Models\CashOperationalDocument;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\Common\Helpers\OtherHelper;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CashOperationalDocumentDownloadService
{

    public function __construct(private readonly CashOperationalTransactionService $cashOperationalTransactionService)
    {
    }


    public function getDownloadHeaders(string $filePath): array
    {
        return [
            'Content-type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filePath . '"'
        ];
    }

    public function getFilePath(CashOperationalDocument $cashOperationalDocument): string
    {
        return OtherHelper::getFilePath($cashOperationalDocument->file);
    }

    public function openInNewTab(CashOperationalDocument $cashOperationalDocument)
    {
        return $this->getFilePath($cashOperationalDocument);
    }

    public function download(CashOperationalDocument $cashOperationalDocument): StreamedResponse
    {
        $filePath = $this->getFilePath($cashOperationalDocument);

        return Storage::disk('public')->download($filePath);
    }

    public function downloadByIdTransaction(int $id): StreamedResponse
    {
        $cashOperational = app(CashOperationalTransactionRepository::class)->getById($id);
        if (!$cashOperational) {
            throw new \RuntimeException(__('cashdesk::cashDesk.WrongID'));
        }
        return $this->download($cashOperational->latestCashOperationalDocument());
    }

    public function getFile(CashOperationalDocument $cashOperationalDocument): BinaryFileResponse
    {
        $filePath = $this->getFilePath($cashOperationalDocument);

        return response()->file($filePath, $this->getDownloadHeaders($filePath));
    }
}
