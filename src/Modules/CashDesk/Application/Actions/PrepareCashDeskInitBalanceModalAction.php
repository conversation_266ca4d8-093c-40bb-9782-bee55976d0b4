<?php

namespace Modules\CashDesk\Application\Actions;

use Modules\Admin\Services\OfficeService;
use Modules\CashDesk\Application\ActionDTO\ModalDTO;
use Modules\CashDesk\Enums\CashOperationalTransactionModalEnum;
use Modules\CashDesk\Http\Requests\CashDeskInitBalanceModalRequest;
use Modules\CashDesk\Services\CashDeskService;
use Modules\CashDesk\Services\CashOperationalTransactionService;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Administrator;

class PrepareCashDeskInitBalanceModalAction
{

    public function __construct(
        private readonly CashDeskService $cashDeskService,
        protected OfficeService $officeService,
        protected CashOperationalTransactionService $cashOperationalTransactionService,
    ) {
    }

    public function execute(
        CashDeskInitBalanceModalRequest $request,
        Administrator $administrator,
    ): ModalDTO {
        $officeId = $request->get('office_id');
        $modalName = $request->get('modalName');
        $modalEnum = CashOperationalTransactionModalEnum::from($modalName);

        $this->cashDeskService->checkDailyTransactions($modalName, $officeId);

        $selectOffice = $this->officeService->getOfficeById($officeId);
        $balance = $this->cashOperationalTransactionService->getDailyBalance(
            $officeId,
            $modalEnum->type()
        );
        $offices = $administrator->offices;

        return ModalDTO::from([
            'selectedOffice' => $selectOffice,
            'balance' => $balance,
            'offices' => $offices,
        ]);
    }
}
