<?php

namespace Modules\CashDesk\Application\Actions;

use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\CashDesk\Application\ActionDTO\TerminalIndexDTO;
use Modules\CashDesk\Application\ActionDTO\TerminalIndexItemDTO;
use Modules\CashDesk\FilterForms\TerminalLogListFilterForm;
use Modules\CashDesk\Http\Requests\TerminalLogIndexRequest;
use Modules\CashDesk\Models\TerminalLog;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\FiscalDevice;

readonly class PrepareTerminalLogIndexAction
{
    public function __construct(
        private AdministratorRepository $administratorRepository,
    ) {
    }

    public function execute(
        TerminalLogIndexRequest $request,
        Administrator $administrator,
        FormBuilder $formBuilder,
        int $limit = 10
    ): TerminalIndexDTO {

        $admin = getAdmin();
        $key = 'admin_fiscal_devices_' . $admin->administrator_id;
        $fiscalDeviceIds = \Cache::remember($key, 60 * 60, function () {
            return FiscalDevice::whereIn('office_id', getAdminOfficeIds())->pluck('fiscal_device_id')->all();
        });

        // $offices = $this->administratorRepository->getOfficesOrderByName($administrator);
        $offices = getOfficesFromArrayIds(getAdminOfficeIds());

        $tableData = TerminalLog::filterBy($request->validated())
            ->with('cashOperationalTransaction')
            ->whereIn('fiscal_device_id', $fiscalDeviceIds)
            ->orderBy('created_at', 'DESC')
            ->paginate($limit);
        $tableData = $tableData->through(function (TerminalLog $terminalLog) {
            return TerminalIndexItemDTO::fromModel($terminalLog);
        });

        $form = $formBuilder->create(TerminalLogListFilterForm::class, ['route' => ['terminal-log.index']]);

        return TerminalIndexDTO::from([
            'offices' => $offices,
            'form' => $form,
            'items' => $tableData
        ]);
    }
}
