<?php

namespace Modules\CashDesk\Application\ActionDTO;

use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\Common\Http\Dto\SpatieDto;

class TransactionDto extends SpatieDto
{
    public function __construct(
        public int                                     $office_id,
        public int                                     $amount,
        public CashOperationalTransactionTypeEnum      $transaction_type,
        public ?string                                 $basis = null,
        public ?string                                 $from_to_whom = null,
        public CashOperationalTransactionDirectionEnum $direction = CashOperationalTransactionDirectionEnum::IN,
        public int                                     $closing_balance = 0,
        public int                                     $active = 1,
        public ?int                                    $admin_id = null,
        public ?int                                    $client_id = null,
        public ?int                                    $loan_id = null,
        public ?string                                 $to_date = null,
        public ?int                                    $payment_method_id = null,
        public ?int                                    $bank_account_id = null,
    ) {}
}
