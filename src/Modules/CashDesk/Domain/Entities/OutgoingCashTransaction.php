<?php

namespace Modules\CashDesk\Domain\Entities;

use Carbon\Carbon;
use Exception;
use Modules\CashDesk\Application\ActionDTO\TransactionDto;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Exceptions\NotEnoughCash;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Models\CashOperationalTransaction as DbModel;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Payment as DbPayment;
use Modules\ThirdParty\Services\FiscalDevice\FiscalDeviceServiceFactory;

class OutgoingCashTransaction extends DomainModel
{
    private ?DbPayment $dbPayment = null;
    public ?Exception $fiscalDeviceException = null;

    public function __construct(
        private DbModel                              $dbModel,
        private CashOperationalTransactionRepository $repo,
        private IncomingCashTransaction              $incomingCashTransaction,
        private FiscalDeviceServiceFactory           $fiscalDeviceService,
    ) {}

    public function buildNewFromPayment(DbPayment $dbPayment): self
    {
        return $this->setPayment($dbPayment)
            ->setOffice($dbPayment->office_id)
            ->setTransactionType()
            ->setAmount($dbPayment->amount)
            ->setDirection()
            ->setFromToWhom($dbPayment->loan->client->getFullName())
            ->setLoanId($dbPayment->loan_id)
            ->setBasis()
            ->save() // event CashOperationalTransactionSaved -> GenerateCashDocsListener
            ->setPaymentCashTransactionLink($dbPayment)
            ->printReceipt();
    }

    public function buildNewFromDto(TransactionDto $dto): self
    {
        return $this
            ->setOffice($dto->office_id, $dto->to_date)
            ->setTransactionType($dto->transaction_type)
            ->setAmount($dto->amount, $dto->to_date)
            ->setDirection()
            ->setFromToWhom($dto->from_to_whom)
            ->setBasis($dto->basis)
            ->save($dto) // event CashOperationalTransactionSaved -> GenerateCashDocsListener
            ->printReceipt();
    }

    private function setPayment(DbPayment $payment): self
    {
        $this->dbPayment = $payment;
        $this->dbModel->payment_id = $payment->getKey();
        return $this;
    }

    private function setOffice(int $officeId, ?string $to_date = null): self
    {
        // тр да има опенинг баланс от днес и да няма клосинг баланс от днес

        $transactions = $this->repo->getDailyOpeningAndClosingTransactionTypes($officeId, $to_date);
        if (empty($transactions)) {
            throw new \RuntimeException(__('cashdesk::cashDesk.NoInitialBalanceRecord'));
        }

        if (!in_array(CashOperationalTransactionTypeEnum::INITIAL_BALANCE, $transactions)) {
            throw new \RuntimeException(__('cashdesk::cashDesk.NoInitialBalanceRecord'));
        }

        if (in_array(CashOperationalTransactionTypeEnum::CASH_CLOSING, $transactions)) {
            throw new \RuntimeException(
                __('cashdesk::cashDesk.NoClosingBalanceRecordYesterday',[
                'linkToClosePreviewDay' => '<button type="button" class="btn btn-success mt-3 closeBalanceFromYesterday">' . __('Close balance from yesterday') . '</button>'
            ]));
        }

        $this->dbModel->office_id = $officeId;

        return $this;
    }

    public function setTransactionType(?CashOperationalTransactionTypeEnum $type = null, ?string $stringType = null): self
    {
        if (!$type && $stringType) {
            $type = CashOperationalTransactionTypeEnum::tryFrom($stringType);
        }
        $this->dbModel->transaction_type = $type ?: CashOperationalTransactionTypeEnum::LOAN_PAYOUT;

        return $this;
    }

    private function setAmount(int $amount, ?string $to_date = null): self
    {
        $officeId = $this->dbModel->office_id;
        $cashInOffice = $this->repo->getCashBalanceForOffice($officeId, $to_date);

        if ($this->dbModel->transaction_type->isClosing()) {
            $amount = $cashInOffice;
        } elseif ($amount > $cashInOffice) {
            throw new NotEnoughCash($officeId, $amount, $cashInOffice);
        }

        $this->dbModel->amount = $amount;
        $this->dbModel->amount_signed = $amount * -1;

        return $this;
    }

    public function setDirection(): self
    {
        $this->dbModel->direction = CashOperationalTransactionDirectionEnum::OUT;
        return $this;
    }

    public function setFromToWhom(?string $fromToWhom): self
    {
        if ($fromToWhom) {
            $this->dbModel->from_to_whom = $fromToWhom;
        }
        return $this;
    }

    public function setLoanId(int $loanId): self
    {
        $this->dbModel->loan_id = $loanId;
        return $this;
    }

    public function setBasis(?string $basis = null): self
    {
        if (!$basis && $this->dbModel->loan_id) {
            $basis = __('cashdesk::cashDesk.disbursed_loan_number', ['loan_id' => $this->dbModel->loan_id]);
        }
        $this->dbModel->basis = $basis;
        return $this;
    }


    private function save(?TransactionDto $dto = null): self
    {
        if ($dto?->to_date) {

            // todo, need to check what is it
            $lastTransaction = CashOperationalTransaction::where('office_id', $dto->office_id)->orderBy('cash_operational_transaction_id', 'DESC')->first();
            if ($lastTransaction->created_at?->diff(Carbon::now())->days > 1) {
                $dto->to_date = $lastTransaction->created_at->format('Y-m-d');
            }

            $this->dbModel->created_at = $dto->to_date;
            $this->dbModel->updated_at = $dto->to_date;
        }

        $this->repo->save($this->dbModel);

        return $this;
    }

    private function printReceipt(): self
    {
        $additionalText = $this->dbModel->loan_id
            ? __('payments::fiscalDevice.disbursed_loan', [
                'loan_id' => $this->dbModel->loan_id,
                'date' => $this->dbModel->loan->created_at->format('d.m.Y'),
            ])
            : '';

        try {

            if ($this->dbModel->transaction_type->isClosing()) {
                $this->fiscalDeviceService->makeDailyReport(
                    $this->dbModel->office->fiscalDevice,
                    $this->dbModel->office
                );
            } else {
                $this->fiscalDeviceService->makeServiceReceipt(
                    $this->dbModel,
                    $this->dbModel->office->fiscalDevice,
                    $additionalText
                );
            }

        } catch (\Throwable $exception) {
            $this->fiscalDeviceException = $exception;
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    private function setPaymentCashTransactionLink(?DbPayment $payment = null): self
    {
        if (!empty($payment->payment_id)) {
            $payment->cash_operational_transaction_id = $this->dbModel->cash_operational_transaction_id;
            $payment->save();
        }

        return $this;
    }
}
