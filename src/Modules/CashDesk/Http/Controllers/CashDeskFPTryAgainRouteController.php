<?php

namespace Modules\CashDesk\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Admin\Services\OfficeService;
use Modules\CashDesk\Domain\Exceptions\TransactionNotFoundById;
use Modules\CashDesk\Domain\Exceptions\TransactionNotFoundLoanId;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\CashDesk\Services\CashDeskService;
use Modules\CashDesk\Services\CashOperationalDocumentService;
use Modules\CashDesk\Services\CashOperationalTransactionService;
use Modules\CashDesk\Services\TremolSessionService;
use Modules\Common\Http\Controllers\BaseController;
use RuntimeException;

class CashDeskFPTryAgainRouteController extends BaseController
{
    public string $indexRoute = 'payment.cashDesk.list';

    public function __construct(
        protected OfficeService $officeService,
        protected CashOperationalTransactionService $cashOperationalTransactionService,
        protected CashOperationalDocumentService $cashOperationalDocumentService,
        protected CashDeskService $cashDeskService,
        protected AdministratorRepository $administratorRepository,
        private readonly TremolSessionService $tremolSessionService = new TremolSessionService,
    ) {
        parent::__construct();
    }

    public function __invoke(Request $request): RedirectResponse
    {
        $params = $this->tremolSessionService->getParams();
        $method = $this->tremolSessionService->getMethod();
        $redirectTo = $this->tremolSessionService->getRedirectTo($this->indexRoute);

        $this->cashOperationalTransactionServiceFactory($method, $params);
        $this->tremolSessionService->clear();

        return redirect()
            ->route($redirectTo)
            ->with('success', __('cashdesk::cashDesk.CreatedSuccessfully'));
    }

    private function cashOperationalTransactionServiceFactory($method, ?array $params): void
    {
        switch ($method) {
            case 'getBalance':
                $this->cashOperationalTransactionService->getBalance(...$params);

                return;
            case 'getById':
                $this->getById(...$params);

                return;
            case 'getByLoanId':
                $this->getByLoanId(...$params);

                return;
            default:
                throw new RuntimeException('incorrect action');
        }
    }

    public function getByLoanId(int $loanId): CashOperationalTransaction
    {
        $cashOperational = app(CashOperationalTransactionRepository::class)->getByLoanId($loanId);

        if (!$cashOperational) {
            throw new TransactionNotFoundLoanId($loanId);
        }

        return $cashOperational;
    }
    public function getById(int $id): CashOperationalTransaction
    {
        $cashOperational = app(CashOperationalTransactionRepository::class)->getById($id);
        if (!$cashOperational) {
            throw new TransactionNotFoundById($id);
        }

        return $cashOperational;
    }
}