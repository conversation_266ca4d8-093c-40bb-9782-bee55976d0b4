<?php

namespace Modules\CashDesk\Http\Controllers;

use Exception;
use Illuminate\View\View;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\CashDesk\Application\Actions\PrepareTerminalLogConfirmAction;
use Modules\CashDesk\Application\Actions\PrepareTerminalLogIndexAction;
use Modules\CashDesk\Application\Actions\PrepareTerminalLogResendAction;
use Modules\CashDesk\Http\Requests\DTO\TerminalLogConfirmRequest;
use Modules\CashDesk\Http\Requests\TerminalLogIndexRequest;
use Modules\CashDesk\Models\TerminalLog;
use Modules\Common\Http\Controllers\BaseController;

class TerminalLogController extends BaseController
{
    public function index(
        PrepareTerminalLogIndexAction $prepareCashDeskListAction,
        FormBuilder $formBuilder,
        TerminalLogIndexRequest $request
    ): View {

        $dto = $prepareCashDeskListAction->execute(
            $request,
            getAdmin(),
            $formBuilder,
            $this->getPaginationLimit()
        );

        return view('cashdesk::terminal-log.index', compact('dto'));
    }

    public function show(
        TerminalLog $terminalLog,
    ) {
        return view('cashdesk::terminal-log.show', ['item' => $terminalLog]);
    }

    public function confirmation(
        TerminalLog $terminalLog,
        PrepareTerminalLogConfirmAction $logAction,
        TerminalLogConfirmRequest $terminalLogRequest
    ) {
        $logAction->execute($terminalLog, $terminalLogRequest);

        return response()->json(['success' => true]);
    }

    /**
     * @throws Exception
     */
    public function resend(
        TerminalLog $terminalLog,
        PrepareTerminalLogResendAction $prepareTerminalLogResendAction,
    ) {
        $prepareTerminalLogResendAction->execute($terminalLog, config('cashdesk.tremolProcessing'));

        return back();
    }

    /**
     * @throws Exception
     */
    public function test(
        PrepareTerminalLogResendAction $prepareTerminalLogResendAction,
    ) {
        $logs = TerminalLog::limit(2)->get();
        $logs->each(function (TerminalLog $terminalLog) use ($prepareTerminalLogResendAction) {
            $prepareTerminalLogResendAction->execute($terminalLog, config('cashdesk.tremolProcessing'));
        });

        return response()->json(['success' => true]);
    }
}
