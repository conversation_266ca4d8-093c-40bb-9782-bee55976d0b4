<?php

namespace Modules\CashDesk\Repositories;

use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Traits\AdminTrait;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Events\CashOperationalTransactionSaved;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Repositories\BaseRepository;

class CashOperationalTransactionRepository extends BaseRepository
{
    use AdminTrait;

    public function listing(
        ?int $limit,
        array $data = [],
    ): array|LengthAwarePaginator {
        return $this->filter($data)
            ->orderBy('cash_operational_transaction_id', 'DESC')
            ->paginate($limit)
            ->withQueryString();
    }

    public function filter(
        array $data = [],
    ): Builder|CashOperationalTransaction {
        return CashOperationalTransaction::withTrashed()->filterBy($data);
    }

    public function getById(int $id): ?CashOperationalTransaction
    {
        return CashOperationalTransaction::where('cash_operational_transaction_id', $id)
            ->with(['updater', 'creator'])
            ->first();
    }

    public function getByLoanId(int $loanId): ?CashOperationalTransaction
    {
        return CashOperationalTransaction::where('loan_id', $loanId)
            ->with(['updater', 'creator'])
            ->first();
    }

    /**
     * USAGE:
     * - CashDesk/Domain/Entities/OutgoingCashTransaction.php:
     * - CashDesk/Domain/Entities/IncomingCashTransaction.php:
     *
     * @param CashOperationalTransaction $model
     * @return CashOperationalTransaction|null
     */
    public function save(CashOperationalTransaction $model): ?CashOperationalTransaction
    {
        if ($model->save()) {
            CashOperationalTransactionSaved::dispatch($model);

            return $model;
        }

        return null;
    }

    /**
     * USAGE: Commands/CashClosingTransaction.php
     *
     * @param array $data
     *
     * @return CashOperationalTransaction
     */
    public function create(array $data): CashOperationalTransaction
    {
        $cashOperational = new CashOperationalTransaction();
        if (is_float($data['amount'])) {
            $data['amount'] = floatToInt($data['amount']);
        }
        $data['amount_signed'] = $data['amount'];
        if ($data['direction'] === CashOperationalTransactionDirectionEnum::OUT->value) {
            $data['amount_signed'] *= -1;
        }
        $cashOperational->fill($data);
        $cashOperational->save();

        CashOperationalTransactionSaved::dispatch($cashOperational);

        return $cashOperational;
    }

    /**
     * @param int $id
     * @param array $data
     *
     * @return CashOperationalTransaction
     */
    public function update(int $id, array $data): CashOperationalTransaction
    {
        $cashOperational = self::getById($id);
        //Temporary BS. Will remove it later
        if (is_float($data['amount'])) {
            $data['amount'] = floatToInt($data['amount']);
        }
        $data['amount_signed'] = $data['amount'];
        if ($data['direction'] === CashOperationalTransactionDirectionEnum::OUT->value) {
            $data['amount_signed'] *= -1;
        }
        $cashOperational->fill($data);
        $cashOperational->save();

        return $cashOperational;
    }

    /**
     * @param Builder $query
     * @return object
     */
    public function getCashBalance(Builder $query): object
    {
        $query->whereDate('created_at', '=', Carbon::today());
        $initialBalance = (clone $query)
            ->where('transaction_type', CashOperationalTransactionTypeEnum::INITIAL_BALANCE)
            ->sum('amount');

        $earnings = (clone $query)
            ->where('direction', CashOperationalTransactionDirectionEnum::IN)
            ->where('amount', '>', 0)
            ->sum('amount');

        $expense = (clone $query)
            ->where('direction', CashOperationalTransactionDirectionEnum::OUT)
            ->sum('amount');

        // изтрити транзакции, на които им създаваме входящи с отрицателна сума
        // тр да ги добавим към разхода
        $rollbackedAmount = -1 * (clone $query)
                ->where('direction', CashOperationalTransactionDirectionEnum::IN)
                ->where('amount', '<', 0)
                ->sum('amount');

        $fpBalance = $earnings - $expense;
        $expense = $expense + $rollbackedAmount;

        return (object) [
            'difference' => $earnings - $expense,
            'earnings' => $earnings,
            'expense' => $expense,
            'fpBalance' => $fpBalance,
            'initialBalance' => $initialBalance
        ];
    }

    public function getCashBalanceForOffice(int $officeId, ?string $to_date = null): int
    {
        if ($to_date) {
            $lastTransaction = CashOperationalTransaction::where('office_id', $officeId)->orderBy(
                'cash_operational_transaction_id',
                'DESC'
            )->first();
            if ($lastTransaction->created_at?->diff(Carbon::now())->days > 1) {
                $to_date = $lastTransaction->created_at->format('Y-m-d');
            }
        }

        return CashOperationalTransaction
            ::withTrashed()
            ->where('office_id', $officeId)
            ->whereDate('created_at', $to_date ?? Carbon::now()->format('Y-m-d'))
            ->sum('amount_signed');
    }

    public function getCashSupplyForOffice(int $officeId): int
    {
        return CashOperationalTransaction
            ::where('office_id', $officeId)
            ->where('transaction_type', CashOperationalTransactionTypeEnum::CASH_SUPPLY)
            ->whereDate('created_at', Carbon::now()->format('Y-m-d'))
            ->sum('amount_signed');
    }

    /**
     * Get the initial_balance and cash_closing transactions for the day
     *
     * @param int $officeId
     *
     * @return array
     */
    public function getDailyOpeningAndClosingTransactionTypes(int $officeId, ?string $to_date = null): array
    {
        // normal flow for IN/OUT payments
        $dates = [
            Carbon::today()->startOfDay(),
            Carbon::today()->endOfDay(),
        ];

        // used for cashdesk, need to check how exactly
        if ($to_date) {
            $lastTransaction = CashOperationalTransaction::where('office_id', $officeId)->orderBy(
                'cash_operational_transaction_id',
                'DESC'
            )->first();
            if ($lastTransaction->created_at?->diff(Carbon::now())->days > 1) {
                $to_date = $lastTransaction->created_at->format('Y-m-d');
            }

            $dates = [
                $to_date,
                Carbon::today()->format('Y-m-d'),
            ];
        }

        return CashOperationalTransaction::select('transaction_type')
            ->whereIn('transaction_type', [
                CashOperationalTransactionTypeEnum::INITIAL_BALANCE,
                CashOperationalTransactionTypeEnum::CASH_CLOSING,
            ])
            ->whereBetween('created_at', $dates)
            ->where('office_id', '=', $officeId)
            ->pluck('transaction_type')
            ->all();
    }

    public function getYesterdaysOpeningAndClosingTransactions(int $officeId): array
    {
        return DB::table(CashOperationalTransaction::getTableName())
            ->select('transaction_type')
            ->whereIn('transaction_type', [
                CashOperationalTransactionTypeEnum::INITIAL_BALANCE,
                CashOperationalTransactionTypeEnum::CASH_CLOSING,
            ])
            ->whereBetween('created_at', [
                Carbon::yesterday()->format('Y-m-d'),
                Carbon::today()->format('Y-m-d'),
            ])
            ->where(['office_id' => $officeId])
            ->pluck('transaction_type')
            ->all();
    }

    public function getLastClosingBalance(int $office_id): int
    {
        /**
         * @var CashOperationalTransaction|null $closeTransaction
         */
        $closeTransaction = CashOperationalTransaction::query()
            ->where('transaction_type', [
                CashOperationalTransactionTypeEnum::CASH_CLOSING,
            ])
            ->where(['office_id' => $office_id])
            ->orderBy('created_at', 'DESC')
            ->limit(1)
            ->first();

        $amount = 0;

        if ($closeTransaction) {
            $amount = $closeTransaction
                ->withTrashed()
                ->where('office_id', $office_id)
                ->whereDate('created_at', '=', $closeTransaction->created_at->format('Y-m-d'))
                ->sum('amount_signed');
        }

        return $amount;
    }

    public function balanceClosed(int $officeId): bool
    {
        $openingCount = CashOperationalTransaction::where([
            'office_id' => $officeId,
            'transaction_type' => CashOperationalTransactionTypeEnum::INITIAL_BALANCE
        ])->count();
        $closingCount = CashOperationalTransaction::where([
            'office_id' => $officeId,
            'transaction_type' => CashOperationalTransactionTypeEnum::CASH_CLOSING
        ])->count();

        return $openingCount === $closingCount;
    }

    public function hasClosingTransactionToDate(int $officeId, string $toDate): ?CashOperationalTransaction
    {
        $toDate = Carbon::parse($toDate)->subDay();

        return CashOperationalTransaction::where([
            'office_id' => $officeId,
            'transaction_type' => CashOperationalTransactionTypeEnum::CASH_CLOSING->value,
        ])
            ->whereBetween('created_at', [
                $toDate->startOfDay()->toDateTime(),
                $toDate->endOfDay()->toDateTime(),
            ])
            ->first();
    }
}
