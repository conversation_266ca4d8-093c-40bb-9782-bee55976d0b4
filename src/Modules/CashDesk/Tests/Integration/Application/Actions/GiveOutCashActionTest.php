<?php

namespace Modules\CashDesk\Tests\Integration\Application\Actions;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithoutEvents;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Modules\CashDesk\Application\Actions\GiveOutCashAction;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalDocument;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Database\Seeders\Test\ActiveInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ApprovedLoanSeeder;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentDescriptionEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Document;
use Modules\Common\Models\File;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Docs\Services\DocumentService;
use Tests\TestCase;

class GiveOutCashActionTest extends TestCase
{
    use DatabaseTransactions;
    use WithoutEvents;

    const AMOUNT = 2000;

    public function testCashPaymentTaskCreatedOnApprove()
    {
        $cash = $this->createInitialBalance();
        $dbLoan = $this->createDbLoan();/** @var Loan $refLoan */
        $refLoan = DbLoan::find(ActiveLoanSeeder::LOAN_ID);
        $payments = $this->createPayments($dbLoan, $refLoan);

        /** @var \Modules\Common\Models\Payment $payments[0] **/
        $payment = $payments[0]->method() === PaymentMethodEnum::CASH
            ? $payments[0]
            : $payments[1];
        app(GiveOutCashAction::class)->execute($payment);
        $cots = CashOperationalTransaction::where('cash_operational_transaction_id', '!=', $cash->getKey())
            ->orderBy('cash_operational_transaction_id')
            ->get();
        $this->assertCount(2, $cots);
        /** @var CashOperationalTransaction $cashIn */
        $cashIn = $cots[0];
        $this->assertEquals(CashOperationalTransactionDirectionEnum::IN, $cashIn->direction);
        $this->assertEquals($refLoan->getEarlyRepaymentDebtDb(), $cashIn->amount);
        /** @var CashOperationalTransaction $cashOut */
        $cashOut = $cots[1];
        $this->assertEquals(self::AMOUNT, $cashOut->amount);
        /** test document values */
        $data = app(DocumentService::class)->generateCashDeskDocument($cashOut);
        /** @var CashOperationalDocument $cashDoc */
        $cashDoc = $data['cash_operational_document'];
        $html = $data['html'];
        unset($data['cash_operational_document'], $data['orderNumber'], $data['html']);
        $expectedVariables = [
            "basis" => "Усвоен кредит No: 5",
            "amount" => "20.00 лв",
            "firmName" => '"СТИК КРЕДИТ" АД',
            "from_to_whom" => "Калоян Патлеев Илиев",
            "amountInWords" => "двадесет лева",
            "transactionDate" => now()->format('d-m-Y'),
            "principalPaid" => 0,
            "regularInterest" => 0,
            "penaltyPaid" => 0,
            "penaltyInterest" => 0,
            "otherPaid" => 0,
            "bulstat" => "202557159",
            "clientPin" => "5609100932",
            "clientAddress" => "alalal 27",
            "clientFirmName" => "yagni ik",
            "clientIdCardIssueDate" => "2011-03-09",
            "clientIdCardIssueFrom" => "МВР",
            "clientAttorney" => ""
        ];
        $this->assertEquals($expectedVariables, $data);
        /** @var File $file */
        $file = File::orderBy('file_id', 'DESC')->first();
        $this->assertNotNull($file);
        $this->assertEquals($file->getKey(), $cashDoc->file_id);
        $fullPath = Storage::path($file->file_path . $file->file_name);
        $this->assertFileExists($fullPath);
        $this->assertStringContainsString("Калоян Патлеев Илиев", $html);
    }

    private function createInitialBalance(): CashOperationalTransaction
    {
        $cash = new CashOperationalTransaction();
        $cash->office_id = 2;
        $cash->transaction_type = CashOperationalTransactionTypeEnum::INITIAL_BALANCE;
        $cash->direction = CashOperationalTransactionDirectionEnum::IN;
        $cash->amount = self::AMOUNT;
        $cash->amount_signed = self::AMOUNT;
        $cash->created_at = now();
        $cash->save();
        return $cash;
    }

    private function createDbLoan(): DbLoan
    {
        $this->seed([
            ApprovedLoanSeeder::class,
            ActiveInstallmentsSeeder::class
        ]);
        /** @var DbLoan $dbLoan */
        $dbLoan = DbLoan::find(ApprovedLoanSeeder::LOAN_ID);
        $dbLoan->office_id = Office::OFFICE_ID_NOVI_PAZAR_1;
        $dbLoan->payment_method_id = PaymentMethodEnum::CASH->id();
        $dbLoan->amount_approved = self::AMOUNT;
        $dbLoan->save();
        DB::table('loan_refinance')->insert([
            'refinancing_loan_id' => ApprovedLoanSeeder::LOAN_ID,
            'refinanced_loan_id' => ActiveLoanSeeder::LOAN_ID,
            'refinanced_due_amount' => 1081
        ]);
        return $dbLoan;
    }

    private function createPayments(DbLoan $dbLoan, ?DbLoan $refinanced): Collection
    {
        $loanAmount = $dbLoan->amount_approved;
        $refinanceAmount = $refinanced->getEarlyRepaymentDebtDb();//1081
        $currentDate = new CurrentDate();
        $data = [[
            'client_id' => $dbLoan->client_id,
            'loan_id' => $dbLoan->loan_id,
            'office_id' => 2,
            'currency_id' => 1,
            'status' => PaymentStatusEnum::NEW,
            'payment_method_id' => $dbLoan->payment_method_id,
            'direction' => PaymentDirectionEnum::OUT,
            'source' => Payment::PAYMENT_SOURCE_CASH,
            'amount_received' => self::AMOUNT,
            'description'=>'',
            'amount_resto' => 0,
            'amount' => $loanAmount,
            'manual' => 1,
            'correction' => 0,
            'created_at' => $currentDate->daysAgo(5),
            'sent_at' => $currentDate->daysAgo(3),
            'created_by' => 2
        ]];
        if($refinanced){
            $data[] = [
                'client_id' => $dbLoan->client_id,
                'loan_id' => $refinanced->loan_id,
                'office_id' => 2,
                'currency_id' => 1,
                'status' => PaymentStatusEnum::NEW,
                'payment_method_id' => PaymentMethodEnum::OFFSET->id(),
                'direction' => PaymentDirectionEnum::IN,
                'source' => Payment::PAYMENT_SOURCE_CASH,
                'amount_received' => $refinanceAmount,
                'description'=>PaymentDescriptionEnum::REFINANCE_INCOMING->text($dbLoan->getKey()),
                'amount_resto' => 0,
                'amount' => $refinanceAmount,
                'manual' => 0,
                'correction' => 0,
                'created_at' => $currentDate->daysAgo(5),
                'sent_at' => $currentDate->daysAgo(3),
                'created_by' => 2
            ];
        }
        DB::table('payment')->insert($data);
        return Payment::all();
    }

}
