<?php

namespace Modules\Communication\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Common\Models\NotificationSetting;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The module namespace to assume when generating URLs to actions.
     *
     * @var string
     */
    protected $moduleNamespace = 'Modules\Communication\Http\Controllers';

    /**
     * Called before routes are registered.
     *
     * Register any model bindings or pattern based filters.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Route::model('emailTemplate', EmailTemplate::class);
        Route::model('smsTemplate', SmsTemplate::class);
        Route::model('notificationSetting', NotificationSetting::class);
        Route::model('logSmsTemplate', ChangelogSmsTemplate::class);
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Communication', '/Routes/web.php'));
        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Communication', '/Routes/email.php'));
        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Communication', '/Routes/sms.php'));
        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Communication', '/Routes/notification.php'));
        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Communication', '/Routes/communication.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
    }
}
