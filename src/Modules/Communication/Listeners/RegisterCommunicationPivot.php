<?php

namespace Modules\Communication\Listeners;

use Modules\Common\Entities\CommunicationPivot;
use Modules\Common\Models\CommunicationComment;
use Modules\Common\Models\Email;
use Modules\Common\Models\Sms;
use Modules\Communication\Application\Enums\CommunicationClassificationEnum;
use Modules\Communication\Events\CommunicationWasCreatedEvent;

class RegisterCommunicationPivot
{
    public function handle(CommunicationWasCreatedEvent $event): void
    {
        $dbModel = $event->dbModel;
        if ($dbModel instanceof Sms) {
            CommunicationPivot::create([
                'communication_type' => 'sms',
                'communication_key' => $dbModel->smsTemplate->key,
                'classification' => $dbModel->type,
                'communication_id' => $dbModel->sms_id,
                'client_id' => $dbModel->client_id,
                'loan_id' => $dbModel->loan_id,
                'created_by' => $dbModel->created_by,
            ]);
        }

        if ($dbModel instanceof Email) {
            $classification = CommunicationClassificationEnum::tryFrom($dbModel->type);
            CommunicationPivot::create([
                'communication_type' => 'email',
                'communication_key' => $dbModel->emailTemplate->key,
                'classification' => $classification?->value ?: CommunicationClassificationEnum::COMMENT->value,
                'communication_id' => $dbModel->email_id,
                'client_id' => $dbModel->client_id,
                'loan_id' => $dbModel->loan_id,
                'created_by' => $dbModel->created_by,
            ]);
        }

        if ($dbModel instanceof CommunicationComment) {
            $data = [
                'communication_type' => 'comment',
                'communication_key' => $dbModel->text,
                'classification' => 'comment',
                'communication_id' => $dbModel->getKey(),
                'client_id' => $dbModel->client_id,
            ];

            if (!empty($dbModel->loan_id)) {
                $data['loan_id'] = $dbModel->loan_id;
            }

            CommunicationPivot::create($data);
        }
    }
}
