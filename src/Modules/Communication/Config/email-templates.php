<?php

use Modules\Common\Models\Office;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;

return [
    'email_templates' => [

        // extend loan reminders
        EmailTemplateKeyEnum::EMAIL_TYPE_REFINANCE_EXTEND_LOAN_REMAINDER->value => [
            'title' => 'Удължи кредита си още днес!',
            'variables' => ['client_first_name', 'loan_extend_amount', 'loan_extend_amount_eur', 'application_id'],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'attach_to_offices' => [Office::OFFICE_ID_WEB]
        ],
        EmailTemplateKeyEnum::EMAIL_TYPE_7_DAYS_EXTEND_LOAN_REMAINDER->value => [
            'title' => 'Как да удължиш кредита си?',
            'variables' => ['client_first_name', 'loan_extend_amount', 'loan_extend_amount_eur', 'application_id'],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'attach_to_offices' => [Office::OFFICE_ID_WEB]
        ],
        EmailTemplateKeyEnum::EMAIL_TYPE_5_DAYS_OVERDUE_EXTEND_LOAN_REMAINDER->value => [
            'title' => 'Не изпадай в затруднение!',
            'variables' => ['client_first_name', 'loan_extend_amount', 'loan_extend_amount_eur', 'application_id'],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'attach_to_offices' => [Office::OFFICE_ID_WEB]
        ],

        // system
        EmailTemplateKeyEnum::NEW_APPLICATION_CREATED->value => [
            'id' => 19,
            'title' => 'Стандартен Европейски Формуляр (СЕФ)',
            'variables' => [
                'client_first_name',
                'client_last_name',
                'client_middle_name',
                'company_name',
                'company_phone',
                'loan_amount',
                'loan_amount_eur',
                'loan_term',
                'frequently_questions_link',
                'blog_link',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::LOAN_PARAMS_CHANGED->value => [
            'id' => 28,
            'title' => 'Стандартен Европейски Формуляр (СЕФ)',
            'variables' => [
                'client_first_name',
                'loan_amount',
                'loan_amount_eur',
                'loan_term',
                'offices_info_link',
                'sign_documents_link',
                'frequently_questions_link',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::LOAN_APPROVED->value => [
            'id' => 16,
            'title' => 'Договорна информация',
            'variables' => [
                'client_first_name',
                'client_last_name',
                'client_middle_name',
                'company_name',
                'application_id',
                'loan_instalment_sum',
                'loan_instalment_sum_eur',
                'loan_first_instalment_date',
                'offices_info_link',
                'frequently_questions_link',
                'how_to_pay_link',
                'loan_amount',
                'loan_amount_eur',
                'loan_term',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::LOAN_REJECTED->value => [
            'id' => 18,
            'title' => 'Заявлението ти не беше одобрено',
            'variables' => [
                'client_first_name',
                'frequently_questions_link',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],

        // auto aprove logic - related to smaller credit limit
        EmailTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT->value => [
            'title' => 'Одобрен си за по-ниска сума. Виж как да приемеш',
            'variables' => [
                'credit_limit',
                'credit_limit_eur',
                'client_first_name',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF->value => [
            'title' => 'Одобрен си за кредит. Виж как да приемеш',
            'variables' => [
                'credit_limit',
                'credit_limit_eur',
                'client_first_name',
                'refinance_receive_amount',
                'refinance_receive_amount_eur',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF_NOT_ENOUGH->value => [
            'title' => 'Отказан кредит. Виж нашето предложение',
            'variables' => [
                'credit_limit',
                'credit_limit_eur',
                'client_first_name',
                'refinance_due_amount',
                'refinance_due_amount_eur',
                'loan_extend_amount',
                'loan_extend_amount_eur',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],

        // collect
        EmailTemplateKeyEnum::UPCOMING_DUE_INSTALMENT->value => [
            'id' => 3,
            'title' => 'Предстои ти вноска по кредита',
            'variables' => [
                'client_first_name',
                'application_id',
                'loan_next_unpaid_installment_date',
                'loan_instalment_sum',
                'loan_instalment_sum_eur',
                'company_easypay_pin',
                'company_office_iban',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::CREDIT_OVERDUE_3->value => [
            'id' => 12,
            'title' => 'Имаш неплатена вноска',
            'variables' => [
                'client_first_name',
                'application_id',
                'loan_last_unpaid_installment_date',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'company_easypay_pin',
                'company_office_iban',
                'company_phone',
                'company_email',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::CREDIT_OVERDUE_12->value => [
            'id' => 13,
            'title' => 'Имаш неплатена вноска',
            'variables' => [
                'client_first_name',
                'application_id',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'company_easypay_pin',
                'company_office_iban',
                'company_phone',
                'company_email',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::CREDIT_OVERDUE_23->value => [
            'id' => 14,
            'title' => 'Кредита Ви е в просрочие',
            'variables' => [
                'client_name_prefix',
                'client_surname',
                'application_id',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'company_easypay_pin',
                'company_office_iban',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::CREDIT_OVERDUE_45->value => [
            'id' => 1,
            'title' => 'Кредита Ви е в просрочие',
            'variables' => [
                'client_name_prefix',
                'client_surname',
                'application_id',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'company_easypay_pin',
                'company_office_iban',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::CREDIT_OVERDUE_60->value => [
            'id' => 15,
            'title' => 'Кредита Ви е в просрочие! Платете незабавно!',
            'variables' => [
                'client_name_prefix',
                'client_surname',
                'application_id',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'company_easypay_pin',
                'company_office_iban',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::CREDIT_OVERDUE_90->value => [
            'id' => 2,
            'title' => 'Кредита Ви е в просрочие! Платете незабавно!',
            'variables' => [
                'client_name_prefix',
                'client_surname',
                'application_id',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'company_easypay_pin',
                'company_office_iban',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],

        EmailTemplateKeyEnum::COL_TASK_EXIT_WRONG_PHONE_OWNER->value => [
            'id' => 24,
            'title' => 'Не успяхме да се свържем с теб',
            'variables' => [
                "application_date",
                "client_primary_phone",
                "client_first_name",
                "client_surname",
                "application_id",
                "loan_last_unpaid_installment_date",
                "today_date",
                "due_amount",
                "due_amount_eur",
                "company_easypay_pin",
                "company_office_iban",
                "company_phone",
                "company_email"
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::COL_TASK_EXIT_NO_SUCH_PHONE->value => [
            'id' => 25,
            'title' => 'Не успяхме да се свържем с теб',
            'variables' => [
                "application_date",
                "client_primary_phone",
                "client_first_name",
                "client_surname",
                "application_id",
                "loan_last_unpaid_installment_date",
                "today_date",
                "due_amount",
                "due_amount_eur",
                "company_easypay_pin",
                "company_office_iban",
                "company_phone",
                "company_email"
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_COLLECT,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],

        EmailTemplateKeyEnum::SALES_TASK_EXIT_WRONG_PHONE_OWNER->value => [
            'id' => 29,
            'title' => 'Не успяхме да се свържем с теб',
            'variables' => [
                'client_primary_phone',
                'client_first_name',
                'client_last_name',
                'application_date',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SALES,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::SALES_TASK_EXIT_NO_SUCH_PHONE->value => [
            'id' => 30,
            'title' => 'Не успяхме да се свържем с теб',
            'variables' => [
                'client_primary_phone',
                'client_first_name',
                'client_last_name',
                'application_date',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SALES,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],

        // payment related
        EmailTemplateKeyEnum::EARLY_LOAN_REPAYMENT_DUE->value => [
            'id' => 17,
            'title' => 'Сума за предсрочно погасяване',
            'variables' => [
                'client_first_name',
                'today_date',
                'due_amount',
                'due_amount_eur',
                'early_repayment_date',
                'early_repayment_amount',
                'early_repayment_amount_eur',
                'application_id',
                'company_easypay_pin',
                'company_office_iban',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_INFO,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::LOAN_REPAID->value => [
            'id' => 27,
            'title' => 'Кажи ни какво мислиш',
            'variables' => [
                'client_first_name',
                'today_date',
                'due_amount'
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_SYSTEM,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],

        // info
        EmailTemplateKeyEnum::OUTSTANDING_OBLIGATIONS_LETTER->value => [
            'id' => 4,
            'title' => 'Удостоверение за текущи задължения',
            'variables' => ['client_first_name',],
            'type' => EmailTemplate::TEMPLATE_TYPE_INFO,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::NO_OBLIGATIONS_LETTER->value => [
            'id' => 5,
            'title' => 'Удостоверение за липса на задължения',
            'variables' => ['client_first_name',],
            'type' => EmailTemplate::TEMPLATE_TYPE_INFO,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::NO_INTEREST_SALE_TASK_EXIT->value => [
            'title' => 'Имаш ли минута? Сподели ни мнението си',
            'variables' => [],
            'type' => EmailTemplate::TEMPLATE_TYPE_MARKETING,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL
        ],
        EmailTemplateKeyEnum::EMAIL_CLIENT_WITHOUT_LOAN_1->value => [
            'title' => [
                'lendivo' => 'Интересуваш ли се от надежден бърз кредит?',
                'stikcredit' => 'Кандидатствай отново - бързо и лесно',
            ],
            'variables' => [
                'client_first_name',
                'marketing_amount_800',
                'marketing_amount_800_eur',
                'marketing_amount_1000',
                'marketing_amount_1000_eur',
                'marketing_amount_1400',
                'marketing_amount_1400_eur',
                'marketing_amount_1500',
                'marketing_amount_1500_eur',
                'marketing_amount_3000',
                'marketing_amount_3000_eur',
                'marketing_amount_6000',
                'marketing_amount_6000_eur',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_MARKETING,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'office' => 'all'
        ],
        EmailTemplateKeyEnum::EMAIL_CLIENT_WITHOUT_LOAN_2->value => [
            'title' => [
                'lendivo' => 'Възможност за заем – само с няколко клика',
                'stikcredit' => 'Търсиш ли заем? Виж предложенията',
            ],
            'variables' => [
                'marketing_amount_800',
                'marketing_amount_800_eur',
                'marketing_amount_1000',
                'marketing_amount_1000_eur',
                'marketing_amount_1400',
                'marketing_amount_1400_eur',
                'marketing_amount_1500',
                'marketing_amount_1500_eur',
                'marketing_amount_3000',
                'marketing_amount_3000_eur',
                'marketing_amount_6000',
                'marketing_amount_6000_eur',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_MARKETING,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'office' => 'all'
        ],
        EmailTemplateKeyEnum::EMAIL_CLIENT_WITHOUT_LOAN_3->value => [
            'title' => [
                'lendivo' => '💸 Още имаш шанс за кредит до заплата с 0% лихва',
                'stikcredit' => 'Какво чакаш? Кандидатствай за кредит с 1 клик',
            ],
            'variables' => [
                'marketing_amount_800',
                'marketing_amount_800_eur',
                'marketing_amount_1000',
                'marketing_amount_1000_eur',
                'marketing_amount_1400',
                'marketing_amount_1400_eur',
                'marketing_amount_1500',
                'marketing_amount_1500_eur',
                'marketing_amount_3000',
                'marketing_amount_3000_eur',
                'marketing_amount_6000',
                'marketing_amount_6000_eur',
            ],
            'type' => EmailTemplate::TEMPLATE_TYPE_MARKETING,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'office' => 'all'
        ],

        EmailTemplateKeyEnum::UNFINISHED_REQUEST_TO_CONTRACT_15_DAYS->value => [
            'title' => [
                'lendivo' => 'Завърши заявката си за кредит',
                'stikcredit' => 'Само сега вземи отстъпка от заявката си',
            ],
            'variables' => [],
            'type' => EmailTemplate::TEMPLATE_TYPE_MARKETING,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'office' => [
                Office::OFFICE_ID_WEB
            ]
        ],
        EmailTemplateKeyEnum::LOAN_APPLICATION_APPROVED->value => [
            'title' => [
                'lendivo' => 'Твоето мнение е важно за нас',
                'stikcredit' => 'Лесно ли кандидатства? Оцени ни',
            ],
            'variables' => [],
            'type' => EmailTemplate::TEMPLATE_TYPE_MARKETING,
            'manual' => EmailTemplate::TEMPLATE_NOT_MANUAL,
            'office' => 'all'
        ],
    ],
];
