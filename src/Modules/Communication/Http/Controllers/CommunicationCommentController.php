<?php

namespace Modules\Communication\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Communication\Http\Requests\CreateCommunicationCommentRequest;
use Modules\Communication\Services\CommunicationCommentService;

class CommunicationCommentController extends BaseController
{
    /**
     * @var CommunicationCommentService
     */
    private CommunicationCommentService $communicationCommentService;

    /**
     * @param CommunicationCommentService $communicationCommentService
     *
     * @throws \ReflectionException
     */
    public function __construct(
        CommunicationCommentService $communicationCommentService
    ) {
        $this->communicationCommentService = $communicationCommentService;

        parent::__construct();
    }

    /**
     * @param CreateCommunicationCommentRequest $request
     * @param Client $client
     * @param ?Loan $loan = null
     *
     * @throws \Exception
     */
    public function createCommunicationComment(
        CreateCommunicationCommentRequest $request,
        Client $client,
        ?Loan $loan = null,
    ) {
        $data = [
            'client_id' => $client->getKey(),
            'loan_id' => $loan?->getKey(),
            'text' => $request->communicationCommentText,
            'administrator_id' => getAdminId(),
        ];

        $obj = $this->communicationCommentService->create($data);

        return $this->backSuccess('Коментар е добавен')->withFragment('#paymentschedule');
    }
}
