<?php

namespace Modules\Communication\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Carbon;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Http\Requests\EarlyRepaymentRequest;
use Modules\Communication\Services\EmailService;
use Modules\Communication\Services\SmsService;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\CalendarService;

class ClientCardCommunicationController extends BaseController
{
    public function __construct(
        private readonly EmailService $emailService,
        private readonly SmsService   $smsService
    ) {
        parent::__construct();
    }

    public function sendEarlyCommunication(EarlyRepaymentRequest $request): RedirectResponse
    {
        $data = $request->validated();

        $isSent = false;
        if ($request->get('sendTo') == 'sendSms') {
            $isSent = $this->earlyRepaymentSendSms($data);
        }

        if ($request->get('sendTo') == 'sendEmail') {
            $isSent = $this->earlyRepaymentSendEmail($data);
        }

        if (!$isSent) {
            return $this->backError('Failed to send message');
        }

        return $this->backSuccess('Success send message');
    }

    private function earlyRepaymentSendEmail(array $data): bool
    {
        $loan = app(LoanRepository::class)->getById($data['loanId']);
        if (!$loan) {
            throw new \Exception('Error invalid loan');
        }

        $vars = $this->getCustomVarsForEarlyRepayment($loan, $data);

        return (bool) $this->emailService->sendByTemplateKeyAndLoanId(
            EmailTemplateKeyEnum::EARLY_LOAN_REPAYMENT_DUE->value,
            $data['loanId'],
            0, // delay in seconds
            $vars
        );
    }

    private function earlyRepaymentSendSms(array $data): bool
    {
        $loan = app(LoanRepository::class)->getById($data['loanId']);
        if (!$loan) {
            throw new \Exception('Error invalid loan');
        }

        $vars = $this->getCustomVarsForEarlyRepayment($loan, $data);

        return (bool) $this->smsService->sendByTemplateKeyAndLoan(
            SmsTemplateKeyEnum::SMS_TYPE_EARLY_LOAN_REPAYMENT_DUE->value,
            $loan,
            $vars
        );
    }

    private function getCustomVarsForEarlyRepayment($loan, array $data): array
    {
        $date = Carbon::parse($data['repaymentDate']);

        $stats = CalendarService::getRepaymentStatsForDate(
            $loan,
            $date->format('Y-m-d')
        );

        $amount = intToFloat($stats['early']);

        return [
            'early_repayment_date' => $date->format('d.m.Y'),
            'early_repayment_amount' => $amount,
            'early_repayment_amount_eur' => amountEur($amount, ''),
        ];
    }
}
