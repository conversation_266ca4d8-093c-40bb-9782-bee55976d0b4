<?php

namespace Modules\Communication\Http\Controllers;

use App\Http\Controllers\Auth\LoginController;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Email;
use Modules\Common\Models\Loan;
use Modules\Communication\Application\Actions\EmailDataAction;
use Modules\Communication\Exports\SendEmailExport;
use Modules\Communication\Http\Requests\EmailSearchRequest;
use Modules\Communication\Http\Requests\EmailSendRequest;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailService;

class EmailController extends BaseController
{
    protected EmailService $emailService;

    protected string $pageTitle = 'Email list';
    protected string $indexRoute = 'communication.email.list';

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;

        parent::__construct();
    }

    public function list(
        EmailSearchRequest $searchRequest,
        EmailDataAction $emailDataAction
    ): View {
        $filters = $searchRequest->validated();

        $data = $emailDataAction->execute($filters);

        return view('communication::email.list', $data);
    }

    public function export(
        EmailSearchRequest $searchRequest
    ) {
        try {
            return Excel::download(
                (new SendEmailExport($searchRequest->validated())),
                'send-email-export_' . Carbon::now()->toDateTimeString() . '.xlsx',
                \Maatwebsite\Excel\Excel::XLSX
            );
        } catch (Exception $e) {
            \Log::debug('Error export emails: ' . $e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return back()->with('fail', $e->getMessage());
        }
    }

    public function preview(Email $email)
    {
        $data['email'] = $email;
        $data['emailFiles'] = $email->emailFiles;

        return view('communication::email.preview', $data);
    }

    /**
     * @param EmailSendRequest $request
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function sendEmail(EmailSendRequest $request): RedirectResponse
    {
        $valid = $request->validated();

        $emailTemplate = EmailTemplate::where('email_template_id', $valid['email_template_id'])->first();
        $loan = Loan::where('loan_id', $valid['loan_id'])->first();

        $resp = $this->emailService->sendByTemplateKey(
            $emailTemplate->key,
            $loan->client,
            $loan,
        );

        if ($resp) {
            return redirect()
                ->route($this->indexRoute)
                ->with('success', __('communication::emailTemplateCrud.emailSendSuccessfully'));
        }

        return redirect()
            ->route($this->indexRoute)
            ->with('fail', __('communication::emailTemplateCrud.emailSendFailed'));
    }
}
