<?php

namespace Modules\Communication\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Common\Models\Sms;
use Modules\Communication\Http\Requests\SmsTemplateEditRequest;
use Modules\Communication\Http\Requests\SmsTemplateSearchRequest;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Services\SmsTemplateServices;
use Modules\Docs\Enums\PlaceholderEnum;

class SmsTemplateController extends BaseController
{
    protected SmsTemplateServices $smsTemplateService;
    protected OfficeService $officeService;

    protected string $pageTitle = 'Sms template list';
    protected string $indexRoute = 'communication.smsTemplate.list';
    protected string $editRoute = 'communication.smsTemplate.edit';

    public function __construct(
        SmsTemplateServices      $smsTemplateService,
        OfficeService            $officeService
    )
    {
        $this->smsTemplateService = $smsTemplateService;
        $this->officeService = $officeService;

        parent::__construct();
    }

    /**
     * @return View
     *
     * @throws Exception
     */
    public function list()
    {
        return view(
            'communication::sms-template.list',
            [
                'smsTemplates' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
                'getSmsTypes' => Sms::getSmsTypes()
            ]
        );
    }

    /**
     * @return View
     *
     * @throws Exception
     */
    public function create()
    {
        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $getSmsTypes = Sms::getSmsTypes();
        $offices = $this->officeService->getOffices();
        $logs = new LengthAwarePaginator([], 0, $this->getPaginationLimit());

        return view(
            'communication::sms-template.crud',
            compact('variables', 'getSmsTypes', 'offices', 'logs')
        );
    }

    /**
     * @param SmsTemplateEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function store(SmsTemplateEditRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['text'] = strip_tags($data['text']);

        $this->smsTemplateService->create($data);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::smsTemplateCrud.smsTemplateCreatedSuccessfully'));
    }

    /**
     * @param SmsTemplate $smsTemplate
     *
     * @return View
     *
     * @throws Exception
     */
    public function edit(SmsTemplate $smsTemplate)
    {
        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $getSmsTypes = Sms::getSmsTypes();
        $offices = $this->officeService->getOffices();
        $logs = $smsTemplate->logs()->paginate($this->getPaginationLimit());


        return view(
            'communication::sms-template.crud',
            compact('variables', 'smsTemplate', 'getSmsTypes', 'offices', 'logs')
        );
    }

    /**
     * @param SmsTemplate $smsTemplate
     * @param SmsTemplateEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function update(SmsTemplate $smsTemplate, SmsTemplateEditRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['text'] = strip_tags($data['text']);

        $this->smsTemplateService->update($smsTemplate, $data);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::smsTemplateCrud.smsTemplateUpdatedSuccessfully'));
    }

    public function revert(SmsTemplate $smsTemplate, ChangelogSmsTemplate $logSmsTemplate): RedirectResponse
    {
        $this->smsTemplateService->revert($smsTemplate, $logSmsTemplate);

        return redirect()->back();
    }

    /**
     * @param SmsTemplate $smsTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function delete(SmsTemplate $smsTemplate): RedirectResponse
    {
        $this->smsTemplateService->delete($smsTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::smsTemplateCrud.smsTemplateDeletedSuccessfully'));
    }

    /**
     * @param SmsTemplate $smsTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function enable(SmsTemplate $smsTemplate): RedirectResponse
    {
        $this->smsTemplateService->enable($smsTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::smsTemplateCrud.smsTemplateEnabledSuccessfully'));
    }

    /**
     * @param SmsTemplate $smsTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function disable(SmsTemplate $smsTemplate): RedirectResponse
    {
        $this->smsTemplateService->disable($smsTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('communication::smsTemplateCrud.smsTemplateDisabledSuccessfully')
            );
    }

    /**
     * @param SmsTemplateSearchRequest $request
     *
     * @return bool
     *
     * @throws Exception
     */
    public function setFilters(SmsTemplateSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    public function getTableData(): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $this->smsTemplateService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param SmsTemplateSearchRequest $request
     *
     * @return string
     *
     * @throws Exception|\Throwable
     */
    public function refresh(SmsTemplateSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'communication::sms-template.list-table',
            [
                'smsTemplates' => $this->getTableData(),
            ]
        )->render();
    }

    public function preview(int $id): Response
    {
        $template = SmsTemplate::find($id, ['text']);

        return response($template?->text, Response::HTTP_OK, ['Content-Type' => 'text/html']);
    }
}
