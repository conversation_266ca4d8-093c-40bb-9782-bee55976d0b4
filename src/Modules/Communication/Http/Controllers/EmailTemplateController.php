<?php

namespace Modules\Communication\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Changelog\ChangelogEmailTemplate;
use Modules\Common\Models\Email;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Http\Requests\EmailTemplateEditRequest;
use Modules\Communication\Http\Requests\EmailTemplateSearchRequest;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailTemplateService;
use Modules\Docs\Enums\PlaceholderEnum;
use Throwable;

final class EmailTemplateController extends BaseController
{
    protected EmailTemplateService $emailTemplateService;
    protected OfficeService $officeService;

    protected string $pageTitle = 'Email template list';
    protected string $indexRoute = 'communication.emailTemplate.list';
    protected string $editRoute = 'communication.emailTemplate.edit';

    public function __construct(
        EmailTemplateService $emailTemplateService,
        OfficeService $officeService
    ) {
        $this->emailTemplateService = $emailTemplateService;
        $this->officeService = $officeService;

        parent::__construct();
    }

    /**
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function list()
    {
        return view(
            'communication::email-template.list',
            [
                'emailTemplates' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
                'getEmailTypes' => Email::getEmailTypes()
            ]
        );
    }

    /**
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function create()
    {
        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $getEmailTypes = Email::getEmailTypes();
        $offices = $this->officeService->getOffices();
        $logs = new LengthAwarePaginator([], 0, $this->getPaginationLimit());

        return view(
            'communication::email-template.crud',
            compact('variables', 'getEmailTypes', 'offices', 'logs')
        );
    }

    /**
     * @param EmailTemplateEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function store(EmailTemplateEditRequest $request): RedirectResponse
    {
        $this->emailTemplateService->create($request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::emailTemplateCrud.emailTemplateCreatedSuccessfully'));
    }

    /**
     * @param EmailTemplate $emailTemplate
     *
     * @return View
     *
     * @throws Exception
     */
    public function edit(EmailTemplate $emailTemplate): View
    {
        $getEmailTypes = Email::getEmailTypes();
        $logs = $emailTemplate->logs()->paginate($this->getPaginationLimit());

        $offices = $this->officeService->getOffices();

        return view('communication::email-template.crud', [
            'emailTemplate' => $emailTemplate,
            'getEmailTypes' => $getEmailTypes,
            'offices' => $offices,
            'logs' => $logs,
            'emailKeys' => EmailTemplateKeyEnum::generateOptions(false),
        ]);
    }

    /**
     * @param EmailTemplate $emailTemplate
     * @param EmailTemplateEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function update(EmailTemplate $emailTemplate, EmailTemplateEditRequest $request): RedirectResponse
    {
        $this->emailTemplateService->update($emailTemplate, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::emailTemplateCrud.emailTemplateUpdatedSuccessfully'));
    }

    /**
     * @param EmailTemplate $emailTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function delete(EmailTemplate $emailTemplate): RedirectResponse
    {
        $this->emailTemplateService->delete($emailTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::emailTemplateCrud.emailTemplateDeletedSuccessfully'));
    }

    /**
     * @param EmailTemplate $emailTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function enable(EmailTemplate $emailTemplate): RedirectResponse
    {
        $this->emailTemplateService->enable($emailTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::emailTemplateCrud.emailTemplateEnabledSuccessfully'));
    }

    /**
     * @param EmailTemplate $emailTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function disable(EmailTemplate $emailTemplate): RedirectResponse
    {
        $this->emailTemplateService->disable($emailTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('communication::emailTemplateCrud.emailTemplateDisabledSuccessfully')
            );
    }

    public function revert(EmailTemplate $emailTemplate, ChangelogEmailTemplate $logEmailTemplate): RedirectResponse
    {
        $this->emailTemplateService->revert($emailTemplate, $logEmailTemplate);

        return redirect()->back();
    }

    /**
     * @param EmailTemplateSearchRequest $request
     *
     * @return bool|RedirectResponse
     * @throws Exception
     */
    public function setFilters(EmailTemplateSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    public function getTableData(): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $this->emailTemplateService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param EmailTemplateSearchRequest $request
     *
     * @return array|string
     * @throws Exception|Throwable
     */
    public function refresh(EmailTemplateSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'communication::email-template.list-table',
            [
                'emailTemplates' => $this->getTableData(),
            ]
        )->render();
    }

    public function preview(int $id): Response
    {
        $template = EmailTemplate::find($id, ['text']);

        return response($template?->text, Response::HTTP_OK, ['Content-Type' => 'text/html']);
    }
}
