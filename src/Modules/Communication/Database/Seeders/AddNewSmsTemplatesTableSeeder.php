<?php

namespace Modules\Communication\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Admin\Entities\Office;
use Modules\Common\Models\Administrator;
use Modules\Communication\Interfaces\CommunicationInterface;
use Modules\Communication\Models\SmsTemplate;

/**
 * @uses When we have new sms templates only run this seeder
 * Before create sms template it will be checked if template already exists
 *
 * php artisan db:seed --class=\\Modules\\Communication\\Database\\Seeders\\AddNewSmsTemplatesTableSeeder
 */
class AddNewSmsTemplatesTableSeeder extends Seeder
{
    public function run(): void
    {
        $countCreated = 0;
        foreach (SmsTemplate::getAllTemplates() as $smsTemplateKey => $smsTemplateData) {
            $hasSmsTemplate = SmsTemplate::where('key', $smsTemplateKey)->exists();
            if ($hasSmsTemplate) {
                continue;
            }

            $countCreated++;
            $smsTemplate = SmsTemplate::create([
                'key' => $smsTemplateKey,
                'name' => $smsTemplateKey,
                'description' => $smsTemplateData['description'],
                'variables' => $smsTemplateData['variables'],
                'text' => $this->getSmsText($smsTemplateData),
                'gender' => CommunicationInterface::TEMPLATE_GENDER,
                'type' => $smsTemplateData['type'] ?? CommunicationInterface::TEMPLATE_TYPE_SYSTEM,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'manual' => $smsTemplateData['manual'] ?? CommunicationInterface::TEMPLATE_NOT_MANUAL
            ]);

            /// attach office templates
            if (!empty($smsTemplateData['office']) && $smsTemplateData['office'] === 'all') {
                $smsTemplate->offices()->attach(
                    Office::select('office_id')->pluck('office_id')->toArray()
                );
            } else {
                $smsTemplate->offices()->attach(
                    $smsTemplate['office']
                );
            }
        }
    }

    private function getSmsText(array $data): string
    {
        if (is_string($data['text'])) {
            return $data['text'];
        }
        if (is_array($data['text'])) {
            return $data['text'][env('PROJECT')] ?? '';
        }

        return '';
    }
}
