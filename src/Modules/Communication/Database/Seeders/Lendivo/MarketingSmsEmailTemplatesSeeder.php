<?php

declare(strict_types=1);

namespace Modules\Communication\Database\Seeders\Lendivo;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Office;
use Modules\Communication\Interfaces\CommunicationInterface;

// php artisan db:seed --class=\\Modules\\Communication\\Database\\Seeders\\Lendivo\\MarketingSmsEmailTemplatesSeeder
final class MarketingSmsEmailTemplatesSeeder extends Seeder
{
    private const SUBJECTS = [
        'marketing_15_days_first' => 'Кой друг ти прави подаръци?',
        'marketing_15_days_last' => 'Ако не действаш сега…',
        'marketing_30_days_first' => 'Писна ти да чакаш банките?',
        'marketing_30_days_last' => 'Оставяш го с лека ръка... така ли, {client_first_name}?',
        'marketing_45_days_first' => 'Дано си готов за това, {client_first_name}?',
        'marketing_45_days_last' => 'Последно повикване, {client_first_name}',
        'marketing_60_days_first' => 'Имаш ли мечти, {client_first_name}?',
        'marketing_60_days_last' => 'Отказваш ли се, {client_first_name}?',
        'marketing_75_days_first' => 'Искаш ли да си като тези 5 души, {client_first_name}?',
        'marketing_75_days_last' => 'Какво чакаш още, {client_first_name}?',
        'marketing_90_days_first' => 'Какво чакаш още, {client_first_name}?',
        'marketing_90_days_last' => 'Ако не действаш сега…',
        'marketing_105_days_first' => 'Писна ти да чакаш банките?',
        'marketing_105_days_last' => 'Оставяш го с лека ръка... така ли, {client_first_name}?',
        'marketing_120_days_first' => 'Дано си готов за това, {client_first_name}?',
        'marketing_120_days_last' => 'Последно повикване, {client_first_name}',
        'marketing_135_days_first' => 'Имаш ли мечти, {client_first_name}?',
        'marketing_135_days_last' => 'Отказваш ли се, {client_first_name}?',
        'marketing_150_days_first' => 'Искаш ли да си като тези 5 души, {client_first_name}?',
        'marketing_150_days_last' => 'Какво чакаш още, {client_first_name}?',
        'marketing_180_days_first' => 'УНИКАЛЕН подарък за теб, {client_first_name}',
        'marketing_180_days_last' => 'ПРОПУСКАШ, така ли, {client_first_name}?',
        'marketing_210_days_first' => 'Безлихвен кредит? ДА, възможно е!',
        'marketing_210_days_last' => 'Наистина ли пропускаш, {client_first_name}?',
        'marketing_360_days_first' => 'Какво чакаш още, {client_first_name}?',
        'marketing_360_days_last' => 'Ако не действаш сега…',
        'marketing_540_days_first' => 'Писна ти да чакаш банките?',
        'marketing_540_days_last' => 'Оставяш го с лека ръка... така ли, {client_first_name}?',
        'marketing_720_days_first' => 'Дано си готов за това, {client_first_name}?',
        'marketing_720_days_last' => 'Последно повикване, {client_first_name}',
        'marketing_900_days_first' => 'Имаш ли мечти, {client_first_name}?',
        'marketing_900_days_last' => 'Отказваш ли се, {client_first_name}?',
        'marketing_1080_days_first' => 'Последно повикване, {client_first_name}?',
        'marketing_1080_days_last' => 'ПРОПУСКАШ, така ли, {client_first_name}?',
    ];

    public function run(): void
    {
        $project = env('PROJECT');
        if (empty($project) || $project != 'lendivo') {
            throw new \Exception('ERROR: The seeder MarketingSmsEmailTemplatesSeeder is only for Lendivo project');
        }

        $this->runSms();
        $this->runEmail();
    }

    private function runSms()
    {
        $messages = [
            'marketing_15_days_first' => '{client_first_name}, imame strahoten podaruk za teb! Vzemi kredit do 3000lv s {discount}% otstypka v sledvashtite 5 dni - lendivo.bg',
            'marketing_15_days_last' => 'Malko ostava, {client_first_name}! Imash otstypka ot {discount}%, koqto izticha sled dva dni - vzemi kredit do 3000 lv. na lendivo.bg',
            'marketing_30_days_first' => '{client_first_name}, ne chakai poveche! Imash podaruk ot vseki kredit do 3000 lv. Vzemi kredit s {discount}% otstypka na lendivo.bg. Ofertata vaji samo 5 dni!',
            'marketing_30_days_last' => 'Zabravi li? Aktivirahme {discount}% otstypka v profila ti za vseki kredit do 3000lv - lendivo.bg. Imash samo 2 dni do kraq na promociata.',
            'marketing_45_days_first' => 'Samo 5 dni, {client_first_name}. Tolkova vreme imash da vzemesh do 3000 lv s {discount}% otstypka na lendivo.bg. Ne propuskai shansa!',
            'marketing_45_days_last' => 'Propuskash, taka li? Otstypkata ti ot {discount}% po vseki kredit izticha sled 2 dni - lendivo.bg.',
            'marketing_60_days_first' => 'NE KAZVAI NA DRUG. {client_first_name}, podarqvame ti celi {discount}% ot vseki zaem do 3000lv na lendivo.bg. Ostavat samo 5 dni!',
            'marketing_60_days_last' => 'Posledno povikvane, {client_first_name}. Ostavat samo 2 dni i otstypkata ti ot {discount}% shte izteche - lendivo.bg',
            'marketing_75_days_first' => 'Mislim si za teb, {client_first_name}. Reshihme da ti podarim {discount}% otstypka ot vseki zaem do 3000 lv - lendivo.bg. Ofertata vaji samo 5 dni!',
            'marketing_75_days_last' => 'Otkazvash se, taka li? Sled 2 dni izticha otstypkata ti ot {discount}% - lendivo.bg',
            'marketing_90_days_first' => 'Ne izpuskai shansa si, {client_first_name}! Imash podaryk {discount}% otstypka po vseki zaem na lendivo.bg. Ofertata e validna 5 dni.',
            'marketing_90_days_last' => 'Ne e za izpuskane, Sveltin. Celi {discount}% otstypka te chaka v profila ti - lendivo.bg. Imash samo oshte 2 dni.',
            'marketing_105_days_first' => 'Kasmeta e s teb! Specheli {discount}% otstypka ot zaem do 3000 lv na lendivo.bg. Vaji samo 5 dni.',
            'marketing_105_days_last' => 'Malko ostava, {client_first_name}! Imash otstypka ot {discount}%, koqto izticha sled dva dni - vzemi kredit do 3000 lv. na lendivo.bg',
            'marketing_120_days_first' => 'PROMOCIA! {client_first_name}, unikalno predlojenie ot {discount}% otstypka ot vseki kredit na lendivo.bg. Poburzai, imash samo 5 dni!',
            'marketing_120_days_last' => 'Dano si gotov, {client_first_name}. Ostavat samo 2 dni do kraq na promociata - lendivo.bg',
            'marketing_135_days_first' => 'GIGANTSKA OFERTA! {client_first_name}, ne izpuskai shansa I vzemi do 3000 lv s {discount}% otstypka - lendivo.bg. Predlojenieto vaji SAMO 5 dni.',
            'marketing_135_days_last' => 'PROPUSKASH LI? Ostavat ti samo 2 dni do kraq na ofertata - celi {discount}% otstypka - lendivo.bg',
            'marketing_150_days_first' => '{client_first_name}, uchastvash li? Vzemi kredit na lendivo.bg I grabni otstypka ot {discount}% po vseki zaem! Ofertata vaji samo 5 dni.',
            'marketing_150_days_last' => 'Pobyrzai, {client_first_name}! Ostavat samo 2 dni do kraq na promociata - {discount}% otstypka na lendivo.bg',
            'marketing_180_days_first' => 'UNIKALNO PREDLOJENIE. Samo za teb, {client_first_name}. Vzemi kredit do 3000 lv s CELI {discount}% otstypka - lendivo.bg. Ofertata vaji za 5 dni.',
            'marketing_180_days_last' => 'Naistina li shte propusnesh, {client_first_name}? Imash samo 2 dni do kraq na promociata - {discount}% otstypka ot vseki zaem na lendivo.bg',
            'marketing_210_days_first' => 'NE E ZA IZPUSKANE! {client_first_name}, podarqvame ti pravo na nov BEZLIHVEN kredit do zaplata - lendivo.bg. Imash samo 5 dni predi kraq na promociata.',
            'marketing_210_days_last' => 'Ne iskame da izpusnesh tozi shans, {client_first_name}. Ostavat samo 2 dni, v koito moje da vzemesh BEZLIHVEN kredit na lendivo.bg',
            'marketing_360_days_first' => 'Ne otlagai poveche, {client_first_name}. Grabni kredit do 3000 lv s {discount}% otstypka na lendivo.bg. Ofertata vaji SAMO 5 dni.',
            'marketing_360_days_last' => 'Vremeto izticha, {client_first_name}! Ostavat ti samo 2 dni do kraq na promociata - {discount}% ostypka ot vseki kredit do 3000lv na lendivo.bg',
            'marketing_540_days_first' => 'ZASLUJAVA SI! {client_first_name}, grabni {discount}% otstypka ot vseki kredit do 3000 lv na lendivo.bg. Imash samo 5 dni!',
            'marketing_540_days_last' => 'Naistina li zabravi, {client_first_name}? Podarihme ti {discount}% otstypka ot vseki zaem na lendivo.bg. Ostavat samo oshte 2 dni do kraq na promociata.',
            'marketing_720_days_first' => 'Trqbvat li ti pari? {client_first_name}, imame predlojenie, koeto ne e za izpuskane - {discount}% otstypka po vseki kredit do 3000 lv - lendivo.bg. Ofertata vaji samo 5 dni.',
            'marketing_720_days_last' => 'Ne zabravqi, {client_first_name}. Ostavat ti samo 2 dni do kraq na promociata - {discount}% otstypka ot vseki kredit na lendivo.bg',
            'marketing_900_days_first' => 'Veche e tuk… {client_first_name}, imash otstypka ot {discount}% ot vseki kredit na lendivo.bg - ofertata vaji 5 dni!',
            'marketing_900_days_last' => 'Ostavat 2 dni! {client_first_name}, nqma vreme za gubene. Otstypkata ti ot {discount}% skoro izticha - lendivo.bg',
            'marketing_1080_days_first' => 'Posleden opit, {client_first_name}. Iskame da ti podarim {discount}% otstypka ot vseki zaem na lendivo.bg. Ofertata vaji SAMO 5 dni, poburzai',
            'marketing_1080_days_last' => 'Ne mojem da povqrvame, che propuskash! Ostavat samo 2 dni, prez koito moje da vzemesh kredit do 3000 lv s CELI {discount}% otstypka - lendivo.bg',
        ];

        foreach ($messages as $key => $message) {
            DB::table('sms_template')->updateOrInsert(
                ['key' => $key],
                [
                    'name' => $key,
                    'description' => $key,
                    'variables' => json_encode([
                        'discount',
                        'client_first_name',
                    ]),
                    'text' => $message,
                    'gender' => CommunicationInterface::TEMPLATE_GENDER,
                    'type' => CommunicationInterface::TEMPLATE_TYPE_MARKETING,
                    'manual' => CommunicationInterface::TEMPLATE_NOT_MANUAL,
                    'created_at' => now(),
                    'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                ]
            );

            DB::table('office_sms_template')->updateOrInsert(
                [
                    'sms_template_id' => DB::table('sms_template')->where('key', $key)
                        ->value('sms_template_id')
                ],
                ['office_id' => Office::OFFICE_ID_WEB]
            );
        }
    }

    public function runEmail(): void
    {
        $fileList = File::allFiles(__DIR__ . '/marketing-email-templates-lendivo');

        $added = 0;
        foreach ($fileList as $file) {
            $key = $file->getFilenameWithoutExtension();
            DB::table('email_template')->updateOrInsert(
                ['key' => $key],
                [
                    'title' => self::SUBJECTS[$key],
                    'description' => self::SUBJECTS[$key],
                    'body' => self::SUBJECTS[$key],
                    'variables' => json_encode([
                        'discount',
                        'client_first_name',
                    ]),
                    'text' => $file->getContents(),
                    'gender' => CommunicationInterface::TEMPLATE_GENDER,
                    'type' => CommunicationInterface::TEMPLATE_TYPE_MARKETING,
                    'manual' => CommunicationInterface::TEMPLATE_NOT_MANUAL,
                    'created_at' => now(),
                    'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                ]
            );

            DB::table('office_email_template')->updateOrInsert(
                ['email_template_id' => DB::table('email_template')->where('key', $key)->value('email_template_id')],
                ['office_id' => Office::OFFICE_ID_WEB]
            );

            $added++;
        }
    }
}
