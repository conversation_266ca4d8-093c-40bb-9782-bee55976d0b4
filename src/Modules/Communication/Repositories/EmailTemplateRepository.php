<?php

namespace Modules\Communication\Repositories;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Helpers\OtherHelper;
use Modules\Common\Repositories\BaseRepository;
use Modules\Communication\Models\EmailTemplate;

final class EmailTemplateRepository extends BaseRepository
{
    public function __construct(
        protected EmailTemplate $emailTemplate = new EmailTemplate
    )
    {
    }

    public function getByKey(string $key): ?EmailTemplate
    {
        return EmailTemplate::query()->where('key', $key)->first();
    }

    /**
     * @throws Exception
     */
    public function findByKeyOrCreate(array $data): EmailTemplate
    {
        if (!isset($data['key'])) {
            throw new Exception('Error no provided email (key) in data');
        }

        $emailTemplate = $this->emailTemplate->where('key', $data['key'])->first();
        if ($emailTemplate) {
            return $emailTemplate;
        }

        return $this->emailTemplate->create($data);
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return LengthAwarePaginator
     */
    public function getAll(
        int   $limit,
        array $joins = [],
        array $where = [],
        array $order = [],
        bool  $showDeleted = false
    ): LengthAwarePaginator
    {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = EmailTemplate::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );

        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    public function getById(int $emailTemplateId): ?EmailTemplate
    {
        return EmailTemplate::where('email_template_id', $emailTemplateId)->first();
    }

    public function getByKeyAndOffice(string $key, int $officeId): ?EmailTemplate
    {
        $result = DB::select("
            SELECT st.*
            FROM email_template AS st
            JOIN office_email_template AS ost ON (
                ost.email_template_id = st.email_template_id
                AND ost.office_id = '" . $officeId . "'
            )
            WHERE
                st.key = '" . $key . "'
                AND st.deleted = 0
                AND st.active = 1
            ORDER BY st.email_template_id DESC
            LIMIT 1
        ");

        if (!$result) {
            return null;
        }

        return EmailTemplate::hydrate($result)->first();
    }

    /**
     * @param array $data
     *
     * @return EmailTemplate
     * @throws NotFoundException
     */
    public function create(array $data): EmailTemplate
    {
        $emailTemplate = new EmailTemplate();
        $emailTemplate->fill($data);
        $emailTemplate->variables = OtherHelper::getVariablesFromText($emailTemplate->text);
        $emailTemplate->save();

        if (isset($data['offices'])) {
            $emailTemplate->adopt('officesRelation', $data['offices']);
        }

        return $emailTemplate;
    }

    /**
     * @param EmailTemplate $emailTemplate
     * @param array $data
     *
     * @return EmailTemplate
     * @throws NotFoundException
     */
    public function edit(EmailTemplate $emailTemplate, array $data): EmailTemplate
    {
        $emailTemplate->fill($data);
        $emailTemplate->setAttribute('body', $emailTemplate->htmlToPlainText($data['text'] ?? ''));
        $emailTemplate->variables = OtherHelper::getVariablesFromText($emailTemplate->text);
        $emailTemplate->save();

        if (isset($data['offices'])) {
            $emailTemplate->adopt('officesRelation', $data['offices']);
        }

        return $emailTemplate;
    }

    /**
     * @param EmailTemplate $emailTemplate
     * @throws Exception
     */
    public function delete(EmailTemplate $emailTemplate)
    {
        $emailTemplate->delete();
    }

    /**
     * @param EmailTemplate $emailTemplate
     */
    public function enable(EmailTemplate $emailTemplate)
    {
        $emailTemplate->enable();
    }

    /**
     * @param EmailTemplate $emailTemplate
     */
    public function disable(EmailTemplate $emailTemplate)
    {
        $emailTemplate->disable();
    }

    public function getManual()
    {
        return EmailTemplate::where(['manual' => 1])->get();
    }

    public function getManualWithAdminOfficeRelation($adminOffices)
    {
        $adminOffices = $adminOffices->pluck('office_id')->toArray();

        return EmailTemplate::whereHas('offices', function (Builder $query) use ($adminOffices) {
            $query->whereIn('office_email_template.office_id', $adminOffices);
        })
            ->where([
                'manual' => 1,
                'custom_template' => false,
            ])
            ->get();
    }
}

