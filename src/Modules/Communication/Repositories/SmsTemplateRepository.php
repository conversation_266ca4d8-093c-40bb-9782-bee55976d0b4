<?php

namespace Modules\Communication\Repositories;

use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Helpers\OtherHelper;
use Modules\Common\Repositories\BaseRepository;
use Modules\Communication\Models\SmsTemplate;

final class SmsTemplateRepository extends BaseRepository
{
    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return LengthAwarePaginator
     */
    public function getAll(
        int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['sms_template_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $builder = SmsTemplate::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );
        $where = $this->checkForDeleted($where, $showDeleted);

        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }
        $builder->where('custom_template', false);

        return $builder->paginate($limit);
    }

    public function getById(int $smsTemplateId): ?SmsTemplate
    {
        return SmsTemplate::where('sms_template_id', $smsTemplateId)->first();
    }

    /**
     * @param array $data
     *
     * @return SmsTemplate
     */
    public function create(array $data)
    {
        $smsTemplate = new SmsTemplate();
        $smsTemplate->fill($data);
        $smsTemplate->variables = OtherHelper::getVariablesFromText($smsTemplate->text);
        $smsTemplate->save();

        if (isset($data['offices'])) {
            $smsTemplate->adopt('offices', $data['offices']);
        }

        return $smsTemplate;
    }

    /**
     * @param SmsTemplate $smsTemplate
     * @param array $data
     *
     * @return SmsTemplate
     */
    public function edit(SmsTemplate $smsTemplate, array $data)
    {
        $smsTemplate->fill($data);
        $smsTemplate->variables = OtherHelper::getVariablesFromText($smsTemplate->text);
        $smsTemplate->save();

        if (isset($data['offices'])) {
            $smsTemplate->adopt('officesRelation', $data['offices']);
        }

        return $smsTemplate;
    }

    /**
     * @param SmsTemplate $smsTemplate
     *
     * @throws Exception
     */
    public function delete(SmsTemplate $smsTemplate)
    {
        $smsTemplate->delete();
    }

    /**
     * @param SmsTemplate $smsTemplate
     */
    public function enable(SmsTemplate $smsTemplate)
    {
        $smsTemplate->enable();
    }

    /**
     * @param SmsTemplate $smsTemplate
     */
    public function disable(SmsTemplate $smsTemplate)
    {
        $smsTemplate->disable();
    }

    /**
     * @param array $criteria
     *
     * @return Collection|SmsTemplate|null
     */
    public function getByCriteria(array $criteria)
    {
        $collection = SmsTemplate::where($criteria)->get();

        if ($collection->count() === 0) {
            return null;
        } elseif ($collection->count() === 1) {
            return $collection->first();
        }

        return $collection;
    }

    public function getManual()
    {
        return SmsTemplate::where(['manual' => 1])->get();
    }

    public function getManualWithAdminOfficeRelation($adminOffices)
    {
        $adminOffices = $adminOffices->pluck('office_id')->toArray();

        return SmsTemplate::whereHas('officesRelation', function (Builder $query) use ($adminOffices) {
            $query->whereIn('office_sms_template.office_id', $adminOffices);
        })
            ->where([
                'manual' => 1,
                'custom_template' => 0
            ])
            ->get();
    }

    public function getByKey(string $key): ?SmsTemplate
    {
        return SmsTemplate::where('key', $key)->first();
    }

    public function getByKeyAndOffice(string $key, int $officeId): ?SmsTemplate
    {
        $result = DB::select("
            SELECT st.*
            FROM sms_template AS st
            JOIN office_sms_template AS ost ON (
                ost.sms_template_id = st.sms_template_id
                AND ost.office_id = '" . $officeId . "'
            )
            WHERE
                st.key = '" . $key . "'
                AND st.deleted = 0
                AND st.active = 1
            ORDER BY st.sms_template_id DESC
            LIMIT 1
        ");

        if (!$result) {
            return null;
            // return $this->getTemplateByKey($key);
        }

        return SmsTemplate::hydrate($result)->first();
    }
}
