<?php

namespace Modules\Communication\Console;

use Carbon\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Office;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailService;
use Modules\Communication\Services\SmsService;
use Modules\Head\Repositories\InstallmentRepository;
use Symfony\Component\Console\Command\Command;

/**
 * Припомняне за наближаваща вноска чрез СМС и Email за клиенти онлайн и от физически офис
 */
class ComingDueDateReminder extends CommonCommand
{
    public const TWO_DAYS = 2; // sms
    public const THREE_DAYS = 3; // email

    protected $name = 'script:coming-due-date-reminder';
    protected $signature = 'script:coming-due-date-reminder {loanId?}';
    protected $description = 'Coming due date reminder via sms and email';

    public function __construct(
        protected InstallmentRepository $installmentRepository,
        protected SmsService $smsService,
        protected EmailService $emailService
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $this->startLog();


        $loanId = (int) $this->argument('loanId');
        if (empty($loanId)) {
            $loanId = null;
        }


        /*
        select i.installment_id, l.loan_id, l.office_id
        from installment i
        join loan l on l.loan_id = i.loan_id
        where
            l.loan_status_id = 6
            and i.paid = 0
            and i.due_date = '2023-11-10'
        order by i.installment_id;
        */

        $twoDaysAhead = Carbon::now()->addDays(self::TWO_DAYS)->format('Y-m-d');
        $smsBuilder = $this->installmentRepository->getInstallmentsForDate(
            $twoDaysAhead,
            true,
            null,
            $loanId
        );

        $smsTodo = $smsBuilder->count();
        $smsDone = [];
        $smsFailed = [];

        $this->info('SMS - COMING_DUE_DATE for: ' . $smsTodo . ' inst(s)');
        $smsBuilder->chunkById(
            200,
            function ($installments) use (&$smsDone, &$smsFailed) {
                foreach ($installments as $installment) {

                    $loan = $installment->loan;
                    $sms = $this->smsService->sendByTemplateKeyAndLoan(
                        SmsTemplateKeyEnum::SMS_TYPE_COMING_DUE_DATE->value,
                        $loan,
                        [
                            'loan_next_unpaid_installment_date' => Carbon::parse($installment->due_date)->format('d.m.Y'),
                            'loan_instalment_sum_eur' => amountEur($installment->getPrimaryTotalRestAmount(), ''),
                            'loan_instalment_sum' => number_format($installment->getPrimaryTotalRestAmount(), 2),
                        ]
                    );

                    $smsRes = 'FAIL';
                    if (!empty($sms->sms_id)) {
                        $smsRes = 'SUCCESS';
                        $smsDone[] = $installment->installment_id;
                    } else {
                        $smsFailed[] = $installment->installment_id;
                    }

                    $this->info('-- processed inst: ' . $installment->installment_id . '(#' . $loan->loan_id . ') - ' . $smsRes);
                }
            }
        );

        /*
        select i.installment_id, l.loan_id, l.office_id
        from installment i
        join loan l on l.loan_id = i.loan_id
        where
            l.loan_status_id = 6
            l.office_id = 1
            and i.paid = 0
            and i.due_date = '2023-11-10'
        order by i.installment_id;
        */
        $threeDaysAhead = Carbon::now()->addDays(self::THREE_DAYS)->format('Y-m-d');
        $emailBuilder = $this->installmentRepository->getInstallmentsForDate(
            $threeDaysAhead,
            true,
            Office::OFFICE_ID_WEB,
            $loanId
        );

        $emailTodo = $emailBuilder->count();
        $emailDone = [];
        $emailFailed = [];

        $this->info('EMAIL - COMING_DUE_DATE for: ' . $emailTodo . ' inst(s)');
        $emailBuilder->chunkById(
            200,
            function ($installments) use (&$emailDone, &$emailFailed) {
                foreach ($installments as $installment) {

                    $loanId = $installment->loan_id;

                    $email = $this->emailService->sendByTemplateKeyAndLoanId(
                        EmailTemplateKeyEnum::UPCOMING_DUE_INSTALMENT->value,
                        $loanId,
                        0, // delay
                        [
                            'loan_next_unpaid_installment_date' => Carbon::parse($installment->due_date)->format('d.m.Y'),
                            'loan_instalment_sum_eur' => amountEur($installment->getPrimaryTotalRestAmount(), ''),
                            'loan_instalment_sum' => number_format($installment->getPrimaryTotalRestAmount(), 2),
                        ]
                    );

                    $emailRes = 'FAIL';
                    if (!empty($email->email_id)) {
                        $emailRes = 'SUCCESS';
                        $emailDone[] = $installment->installment_id;
                    } else {
                        $emailFailed[] = $installment->installment_id;
                    }

                    $this->info('-- processed inst: ' . $installment->installment_id . '(#' . $loanId . ') - ' . $emailRes);
                }
            }
        );


        $totalTodo = $smsTodo + $emailTodo;
        $totalDone = count($smsDone) + count($emailDone);


        $this->finishLog([$this->executionTimeString()], $totalTodo, $totalDone, 'finished');


        return Command::SUCCESS;
    }
}
