<?php

namespace Modules\Communication\Console;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanMeta;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Communication\Application\Enums\OverdueDaysEnum;
use Modules\Head\Services\PlannerClientProcesses\NotifyOverduePhysicalOfficeLoan;
use Modules\Head\Services\PlannerClientProcesses\NotifyOverdueWebOfficeLoan;

/**
 * Overdue X days notification
 */
class OverdueNotification extends CommonCommand
{
    protected $name = 'script:overdue-notification';
    protected $signature = 'script:overdue-notification';
    protected $description = 'Overdue notification';

    protected string $logChannel = 'emailExec';

    public function __construct(
        protected NotifyOverduePhysicalOfficeLoan $notifyOverduePhysicalOfficeLoan,
        protected NotifyOverdueWebOfficeLoan      $notifyOverdueWebOfficeLoan
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $this->notifyOverduePhysicalOfficeLoan();
        $this->notifyOverdueWebOfficeLoan();

        return $this::SUCCESS;
    }

    private function notifyOverduePhysicalOfficeLoan()
    {
        $this->startLog($this->getClassName() . '::' . __FUNCTION__);


        $total = 0;
        $failed = 0;
        $processed = 0;


        $builder = Loan::select(DB::raw("
                loan.*,
                loan_actual_stats.current_overdue_days
            "))
            ->join('loan_actual_stats', 'loan.loan_id', '=', 'loan_actual_stats.loan_id')
            ->where('loan.loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->where('loan.office_id', '!=', Office::OFFICE_ID_WEB)
            ->whereIn('loan_actual_stats.current_overdue_days', OverdueDaysEnum::possibleDaysPhysicalOffice())
            ->orderBy('loan.loan_id');


        $this->info("TODO OFFICES: " . $builder->count());
        $builder->chunkById(
            100,
            function (Collection $loans) use (&$total, &$failed, &$processed) {

                $total += $loans->count();

                $loans->each(function (Loan $loan) use (&$failed, &$processed) {

                    $res = 'FAIL';
                    if ($this->notifyOverduePhysicalOfficeLoan->execute($loan)) {
                        $res = 'SUCCESS';
                        $processed++;
                    } else {
                        $failed++;
                    }

                    $this->info('-- processed #' . $loan->loan_id . ' with overdue = ' . $loan->current_overdue_days . ' (' . $res . ')');
                });
            },
            'loan.loan_id',
            'loan_id'
        );

        $msg = 'OFFICES processed loans: ' . $processed;
        $logMessages = [
            $msg,
            $this->executionTimeString()
        ];


        $this->finishLog($logMessages, $processed, $processed, $msg);
    }

    private function notifyOverdueWebOfficeLoan()
    {
        $this->startLog($this->getClassName() . '::' . __FUNCTION__);

        $total = 0;
        $failed = 0;
        $processed = 0;


        $builder = Loan::select(DB::raw("
                loan.*,
                loan_actual_stats.current_overdue_days
            "))
            ->join('loan_actual_stats', 'loan.loan_id', '=', 'loan_actual_stats.loan_id')
            ->where('loan.loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->where('loan.office_id', '=', Office::OFFICE_ID_WEB)
            ->whereIn('loan_actual_stats.current_overdue_days', OverdueDaysEnum::possibleDaysOnlineOffice())
            ->orderBy('loan.loan_id');


        $this->info("TODO ONLINE: " . $builder->count());
        $builder->chunkById(
            100,
            function (Collection $loans) use (&$total, &$failed, &$processed) {

                $total += $loans->count();

                $loans->each(function (Loan $loan) use (&$failed, &$processed) {

                    $res = 'FAIL';
                    if ($this->notifyOverdueWebOfficeLoan->execute($loan)) {
                        $res = 'SUCCESS';
                        $processed++;
                    } else {
                        $failed++;
                    }

                    $this->info('-- processed #' . $loan->loan_id . ' with overdue = ' . $loan->current_overdue_days . ' (' . $res . ')');
                });
            },
            'loan.loan_id',
            'loan_id'
        );

        $msg = 'ONLINE processed loans: ' . $processed;
        $logMessages = [
            $msg,
            $this->executionTimeString()
        ];


        $this->finishLog($logMessages, $processed, $processed, $msg);
    }
}
