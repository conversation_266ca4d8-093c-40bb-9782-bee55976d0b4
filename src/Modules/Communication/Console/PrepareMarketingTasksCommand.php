<?php

namespace Modules\Communication\Console;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Communication\Enums\MarketingTaskMessageTypeEnum;
use Modules\Communication\Enums\MarketingTaskStatusEnum;
use Modules\Communication\Models\MarketingTask;
use Modules\Communication\Models\MarketingTaskMessage;
use Modules\Docs\Domain\DataSource\Custom as CustomDataSource;
use Modules\ThirdParty\Libraries\EmailProvider;
use RuntimeException;

final class PrepareMarketingTasksCommand extends CommonCommand
{
    public const DAYS_WITHOUT_LOAN_DISCOUNT = [
        15 => 6,
        30 => 8,
        45 => 10,
        60 => 16,
        75 => 12,
        90 => 14,
        105 => 16,
        120 => 22,
        135 => 20,
        150 => 25,
        180 => 35,
        210 => 100,
        360 => 30,
        540 => 50,
        720 => 30,
        900 => 35,
        1080 => 40,

        18 => 6,
        33 => 8,
        48 => 10,
        63 => 16,
        78 => 12,
        93 => 14,
        108 => 16,
        123 => 22,
        138 => 20,
        153 => 25,
        183 => 35,
        213 => 100,
        363 => 30,
        543 => 50,
        723 => 30,
        903 => 35,
        1083 => 40,
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $name = 'script:communication:prepare-marketing-tasks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Prepare marketing tasks';

    private array $vars = [];

    public function handle(): void
    {
        $this->startLog($this->description);

        $this->setVars();

        $query = DB::query()->fromSub($this->getSql(), 'mc');

        $totalClients = $query->count();
        $processedClients = 0;
        $createdTasks = 0;
        $createdMessages = 0;
        $errors = [];

        $query->orderBy('mc.client_id')
            ->chunk(100, function ($clients) use (&$createdTasks, &$createdMessages, &$processedClients, &$errors) {
                foreach ($clients as $client) {
                    try {
                        DB::transaction(function () use ($client, &$createdTasks, &$createdMessages) {
                            $task = $this->createTask($client);
                            $createdTasks++;
                            if (
                                $task->status !== MarketingTaskStatusEnum::SKIPPED // general logic
                                && empty($task->skipp_sending) // backup logic
                            ) {
                                $this->createMessages($task);
                                $createdMessages++;
                            }
                        });

                        $processedClients++;
                    } catch (\Throwable $e) {
                        Log::error($e->getMessage(), ['client' => (array) $client]);
                        $errors[(string)$client->client_id] = $e->getMessage();
                    }
                }
            });

        $stats = [
            "Total clients:  $totalClients",
            "Processed clients: $processedClients",
            "Created tasks: $createdTasks",
            "Created messages: $createdMessages",
        ];

        if ($errors) {
            $this->sendEmailReport($stats, $errors);
        }

        $this->finishLog($stats);
    }

    private function setVars(): void
    {
        $lds = app(CustomDataSource::class);

        $this->vars = [
            'marketing_amount_800' => $lds->getCustomAmount(800),
            'marketing_amount_800_eur' => $lds->getCustomAmountEur(800),
            'marketing_amount_1000' => $lds->getCustomAmount(1000),
            'marketing_amount_1000_eur' => $lds->getCustomAmountEur(1000),
            'marketing_amount_1400' => $lds->getCustomAmount(1400),
            'marketing_amount_1400_eur' => $lds->getCustomAmountEur(1400),
            'marketing_amount_1500' => $lds->getCustomAmount(1500),
            'marketing_amount_1500_eur' => $lds->getCustomAmountEur(1500),
            'marketing_amount_3000' => $lds->getCustomAmount(3000),
            'marketing_amount_3000_eur' => $lds->getCustomAmountEur(3000),
            'marketing_amount_6000' => $lds->getCustomAmount(6000),
            'marketing_amount_6000_eur' => $lds->getCustomAmountEur(6000),
        ];
    }

    private function getSql(): string
    {
        $daysWithoutLoanString = implode(',', array_keys(self::DAYS_WITHOUT_LOAN_DISCOUNT));
        $loanActiveStatusId = LoanStatus::ACTIVE_STATUS_ID;
        $loanRepaidStatusId = LoanStatus::REPAID_STATUS_ID;
        $webOfficeId = Office::OFFICE_ID_WEB;

        return <<<SQL
with last_repaid_loan as (
    select
        rl.loan_id,
        rl.client_id,
        rl.amount_approved,
        rl.product_id,
        rl.repaid_at,
        rl.created_at,
        row_number() over (
            partition by rl.client_id
            order by rl.repaid_at desc
        ) as rn
    from loan rl
    where
        rl.office_id = {$webOfficeId}
        and rl.loan_status_id = {$loanRepaidStatusId}
), last_online_application as (
    select
        ll.loan_id,
        ll.client_id,
        ll.created_at,
        row_number() over (
            partition by ll.client_id
            order by ll.created_at desc
        ) as rn
    from loan ll
    where ll.office_id = {$webOfficeId}
)
select
    c.client_id,
    c.phone,
    c.email,
    c.first_name,
    lrl.loan_id as last_repaid_loan_id,
    lrl.repaid_at,
    (current_date - lrl.repaid_at::date) as days_without_loan,
    (current_date - loa.created_at::date) as days_after_last_application
from client c
    join last_repaid_loan lrl on (
        c.client_id = lrl.client_id
        and lrl.rn = 1
        and (current_date - lrl.repaid_at::date) in ({$daysWithoutLoanString})
    )
    join last_online_application loa on c.client_id = loa.client_id and loa.rn = 1
where
    c.active = 1
    and c.disabled_at is null
    and c.deleted_at is null
    and c.deleted = 0
    and (current_date - loa.created_at::date) > 5
    and not exists (
    select 1
    from loan l1
    where
        c.client_id = l1.client_id
        and l1.loan_status_id = {$loanActiveStatusId}
    )
SQL;
    }

    private function createTask(object $client): MarketingTask
    {

        $task = new MarketingTask();
        $task->client_id = $client->client_id;
        $task->discount = self::DAYS_WITHOUT_LOAN_DISCOUNT[$client->days_without_loan];
        $task->variables = array_merge($this->vars, [
            'client_first_name' => $client->first_name,
            'discount' => $task->discount,
        ]);
        $task->last_repaid_loan_id = $client->last_repaid_loan_id;
        $task->days_without_loan = $client->days_without_loan;
        $task->days_after_last_application = $client->days_after_last_application;
        if (!$this->isFirstMessage($task->days_without_loan)) {
            $parentId = MarketingTask::where([
                'days_without_loan' => $task->days_without_loan - 3,
                'client_id' => $task->client_id,
                'status' => MarketingTaskStatusEnum::COMPLETED,
            ])->value('id');
            if ($parentId) {
                $task->parent_id = $parentId;
            } else {
                $task->status = MarketingTaskStatusEnum::SKIPPED;
                $task->details = 'Parent task not found for ' . $task->days_without_loan - 3 . ' days';

                // unset($task);
                // $task = new MarketingTask();
                // $task->client_id = $client->client_id;
                // $task->discount = self::DAYS_WITHOUT_LOAN_DISCOUNT[$client->days_without_loan];
                // $task->variables = [
                //     'client_first_name' => $client->first_name,
                //     'discount' => $task->discount,
                // ];
                // $task->last_repaid_loan_id = $client->last_repaid_loan_id;
                // $task->days_without_loan = $client->days_without_loan - 3;
                // $task->days_after_last_application = $client->days_after_last_application;
                // $task->save();

                // $task->skipp_sending = true;
                // return $task;
            }
        }
        $task->save();

        return $task;
    }

    private function isFirstMessage(int $daysWithoutLoan): bool
    {
        return $daysWithoutLoan % 5 === 0;
    }

    private function createMessages(MarketingTask $task): void
    {
        foreach (MarketingTaskMessageTypeEnum::cases() as $messageType) {
            $order = $this->isFirstMessage($task->days_without_loan) ? 'first' : 'last';
            // To select the right template, because our templates are named after the day of the first message.
            $days = $order === 'first' ? $task->days_without_loan : $task->days_without_loan - 3;
            $template = ($messageType->templatable())::firstWhere('key', "marketing_{$days}_days_{$order}");

            if (!$template) {
                throw new RuntimeException("Template with key marketing_{$days}_days_{$order} not found");
            }

            $message = new MarketingTaskMessage();
            $message->marketing_task_id = $task->id;
            $message->type = $messageType;
            $message->templatable()->associate($template);
            $message->save();
        }
    }

    private function sendEmailReport(array $stats, array $errors): void
    {
        $html = $this->prepareMail($stats, $errors);
        $env = strtoupper(config('app.env'));
        $prj = strtoupper(config('app.project'));

        $senderData = config('mail.log_monitor.sender');
        $receivers = config('mail.log_monitor.receivers');
        $title = "$prj - Prepare marketing tasks Credit-Hunter ($env): " . now()->format('Y-m-d');

        foreach ($receivers as $receiver) {
            (new EmailProvider)->sendEmail(
                $senderData['from'],
                $receiver,
                $title,
                $html
            );
        }
    }

    private function prepareMail(array $stats, array $errors): string
    {
        $html = '
            <html>
            <h1 id="prepare-marketing-tasks-report">"Prepare marketing tasks" report</h1>
            <h2 id="stats">Stats</h2>
            <ul>
        ';

        foreach ($stats as $item) {
            $html .= "<li>$item</li>";
        }

        $html .= '
            </ul>
            <h2 id="problems">Problems</h2>
            <table>
                <thead>
                    <tr>
                        <td>Client ID</td>
                        <td>Error message</td>
                    </tr>
                </thead>
                <tbody>
        ';

        foreach ($errors as $clientId => $message) {
            $html .= "
                <tr>
                    <td>$clientId</td>
                    <td>$message</td>
                </tr>
            ";
        }

        $html .= '
                </tbody>
            </table>
            </html>
        ';

        return $html;
    }
}
