<?php

namespace Modules\Communication\Console;

use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\Office;
use Modules\Common\Models\SaleAttempt;
use Modules\Common\Models\SaleDecision;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Repositories\EmailTemplateRepository;
use Modules\Communication\Services\EmailService;

class SaleTaskNoInterestSendEmail extends CommonCommand
{
    protected $signature = 'script:communication:send-email-no-interest';
    protected $description = 'Send email for clients which had exit from sale task with no_interest decision(Kernel: from 09:00 till 18:59)';

    public function handle()
    {
        $this->startLog($this->description);
        $processed = 0;
        $failed = 0;


        $emailService = app(EmailService::class);
        $emailTemplateId = app(EmailTemplateRepository::class)->getByKey(
            EmailTemplateKeyEnum::NO_INTEREST_SALE_TASK_EXIT->value
        )?->email_template_id;

        if (empty($emailTemplateId)) {
            throw new \RuntimeException(
                'No available email template: ' . EmailTemplateKeyEnum::NO_INTEREST_SALE_TASK_EXIT->value
            );
        }

        // there are not so many ids, since we take them every hour.
        $clientIds = SaleAttempt::query()
            ->selectRaw('DISTINCT COALESCE(st.client_id, sth.client_id) AS client_id')
            ->leftJoin('sale_task as st', 'st.sale_task_id', '=', 'sale_attempt.sale_task_id')
            ->leftJoin('sale_task_history as sth', 'sth.sale_task_id', '=', 'sale_attempt.sale_task_id')
            ->join('client as c', DB::raw('COALESCE(st.client_id, sth.client_id)'), '=', 'c.client_id')
            ->where(
                'sale_attempt.created_at',
                '>',
                now()->subDay()->startOfDay()->toDateTimeString()
            ) // Only today & yesterday attempts
            ->where(
                'sale_attempt.sale_decision_id',
                SaleDecision::SALE_DECISION_ID_NO_INTEREST
            ) // Tasks with decision id
            ->where(
                'sale_attempt.office_id',
                Office::OFFICE_ID_WEB
            ) // Only for web office
            ->where(
                'sale_attempt.end_at',
                '<=',
                now()->subHour()->toDateTimeString()
            ) // Email after one hour exit from task
            ->whereNotNull(DB::raw('COALESCE(st.client_id, sth.client_id)')) // Ensure client_id is not null
            ->whereNotNull('c.email') // Ensure client has an email
            ->whereRaw(
                'c.client_id NOT IN (
        SELECT DISTINCT e.client_id
        FROM email e 
        WHERE
            e.email_template_id = ?
            AND e.created_at::DATE >= NOW()::DATE - INTERVAL \'60 days\'
    )',
                [$emailTemplateId]
            ) // Ensure that we did not send such email template to client last 60 days
            ->whereRaw(
                'NOT EXISTS (
            SELECT 1 FROM loan l
            WHERE l.client_id = c.client_id
            AND l.created_at > sale_attempt.created_at
        )'
            )
            ->pluck('client_id'); // Get only client_id values


        $builder = Client::whereIn('client_id', $clientIds);


        $builder->chunkById(100, function ($rows) use ($emailService, $emailTemplateId, &$processed, &$failed) {
            /** @var Client $row */
            foreach ($rows as $client) {
                try {
                    /// send email
                    $resp = $emailService->sendByTemplateId(
                        $emailTemplateId,
                        $client
                    );

                    if ($resp) {
                        $processed++;
                    }
                } catch (\Throwable $e) {
                    $this->log($e->getMessage(), true);

                    $failed++;
                }
            }
        });

        $this->finishLog([
            'Total processing: ' . $processed . "\n",
            'Total failed: ' . $failed . "\n",
        ]);
    }
}