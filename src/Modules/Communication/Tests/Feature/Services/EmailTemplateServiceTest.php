<?php

namespace Modules\Communication\Tests\Feature\Services;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Models\Changelog\ChangelogEmailTemplate;
use Modules\Common\Models\Office;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailTemplateService;
use Tests\TestCase;

class EmailTemplateServiceTest extends TestCase
{
    use DatabaseTransactions;


    public function __construct(
        private readonly EmailTemplateService $service = new EmailTemplateService()
    ) {
        parent::__construct();
    }

    public function testGetTemplateById()
    {
        $template = EmailTemplate::factory()->create();
        $result = $this->service->getById($template->getKey());
        $this->assertEquals($template->refresh()->toArray(), $result->toArray());
    }

    public function testRevert()
    {
        $fromName = fake()->text();
        $toName = fake()->text();
        $template = EmailTemplate::factory()->create();
        $historyRow = ChangelogEmailTemplate::factory()->create([
            'from' => ['title' => $fromName],
            'to' => ['title' => $toName],
            'email_template_id' => $template->getKey()
        ]);
        $this->service->revert($template, $historyRow);
        $this->assertEquals($template->title, $fromName);
    }

    public function testDelete()
    {
        $template = EmailTemplate::factory()->create();
        $this->service->delete($template);
        $template->refresh();
        $this->assertNotNull($template->deleted_at);
    }

    public function testEnable()
    {
        $template = EmailTemplate::factory()->create(['active' => false]);
        $this->service->enable($template);
        $template->refresh();
        $this->assertNotNull($template->enabled_at);
        $this->assertTrue($template->active);
    }

    public function testDontEnable()
    {
        $this->expectException(\RuntimeException::class);
        $template = EmailTemplate::factory()->create(['active' => true]);
        $this->service->enable($template);
    }

    public function testDisable()
    {
        $template = EmailTemplate::factory()->create(['active' => true]);
        $this->service->disable($template);
        $template->refresh();
        $this->assertNotNull($template->disabled_at);
        $this->assertFalse($template->active);
    }

    public function testDontDisable()
    {
        $this->expectException(\RuntimeException::class);
        $template = EmailTemplate::factory()->create(['active' => false]);
        $this->service->disable($template);
    }

    public function testGetTemplateByKeyAndOffice()
    {
        $office = Office::factory()->create();
        $template = EmailTemplate::factory()
            ->hasAttached($office, [], 'offices')
            ->create(['active' => true]);
        $result = $this->service->getTemplateByKeyAndOffice($template->key, $office->getKey());
        $this->assertEquals($result->getKey(), $template->getKey());
    }

    public function testUpdate()
    {
        $fromName = fake()->text();
        $toName = fake()->text();
        $template = EmailTemplate::factory()->create(['title' => $fromName]);
        $this->service->update($template, ['title' => $toName]);
        $template->refresh();
        $this->assertEquals($template->title, $toName);
    }

    public function testCreate()
    {
        $template = $this->service->create([
            'title' => fake()->title,
            'key' => fake()->slug,
            'text' => fake()->text,
            'description' => fake()->text,
            'body' => fake()->text,
        ]);
        $template->refresh();

        $this->assertNotNull($template->getKey());
    }

    public function testGetByFilters()
    {
        $fromName = fake()->text();
        $template = EmailTemplate::factory()->create(['title' => $fromName]);
        $template->refresh();
        $result = $this->service->getByFilters(1, ['title' => $fromName]);
        $this->assertEquals($template->toArray(), $result->items()[0]->toArray());
    }
}
