<?php

namespace Modules\Communication\Tests\Feature\Services;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Common\Models\Office;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Services\SmsTemplateServices;
use Tests\TestCase;

class SmsTemplateServiceTest extends TestCase
{
    use DatabaseTransactions;


    public function __construct(
        private readonly SmsTemplateServices $service = new SmsTemplateServices()
    ) {
        parent::__construct();
    }

    public function testGetTemplateById()
    {
        $template = SmsTemplate::factory()->create();
        $result = $this->service->getTemplateById($template->getKey());
        $this->assertEquals($template->refresh()->toArray(), $result->toArray());
    }

    public function testRevert()
    {
        $fromName = fake()->text();
        $toName = fake()->text();
        $template = SmsTemplate::factory()->create();
        $historyRow = ChangelogSmsTemplate::factory()->create([
            'from' => ['name' => $fromName],
            'to' => ['name' => $toName],
            'sms_template_id' => $template->getKey()
        ]);
        $this->service->revert($template, $historyRow);
        $this->assertEquals($template->name, $fromName);
    }

    public function testDelete()
    {
        $template = SmsTemplate::factory()->create();
        $this->service->delete($template);
        $template->refresh();
        $this->assertNotNull($template->deleted_at);
    }

    public function testEnable()
    {
        $template = SmsTemplate::factory()->create(['active' => false]);
        $this->service->enable($template);
        $template->refresh();
        $this->assertNotNull($template->enabled_at);
        $this->assertTrue($template->active);
    }

    public function testDontEnable()
    {
        $this->expectException(\RuntimeException::class);
        $template = SmsTemplate::factory()->create(['active' => true]);
        $this->service->enable($template);
    }

    public function testDisable()
    {
        $template = SmsTemplate::factory()->create(['active' => true]);
        $this->service->disable($template);
        $template->refresh();
        $this->assertNotNull($template->disabled_at);
        $this->assertFalse($template->active);
    }

    public function testDontDisable()
    {
        $this->expectException(\RuntimeException::class);
        $template = SmsTemplate::factory()->create(['active' => false]);
        $this->service->disable($template);
    }

    public function testGetTemplateByKeyAndOffice()
    {
        $office = Office::factory()->create();
        $template = SmsTemplate::factory()
            ->hasAttached($office, [], 'offices')
            ->create(['active' => true]);
        $result = $this->service->getTemplateByKeyAndOffice($template->key, $office->getKey());
        $this->assertEquals($result->getKey(), $template->getKey());
    }

    public function testUpdate()
    {
        $fromName = fake()->text();
        $toName = fake()->text();
        $template = SmsTemplate::factory()->create(['name' => $fromName]);
        $this->service->update($template, ['name' => $toName]);
        $template->refresh();
        $this->assertEquals($template->name, $toName);
    }

    public function testCreate()
    {
        $template = $this->service->create([
            'name' => fake()->title,
            'key' => fake()->slug,
            'text' => fake()->text,
            'description' => fake()->text,
            'body' => fake()->text,
        ]);
        $template->refresh();

        $this->assertNotNull($template->getKey());
    }

    public function testGetByFilters()
    {
        $fromName = fake()->text();
        $template = SmsTemplate::factory()->create(['name' => $fromName]);
        $template->refresh();
        $result = $this->service->getByFilters(1, ['name' => $fromName]);
        $this->assertEquals($template->toArray(), $result->items()[0]->toArray());
    }
}
