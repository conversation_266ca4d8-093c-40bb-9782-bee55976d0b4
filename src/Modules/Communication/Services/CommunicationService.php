<?php

namespace Modules\Communication\Services;

use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Client;
use Modules\Common\Models\Email;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Sms;
use Modules\Common\Services\BaseService;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;

class CommunicationService extends BaseService
{
    public function __construct(
        AdministratorRepository $administratorRepository,
        private readonly UnsentCommunicationLogService $unsentLog,
    ) {
        parent::__construct($administratorRepository);
    }

    protected array $repositoryNamespace = ['Modules\\Communication\\Services\\'];

    public const COMMUNICATION_CHANNELS = [
        'sms' => 'Sms',
        'email' => 'Email',
        'letter' => 'Letter',
        'comment' => 'Comment',
    ];

    public const COMMUNICATION_TYPE_SMS = 'sms_template';
    public const COMMUNICATION_TYPE_EMAIL = 'email_template';
    public const COMMUNICATION_TYPE_MAIL = 'mail_template';

    private $emailService = null;
    private $smsService = null;
    private $notificationSettingService = null;
    private $emailTemplateService = null;
    private $smsTemplateServices = null;

    private function getService(string $type): SendingMessageService
    {
        return match ($type) {
            self::COMMUNICATION_TYPE_SMS => $this->getSmsService(),
            self::COMMUNICATION_TYPE_EMAIL => $this->getEmailService(),
            default => throw new \RuntimeException(
                __('communication::communicationChannels.Invalid')
            ),
        };
    }

    /**
     * @return false|Email|Sms
     */
    public function manualSend(
        Client $client,
        ?Loan  $loan,
        array  $preview,
        array  $vars = []
    ) {
        $template = $preview['template'];
        $templateId = (int)$preview['template_id'];

        $className = get_class($template);
        $lastSlash = strrpos($className, '\\') + 1;
        $simpleClassName = substr($className, $lastSlash);
        $channel = strtolower(substr($simpleClassName, 0, strpos($simpleClassName, 'Template')));
        // if (!empty(getAdminId()) && getAdminId() == 2) dd('tuk', $prefix);

        $isNotificationsStopped = $this->isNotificationStopped(
            $client->getKey(),
            $template->type,
            $channel
        );

        $service = $this->getService($template->getTableName());
        unset($preview['template_id']);

        if ($isNotificationsStopped) {

            $msg = __("communication::communication.errorSendCommunicationToChannel", [
                'channel' => $channel,
            ]);

            $this->unsentLog->create([
                'template_key' => $template->key,
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey(),
                'status' => 'failed',
                'reason' => $msg,
            ], $template);

            throw new \Exception($msg);
        }

        if ($template->key === SmsTemplateKeyEnum::SMS_SIGN_LINK->value) {
            /** @var \Modules\Communication\Services\SmsService $service **/
            $sms = $service->sendSignLink($loan);
            if (!$sms) {
                throw new \Exception(__('communication::communication.errorSendSignLink'));
            }

            return $sms;
        }

        $attachments = [];
        if ($template instanceof EmailTemplate) {
            /** @var \Modules\Communication\Services\EmailService $service **/
            $attachments = $service->generateEmailAttachments(
                $template,
                $loan
            );
        }

        if ($service instanceof EmailService) {
            return $service->sendByTemplateId(
                $templateId,
                $client,
                $loan,
                $vars,
                true, // Important to send FORCE here, to show that this is manual communication
                $attachments
            );
        }

        /** @var \Modules\Communication\Services\EmailService|\Modules\Communication\Services\SmsService $service */
        return $service->sendByTemplateId(
            $templateId,
            $client,
            $loan,
            $vars,
            true // Important to send FORCE here, to show that this is manual communication
        );
    }

    /**
     * @param int $clientId
     * @param string $notificationSettingType
     * @param string $notificationSettingChannel
     *
     * @return bool
     */
    private function isNotificationStopped(
        int    $clientId,
        string $notificationSettingType, // marketing, collect
        string $notificationSettingChannel // sms, email, call
    ): bool {

        if (!in_array($notificationSettingType, ['marketing', 'collect'])) {
            $notificationSettingType = 'marketing';
        }

        $criteria = [
            'client_id' => $clientId,
            'type' => $notificationSettingType,
            'channel' => $notificationSettingChannel,
            'value' => 1,
        ];

        $notificationSetting = $this->getNotificationSettingService()->getByCriteria($criteria);

        return is_null($notificationSetting);
    }

    public function getTemplatesForManualSending($adminOffices)
    {
        $templates = $this->getEmailTemplateService()->getManualWithAdminOfficeRelation($adminOffices);

        $templates->push(...$this->getSmsTemplateServices()->getManualWithAdminOfficeRelation($adminOffices));

        $templates->map(function ($template) {
            return $this->addAdditionalVars($template);
        });

        return $templates;
    }

    /**
     * @param array $data
     * $data format: ['template_type' => '(email|sms)_template', 'template_id' => 0]
     */
    public function templatePreview(
        ?Client $client,
        ?Loan   $loan,
        array   $data,
        array   $vars = []
    ): array {
        /** @var \Modules\Communication\Services\EmailService|\Modules\Communication\Services\SmsService $service */
        $service = $this->getService($data['template_type']);
        $result = $service->getPreview($data['template_id'], $loan, $vars);
        if (!empty($result['template'])) {
            $result['template'] = $this->addAdditionalVars($result['template']);
        }

        return $result;
    }

    private function addAdditionalVars($template)
    {
        $template->name = ucfirst(explode('_', $template->getTableName())[0]);
        $template->translated_name = __('communication::templates.' . $template->key);
        $template->table_name = $template::getTableName();

        if ($template instanceof SmsTemplate) {
            $template->name = strtoupper($template->name);
        }

        return $template;
    }

    private function getEmailService(): EmailService
    {
        if ($this->emailService === null) {
            $this->emailService = app(EmailService::class);
        }

        return $this->emailService;
    }

    private function getSmsService(): SmsService
    {
        if ($this->smsService === null) {
            $this->smsService = app(SmsService::class);
        }

        return $this->smsService;
    }

    private function getNotificationSettingService(): NotificationSettingService
    {
        if ($this->notificationSettingService === null) {
            $this->notificationSettingService = app(NotificationSettingService::class);
        }

        return $this->notificationSettingService;
    }

    private function getEmailTemplateService(): EmailTemplateService
    {
        if ($this->emailTemplateService === null) {
            $this->emailTemplateService = app(EmailTemplateService::class);
        }

        return $this->emailTemplateService;
    }

    private function getSmsTemplateServices(): SmsTemplateServices
    {
        if ($this->smsTemplateServices === null) {
            $this->smsTemplateServices = app(SmsTemplateServices::class);
        }

        return $this->smsTemplateServices;
    }
}
