<?php

namespace Modules\Communication\Services;

use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Common\Enums\LogActionEnum;
use Modules\Common\Libraries\Logs\SmsTemplateState;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Common\Services\BaseService;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Repositories\SmsTemplateRepository;
use RuntimeException;

final class SmsTemplateServices extends BaseService
{
    public function __construct(
        private readonly SmsTemplateRepository $templateRepository = new SmsTemplateRepository(),
    ) {
        parent::__construct();
    }

    public function getTemplateByKeyAndOffice(string $key, int $officeId): ?SmsTemplate
    {
        return $this->templateRepository->getByKeyAndOffice($key, $officeId);
    }

    public function getTemplatesExistingForOffice(int $officeId): array
    {
        $result = DB::select("
            SELECT st.sms_template_id, st.key
            FROM sms_template st
            INNER JOIN office_sms_template ost ON st.sms_template_id = ost.sms_template_id
            WHERE ost.office_id = " . $officeId . ";
        ");

        return $result;
    }

    public function getTemplatesNotExistingForOffice(int $officeId): array
    {
        $result = DB::select("
            SELECT st.sms_template_id, st.key
            FROM sms_template st
            LEFT JOIN office_sms_template ost ON (
                st.sms_template_id = ost.sms_template_id
                AND ost.office_id = " . $officeId . "
            )
            WHERE ost.office_id IS NULL;
        ");

        return $result;
    }

    public function getTemplateById(int $id): ?SmsTemplate
    {
        return $this->templateRepository->getById($id);
    }

    /**
     * @param array $data
     *
     * @return SmsTemplate
     */
    public function create(array $data): SmsTemplate
    {
        $data['key'] = Str::of($data['key'])->slug('_');

        $template = $this->templateRepository->create($data);
        $templateState = new SmsTemplateState($template, LogActionEnum::create);
        $templateState->save();

        return $template;
    }

    /**
     * @param SmsTemplate $template
     * @param array $data
     */
    public function update(SmsTemplate $template, array $data)
    {
        if (!empty($template->sms_template_id) && isset($data['key'])) {
            unset($data['key']);
        }

        if (isset($data['key'])) {
            $data['key'] = Str::of($data['key'])->slug('_');
        }

        $data['manual'] = isset($data['manual']) ? 1 : 0;

        $this->edit($template, LogActionEnum::update, $data);
    }

    private function edit(SmsTemplate $template, LogActionEnum $action, array $data)
    {
        $templateState = new SmsTemplateState($template, $action);
        $templateState->stateFrom();
        $this->templateRepository->edit($template, $data);
        $templateState->save();
    }

    /**
     * @param SmsTemplate $template
     *
     * @throws Exception
     */
    public function delete(SmsTemplate $template)
    {
        $this->templateRepository->delete($template);
        $templateState = new SmsTemplateState($template, LogActionEnum::delete);
        $templateState->save();
    }

    public function enable(SmsTemplate $template)
    {
        if ($template->isActive()) {
            throw new \RuntimeException(
                __('communication::smsTemplateCrud.smsTemplateEnableForbidden')
            );
        }

        $this->templateRepository->enable($template);
        $templateState = new SmsTemplateState($template, LogActionEnum::enable);
        $templateState->save();
    }

    public function disable(SmsTemplate $template)
    {
        if (!$template->isActive()) {
            throw new \RuntimeException(__('communication::smsTemplateCrud.smsTemplateDisableForbidden'));
        }

        $this->templateRepository->disable($template);
        $templateState = new SmsTemplateState($template, LogActionEnum::disable);
        $templateState->save();
    }


    /**
     * @param SmsTemplate $template
     * @param ChangelogSmsTemplate $logTemplate
     */
    public function revert(SmsTemplate $template, ChangelogSmsTemplate $logTemplate)
    {
        if ($logTemplate->getTargetKey() !== $template->getKey()) {
            throw new RuntimeException(__('template id not equal with point log template id'));
        }

        if (!$logTemplate->from) {
            throw new RuntimeException(__('No revert'));
        }

        $changes = $logTemplate::query()
            ->where('created_at', '>=', $logTemplate->created_at)
            ->where($logTemplate->getTargetKeyName(), $logTemplate->getTargetKey())
            ->whereIn('action', [LogActionEnum::update->value, LogActionEnum::revert->value])
            ->orderBy('created_at', 'desc')
            ->get();

        $changeFields = [];
        foreach ($changes as $change) {
            $changeFields = array_replace($changeFields, $change->from);
        }

        $this->edit($template, LogActionEnum::revert, $changeFields);
    }

    public function getByFilters(int $limit, array $data): LengthAwarePaginator
    {
        return $this->templateRepository->getAll(
            $limit,
            $this->getJoins($data),
            ['custom_template' => false, ...$this->getWhereConditions($data)],
            [
                'sms_template_id' => 'DESC',
                'active' => 'DESC',
            ]
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    /**
     * @param string $key
     *
     * @return SmsTemplate|null
     */
    public function getTemplateByKey(string $key): ?SmsTemplate
    {
        return $this->templateRepository->getByCriteria(['key' => $key]);
    }

    public function getManual()
    {
        return $this->templateRepository->getManual();
    }

    public function getManualWithAdminOfficeRelation($adminOffices)
    {
        return $this->templateRepository->getManualWithAdminOfficeRelation($adminOffices);
    }
}
