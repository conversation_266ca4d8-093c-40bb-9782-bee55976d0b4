<?php

namespace Modules\Communication\Services;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Modules\Api\Database\Entities\ClientPoll;
use Modules\Common\Enums\NotificationChannel;
use Modules\Common\Models\Client;
use Modules\Common\Models\Document;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Email;
use Modules\Common\Models\Loan;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\ProductSetting;
use Modules\Communication\Jobs\SendEmailJob;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Repositories\EmailRepository;
use Modules\Communication\Repositories\EmailTemplateRepository;
use Modules\Docs\Domain\Template;
use Modules\Docs\Enums\PlaceholderEnum;
use Modules\Docs\Services\DocumentService;
use Modules\Head\Repositories\LoanRepository;
use Modules\Product\Services\ProductService;
use Modules\ThirdParty\Libraries\EmailProvider;
use Throwable;

final class EmailService extends SendingMessageService
{
    const SKIP_OFFICES = [
        148, // Shumen1 - Stefan request - 27.06.2024
    ];

    public function __construct(
        private readonly EmailRepository $emailRepository,
        private readonly LoanRepository $loanRepository,
        private readonly EmailTemplateRepository $templateRepository,
        private readonly UnsentCommunicationLogService $unsentLog,
        private readonly ProductService $productService,
        private $juridicalLoanChecks = false,
    ) {
        parent::__construct();
    }

    public function withJuridicalLoanChecks(): self
    {
        $this->juridicalLoanChecks = true;
        return $this;
    }

    public function generateEmailAttachments(
        EmailTemplate $emailTemplate,
        Loan          $loan
    ): array {
        $attachments = [];
        $emailAttachments = $emailTemplate->getEmailAttachments();

        /// if no have email attachments return empty array
        if (empty($emailAttachments)) {
            return [];
        }

        $docTemplateIds = [];

        foreach ($emailAttachments as $attachment) {
            $document = DocumentTemplate::where('type', $attachment)->first();
            if (!$document) {
                continue;
            }
            $docTemplateIds[] = $document->getKey();

            /// generate document
            app(DocumentService::class)->generatePdfDocumentForLoanByTemplate(
                $loan,
                $document
            );
        }

        $loan->refresh();
        $generatedDocs = $loan->documents->whereIn('document_template_id', $docTemplateIds);

        $generatedDocs->each(function (Document $document) use (&$attachments) {
            $mimeContentType = mime_content_type($document->file->filepath());

            $attachments[] = [
                'file_id' => $document->file->getKey(),
                'path' => $document->file->filepath(),
                'name' => $document->getHumanFileName(),
                'mime' => $mimeContentType,
            ];
        });


        return $attachments;
    }

    // ============================ Send  ============================

    /**
     * Used in:
     * - EmailController->sendEmail() - ONLY FOR TEST sending emails from template editing page
     */
    public function sendByTemplateIdAndLoanId(int $templateId, int $loanId): bool|Email
    {
        $client = null;
        try {
            $loan = $this->loanRepository->getById($loanId);
            if (!$loan) {
                throw new \RuntimeException('EmailService: wrong loan ID provided');
            }

            $client = $loan->client;
            if (!$client) {
                throw new \RuntimeException('EmailService: wrong loan provided, no client_id');
            }

            $this->mandatoryChecks($client);

            return $this->sendByTemplateId(
                $templateId,
                $client,
                $loan
            );

        } catch (Throwable $e) {
            Log::channel('emailExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $this->unsentLog->createByEmailTemplateId([
                'client_id' => !empty($client->client_id) ? $client->client_id : null,
                'loan_id' => $loanId,
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $templateId);

            return false;
        }
    }

    /**
     * Used in:
     * - Here: sendByTemplateIdAndLoanId()
     * - CommunicationService->manualSend(Manual communication from Client Card, by selected template from drop down)
     */
    public function sendByTemplateId(
        int    $templateId,
        Client $client,
        Loan   $loan = null,
        array  $vars = [],
        bool   $force = false,
        array  $attachments = [],
        ?string $queue = null,
    ): bool|Email {

        try {

            // get template
            $template = $this->templateRepository->getById($templateId);
            if (!$template) {
                throw new \RuntimeException('EmailService. Can not find template #' . $templateId);
            }

            $this->mandatoryChecks($client);

            // save in db and send
            return $this->sendByTemplateWithChecks(
                $template,
                $client,
                $loan,
                $vars,
                $attachments,
                $force,
                queue: $queue,
            );

        } catch (Throwable $e) {
            Log::channel('emailExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $this->unsentLog->createByEmailTemplateId([
                'template_key' => $template?->key,
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey(),
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $templateId);

            return false;
        }
    }

    // Used in:
    // - SendNewLoanDocsEmailListener (EmailTemplate::NEW_APPLICATION_CREATED)
    // - SendNewLoanDocsEmailListener (EmailTemplate::LOAN_PARAMS_CHANGED)
    // - ApproveDockPackSender (EmailTemplate::LOAN_APPROVED)
    // - SendDisapprovedSmsAndEmail (EmailTemplate::LOAN_REJECTED)
    // - RepaidLoanCommunication (EmailTemplate::LOAN_REPAID)
    // - ClientCardCommunicationController::earlyRepaymentSendEmail() (EmailTemplate::EARLY_LOAN_REPAYMENT_DUE)
    // - SendEmailAboutWrongPhoneListener (EmailTemplate::COL_TASK_EXIT_WRONG_PHONE_OWNER, EmailTemplate::COL_TASK_EXIT_NO_SUCH_PHONE)
    // - LetterNotificationController (EmailTemplate::OUTSTANDING_OBLIGATIONS_LETTER, EmailTemplate::NO_OBLIGATIONS_LETTER)
    // - ComingDueDateReminder (EmailTemplate::UPCOMING_DUE_INSTALMENT)
    // - NotifyOverdueWebOfficeLoan (EmailTemplate::CREDIT_OVERDUE_3, EmailTemplate::CREDIT_OVERDUE_12, EmailTemplate::CREDIT_OVERDUE_23, EmailTemplate::CREDIT_OVERDUE_45, EmailTemplate::CREDIT_OVERDUE_60, EmailTemplate::CREDIT_OVERDUE_90)
    public function sendByTemplateKeyAndLoanId(
        string $templateKey,
        int    $loanId,
        int    $delayInSec = 5,
        array  $vars = [],
        array  $attachments = [],
        bool   $force = false
    ): bool|Email {

        $client = null;
        try {
            $loan = $this->loanRepository->getById($loanId);
            if (!$loan) {
                throw new \RuntimeException('EmailService: wrong loan ID provided');
            }

            $client = $loan->client;
            if (!$client) {
                throw new \RuntimeException('EmailService: wrong loan provided, no client_id');
            }

            $this->mandatoryChecks($client, $this->juridicalLoanChecks ? $loan : null);

            return $this->sendByTemplateKey(
                $templateKey,
                $client,
                $loan,
                $vars,
                $attachments,
                $force,
                $delayInSec
            );
        } catch (Throwable $e) {
            Log::channel('emailExec')->info( $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $template = $this->templateRepository->getByKey($templateKey);
            if ($template) {
                $this->unsentLog->createByEmailTemplateId([
                    'template_key' => $template->key,
                    'client_id' => !empty($client->client_id) ? $client->client_id : null,
                    'loan_id' =>  $loan?->getKey(),
                    'status' => 'failed',
                    'reason' => $e->getMessage(),
                ], $template->getKey());
            } else {
                throw $e;
            }

            return false;
        }
    }

    /**
     * checks permissions and settings before going to real send
     * Used in:
     * - UpdateLoanFromAutoProcessBecauseOfCreditLimitAction
     */
    public function sendByTemplateKey(
        string $templateKey,
        Client $client,
        Loan   $loan = null,
        array  $vars = [],
        array  $attachments = [],
        bool   $force = false,
        int    $delayInSec = 0,
        ?string $queue = null,
    ): bool|Email {
        try {

            $template = $this->templateRepository->getByKey($templateKey);
            if (!$template) {
                throw new Exception('EmailService. Can not find template #' . $templateKey);
            }

            return $this->sendByTemplateWithChecks(
                $template,
                $client,
                $loan,
                $vars,
                $attachments,
                $force,
                $delayInSec,
                $queue,
            );

        } catch (Throwable $e) {
            Log::channel('emailExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            if (isset($template)) {
                $this->unsentLog->create([
                    'template_key' => $templateKey,
                    'client_id' => $client->getKey(),
                    'loan_id' => $loan?->getKey(),
                    'status' => 'failed',
                    'reason' => $e->getMessage(),
                ], $template);
            } else {
                throw $e;
            }

            return false;
        }
    }

    public function sendByTemplateWithChecks(
        EmailTemplate $template,
        Client $client,
        Loan   $loan = null,
        array  $vars = [],
        array  $attachments = [],
        bool   $force = false,
        int    $delayInSec = 0,
        ?string $queue = null,
    ): bool|Email {

        try {
            $this->mandatoryChecks($client);

            /// if we have loan do check for office relation & product relation
            if ($loan && !$template->custom_template) {
                // skip office
                if ($loan->office_id !== 1) {
                    throw new \RuntimeException('Office #' . $loan->office_id . ' has no permissions for sending emails');
                }

                $product = $loan->product;
                $productSendNotifVlaue = $this->productService->getProductSettingByKey(
                    $product,
                    ProductSetting::SEND_EMAIL_KEY
                );
                $allowedSendingByProduct = (1 == $productSendNotifVlaue);
                if (!$allowedSendingByProduct) {
                    throw new \RuntimeException('Product #' . $product->getKey() . ' has no email setup(global)');
                }

                $hasRelatedTemplateToOffice = $this->templateRepository->getByKeyAndOffice(
                    $template->key,
                    $loan->office_id
                );
                if (!$hasRelatedTemplateToOffice) {
                    throw new \RuntimeException("Office #$loan->office_id has no email_template setup($template->key)");
                }
            }

            // check if on client level, we have setting to send him notifications
            // this is especially important for marketing messages, where client can unsubscribe for them
            if (!$client->allowedSendingForTypeAndChanel($template->type, NotificationChannel::Email)) {
                throw new \RuntimeException("Client # $client->client_id did not allow email sending ($template->type)");
            }

            // save in db and send
            return $this->sendByTemplate(
                $template,
                $client,
                $loan,
                $vars,
                $attachments,
                $force,
                $delayInSec,
                $queue,
            );
        } catch (Throwable $e) {
            Log::channel('emailExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $this->unsentLog->create([
                'template_key' => $template->key,
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey(),
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $template);

            return false;
        }
    }

    // Used in:
    // - SaleService->storeAttempt()
    public function sendByTemplateKeyAndSaleTask(string $templateKey, SaleTask $saleTask): bool|Email
    {
        try {

            $clientId = Client::UNREG_SALE_CLIENT_ID; // Default, since sale task could be created before we create a client
            $loanId = 0; // Default
            $loan = null;
            $client = null;
            $vars = [];

            if (!empty($saleTask->client_id)) {
                $clientId = $saleTask->client_id;
                $client = Client::find($clientId);
                $this->mandatoryChecks($client);
            } else {
                $vars[PlaceholderEnum::CLIENT_PRIMARY_PHONE->value] = $saleTask->phone;
            }

            if (!empty($saleTask->loan_id)) {
                $loanId = $saleTask->loan_id;
                $loan = Loan::find($loanId);
            } else {
                $vars[PlaceholderEnum::APPLICATION_DATE->value] = Carbon::parse($saleTask->created_at)->format('d.m.Y');
            }



            try {
                $scamLoanIds = getScamLoanIds();
                if (!empty($loan->loan_id) && !empty($scamLoanIds) && in_array($loan->loan_id, $scamLoanIds)) {
                    return false;
                }
            } catch (\Throwable $e) {
                $msg = $e->getMessage() . ':' . $e->getFile() . ':' . $e->getLine();
                \Log::error('ES->sendByTemplateKeyAndSaleTask(): ' . $msg);
            }



            $template = $this->templateRepository->getByKeyAndOffice($templateKey, $saleTask->office_id);
            if (!$template) {
                throw new \RuntimeException('Office #' . $saleTask->office_id . ' has no email_template setup(' . $templateKey . ')');
            }


            $email = $saleTask->email ?? '';
            if (!$email) {
                throw new \RuntimeException('SaleTask #' . $saleTask->sale_task_id . ' has no email');
            }
            if (!isProd()) {
                $email = env('MAIL_TEST_EMAIL', null);
            }


            $text = app(Template::class)->getFilledContentFromData($template, $client, $loan, $vars);
            if (empty($text)) {
                throw new \RuntimeException('EmailService. Empty text compiled');
            }

            // save email in DB before send
            $newEmail = $this->storeEmail(
                $template,
                $clientId,
                $loanId,
                $text,
                $email
            );
            if (!$newEmail->email_id) {
                throw new \RuntimeException('EmailService: failed to save email in DB');
            }

            return $this->sendByEmail(
                $newEmail,
                $clientId,
                $loanId ?: null
            );

        } catch (Throwable $e) {
            Log::channel('emailExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            if (isset($template)) {
                $this->unsentLog->create([
                    'template_key' => $templateKey,
                    'client_id' => $clientId ?: null,
                    'loan_id' => $loanId ?: null,
                    'status' => 'failed',
                    'reason' => $e->getMessage(),
                ], $template);
            } else {
                throw $e;
            }

            return false;
        }
    }

    /**
     * Send email via EmailTemplate
     *
     * Used in:
     * - Here - sendByTemplateId
     * - Here - sendByTemplateKey
     *
     * @param EmailTemplate $emailTemplate
     * @param Client $client
     * @param Loan|null $loan
     * @param array $varsCustom
     * @param array $attachments
     * @param bool $force - IF true - send directly ELSE use job
     * @param int $delayInSec
     * @param string|null $queue
     * @return bool|Email
     * IMPORTANT do not change (private)
     */
    private function sendByTemplate(
        EmailTemplate $emailTemplate,
        Client        $client,
        ?Loan         $loan = null,
        array         $varsCustom = [],
        array         $attachments = [],
        bool          $force = false,
        int           $delayInSec = 0,
        ?string $queue = null,
    ): bool|Email {

        try {
            $scamLoanIds = getScamLoanIds();
            if (!empty($loan->loan_id) && !empty($scamLoanIds) && in_array($loan->loan_id, $scamLoanIds)) {
                return false;
            }
        } catch (\Throwable $e) {
            $msg = $e->getMessage() . ':' . $e->getFile() . ':' . $e->getLine();
            \Log::error('ES->sendByTemplate(): ' . $msg);
        }


        $email = $client->email;
        if (!isProd()) {
            $email = env('MAIL_TEST_EMAIL', null);
        }
        if (!$email) {
            throw new \RuntimeException('Client #' . $client->getKey() . ' has no email');
        }

        $tplVars = !empty($emailTemplate->variables) ? getArrayFromDbArrayField($emailTemplate->variables) : [];
        $pollHash = '';
        $pollDate = null;
        if (in_array('poll', $tplVars)) {
            // Poll Date
            $pollDate = date('Ymd');

            // generate hash
            $pollHash = generateClientPollHash(
                $client->getKey(),
                $emailTemplate->getKey(),
                $pollDate,
                $loan?->getKey()
            );
        }

        // prepare vars and html
        $template = app(Template::class);
        $text = $template->getFilledContentFromData($emailTemplate, $client, $loan, $varsCustom);

        $title = $template->getFilledTitle();
        if (empty($text)) {
            throw new \RuntimeException('EmailService. Empty text compiled');
        }

        // save email in DB before send
        $newEmail = $this->storeEmail(
            $emailTemplate,
            $client->getKey(),
            $loan?->getKey(),
            $text,
            $email,
            $title,
        );
        if (!$newEmail->email_id) {
            throw new \RuntimeException('EmailService: failed to save email in DB');
        }

        if (count($attachments)) {
            foreach ($attachments as $attachment) {
                $newEmail->emailFiles()->attach($attachment['file_id']);
            }
        }

        // Store Poll Hash if needed
        if (!empty($pollHash) && $newEmail->email_id) {
            ClientPoll::setHash(
                $pollHash,
                $client->getKey(),
                $emailTemplate->getKey(),
                $newEmail->getKey(),
                $pollDate
            );
        }

        //Debugger Line: $force = true;
        if (!$force) {
            SendEmailJob::dispatch(
                $newEmail,
                $client->getKey(),
                $loan?->getKey(),
                $attachments
            )
                ->onQueue($queue ?? 'email')
                ->delay(Carbon::now()->addSeconds($delayInSec));

            return $newEmail;
        }

        // real sending
        return $this->sendByEmail(
            $newEmail,
            $client->getKey(),
            $loan?->getKey(),
            $attachments
        );
    }

    /**
     * public because of job
     */
    public function sendByEmail(
        Email $email,
        int   $clientId = null,
        int   $loanId = null,
        array $attachments = []
    ): Email|bool {
        // real sending email via provider
        try {
            if (isProd() && !$email->rateLimit($clientId, $loanId)) {
                $email->response = 'RATE LIMITATION';
                $email->client_id = $clientId;
                $email->loan_id = $loanId;
                $email->save();

                return false;
            }

            $sentMessage = (new EmailProvider)->sendEmail(
                $email->sender_from,
                $email->sender_to,
                $email->title,
                $email->text,
                $attachments
            );

            // update email sent time
            $email->tries += 1;
            $email->response = ($sentMessage ? Email::POSITIVE_RESPONSE : Email::NEGATIVE_RESPONSE);
            $email->send_at = Carbon::now();
            $email->client_id = $clientId;
            $email->loan_id = $loanId;
            $email->external_id = $sentMessage?->getMessageId(); // Mailgun message id
            $email->save();

            return $email;
        } catch (Exception $e) {
            $message = $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine();
            Log::channel('emailExec')->info($message);

            $email->tries += 1;
            $email->response = 'KO - ' . $message;
            $email->send_at = Carbon::now();
            $email->save();

            $this->unsentLog->createByEmailTemplateId([
                'client_id' => $email->client_id,
                'loan_id' => $email->loan_id,
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $email->email_template_id);

            return false;
        }
    }

    private function storeEmail(
        EmailTemplate $template,
        ?int          $clientId,
        ?int          $loanId,
        string        $text,
        string        $email,
        ?string       $title = null,
    ): Email {
        $adminId = getAdminId();

        // used for email without client/loan created - on sale task
        $identifier = md5(time());
        if ($clientId) {
            $identifier = $template->getIdentifier($clientId, $loanId ?? '00000');
        }

        return $this->emailRepository->create([
            'email_template_id' => $template->getKey(),
            'manual' => $template->manual,
            'client_id' => $clientId,
            'loan_id' => $loanId,
            'identifier' => $identifier,
            'type' => $template->type,
            'title' => $title ?? $template->title,
            'text' => $text,
            'body' => $text,
            'sender_from' => config('mail.sender.default'),
            'sender_to' => $email,
            'created_by' => $adminId,
            'administrator_id' => $adminId,
        ]);
    }

    // ============================ Preview ============================

    public function getPreview(
        int  $emailTemplateId,
        Loan $loan
    ): array {
        // get template
        try {
            $emailTemplate = $this->templateRepository->getById($emailTemplateId);
            if (!$emailTemplate) {
                throw new \RuntimeException('EmailService. Can not find template #' . $emailTemplateId);
            }
        } catch (Throwable $e) {
            throw new \RuntimeException(
                __('communication::emailTemplateCrud.emailTemplateNotFound'),
                $e
            );
        }

        $text = app(Template::class)->getFilledContentFromData($emailTemplate, null, $loan);


        return [
            'text' => $text,
            'template' => $emailTemplate,
        ];
    }
}
