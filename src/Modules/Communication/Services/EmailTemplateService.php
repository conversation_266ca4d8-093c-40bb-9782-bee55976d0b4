<?php

namespace Modules\Communication\Services;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Modules\Common\Enums\LogActionEnum;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Libraries\Logs\EmailTemplateState;
use Modules\Common\Models\Changelog\ChangelogEmailTemplate;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Common\Services\BaseService;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Repositories\EmailTemplateRepository;
use RuntimeException;

final class EmailTemplateService extends BaseService
{
    public function __construct(
        private readonly EmailTemplateRepository $emailTemplateRepository = new EmailTemplateRepository(),
    )
    {
        parent::__construct();
    }

    public function getById(int $id): ?EmailTemplate
    {
        $emailTemplates = $this->emailTemplateRepository->getById($id);
        if (!$emailTemplates) {
            throw new \RuntimeException(__('communication::emailTemplateCrud.emailTemplateNotFound'));
        }

        return $emailTemplates;
    }

    public function getTemplateByKeyAndOffice(string $key, int $officeId): ?EmailTemplate
    {
        $emailTemplate = $this->emailTemplateRepository->getByKeyAndOffice($key, $officeId);
        // if (!$emailTemplate) {
        //     $emailTemplate = EmailTemplate::where(['key' => $key])->firstOrFail();
        // }
        if (!$emailTemplate) {
            throw new RuntimeException("Email template not found: [key: {$key}][officeId: {$officeId}]");
        }

        return $emailTemplate;
    }

    public function getTemplatesExistingForOffice(int $officeId): array
    {
        $result = DB::select("
            SELECT st.email_template_id, st.key
            FROM email_template st
            INNER JOIN office_email_template ost ON st.email_template_id = ost.email_template_id
            WHERE ost.office_id = " . $officeId . ";
        ");

        return $result;
    }

    public function getTemplatesNotExistingForOffice(int $officeId): array
    {
        $result = DB::select("
            SELECT st.email_template_id, st.key
            FROM email_template st
            LEFT JOIN office_email_template ost ON (
                st.email_template_id = ost.email_template_id
                AND ost.office_id = " . $officeId . "
            )
            WHERE ost.office_id IS NULL;
        ");

        return $result;
    }

    /**
     * @param array $data
     * @return EmailTemplate
     * @throws NotFoundException
     */
    public function create(array $data): EmailTemplate
    {
        $data['key'] = Str::of($data['key'])->slug('_');
        if (!isset($data['body'])) {
            $data['body'] = $data['description'];
        }
        $emailTemplates = $this->emailTemplateRepository->create($data);

        $emailTemplateState = new EmailTemplateState($emailTemplates, LogActionEnum::create);
        $emailTemplateState->save();

        return $emailTemplates;
    }

    /**
     * @param EmailTemplate $emailTemplate
     * @param array $data
     *
     */
    public function update(EmailTemplate $emailTemplate, array $data)
    {
        if (!empty($emailTemplate->email_template_id) && isset($data['key'])) {
            unset($data['key']);
        }

        if (isset($data['key'])) {
            $data['key'] = Str::of($data['key'])->slug('_');
        }

        $data['manual'] = isset($data['manual']) ? 1 : 0;

        $this->edit($emailTemplate, LogActionEnum::update, $data);
    }

    /**
     * @param EmailTemplate $template
     * @param ChangelogSmsTemplate $logTemplate
     */
    public function revert(EmailTemplate $template, ChangelogEmailTemplate $logTemplate)
    {
        if ($logTemplate->getTargetKey() !== $template->getKey()) {
            throw new RuntimeException(__('template id not equal with point log template id'));
        }

        if (!$logTemplate->from) {
            throw new RuntimeException(__('No revert'));
        }

        $changes = $logTemplate::query()
            ->where('created_at', '>=', $logTemplate->created_at)
            ->where($logTemplate->getTargetKeyName(), $logTemplate->getTargetKey())
            ->whereIn('action', [LogActionEnum::update->value, LogActionEnum::revert->value])
            ->orderBy('created_at', 'desc')
            ->get();

        $changeFields = [];
        foreach ($changes as $change) {
            $changeFields = array_replace($changeFields, $change->from);
        }

        $this->edit($template, LogActionEnum::revert, $changeFields);
    }

    private function edit(EmailTemplate $template, LogActionEnum $action, array $data)
    {
        $templateState = new EmailTemplateState($template, $action);
        $templateState->stateFrom();
        $this->emailTemplateRepository->edit($template, $data);
        $templateState->save();
    }


    /**
     * @param EmailTemplate $template
     *
     */
    public function delete(EmailTemplate $template)
    {
        $this->emailTemplateRepository->delete($template);
        $templateState = new EmailTemplateState($template, LogActionEnum::delete);
        $templateState->save();
    }

    public function enable(EmailTemplate $template)
    {
        if ($template->isActive()) {
            throw new \RuntimeException(__('communication::emailTemplateCrud.emailTemplateEnableForbidden'));
        }
        $this->emailTemplateRepository->enable($template);

        $templateState = new EmailTemplateState($template, LogActionEnum::enable);
        $templateState->save();
    }

    public function disable(EmailTemplate $template)
    {
        if (!$template->isActive()) {
            throw new \RuntimeException(__('communication::emailTemplateCrud.emailTemplateDisableForbidden'));
        }

        $this->emailTemplateRepository->disable($template);

        $templateState = new EmailTemplateState($template, LogActionEnum::disable);
        $templateState->save();
    }

    /**
     * @param int $limit
     * @param array $data
     * @return LengthAwarePaginator
     */
    public function getByFilters(int $limit, array $data): LengthAwarePaginator
    {
        return $this->emailTemplateRepository->getAll(
            $limit,
            $this->getJoins($data),
            ['custom_template' => false, ...$this->getWhereConditions($data)],
            [
                'email_template_id' => 'DESC',
                'active' => 'DESC'
            ]
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    public function getManualWithAdminOfficeRelation($adminOffices)
    {
        return $this->emailTemplateRepository->getManualWithAdminOfficeRelation($adminOffices);
    }
}
