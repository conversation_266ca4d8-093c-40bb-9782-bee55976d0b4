<?php

namespace Modules\Communication\Services;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Modules\Common\Enums\InviteUrlActionEnum;
use Modules\Common\Enums\NotificationChannel;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\ProductSetting;
use Modules\Common\Models\Sms;
use Modules\Common\Models\SmsLoginCode;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Jobs\SendSmsJob;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Repositories\SmsLoginCodeRepository;
use Modules\Communication\Repositories\SmsRepository;
use Modules\Communication\Repositories\SmsTemplateRepository;
use Modules\Docs\Domain\Template;
use Modules\Docs\Enums\PlaceholderEnum;
use Modules\Head\Repositories\LoanRepository;
use Modules\Product\Services\ProductService;
use Modules\ThirdParty\Libraries\SmsProvider;
use RuntimeException;
use Throwable;

final class SmsService extends SendingMessageService
{
    public function __construct(
        private readonly SmsRepository $smsRepository,
        private readonly SmsTemplateRepository $templateRepository,
        private readonly SmsLoginCodeRepository $smsLoginCodeRepository,
        private readonly LoanRepository $loanRepository,
        private readonly ProductService $productService,
        private readonly UnsentCommunicationLogService $unsentLog,
        private $juridicalLoanChecks = false
    ) {
        parent::__construct();
    }

    public function withJuridicalLoanChecks(): self
    {
        $this->juridicalLoanChecks = true;

        return $this;
    }

    // ============================ Send & Preview ============================

    /**
     * Used in: SmsController->sendSms() - for tests only!
     */
    public function sendByTemplateIdAndLoanId(
        int    $templateId,
        int    $loanId,
        string $phone = '0000000000'
    ): Sms|bool|null {

        $client = null;
        try {
            $loan = $this->loanRepository->loadRelations(['client'])->getById($loanId);
            if (!$loan) {
                throw new \RuntimeException('SmsService: wrong loan ID provided');
            }

            $client = $loan->client;
            if (!$client) {
                throw new \RuntimeException('SmsService: wrong loan provided, no client_id');
            }

            $this->mandatoryChecks($client);

            $client->phone = $phone;

            return $this->sendByTemplateId(
                $templateId,
                $client,
                $loan
            );

        } catch (Throwable $e) {
            Log::channel('smsExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $this->unsentLog->createBySmsTemplateId([
                'client_id' => !empty($client->client_id) ? $client->client_id : null,
                'loan_id' => $loanId,
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $templateId);

            return false;
        }
    }

    /**
     * Used in:
     * - SmsService::sendByTemplateIdAndLoanId() - used for test
     * - CommunicationTemplateController::send() -> CommunicationService::manualSend()
     * - SendSmsCodeByPinAction - api sms code for login
     */
    public function sendByTemplateId(
        int    $templateId,
        Client $client,
        ?Loan  $loan = null,
        array  $vars = [],
        bool   $force = false,
        ?string $queue = null,
        bool $withChecks = true,
    ): Sms|bool|null {
        try {
            $template = $this->templateRepository->getById($templateId);
            if (!$template) {
                throw new \RuntimeException('No sms template found, #' . $templateId);
            }

            $this->mandatoryChecks($client);

            if ($withChecks) {
                return $this->sendByTemplateWithChecks(
                    $template,
                    $client,
                    $loan,
                    $vars,
                    $force,
                    queue: $queue,
                );
            }

            return $this->sendByTemplate(
                $template,
                $client,
                $loan,
                $vars,
                $force,
                queue: $queue,
            );

        } catch (Throwable $e) {
            Log::channel('smsExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $this->unsentLog->createBySmsTemplateId([
                'template_key' => $template?->key,
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey(),
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $templateId);

            return false;
        }
    }

    public function sendByTemplateWithChecks(
        SmsTemplate $template,
        Client $client,
        ?Loan $loan = null,
        array $vars = [],
        bool $force = false,
        ?string $queue = null,
    ): Sms|bool|null {
        try {
            $this->mandatoryChecks($client);

            if ($loan && !$template->custom_template) {
                // skip office
                if ($loan->office_id !== 1) {
                    throw new \RuntimeException('Office #' . $loan->office_id . ' has no permissions for sending sms');
                }

                /// check product relation
                $product = $loan->product;
                $productSendNotifVlaue = $this->productService->getProductSettingByKey(
                    $product,
                    ProductSetting::SEND_SMS_KEY
                );
                $allowedSendingByProduct = (1 == $productSendNotifVlaue);

                // sending is skipped, because for such product not allowed such type sending
                if (!$allowedSendingByProduct) {
                    throw new \RuntimeException('Product #' . $product->product_id . ' has no sms setup');
                }

                //// check office relation
                $hasRelatedTemplateToOffice = $this->templateRepository
                    ->getByKeyAndOffice($template->key, $loan->office_id);
                // sending is failed, because for such office there is not setted current template
                if (!$hasRelatedTemplateToOffice) {
                    throw new \RuntimeException("Office #$loan->office_id has no sms_template setup($template->key)");
                }
            }

            // check if on client level, we have setting to send him notifications
            // this is especially important for marketing messages, where client can unsubscribe for them
            if (!$client->allowedSendingForTypeAndChanel($template->type, NotificationChannel::Sms)) {
                throw new \RuntimeException("Client # $client->client_id did not allow sms sending ($template->type)");
            }

            // save in db and send
            return $this->sendByTemplate(
                $template,
                $client,
                $loan,
                $vars,
                $force,
                queue: $queue,
            );

        } catch (Throwable $e) {
            Log::channel('smsExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            $this->unsentLog->create([
                'template_key' => $template->key,
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey(),
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $template);

            return false;
        }
    }

    // Used in:
    // - SendUrlInSmsToClientAction
    // - CommunicationService
    public function sendSignLink(Loan $loan): Sms|bool
    {
        try {
            return $this->sendByTemplateKeyAndLoan(
                InviteUrlActionEnum::signLink->smsTemplate(),
                $loan
            );
        } catch (Throwable $e) {

            $message = $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine();
            Log::channel('smsExec')->info($message);
        }

        return false;
    }

    // Used in:
    // - ApproveDockPackSender
    public function sendApproveCongrats(Loan $loan): Sms|bool
    {
        try {
            $smsTemplate = '';

            switch (true) {
                case $loan->isPaymentMethodCash():
                    $smsTemplate = SmsTemplateKeyEnum::SMS_TYPE_LOAN_APPROVED_CASH->value;
                    break;
                case $loan->isPaymentMethodBank():
                    $smsTemplate = SmsTemplateKeyEnum::SMS_TYPE_LOAN_APPROVED_BANK->value;
                    break;
                case $loan->isPaymentMethodEasypay():
                    $smsTemplate = SmsTemplateKeyEnum::SMS_TYPE_LOAN_APPROVED_EASYPAY->value;
                    break;
                default:
                    $smsTemplate = '';
                    break;
            }

            if (empty($smsTemplate)) {
                throw new \Exception('(sendApproveCongrats) No template for payment method of loan #' . $loan->loan_id);
            }

            $this->sendByTemplateKeyAndLoan($smsTemplate, $loan);

            return true;

        } catch (Throwable $e) {

            $message = $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine();
            Log::channel('smsExec')->info($message);

            return false;
        }
    }

    /**
     * Used in:
     * - SmsService::sendSignLink()
     * - SmsService::sendApproveCongrats()
     * - SendExtendLoanSmsListener
     * - NotifyOverduePhysicalOfficeLoan
     * - NotifyRepaidWebOfficeClient
     * - NotifyOverdueWebOfficeLoan
     * - NotifySmsLoyalClient
     * - OverdueSmsSender
     * - ClientCardCommunicationController
     * - SendDisapprovedSmsAndEmail
     * - SendSmsPaymentWasReceived
     * - ComingDueDateReminder
     */
    public function sendByTemplateKeyAndLoan(
        string $templateKey,
        Loan   $loan,
        array  $vars = [],
        bool   $force = false, // immediate send
        int    $delay = 5
    ): Sms|bool {
        // try to compile msg
        try {
            $client = $loan->client;

            $this->mandatoryChecks($client, $this->juridicalLoanChecks ? $loan : null);

            if (!$client?->phone) {
                throw new \RuntimeException('Client #' . $client->client_id . ' has no phone');
            }

            $product = $loan->product;
            $productSendNotifVlaue = $this->productService->getProductSettingByKey(
                $product,
                ProductSetting::SEND_SMS_KEY
            );
            $allowedSendingByProduct = (1 == $productSendNotifVlaue);

            $template = $this->templateRepository->getByKey($templateKey);
            if (!$template) {
                throw new \RuntimeException('Sms template with key ' . $templateKey . ' not found');
            }

            // skip office
            if ($loan->office_id !== 1) {
                throw new \RuntimeException('Office #' . $loan->office_id . ' has no permissions for sending');
            }

            // sending is skipped, because for such product not allowed such type sending
            if (!$allowedSendingByProduct) {
                throw new \RuntimeException('Product #' . $product->product_id . ' has no sms setup(global)');
            }

            $hasRelatedTemplateToOffice = $this->templateRepository->getByKeyAndOffice($templateKey, $loan->office_id);

            // sending is failed, because for such office there is not setted current template
            if (!$hasRelatedTemplateToOffice) {
                throw new \RuntimeException('Office #' . $loan->office_id . ' has no sms_template setup(' . $templateKey . ')');
            }


            // check if on client level, we have setting to send him notifications
            // this is especially important for marketing messages, where client can unsubscribe for them
            if (!$client->allowedSendingForTypeAndChanel($template->type, NotificationChannel::Sms)) {
                throw new \RuntimeException('Client #' . $client->client_id . ' did not allow sms sending');
            }


            // save in db and send
            return $this->sendByTemplate(
                $template,
                $client,
                $loan,
                $vars,
                $force,
                $delay
            );

        } catch (Throwable $e) {
            Log::channel('smsExec')->info($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            if (isset($template)) {
                $this->unsentLog->create([
                    'template_key' => $template->key,
                    'client_id' => $client?->getKey(),
                    'loan_id' => $loan->getKey(),
                    'status' => 'failed',
                    'reason' => $e->getMessage(),
                ], $template);
            }

            return false;
        }
    }

    /**
     * Used in:
     * - SmsService::sendByTemplateId()
     * - SmsService::sendByTemplateKeyAndLoan()
     *
     * @param SmsTemplate $template
     * @param Client $client
     * @param Loan|null $loan
     * @param array $vars
     * @param bool $force - IF true - send directly ELSE use job
     * @param int $delay
     * @param string|null $queue
     * @return Sms
     *
     * IMPORTANT do not change (private)
     */
    private function sendByTemplate(
        SmsTemplate $template,
        Client      $client,
        ?Loan       $loan = null,
        array       $vars = [],
        bool        $force = false,
        int         $delay = 5,
        ?string $queue = null,
    ): Sms|bool {


        try {
            $scamLoanIds = getScamLoanIds();
            if (!empty($loan->loan_id) && !empty($scamLoanIds) && in_array($loan->loan_id, $scamLoanIds)) {
                return false;
            }
        } catch (\Throwable $e) {
            $msg = $e->getMessage() . ':' . $e->getFile() . ':' . $e->getLine();
            \Log::error('SS->sendByTemplate(): ' . $msg);
        }


        $text = $this->getTextByTemplate(
            $template,
            $client,
            $loan,
            $vars
        );
        if (empty($text)) {
            throw new \RuntimeException('SmsService: failed to compile text');
        }

        $sms = $this->storeSms(
            $template,
            $client,
            $loan?->getKey(),
            $text,
        );
        if (!$sms->sms_id) {
            throw new \RuntimeException('SmsService: failed to save sms in DB');
        }

        if (!$this->sendSms($sms, $client, $loan, $force, $delay, $queue)) {
            throw new \RuntimeException('SmsService: failed to send sms');
        }

        return $sms;
    }

    public function sendByTemplateKeyAndPhone(
        string $templateKey,
        string $phone,
        array $vars = [],
        bool $force = false,
        int $delay = 5,
        ?string $queue = null,
    ): Sms {
        $template = $this->templateRepository->getByKey($templateKey);
        if (!$template) {
            throw new \RuntimeException('Sms template with key ' . $templateKey . ' not found');
        }

        return $this->sendByTemplateAndPhone($template, $phone, $vars, $force, $delay, $queue);
    }

    public function sendByTemplateAndPhone(
        SmsTemplate $template,
        string $phone,
        array $vars = [],
        bool $force = false,
        int $delay = 5,
        ?string $queue = null,
    ): Sms {
        if ($client = Client::firstWhere(['phone' => $phone])) {
            $this->mandatoryChecks($client);
        }

        $text = $this->getTextByTemplate(
            $template,
            null,
            null,
            $vars
        );
        if (empty($text)) {
            throw new \RuntimeException('SmsService: failed to compile text');
        }

        $sms = $this->storeSmsByPhone(
            $template,
            $phone,
            $text,
        );
        if (!$sms->sms_id) {
            throw new \RuntimeException('SmsService: failed to save sms in DB');
        }

        if (!$this->sendSms($sms, null, null, $force, $delay, $queue)) {
            throw new \RuntimeException('SmsService: failed to send sms');
        }

        return $sms;
    }

    private function sendSms(
        Sms $sms,
        ?Client $client,
        ?Loan $loan = null,
        bool $force = false,
        int $delay = 5,
        ?string $queue = null,
    ): bool {

        if (!$force) {
            if ($sms->isLoginCodeSms()) {
                $delay = 0;
            }

            SendSmsJob::pushToQueue(
                $sms,
                $client?->getKey(),
                $loan?->getKey(),
                $delay,
                $queue,
            );

            return true;
        }

        // real sending
        return $this->sendBySms($sms, $client?->getKey(), $loan?->getKey());
    }

    /**
     * public because of job
     */
    public function sendBySms(
        Sms $sms,
        ?int $clientId = null,
        ?int $loanId = null,
    ): bool {
        // real sending sms via provider
        try {

            if (!$sms->rateLimit($clientId, $loanId)) {
                $sms->response = 'RATE LIMITATION';
                $sms->client_id = $clientId;
                $sms->loan_id = $loanId;
                $sms->save();

                throw new \Exception('Rate limited sending');
            }

            // commented, because SMS service not tested, waits https
            $sendResult = SmsProvider::send($sms->phone, $sms->text);

            // update sms sent time
            $sms->tries += 1;
            $sms->response = $sendResult;
            $sms->client_id = $clientId;
            $sms->loan_id = $loanId;
            $sms->sent_at = Carbon::now();
            $sms->save();

            if (empty($sendResult)) {
                throw new \Exception('No response from Operator');
            }
            if (!preg_match('/(\:1004\,)/', $sendResult) && !preg_match('/(OK)/', $sendResult)) { // if not success code
                throw new \Exception('Operator error: ' . $sendResult);
            }

            return true;
        } catch (Exception $e) {
            $message = $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine();
            Log::channel('smsExec')->info($message);

            $sms->tries += 1;
            $sms->response = 'KO - ' . $message;
            $sms->sent_at = Carbon::now();
            $sms->save();

            $this->unsentLog->createBySmsTemplateId([
                'client_id' => $sms->client_id,
                'loan_id' => $sms->loan_id,
                'status' => 'failed',
                'reason' => $e->getMessage(),
            ], $sms->sms_template_id);

            return false;
        }
    }

    private function storeSms(
        SmsTemplate $template,
        Client $client,
        ?int $loanId,
        string $text,
    ): Sms {
        $phone = $client->phone;
        if (!isProd()) {
            $phone = config('app.sms_service_test_phone');
        }
        if (empty($phone)) {
            throw new \RuntimeException('SmsService: client without phone');
        }

        $adminId = getAdminId();

        /// for duplicated sms just append count to end of sms text
        $rows = $this->smsRepository->getSameSmsForToday($phone, $template->sms_template_id);
        if ($rows->count() > 0) {
            $text = $text . ' (' . $rows->count() . ')';
        }

        return $this->smsRepository->create([
            'identifier' => $template->getIdentifier($client->getKey(), $loanId),
            'sms_template_id' => $template->getKey(),
            'administrator_id' => $adminId,
            'created_by' => $adminId,
            'client_id' => $client->getKey(),
            'loan_id' => $loanId,
            'type' => $template->type,
            'text' => $text,
            'phone' => $phone,
            'manual' => $template->manual,
            'sender' => Sms::SMS_SENDER,
        ]);
    }

    private function storeSmsByPhone(
        SmsTemplate $template,
        string $phone,
        string $text,
    ): Sms {
        if (!isProd()) {
            $phone = config('app.sms_service_test_phone');
        }

        $adminId = getAdminId();

        /// for duplicated sms just append count to end of sms text
        $rows = $this->smsRepository->getSameSmsForToday($phone, $template->sms_template_id);
        if ($rows->count() > 0) {
            $text = $text . ' (' . $rows->count() . ')';
        }

        // To avoid the CommunicationWasCreatedEvent event
        return $this->smsRepository->createQuietly([
            'identifier' => $template->getIdentifier($phone),
            'sms_template_id' => $template->getKey(),
            'administrator_id' => $adminId,
            'created_by' => $adminId,
            'client_id' => null,
            'loan_id' => null,
            'type' => $template->type,
            'text' => $text,
            'phone' => $phone,
            'manual' => $template->manual,
            'sender' => Sms::SMS_SENDER,
        ]);
    }

    // ============================= Preview ================================

    // Used in: CommunicationTemplateController - Client card manual sms sending
    public function getPreview(
        int   $smsTemplateId,
        Loan  $loan,
        array $varsCustom = []
    ): array {
        // get template
        try {
            $smsTemplate = $this->templateRepository->getById($smsTemplateId);

            if (!$smsTemplate) {
                throw new \RuntimeException('SmsService. Can not find template #' . $smsTemplateId);
            }
        } catch (Throwable $e) {
            throw new \RuntimeException(
                __('communication::smsTemplateCrud.smsTemplateNotFound'),
                $e
            );
        }

        $text = $this->getTextByTemplate(
            $smsTemplate,
            $loan->client,
            $loan,
            $varsCustom
        );


        return [
            'text' => $text,
            'template' => $smsTemplate,
        ];
    }

    // ============================= Sms Codes ================================

    private function getTextByTemplate(
        SmsTemplate $template,
        ?Client $client,
        ?Loan $loan = null,
        array $varsCustom = []
    ): string {

        if (empty($template->sms_template_id)) {
            throw new \RuntimeException(
                __('communication::smsTemplateCrud.smsTemplateNotFound')
            );
        }

        if ($template->key === SmsTemplateKeyEnum::SMS_TYPE_LOGIN_CODE->value) {
            if (!$client) {
                throw new RuntimeException('Client not specified');
            }
            //It is actually called profile_login_code and aken care of inside Doc/Client
            $varsCustom[PlaceholderEnum::PROFILE_LOGIN_CODE->value] = $this->generateRandomLoginCode(
                $client,
                $varsCustom
            );
        }

        $text = app(Template::class)->getFilledContentFromData(
            $template,
            $client,
            $loan,
            $varsCustom
        );

        return cyrillicToLatin($text);
    }

    private function generateRandomLoginCode(
        Client $client,
        array  $data
    ): string {

        $data['code'] = substr(
            str_shuffle(SmsTemplate::PERMITTED_CHARS),
            0,
            SmsTemplate::SMS_CODE_LENGTH
        );

        $this->saveSmsLoginCode(
            $client,
            $data
        );

        return $data['code'];
    }

    private function saveSmsLoginCode(
        Client $client,
        array  $data
    ): ?SmsLoginCode {
        return $this->smsLoginCodeRepository->save(
            (new SmsLoginCode())
                ->setAttribute('client_id', $client->getKey())
                ->setAttribute('phone_number', $client->phone)
                ->setAttribute('code', $data['code'])
                ->setAttribute('used', 0)
                ->setAttribute('ip_requested', $data['ip_requested'] ?? '')
                ->setAttribute('browser_requested', $data['browser_requested'] ?? '')
        );
    }
}
