<?php

declare(strict_types=1);

namespace Modules\Communication\Services;

use Exception;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Services\BaseService;

abstract class SendingMessageService extends BaseService
{
    /**
     * Client checks before sending
     */
    final protected function mandatoryChecks(Client $client, ?Loan $loan = null): void
    {
        if ($client->isBlocked()) {
            throw new \RuntimeException('Client is blocked');
        }

        if ($client->isDeleted() || $client->trashed()) {
            throw new \RuntimeException('Client is deleted');
        }

        $className = get_class($this);
        if ($client->isCompany()) {
            throw new Exception("{$className}. Cant send email to company");
        }

        if ($loan?->isJuridical()) {
            throw new Exception("{$className}. Loan is juridical");
        }
        // All checks passed, nothing to do
    }
}
