<?php

namespace Modules\Discounts\Services;

use Cache;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Administrator;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\StorageService;
use Modules\Discounts\Imports\BaseImport;
use Modules\Discounts\Repositories\ClientDiscountActualRepository;
use Modules\Sales\Services\SaleService;
use App\Exceptions\FrontEndExceptions;
use RuntimeException;

class DiscountService extends BaseService
{
    const CACHE_KEY_ADMINS = 'discount_admins';
    const CACHE_KEY_PRODUCTS = 'discount_products';

    private $repo = null;
    private StorageService $storageService;
    private SaleService $saleService;
    private string $manualDiscountRoute = 'head.clients.importClientsDiscountManual';
    private string $automaticDiscountRoute = 'head.clients.importClientsDiscount';

    /**
     * DiscountService constructor.
     *
     * @param StorageService $storageService
     * @param SaleService $saleService
     */
    public function __construct(
        StorageService $storageService,
        SaleService $saleService
    ) {
        $this->storageService = $storageService;
        $this->saleService = $saleService;
        parent::__construct();
    }

    /**
     * @param int $discountId
     *
     * @return mixed
     * @throws NotFoundException
     */
    public function getClientDiscountActualById(int $discountId)
    {
        $clientDiscountActual = $this->getRepo()->getById($discountId);

        if (!$clientDiscountActual) {
            throw new RuntimeException(
                __('discounts::clientDiscountActualCrud.clientDiscountActualNotFound')
            );
        }

        return $clientDiscountActual;
    }

    public function getClientDiscounts(int $clientId)
    {
        return $this->getRepo()->getAll($clientId);
    }

    /**
     * @param $discountId
     *
     * @throws NotFoundException
     * @throws Exception
     */
    public function removeDiscount($discountId): bool
    {
        $clientDiscountActual = $this->getClientDiscountActualById($discountId);
        $isAdded = $this->getRepo()->addClientDiscountHistory($clientDiscountActual);
        $clientId = (int)$clientDiscountActual->client_id;

        if (true === $isAdded) {
            $clientDiscountActual->delete();
        }

        // since discount is removed we should check do client has sale task for discount
        // and does he have another discount

        // if no sale tasks -> nothing to do
        $saleTasks = $this->saleService->getDiscountSaleTasks($clientId);
        if (is_null($saleTasks)) {
            return true;
        }

        // if no discounts, but there are sale tasks for discount -> we should remove them
        $discounts = $this->getClientDiscounts($clientId);
        if (empty($discounts)) {
            foreach ($saleTasks as $saleTask) {
                $this->saleService->deactivateSaleTask($saleTask);
            }
            return true;
        }

        // if there are discounts and there are sale task we should compare its discount percent,
        // to make sure that sale task offer the discount percent client really have
        $percents = [];
        foreach ($discounts as $discount) {
            $percents[] = $discount->percent;
        }
        if (!empty($percents)) {
            foreach ($saleTasks as $saleTask) {
                if (!in_array($saleTask->discount, $percents)) {
                    $this->saleService->deactivateSaleTask($saleTask);
                }
            }
        }

        return true;
    }

    public function massRemoveDiscount(array $discounts): bool
    {
        foreach ($discounts as $discount) {
            $this->removeDiscount($discount);
        }

        return true;
    }

    public function import(
        UploadedFile $file,
        Administrator $loggedAdministrator,
        bool $createSaleTask = false
    ): bool {

        try {
            // Save file and create log history
            $savedAndLogged = $this->storageService->importDiscounts($file);
            if (!$savedAndLogged) {
                throw new RuntimeException(
                    __('head::storageService.FileIsNotSaved')
                );
            }

            // Retrieving data from a file and converting it into an array
            $data = Excel::toArray(new BaseImport(), $file);
            // Get the first element of the array
            $data = reset($data);
            // Convert an array of data into an appropriate format
            [$parsedData, $clientIds, $productIds] = $this->getFileParsedData($data);


            // Move to history previous discounts of clients for certain products
            $this->logPreviousDiscounts($parsedData, $clientIds, $productIds);

            $this->checkAdministratorDiscountPercent(
                $loggedAdministrator,
                max(array_column($parsedData, 'percent')),
                // We choose only the biggest percent, no need to loop the array
                $this->automaticDiscountRoute
            );


            $chunks = array_chunk($parsedData, 200);
            foreach ($chunks as $chunk) {
                // Fill in an array for massive insert
                $this->getRepo()->bulkCreate($chunk);
            }


            // Create sale task if needed
            if ($createSaleTask) {
                $added = [];
                foreach ($parsedData as $row) {
                    if (isset($added[$row['client_id']])) {
                        continue;
                    }

                    $this->saleService->createSaleTaskWithDiscount(
                        $row['client_id'],
                        $row['percent'],
                        $productIds,
                        $row['valid_from'],
                        $row['valid_till']
                    );

                    $added[$row['client_id']] = $row['client_id'];
                }
            }

            return true;

        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::storageService.FileProblemImporting'),
                $e
            );
        }
    }

    public function addDiscount(
        Administrator $loggedAdministrator,
        int $clientId,
        int $percent,
        string $validFrom,
        string $validTo,
        array $productIds,
        bool $createSaleTask = false
    ): bool {

        $this->checkAdministratorDiscountPercent(
            $loggedAdministrator,
            $percent,
            $this->manualDiscountRoute
        );

        try {
            $data = [];
            $now = Carbon::now();

            foreach ($productIds as $productId) {
                $key = $this->getKey($clientId, $productId);

                $data[$key] = [
                    'client_id' => $clientId,
                    'product_id' => $productId,
                    'percent' => $percent,
                    'valid_from' => dbDate($validFrom, '00:00:00'),
                    'valid_till' => dbDate($validTo, '23:59:59'),
                    'created_at' => $now,
                    'created_by' => getAdminId(),
                ];
            }

            // Move to history previous discounts of clients for certain products
            $this->logPreviousDiscounts($data, [$clientId], $productIds);


            // Fill in an array for massive insert
            $this->getRepo()->bulkCreate($data);


            // Create sale task if needed
            if ($createSaleTask) {
                $this->saleService->createSaleTaskWithDiscount(
                    $clientId,
                    $percent,
                    $productIds,
                    dbDate($validFrom, '00:00:00'),
                    dbDate($validTo, '23:59:59'),
                    'manual_discount',
                    'Manually added discount'
                );
            }

            return true;

        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::storageService.FileProblemImporting'),
                $e
            );
        }
    }

    /**
     * Read line by line and prepare array of format:
     * [
     *     'client_id',
     *     'product_id',
     *     'percent',
     *     'valid_from',
     *     'valid_to',
     *     'created_at',
     *     'created_by',
     * ]
     *
     * @param array $rows
     *
     * @return array
     */
    private function getFileParsedData(array $rows): array
    {
        $data = [];
        $clientIds = [];
        $productIds = [];
        $now = Carbon::now();

        foreach ($rows as $el) {
            if (
                !is_numeric($el[0])
                || !is_numeric($el[1])
                || empty($el[2])
            ) {
                continue;
            }

            $validFrom = (Carbon::today())->addDays(intval($el[3]));
            $validTo = (Carbon::today())->addDays(intval($el[4]));

            $products = explode(",", $el[2]);

            foreach ($products as $productId) {
                $clientId = $el[0];
                $productId = trim($productId);
                $key = $this->getKey($clientId, $productId);

                $data[$key] = [
                    'client_id' => $clientId,
                    'product_id' => $productId,
                    'percent' => $el[1],
                    'valid_from' => dbDate($validFrom, '00:00:00'),
                    'valid_till' => dbDate($validTo, '23:59:59'),
                    'created_at' => $now,
                    'created_by' => getAdminId(),
                ];

                $clientIds[$clientId] = $clientId;
                $productIds[$productId] = $productId;
            }
        }

        return [$data, $clientIds, $productIds];
    }

    private function logPreviousDiscounts(
        array $parsedData,
        array $clientIds,
        array $productIds
    ): void {
        DB::beginTransaction();
        try {

            DB::statement("
                INSERT INTO client_discount_history (
                    client_discount_actual_id,
                    client_id,
                    product_id,
                    percent,
                    valid_from,
                    valid_till,
                    archived_at,
                    archived_by,
                    active,
                    deleted,
                    created_at,
                    created_by,
                    updated_at,
                    updated_by,
                    deleted_at,
                    deleted_by,
                    enabled_at,
                    enabled_by,
                    disabled_at,
                    disabled_by
                )
                SELECT
                    client_discount_actual_id,
                    client_id,
                    product_id,
                    percent,
                    valid_from,
                    valid_till,
                    NOW() AS archived_at,
                    1 AS archived_by,
                    active,
                    deleted,
                    created_at,
                    created_by,
                    updated_at,
                    updated_by,
                    deleted_at,
                    deleted_by,
                    enabled_at,
                    enabled_by,
                    disabled_at,
                    disabled_by
                FROM client_discount_actual
                WHERE
                    client_id IN (" . implode(',', $clientIds) . ")
                    AND product_id IN (" . implode(',', $productIds) . ");
            ");

            DB::statement("
                DELETE
                FROM client_discount_actual
                WHERE
                    client_id IN (" . implode(',', $clientIds) . ")
                    AND product_id IN (" . implode(',', $productIds) . ");
            ");

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();
            report($e);
        }

        // $discountsToRemove = $this->getRepo()->getDiscountsByClientIdsAndProducts(
        //     $clientIds,
        //     $productIds
        // );

        // foreach ($discountsToRemove as $clientDiscountActual) {
        //     $key = $this->getKey(
        //         $clientDiscountActual->client_id,
        //         $clientDiscountActual->product_id
        //     );

        //     // Check if client already has discount for certain product
        //     if (!isset($parsedData[$key])) {
        //         continue;
        //     }

        //     // Move to history
        //     $isAdded = $this->getRepo()->addClientDiscountHistory(
        //         $clientDiscountActual
        //     );

        //     // Delete actual records
        //     if (true === $isAdded) {
        //         $clientDiscountActual->forceDelete();
        //     }
        // }
    }

    /**
     * @return ClientDiscountActualRepository
     */
    private function getRepo(): ClientDiscountActualRepository
    {
        if (null === $this->repo) {
            $this->repo = app(ClientDiscountActualRepository::class);
        }

        return $this->repo;
    }

    private function getKey(int $clientId, int $productId): string
    {
        return $clientId . '_' . $productId;
    }

    /**
     * @return array
     */
    public function getDiscountAdmins(): array
    {
        return Cache::remember(self::CACHE_KEY_ADMINS,60*60,function(){
            return $this->getRepo()->getDiscountAdmins();
        });
    }

    public function clearCacheDiscountAdmins()
    {
        Cache::forget(self::CACHE_KEY_ADMINS);
    }

    public function clearCacheDiscountProducts()
    {
        Cache::forget(self::CACHE_KEY_PRODUCTS);
    }

    private function checkAdministratorDiscountPercent(
        Administrator $loggedAdministrator,
        int $percent,
        string $route
    ) {
        $adminDiscountPercent = $loggedAdministrator->getAdminDiscount();

        if ($adminDiscountPercent < $percent) {
            throw new FrontEndExceptions(__('admin::adminCrud.adminAccessDenied'));
        }
    }
}
