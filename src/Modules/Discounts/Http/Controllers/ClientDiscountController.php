<?php

namespace Modules\Discounts\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\View\View;
use Modules\Admin\Services\AdministratorService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Services\SessionService;
use Modules\Discounts\Http\Requests\ClientDiscountSearchRequest;
use Modules\Discounts\Http\Requests\ManualDiscountRequest;
use Modules\Discounts\Http\Requests\MassDeleteDiscountRequest;
use Modules\Discounts\Services\DiscountService;
use Modules\Head\Http\Requests\ClientCommunicationSearchRequest;
use Modules\Head\Services\ClientService;
use Modules\Product\Services\ProductService;
use Throwable;

final class ClientDiscountController extends BaseController
{
    protected ?SessionService $sessionService;
    protected AdministratorService $adminService;
    protected DiscountService $discountService;
    protected ClientService $clientService;
    protected ProductService $productService;

    public string $pageTitle = 'Client with discounts';
    public string $indexDiscountsRoute = 'head.discountsClients.list';

    public function __construct(
        ClientService $clientService,
        DiscountService $discountService,
        SessionService $sessionService,
        AdministratorService $adminService,
        ProductService $productService
    ) {
        $this->clientService = $clientService;
        $this->discountService = $discountService;
        $this->sessionService = $sessionService;
        $this->adminService = $adminService;
        $this->productService = $productService;
        parent::__construct();
    }

    public function getClientsWithDiscount(): View
    {
        session()->put($this->cacheKey, []);

        return view(
            'discounts::client.list',
            [
                'admins' => $this->getAdmins(),
                'clients' => $this->getDiscountClientsTableData(),
                'products' => $this->getProducts(),
                'cacheKey' => $this->getDiscountsListCacheKey(),
            ]
        );
    }

    /**
     * @param int $clientDiscountActualId
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function removeDiscount(int $clientDiscountActualId): JsonResponse
    {
        $this->discountService->removeDiscount($clientDiscountActualId);

        return Response::json(
            [
                'message' => __('discounts::clientDiscountActualCrud.discountRemovedSuccessfully'),
            ]
        );
    }

    /**
     * @param MassDeleteDiscountRequest $request
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function removeDiscounts(MassDeleteDiscountRequest $request): JsonResponse
    {
        $data = $request->validated();

        $this->discountService->massRemoveDiscount($data['discount_ids']);

        return Response::json(
            [
                'message' => __('discounts::clientDiscountActualCrud.discountsRemovedSuccessfully'),
            ]
        );
    }

    public function refreshDiscounts(ClientDiscountSearchRequest $request): string
    {
        parent::setFiltersFromRequest($request);

        return view(
            'discounts::client.discount-clients-list-table',
            [
                'clients' => $this->getDiscountClientsTableData(),
            ]
        )->render();
    }

    public function getDiscountClientsTableData(?int $limit = null): LengthAwarePaginator
    {
        return $this->clientService->getClientsWithDiscount(
            $limit ?? parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    public function cleanDiscountsFilters(): true
    {
        session()->forget($this->getDiscountsListCacheKey());

        return true;
    }

    /**
     * @param ClientDiscountSearchRequest $request
     *
     * @return bool|RedirectResponse
     *
     * @throws Exception
     */
    public function setDiscountsFilters(ClientDiscountSearchRequest $request)
    {
        return parent::setFiltersFromRequest(
            $request,
            $this->getDiscountsListCacheKey()
        );
    }

    /**
     * @return mixed
     *
     * @throws Exception
     */
    public function getDiscountsFilters()
    {
        return parent::getFiltersSpecifyCacheKey($this->getDiscountsListCacheKey());
    }

    /**
     * @return string
     */
    private function getDiscountsListCacheKey(): string
    {
        return $this->cacheKey;
    }

    public function importClientsDiscount(Request $request): RedirectResponse
    {
        $file = $request->import_file;
        if (empty($file)) {
            return redirect()
                ->route($this->indexDiscountsRoute)
                ->with('fail', __('discounts::clientDiscountActualCrud.pleaseSelectFile'));
        }

        $dataRequest = $request->all();
        $createSaleTasks = (
            isset($dataRequest['create_sale_task'])
            && 1 == $dataRequest['create_sale_task']
        );

        $successfulImport = $this->discountService->import(
            $file,
            getAdmin(),
            $createSaleTasks
        );
        if (!$successfulImport) {
            return redirect()
                ->route($this->indexDiscountsRoute)
                ->with(
                    'fail',
                    __('discounts::clientDiscountActualCrud.unsuccessfullyImportedDiscounts')
                );
        }

        $this->discountService->clearCacheDiscountAdmins();
        $this->discountService->clearCacheDiscountProducts();

        return redirect()
            ->route($this->indexDiscountsRoute)
            ->with(
                'successfullyImported',
                __('discounts::clientDiscountActualCrud.successfullyImportedDiscounts')
            );
    }

    public function importClientsDiscountManual(ManualDiscountRequest $request): RedirectResponse
    {
        $discountData = $request->validated();

        preg_match(
            "/\#([1-9][0-9]{0,7})[ ]/",
            $discountData['client_id'],
            $match
        );
        $clientId = (isset($match[1]) ? intval($match[1]) : null);
        if (empty($clientId)) {
            throw new Exception("Wrong client");
        }

        preg_match(
            "/([0-9]{2}\-[0-9]{2}\-[0-9]{4})[\s]\-[\s]([0-9]{2}\-[0-9]{2}\-[0-9]{4})/",
            $discountData['validPeriod'],
            $match
        );

        $from = (Carbon::parse($match[1]))->format('Y-m-d');
        $to = (Carbon::parse($match[2]))->format('Y-m-d');

        $added = $this->discountService->addDiscount(
            getAdmin(),
            $clientId,
            intval($discountData['discountPercent']),
            $from,
            $to,
            $discountData['discountProduct'],
            (1 == $discountData['create_sale_task'])
        );

        /// if client come from client-card redirect back to client card
        $referer = $request->headers->get('referer');
        if (str_contains($referer, 'clientCard')) {
            if (!$added) {
                return redirect($referer)->with(
                    'fail',
                    __('discounts::clientDiscountActualCrud.unsuccessfullyAddedDiscounts')
                );
            }

            $this->discountService->clearCacheDiscountAdmins();
            $this->discountService->clearCacheDiscountProducts();

            return redirect($referer)->with(
                'successfullyImported',
                __('discounts::clientDiscountActualCrud.successfullyCreateDiscounts')
            );
        }

        /// default logic
        if (!$added) {
            return to_route($this->indexDiscountsRoute)->with(
                'fail',
                __('discounts::clientDiscountActualCrud.unsuccessfullyAddedDiscounts')
            );
        }

        $this->discountService->clearCacheDiscountAdmins();
        $this->discountService->clearCacheDiscountProducts();

        return to_route($this->indexDiscountsRoute)->with(
            'successfullyImported',
            __('discounts::clientDiscountActualCrud.successfullyImportedDiscounts')
        );
    }

    /**
     * @param ClientCommunicationSearchRequest $request
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function search(Request $request): JsonResponse
    {
        if (empty($request['query'])) {
            return Response::json([]);
        }

        $res = $this->clientService->getClientsBySearchStringLite($request['query']);

        return Response::json($res);
    }

    private function getAdmins()
    {
        // first check only admins from discounts
        $admins = $this->discountService->getDiscountAdmins();
        if (!empty($admins)) {
            return $admins;
        }

        // else by default load all admins
        $admins = $this->adminService->getActiveAdmins();
        if (!empty($admins)) {
            return $admins;
        }

        return [];
    }

    private function getProducts(): array
    {
        $admin = getAdmin();
        $products = $admin->getAdminProducts();
        if (!empty($products)) {
            return $products;
        }

        // else by default load all products
        return [];
    }
}
