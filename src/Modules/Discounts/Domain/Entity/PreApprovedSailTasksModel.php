<?php

namespace Modules\Discounts\Domain\Entity;

use Illuminate\Support\Collection;
use Modules\Common\Models\Client;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;
use Modules\Discounts\Models\PreApprovedClient;
use Modules\Discounts\Repositories\PreApprovedClientRepository;
use Modules\Sales\Repositories\SaleTaskRepository;

class PreApprovedSailTasksModel
{
    public function __construct(
        private readonly PreApprovedClientRepository $preApprovedClientRepository,
        private readonly SaleTaskRepository          $saleTaskRepository,
        private Collection                           $preApprovedClients,
        private ?SaleTask                            $saleTask = null,
    )
    {
    }

    public function build(): Collection
    {
        return $this->setRows()->save();
    }


    public function save(): Collection
    {
        $processedRows = collect([]);

        $this->preApprovedClients->each(function (PreApprovedClient $preApprovedClient, int $index) use ($processedRows) {
            $this->saleTask = app(SaleTask::class);

            /**
             * @var Client $client
             */
            $client = $preApprovedClient->client;

            $this->saleTask->setAttribute('sale_task_type_id', SaleTaskType::SALE_TASK_TYPE_ID_PRE_APPROVED);
            $this->saleTask->setAttribute('client_id', $client->getKey());
            $this->saleTask->setAttribute('pin', $client->pin);
            $this->saleTask->setAttribute('client_full_name', $client->getFullName());
            $this->saleTask->setAttribute('phone', $client->phone);
            $this->saleTask->setAttribute('email', $client->email);
            $this->saleTask->setAttribute('office_id', $client->loans()->first()->office_id);
            $this->saleTask->setAttribute('valid_from', $preApprovedClient->from_date);
            $this->saleTask->setAttribute('valid_till', $preApprovedClient->to_date);
            $this->saleTask->setAttribute('amount', $preApprovedClient->amount);
            $this->saleTask->setAttribute('status', 'new');
            $this->saleTask->setAttribute('show_after', date('Y-m-d H:i:s'));

            $saleTask = $this->saleTaskRepository->save($this->saleTask);
            if ($saleTask) {
                $preApprovedClient->setAttribute('sale_task_created', 'yes');
                $preApprovedClient->setAttribute('sale_task_id', $saleTask->getKey());

                $preApprovedClient->save();

                $processedRows->put($index, [
                    'sale_task_id' => $saleTask->getKey(),
                    'pre_approved_id' => $preApprovedClient->getKey(),

                ]);
            }
        });

        return $processedRows;
    }

    /**
     * @throws \Exception
     */
    private function setRows(): self
    {
        $this->preApprovedClients = $this->preApprovedClientRepository->getRowsByConditions([
            'sale_task_created' => 'no'
        ]);

        return $this;
    }
}
