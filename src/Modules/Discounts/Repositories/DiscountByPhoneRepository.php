<?php

declare(strict_types=1);

namespace Modules\Discounts\Repositories;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Common\ModelFilters\Base\CreatedAtFilter;
use Modules\Common\ModelFilters\Base\PhoneFilter;
use Modules\Common\ModelFilters\Base\ProductIdFilter;
use Modules\Common\Repositories\BaseRepository;
use Modules\Discounts\Filters\DiscountByPhone\CreatedByIdsFilter;
use Modules\Discounts\Filters\DiscountByPhone\FromToDiscountFilter;
use Modules\Discounts\Filters\DiscountByPhone\ValidFromFilter;
use Modules\Discounts\Filters\DiscountByPhone\ValidUntilFilter;
use Modules\Discounts\Models\DiscountByPhone;

final class DiscountByPhoneRepository extends BaseRepository
{
    public function getPaginator(
        array $filters = [],
        int $perPage = 10
    ): LengthAwarePaginator {
        return $this->builder($filters)->paginate($perPage);
    }

    private function builder(array $filters = []): Builder
    {
        return DiscountByPhone::query()->with([
            'creator:administrator_id,first_name,middle_name,last_name',
            'product:product_id,name',
        ])->orderByDesc('created_at')->filterByWithMap($filters, [
            'phone' => PhoneFilter::class,
            'product_ids' => ProductIdFilter::class,
            'created_at' => CreatedAtFilter::class,
            'created_by' => CreatedByIdsFilter::class,
            'discount' => FromToDiscountFilter::class,
            'valid_from' => ValidFromFilter::class,
            'valid_until' => ValidUntilFilter::class,
        ]);
    }

    public function getBuilder(array $filters = []): Builder
    {
        return $this->builder($filters);
    }

    // returns an array in format:
    // [
    //   'product_id' => [
    //      'discount' => xxx,
    //      'from' => xxx,
    //      'till' => xxx,
    //   ]
    // ]
    public function getPercentsByPhoneAndProducts(
        string $phone,
        array $productIds
    ): array {
        $rows = DiscountByPhone::where('phone', $phone)
            ->whereIn('product_id', $productIds)
            ->where('valid_from', '<=', now())
            ->where('valid_until', '>=', now())
            ->get();

        if ($rows->count() < 1) {
            return [];
        }

        $res = [];
        foreach ($rows as $row) {
            // first time add
            if (!isset($res[$row->product_id])) {
                $res[$row->product_id] = [
                    'discount' => $row->discount,
                    'from' => $row->valid_from,
                    'till' => $row->valid_until,
                ];
                continue;
            }

            // if we have another offer for same product and better discount -> use it
            if ($res[$row->product_id]['discount'] < $row->discount) {
                $res[$row->product_id] = [
                    'discount' => $row->discount,
                    'from' => $row->valid_from,
                    'till' => $row->valid_until,
                ];
            }
        }

        return $res;
    }

    public function getPercentByPhoneAndProduct(string $phone, int $productId): ?int
    {
        return DiscountByPhone::where(['phone' => $phone, 'product_id' => $productId])
            ->where('valid_from', '<=', now())
            ->where('valid_until', '>=', now())
            ->orderBy('discount', 'desc')
            ->value('discount');
    }

    public function getByPhoneAndProduct(
        string $phone,
        int $productId,
        array $columns = ['*'],
        ?CarbonInterface $validOn = null
    ): ?DiscountByPhone {
        return DiscountByPhone::where(['phone' => $phone, 'product_id' => $productId])
            ->where('valid_from', '<=', $validOn ?? now())
            ->where('valid_until', '>=', $validOn ?? now())
            ->orderBy('discount', 'desc')
            ->first($columns);
    }

    public function getByPhoneAndProductWithHistory(
        string $phone,
        int $productId,
        array $columns = ['*'],
        ?CarbonInterface $validOn = null
    ): ?DiscountByPhone {
        return DiscountByPhone::fromRaw(<<<SQL
(
    select id, phone, discount, valid_from, valid_until, product_id, created_by, created_at, updated_at
    from discounts_by_phones
    union
    select id, phone, discount, valid_from, valid_until, product_id, created_by, created_at, updated_at
    from discounts_by_phones_history
) as discounts_by_phones
SQL
        )
            ->where(['phone' => $phone, 'product_id' => $productId])
            ->where('valid_from', '<=', $validOn ?? now())
            ->where('valid_until', '>=', $validOn ?? now())
            ->orderBy('discount', 'desc')
            ->first($columns);
    }
}
