<?php

namespace Modules\Discounts\Application\Actions\InterestFreeOffer;

use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Client;
use Modules\Discounts\Application\Dto\InterestFreeOfferRequestDto;
use Modules\Discounts\Models\InterestFreeOffer;
use Modules\Sales\Services\SaleTaskService;
use RuntimeException;

final readonly class CreateInterestFreeOfferAction
{
    public function __construct(
        private SaleTaskService $saleTaskService,
    ) {
    }

    public function execute(InterestFreeOfferRequestDto $dto): void
    {
        $clientIds = $this->getClientIds($dto);

        if (!$clientIds) {
            throw new RuntimeException('No clients to create offer');
        }

        DB::transaction(function () use ($clientIds, $dto) {
            foreach ($clientIds as $clientId) {
                $interestFreeOffer = new InterestFreeOffer();

                $interestFreeOffer->client_id = $clientId;
                $interestFreeOffer->from_date = $dto->dateFrom;
                $interestFreeOffer->to_date = $dto->dateTo;
                $interestFreeOffer->product_ids = $dto->productIds;

                if (!$interestFreeOffer->save()) {
                    throw new RuntimeException('Failed to save interest free offer(s)');
                }

                $this->createSaleTask($interestFreeOffer);
            }
        });
    }

    private function getClientIds(InterestFreeOfferRequestDto $dto): array
    {
        if ($dto->clientIds) {
            return $dto->clientIds;
        }

        if ($dto->clientsToImport) {
            return $this->getClientIdsFromImport($dto->clientsToImport);
        }

        return [];
    }

    private function getClientIdsFromImport(array $rows): array
    {
        $ids = [];

        foreach ($rows as $row) {
            if (!isset($ids[$row[0]])) {
                $client = Client::where('pin', $row[0])->first(['client_id']);
                if ($client) {
                    $ids[$row[0]] = $client->getKey();
                }
            }
        }

        return array_values($ids);
    }

    private function createSaleTask(InterestFreeOffer $offer): void
    {
        $client = $offer->client()->first(
            ['client_id', 'first_name', 'last_name', 'middle_name', 'email', 'phone', 'pin']
        );

        if (!$client instanceof Client) {
            throw new RuntimeException('Client not found');
        }

        $this->saleTaskService->createWithDiscountByProducts(
            $client,
            $offer->product_ids,
            100,
            'interest_free_offers#' . $offer->getKey(),
            $offer->from_date,
            $offer->to_date->endOfDay(),
        );
    }
}
