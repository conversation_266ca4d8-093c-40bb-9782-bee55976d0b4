<?php

namespace Modules\Approve\Domain\Entities\Loan;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Approve\Domain\Entities\Admin;
use Modules\Approve\Domain\Entities\AdminOffice;
use Modules\Approve\Domain\Entities\Office;
use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Approve\Domain\Exceptions\ApproveDecisionDoesntMatchStatus;
use Modules\Approve\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Approve\Domain\Exceptions\LoanNotSaved;
use Modules\Approve\Domain\Exceptions\RefinanceActualizedAmountHigherThanApprovedAmount;
use Modules\Approve\Domain\Exceptions\RefinanceActualizedAmountToSmall;
use Modules\Approve\Domain\Exceptions\RefinanceRollbackedAmountIsWrong;
use Modules\Approve\Domain\Exceptions\RefinanceSnapshotMissing;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Entities\RefinanceSnapshot;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office as DbOffice;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Head\Repositories\LoanRepository as Repo;

class LoanForApproval extends DecisionLoan
{
    public function __construct(
        protected DbModel $dbModel,
        protected Repo $repo,
        protected CurrentDate $currentDate,
        protected Office $office,
        protected Admin $admin,
        protected AdminOffice $adminOffice,
        protected DecisionAttempt $decisionAttempt,
        protected Installments $installments
    ) {
        parent::__construct(
            $dbModel,
            $repo,
            $currentDate,
            $office,
            $admin,
            $adminOffice,
            $decisionAttempt,
            $installments
        );
    }

    // Used in:
    // - ActualizeRefinanceAmountsListener - when loan A start refinance process to repay loan B, but loan receive payment,
    // so we need to increase payout amount
    // - IncomingPayment::validateAmount() - when refinance is started, but ref.payment task is handled on next days
    // and there are some new accruals, so we need to rollback/decrease loan to state when refinance is started
    public function reActualizeRefinance(?int $paymentId = null)
    {
        $this->actualizeRefinaceLoanRows(true);

        if (!empty($paymentId)) {
            $this->actualizePaymentAndPaymentTask($paymentId);
        }

        return $this->saveSimple();
    }

    public function process(DecisionDto $dto): static
    {
        return $this
            ->setOffice()
            ->setAdmin($dto->admin_id)
            ->setAdminOffice($dto->admin_id, $this->dbModel->office_id)
            ->setDecisionAttempt($dto)
            ->setFinalPeriod()
            ->setStatus()
            ->setStatusUpdateDate()
            ->actualizeRefinaceLoanRows()
            ->setInstallments()
            ->save();
    }

    protected function setStatus(): static
    {
        $currentAdminId = getAdminId();
        $statuses = [LoanStatus::STATUS_PROCESSING];
        if (!in_array($this->dbModel->getStatus(), $statuses)) {
            throw new LoanHasIncorrectStatus(
                $this->dbModel->getKey(),
                $this->dbModel->getStatus(),
                LoanStatus::STATUS_APPROVED
            );
        }

        $decision = $this->decisionAttempt->dbModel()->getDecisionName();
        if ($decision !== ApproveDecision::APPROVE_DECISION_APPROVED){
            throw new ApproveDecisionDoesntMatchStatus($decision, LoanStatus::STATUS_APPROVED);
        }

        $this->dbModel->loan_status_id = LoanStatus::APPROVED_STATUS_ID;

        return $this;
    }

    protected function setStatusUpdateDate(): self
    {
        $this->dbModel->last_status_update_date = $this->currentDate->now();

        return $this;
    }

    /**
     * Here we cover 4 variants:
     * 1) Refinance in same day - no changes on refinanced amounts => normal flow
     * 2) Refinance in same day - we have changes on refinanced amounts => if amount is lower: increase amount_rest, update loan_refinance.amount, if higher: rollback to snapshot
     * 3) Refinance not in same day - no changes on refinanced amounts => normal flow
     * 4) Refinance not in same day - we have changes on refinanced amounts => if amount is lower: increase amount_rest, update loan_refinance.amount, if higher: rollback to snapshot
     *
     * @param  bool $actualizeInPayment - on approve it false, since no payments created,
     *                                    but true on received payment during open refinance process
     * @return self
     */
    protected function actualizeRefinaceLoanRows(bool $actualizeInPayment = false): self
    {
        $refLoans = $this->dbModel->getRefinancedLoans();
        if (empty($refLoans) || $refLoans->count() < 1) {
            return $this;
        }

        $isForOnline = (DbOffice::OFFICE_ID_WEB == $this->dbModel->office_id);

        $amountRest = $this->dbModel->getAmountToPaid();
        $meta = [
            'loan_amount_approved' => $amountRest,
            'old_loan_amount_rest' => $this->dbModel->amount_rest,
        ];

        // we need to check does refinanced loan amounts has been changed
        foreach ($refLoans as $refLoan) {

            $amountForRefinancing = $refLoan->getAmountForRefinance($isForOnline, true);

            $refLoanRelObj = DbModel::getLoanRefinanceObj(
                $this->dbModel->loan_id,
                $refLoan->loan_id
            );

            // here we keep stats on refinanced_loan level
            $metaRefLoan = [];

            // 1) if no changes -> skip, nothing todo
            if ($refLoanRelObj->refinanced_due_amount == $amountForRefinancing) {

                // actualizing of loan.amount_rest
                if ($amountForRefinancing > 0) {
                    $amountRest = $amountRest - $amountForRefinancing;
                }

                $metaRefLoan[] = 'final: no changes, amount_for_refinance = ' . $amountForRefinancing;

                $meta[$refLoan->loan_id] = $metaRefLoan;
                continue;
            }


            $metaRefLoan[] = 'old_amount_for_refinance = ' . $refLoanRelObj->refinanced_due_amount;
            $metaRefLoan[] = 'new_amount_for_refinance = ' . $amountForRefinancing;

            $oldRefAmount = $refLoanRelObj->refinanced_due_amount;

            // 2) if new amount become lower, means something were paid, before refinance process is finished
            if ($amountForRefinancing < $oldRefAmount) {

                // actualizing of loan.amount_rest
                if ($amountForRefinancing > 0) {
                    $amountRest = $amountRest - $amountForRefinancing;
                }

                // actualizing of loan_refinance.refinanced_due_amount
                $refLoanRelObj->refinanced_due_amount = $amountForRefinancing;

                // if new refinanced amount is zero, means loan is repaid, so we mark it as deleted
                if ($amountForRefinancing == 0) {
                    $refLoanRelObj->active = 0;
                    $refLoanRelObj->deleted = 1;
                    $refLoanRelObj->deleted_at = now();
                    $refLoanRelObj->deleted_by = getAdminId();

                    $metaRefLoan[] = 'loan_refinance relation marked as deleted';
                }

                $refLoanRelObj->save();

                if ($actualizeInPayment && $this->dbModel->isApproved()) {
                    // search for IN payment with source = loan_refinance
                    $inPayment = Payment::where('loan_id', $refLoanRelObj->refinanced_loan_id)
                        ->where('direction', 'in')
                        ->where('amount', $oldRefAmount)
                        ->where('source', PaymentSourceEnum::LOAN_REFINANCE)
                        ->where('status', PaymentStatusEnum::NEW)
                        ->first();

                    if (empty($inPayment->payment_id)) {
                        throw new \Exception('No refinance payment prepared');
                    }

                    // actualizing of prepared payment with source = loan_refinance
                    $inPayment->amount = $amountForRefinancing;
                    if ($amountForRefinancing == 0) {
                        $inPayment->status = PaymentStatusEnum::CANCELED;
                        $refLoanRelObj->deleted_at = now();
                        $refLoanRelObj->deleted_by = getAdminId();
                    }

                    $inPayment->save();
                }

                $metaRefLoan[] = 'final: decrease amount_for_refinance';

                $meta[$refLoan->loan_id] = $metaRefLoan;

                continue;
            }

            // 3) if new amount is higher than it was, means we need to do rollback to snapshot, when refinance process is started

            // get snapshot
            $snapshot = RefinanceSnapshot::where('refinancing_loan_id', $this->dbModel->loan_id)
                ->where('refinanced_loan_id', $refLoan->loan_id)
                ->whereNull('reverted_at')
                ->orderBy('id', 'DESC')
                ->first();

            if (empty($snapshot->id)) {
                throw new RefinanceSnapshotMissing();
            }

            // get snapshot properties and validate them
            $loanSnapshot = $snapshot->loan;
            $taxesSnapshot = $snapshot->taxes;
            $loanStatsSnapshot = $snapshot->loan_stats;
            $clientStatsSnapshot = $snapshot->client_stats;
            $installmentsSnapshot = $snapshot->installments;
            if (empty($loanSnapshot) || empty($loanStatsSnapshot) || empty($clientStatsSnapshot) || empty($installmentsSnapshot)) {
                throw new RefinanceSnapshotMissing();
            }

            // get refianced loan main properties to be changed
            $taxes = $refLoan->getAllTaxes();
            $client = $refLoan->client;
            $loanStats = $refLoan->loanActualStats;
            $clientStats = $client->clientActualStats;
            $installments = $refLoan->getAllInstallments();

            // set snapshot usage
            $snapshot->reverted_at = now();
            $snapshot->reverted_by = getAdminId();
            $snapshot->save();


            // return objects to prev.state
            if (isset($loanSnapshot['created_at'])) {
                unset($loanSnapshot['created_at']);
            }
            $refLoan->fill($loanSnapshot);
            $refLoan->saveQuietly();

            $loanStats->fill($loanStatsSnapshot);
            $loanStats->saveQuietly();

            $clientStats->fill($clientStatsSnapshot);
            $clientStats->saveQuietly();

            foreach ($installments as $inst) {
                $instData = $installmentsSnapshot[$inst->installment_id];
                $instData['due_date'] = Carbon::parse($instData['due_date'])->format('Y-m-d'); // костъль, because of: 'due_date' => 'date:d-m-Y',

                $inst->fill($instData);
                $inst->saveQuietly();
            }

            if ($taxes->count() > 0) {
                foreach ($taxes as $tax) {
                    if (isset($taxesSnapshot[$tax->tax_id])) {
                        $taxData = $taxesSnapshot[$tax->tax_id];
                        $tax->fill($taxData);
                        $tax->saveQuietly();
                    } else {
                        DB::statement('DELETE FROM tax_history WHERE tax_id = ' . $tax->tax_id);
                        DB::statement('DELETE FROM tax WHERE tax_id = ' . $tax->tax_id);
                    }
                }
            }

            $amountForRefinancingRollbacked = $refLoan->getAmountForRefinance($isForOnline, true);
            if ($amountForRefinancingRollbacked != $refLoanRelObj->refinanced_due_amount) {
                throw new RefinanceRollbackedAmountIsWrong($refLoanRelObj->refinanced_due_amount, $amountForRefinancingRollbacked);
            }

            // actualizing of loan.amount_rest
            if ($amountForRefinancing > 0) {
                $amountRest = $amountRest - $amountForRefinancingRollbacked;
            }

            $metaRefLoan[] = 'final: rollbacked to ref.snapshot #' . $snapshot->id;
            $meta[$refLoan->loan_id] = $metaRefLoan;
        }

        // actualize loan.amount_rest
        if ($this->dbModel->amount_rest != $amountRest) {
            $this->dbModel->amount_rest = $amountRest;
        }

        // check for incremented max possible amount
        if ($this->dbModel->amount_rest > $this->dbModel->getAmountToPaid()) {
            throw new RefinanceRollbackedAmountIsWrong($this->dbModel->amount_rest, $this->dbModel->getAmountToPaid());
        }

        if ($this->dbModel->amount_rest < 0) {
            throw new RefinanceActualizedAmountToSmall();
        }

        // save changes on rest_amount into loan_meta
        $meta['new_loan_amount_rest'] = $this->dbModel->amount_rest;
        $this->dbModel->addMeta('refinance_recalc_approve_' . time(), json_encode($meta));


        return $this;
    }

    protected function actualizePaymentAndPaymentTask(int $inPaymentId): self
    {
        if (!$this->dbModel->isApproved()) {
            return $this;
        }

        $outPayment = Payment::where('loan_id', $this->dbModel->loan_id)
            ->where('direction', 'out')
            ->where('status', PaymentStatusEnum::NEW)
            ->first();

        if (empty($outPayment->payment_id)) {
            throw new \Exception('Out payment not found, loan #' . $this->dbModel->loan_id);
        }

        $outPaymentTask = PaymentTask::where('payment_id', $outPayment->payment_id)
            ->where('status', '!=', 'done')
            ->first();

        if (empty($outPaymentTask->payment_task_id)) {
            throw new \Exception(
                'Out payment task not found, loan #' . $this->dbModel->loan_id
                . '(payment ' . $outPayment->payment_id . ')'
            );
        }

        $this->dbModel->addMeta(
            'refinance_recalc_in_payment_' . $inPaymentId,
            json_encode([
                'payment_id' => $outPayment->payment_id,
                'old_amount' => $outPayment->amount,
                'new_amount' => $this->dbModel->amount_rest,
            ])
        );

        $outPayment->amount = $this->dbModel->amount_rest;
        $outPayment->save();

        $outPaymentTask->amount = $this->dbModel->amount_rest;
        $outPaymentTask->save();

        return $this;
    }

    protected function setInstallments(): self
    {
        $this->installments->buildFromExisting($this);
        return $this;
    }

    protected function setFinalPeriod(): self
    {
        $this->dbModel->period_final = $this->dbModel->period_approved;
        return $this;
    }

    protected function save(): static
    {
        $this->dbModel->installments_approved = $this->installments->count();
        if(! $this->repo->save($this->dbModel)){
            throw new LoanNotSaved();
        }

        // save meta
        // update loan & client stats
        // send approve docs
        // create payment -> CreatePaymentsFromLoanApprovalListener
        LoanWasApproved::dispatch($this);

        return $this;
    }

    protected function saveSimple(): self
    {
        if (! $this->repo->save($this->dbModel)) {
            throw new LoanNotSaved();
        }

        return $this;
    }
}
