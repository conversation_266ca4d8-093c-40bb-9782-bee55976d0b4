<?php

namespace Modules\Approve\Domain\Entities\Loan;

use Illuminate\Support\Facades\DB;
use Modules\Approve\Domain\Entities\Admin;
use Modules\Approve\Domain\Entities\AdminOffice;
use Modules\Approve\Domain\Entities\Office;
use Modules\Approve\Domain\Exceptions\LoanNotFound;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\AggregateRootInterface;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Domain\InstallmentsInterface;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\BlockReason;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\LoanRepository as Repo;
use Modules\Head\Services\ClientService;

abstract class DecisionLoan extends DomainModel implements DecisionLoanInterface, AggregateRootInterface
{
    const MAX_CANCEL_ATTEMPTS_NO_INCOME = 3;

    public function __construct(
        protected DbModel         $dbModel,
        protected Repo            $repo,
        protected CurrentDate     $currentDate,
        protected Office          $office,
        protected Admin           $admin,
        protected AdminOffice     $adminOffice,
        protected DecisionAttempt $decisionAttempt,
        protected Installments    $installments
    ) {}

    public static function loadSelf(int $id): static
    {
        return app()
            ->make(static::class)
            ->loadById($id);
    }

    public function loadById(int $id): static
    {
        $existing = $this->repo->getById($id);
        if (!$existing) {
            throw new LoanNotFound($id);
        }

        return $this->buildFromExisting($existing);
    }

    public function buildFromExisting(DbModel $dbModel): static
    {
        if (!$dbModel->exists) {
            throw new LoanNotFound(0);
        }
        $this->dbModel = $dbModel;
        return $this;
    }

    protected function checkLoanStatus(): self
    {
        // if ($this->dbModel->loan_status_id === LoanStatus::ACTIVE_STATUS_ID) {
        //     throw new \Exception('Error you cant cancel active loan.');
        // }

        return $this;
    }

    protected function setOffice(): static
    {
        $this->office->loadById($this->dbModel->office_id);
        return $this;
    }

    protected function setAdmin(int $adminId): static
    {
        $this->admin->loadById($adminId)
            ->checkPermissionsForLoanDecision(
                $this->dbModel->administrator_id,
                $this->dbModel->amount_approved,
                $this->dbModel->getStatus() === LoanStatus::STATUS_APPROVED
            );
        $this->dbModel->last_status_update_administrator_id = $adminId;
        $this->dbModel->administrator_id = null;
        return $this;
    }

    protected function setAdminOffice(int $adminId, int $officeId): static
    {
        $this->adminOffice->loadByIds($adminId, $officeId)
            ->checkApprovalPermissionsForOffice($this->office->dbModel());
        return $this;
    }

    protected function setDecisionAttempt(DecisionDto $dto): static
    {
        $this->decisionAttempt->build($this->dbModel, $dto);
        return $this;
    }

    // task: https://trello.com/c/lvG8bIIn
    protected function blockClient(): static
    {
        $loan = $this->dbModel();
        if (empty($loan->client_id)) {
            return $this;
        }

        $attempt = $this->decisionAttempt->dbModel();
        if (
            empty($attempt->approve_decision_id)
            || empty($attempt->approve_decision_reason_id)
        ) {
            return $this;
        }

        if (
            $attempt->approve_decision_id != ApproveDecision::APPROVE_DECISION_ID_CANCELED
            || $attempt->approve_decision_reason_id != ApproveDecisionReason::APPROVE_DECISION_REASON_ID_NO_INCOME
        ) {
            return $this;
        }


        // get canceled counts with NO INCOME for a client
       $matches = DB::table('approve_attempt as aa')
            ->join('loan as l', 'aa.loan_id', '=', 'l.loan_id')
            ->where('l.client_id', $loan->client_id)
            ->orderByDesc('aa.approve_attempt_id')
            ->limit(self::MAX_CANCEL_ATTEMPTS_NO_INCOME)
            ->get(['aa.approve_decision_id', 'aa.approve_decision_reason_id']);

        $allMatch = $matches->count() === self::MAX_CANCEL_ATTEMPTS_NO_INCOME && $matches->every(function ($row) {
            return $row->approve_decision_id === ApproveDecision::APPROVE_DECISION_ID_CANCELED
                && $row->approve_decision_reason_id === ApproveDecisionReason::APPROVE_DECISION_REASON_ID_NO_INCOME;
        });

        // block client if we have 3 last attemts - NO INCOME
        if ($allMatch) {

            $blReason = BlockReason::where('name', BlockReason::BLOCK_REASON_NO_INCOME)->first();
            if (!$blReason) {
                throw new \Exception('No block reason created, name: ' . BlockReason::BLOCK_REASON_NO_INCOME);
            }

            app(ClientService::class)->block($loan->client, [
                'block_reason_id' => $blReason->block_reason_id,
                'to_date' => null, // ръчно изваждане
                'comment' => '3 поредни отказа с причина Без доход',
                'created_at' => now(),
                'created_by' => 1, // автоматично
            ]);
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function decisionAttempt(): DecisionAttempt
    {
        return $this->decisionAttempt;
    }

    public function installments(): InstallmentsInterface
    {
        return $this->installments;
    }

    // WE NEED ALL THESE EMPTY METHODS, because of phpstan!
    protected function save(): static
    {
        return $this;
    }

    protected function setStatus(): static
    {
        return $this;
    }

    protected function removeRefinanced(): static
    {
        return $this;
    }

    protected function setA4ePerformanceFlag(string $decisionReason): static
    {
        return $this;
    }

    protected function addMeta(?string $decision = null, ?string $decisionReason = null ): static
    {
        return $this;
    }
}
