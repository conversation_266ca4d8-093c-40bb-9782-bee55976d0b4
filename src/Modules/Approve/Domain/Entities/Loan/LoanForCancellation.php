<?php

namespace Modules\Approve\Domain\Entities\Loan;

use Carbon\Carbon;
use Modules\Approve\Domain\Entities\Admin;
use Modules\Approve\Domain\Entities\AdminOffice;
use Modules\Approve\Domain\Entities\Office;
use Modules\Approve\Domain\Events\LoanWasCanceled;
use Modules\Approve\Domain\Exceptions\ApproveDecisionDoesntMatchStatus;
use Modules\Approve\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Approve\Domain\Exceptions\LoanNotSaved;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\LoanRepository as Repo;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\TaskStatusEnum;


// used in:
// 1. CancelLoanAction
//  - 1a. approve (LoanDecisionController->cancel())
//  - 1b. auto process ->disapprove (AutoProcessService)
//  - 1c. not aquire money (ClientCardController->cancelLoan())
//  - 1d. easypay refund
//  - 1e. too many attempt to approve, postponed
//  - 1f. auto process -> refinance with too small credit limit
//  - 1g. approve of blocked client
// 2. PaymentTaskController->cancelTaskAndLoan() : OutgoingPayment->cancelLoan() - when we cancel payment task
// 3. MaximumApproveAttemptsLimitReached -> CancelLoanListener
class LoanForCancellation extends DecisionLoan
{
    const STATUS_CANCEL_FROM_CLIENT = -6;
    const STATUS_CANCEL_FROM_US = -7;

    public function __construct(
        protected DbModel $dbModel,
        protected Repo $repo,
        protected CurrentDate $currentDate,
        protected Office $office,
        protected Admin $admin,
        protected AdminOffice $adminOffice,
        protected DecisionAttempt $decisionAttempt,
        protected Installments $installments,
        protected CancellationRefinancedLoans $approvalRefLoans,
        protected CancelledLoanMeta $loanMeta
    ) {
        parent::__construct(
            $dbModel,
            $repo,
            $currentDate,
            $office,
            $admin,
            $adminOffice,
            $decisionAttempt,
            $installments
        );
    }

    public function process(DecisionDto $dto): static
    {
        return $this
            ->checkLoanStatus() /// check if loan is active
            ->setOffice()
            ->setAdmin($dto->admin_id)
            ->setAdminOffice($dto->admin_id, $this->dbModel->office_id)
            ->setDecisionAttempt($dto)
            ->setStatus()
            ->setA4ePerformanceFlag($dto->decision_reason)
            ->save()
            ->blockClient() // optional
            ->removeRefinanced()
            ->addMeta($dto->decision, $dto->decision_reason);
    }

    protected function setStatus(): static
    {
        if (!LoanStatus::isAllowedChangeTo($this->dbModel->loan_status_id, LoanStatus::CANCELLED_STATUS_ID)) {
            throw new LoanHasIncorrectStatus(
                $this->dbModel->getKey(),
                $this->dbModel->getStatus(),
                LoanStatus::STATUS_CANCELLED
            );
        }

        $decision = $this->decisionAttempt->dbModel()->getDecisionName();
        if (!in_array($decision, [
            ApproveDecision::APPROVE_DECISION_CANCELED,
            ApproveDecision::APPROVE_DECISION_WRONG_PHONE
        ])) {
            throw new ApproveDecisionDoesntMatchStatus($decision, LoanStatus::STATUS_APPROVED);
        }

        $this->dbModel->last_status_update_date = $this->currentDate->now();
        $this->dbModel->loan_status_id = LoanStatus::CANCELLED_STATUS_ID;

        return $this;
    }

    protected function setA4ePerformanceFlag(string $decisionReason): static
    {
        $this->dbModel->a4e_performance_flag = in_array($decisionReason, [
            ApproveDecisionReason::APPROVE_DECISION_REASON_DISAPPROVE_AMOUNT,
            ApproveDecisionReason::APPROVE_DECISION_REASON_DISAPPROVE_PERIOD
        ])
            ? self::STATUS_CANCEL_FROM_CLIENT
            : self::STATUS_CANCEL_FROM_US;

        return $this;
    }

    // important, if LoanForCancelation executed from CancelPaymentAction - refinance already removed by CancelIncomingRefinancePaymentsAction
    // so here we go only from LoanDecisionController->cancel()
    protected function removeRefinanced(): static
    {
        $this->approvalRefLoans->loadByLoan($this->dbModel)->removeAllCurrent();

        return $this;
    }

    protected function save(): static
    {
        if (! $this->repo->save($this->dbModel)) {
            throw new LoanNotSaved();
        }

        LoanWasCanceled::dispatch($this->dbModel());

        return $this;
    }

    protected function addMeta(
        ?string $decision = null,
        ?string $decisionReason = null
    ): static {

        // we save meta only for CANCEL, not for Wrong Phone
        // also wrong phone has no reason
        if (
            empty($decisionReason)
            || $decision == ApproveDecision::APPROVE_DECISION_WRONG_PHONE
        ) {
            return $this;
        }

        $this->loanMeta->build($this);
        return $this;
    }

    public function loanMeta(): CancelledLoanMeta
    {
        return $this->loanMeta;
    }

    public function setAdmininstrator(int $adminId)
    {
        if (empty($this->dbModel->administrator_id)) {
            $this->dbModel->administrator_id = $adminId;
        }

        return $this;
    }
}
