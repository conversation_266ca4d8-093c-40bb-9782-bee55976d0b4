<?php

namespace Modules\Approve\Domain\Entities\Loan;

use Modules\Approve\Domain\Entities\Admin;
use Modules\Approve\Domain\Entities\AdminOffice;
use Modules\Approve\Domain\Entities\Office;
use Modules\Approve\Domain\Events\MaximumApproveAttemptsLimitReached;
use Modules\Approve\Domain\Exceptions\ApproveDecisionDoesntMatchStatus;
use Modules\Approve\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Approve\Domain\Exceptions\LoanNotSaved;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\LoanRepository as Repo;

class LoanForDelay extends DecisionLoan
{
    public $canceled = false;

    public function __construct(
        protected DbModel $dbModel,
        protected Repo $repo,
        protected CurrentDate $currentDate,
        protected Office $office,
        protected Admin $admin,
        protected AdminOffice $adminOffice,
        protected DecisionAttempt $decisionAttempt,
        protected Installments $installments
    ) {
        parent::__construct(
            $dbModel,
            $repo,
            $currentDate,
            $office,
            $admin,
            $adminOffice,
            $decisionAttempt,
            $installments
        );
    }

    public function process(DecisionDto $dto): self
    {
        return $this
            ->setOffice()
            ->setAdmin($dto->admin_id)
            ->setAdminOffice($dto->admin_id, $this->dbModel->office_id)
            ->setDecisionAttempt($dto)
            ->setStatus()
            ->save();
    }

    protected function setStatus(): static
    {
        if ($this->dbModel->getStatus() !== LoanStatus::STATUS_PROCESSING) {
            throw new LoanHasIncorrectStatus(
                $this->dbModel->getKey(),
                $this->dbModel->getStatus(),
                LoanStatus::STATUS_APPROVED
            );
        }

        $attempt = $this->decisionAttempt->dbModel();
        $decision = $attempt->getDecisionName();
        if (! in_array($decision, [
            ApproveDecision::APPROVE_DECISION_CALL_LATER,
            ApproveDecision::APPROVE_DECISION_BUSY,
            ApproveDecision::APPROVE_DECISION_NO_ANSWER,
            ApproveDecision::APPROVE_DECISION_WAITING_DOCUMENTS
        ])){
            throw new ApproveDecisionDoesntMatchStatus($decision, LoanStatus::STATUS_SIGNED);
        }

        // set last attempt decision and skip till to loan, to hide it from approve list
        $lastDecision = getAdmin()->getName()
            . ' ' . __('approve::approveLoanList.previousExit')
            . ' <b>' . __('approve::approveDecision.' . $decision) . '</b>';
        $skipTill = $attempt->skip_till;

        if ($decision === ApproveDecision::APPROVE_DECISION_WAITING_DOCUMENTS) {
            /// Когато бутона се натисне, задачата се маркира като обработена и се скрива от страница Задачи одобрение за 24 часа.
            $skipTill = now()->addDay()->toDateTimeString();
        }

        $this->dbModel->last_status_update_date = $this->currentDate->now();
        $this->dbModel->loan_status_id = LoanStatus::SIGNED_STATUS_ID;
        $this->dbModel->last_attempt = $lastDecision;

        // hide loan for cetain time from approve listing, based on agent decision
        $this->dbModel->updateSkipTill($skipTill, 'approve decision: ' . $decision);

        return $this;
    }

    public function save(): static
    {
        if ($this->decisionAttempt->isLast()) {
            MaximumApproveAttemptsLimitReached::dispatch($this->dbModel->getKey());
            $this->canceled = true;
        } elseif (! $this->repo->save($this->dbModel)) {
            throw new LoanNotSaved();
        }

        return $this;
    }

    public function getCanceled(): bool
    {
        return $this->canceled;
    }
}
