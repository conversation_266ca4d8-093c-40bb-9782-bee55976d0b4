<?php

namespace Modules\Approve\Domain\Entities\Loan;

use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\LoanMeta as DbModel;
use Modules\Head\Repositories\Loan\LoanMetaRepository as Repo;
use Modules\Sales\Domain\Exceptions\LoanRelations\MetaNotSaved;

class CancelledLoanMeta extends DomainModel
{
    private LoanForCancellation $loan;
    public const CANCEL_KEY = 'cancel_approved_loan';

    public function __construct(
        private DbModel $dbModel,
        private Repo $repo,
    ){}

    public function build(LoanForCancellation $loan): self
    {
        return $this
            ->setLoan($loan)
            ->setKey()
            ->setDbModel()
            ->setValue()
            ->save();
    }

    private function setLoan(LoanForCancellation $loan): self
    {
        $this->loan = $loan;
        $this->dbModel->loan_id = $loan->dbModel()->loan_id;
        return $this;
    }

    private function setKey(): self
    {
        $this->dbModel->key = self::CANCEL_KEY;
        return $this;
    }

    private function setDbModel(): self
    {
        $existing = $this->repo->getByLoanIdAndKey($this->dbModel->loan_id, $this->dbModel->key);
        if($existing){
            $this->dbModel = $existing;
        }
        return $this;
    }

    private function setValue(): self
    {
        $approveAttempt = $this->loan->decisionAttempt()->dbModel();

        $this->dbModel->value = json_encode([
            'reason' => $approveAttempt->getDecisionReasonName(),
            'description' => $approveAttempt->details
        ]);

        return $this;
    }

    private function save(): self
    {
        if(! $this->repo->save($this->dbModel)){
            throw new MetaNotSaved();
        }
        return $this;
    }

    public function delete(): self
    {
        $this->dbModel?->delete();
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
