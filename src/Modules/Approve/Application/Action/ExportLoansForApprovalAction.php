<?php

namespace Modules\Approve\Application\Action;

use Illuminate\Support\Collection;
use Modules\Head\Repositories\LoanRepository;

readonly class ExportLoansForApprovalAction
{
    public function __construct(private LoanRepository $repo) {}

    public function execute(array $filters = []): Collection
    {
        $rows = new Collection();

        /** @var \Modules\Common\Models\Loan $loanForApproval **/
        foreach ($this->repo->getLoansForApproval($filters) as $loanForApproval) {
            $tasks = '';
            $apTasks = $loanForApproval->approve_tasks;
            if (!empty($apTasks)) {
                foreach($loanForApproval->approve_tasks as $agentApproveTask) {
                    $tasks .= __('approve::approveLoanList.' . $agentApproveTask);
                }
            }

            /// on stage we have cases without client
            if (!$loanForApproval->client) {
                continue;
            }

            $row = [
                'Клиент ID' => (int) $loanForApproval->client->getKey(),
                'No. Заявка' => $loanForApproval->getKey(),
                'Офис' => $loanForApproval->office->name,
                'Метод на плащане' => $loanForApproval->paymentMethod->name,
                'Продукт' => $loanForApproval->product->name,
                'Искана сума' => amount($loanForApproval->amount_requested),
                'Пероид на Кредита' => $loanForApproval->period_requested. trans_choice('product::product.keys.' . $loanForApproval->loanProductSetting->period, $loanForApproval->period_approved),
                'Задача' => $tasks,
                'Клиент' => $loanForApproval->client->isNewLabel(),
                'Имена на Клиента' => $loanForApproval->client->getFullName(),
                'ЕГН' => $loanForApproval->client->pin,
                'Телефон' => $loanForApproval->client->phone,
                'Дата на Задачата' => formatDate($loanForApproval->created_at, 'd.m.Y H:i:s'),
                'Таймер' => $loanForApproval->loanForApproveTimer(),
                'Статус' => strip_tags($loanForApproval->getLastApproveDecisionLabel())
            ];

            $rows->push((object)$row);
        }

        return $rows;
    }
}
