<?php

namespace Modules\Approve\Application\Listeners;

use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Common\Models\Document;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Loan;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailService;
use Modules\Communication\Services\SmsService;

class ApproveDockPackSender
{
    public function __construct(
        private EmailService $emailService,
        private SmsService   $smsService
    ) {
    }

    public function handle(LoanWasApproved $event): string
    {
        $dbLoan = $event->loan->dbModel();

        // send sms
        $this->smsService->sendApproveCongrats($dbLoan);


        // send email only for online offices, docs for approve are generated on creation!
        // for physical offices we don't have email-address
        $this->sendEmailForLoan($dbLoan);

        return 'success';
    }

    public function sendEmailForLoan(Loan $dbLoan): void
    {
        if (!$dbLoan->isOnlineLoan()) {
            return ;
        }

        if ($dbLoan?->insurance?->isTrue()) {
            // TODO:
            // get insurance certificate
            // if exists add it to attachments and send email
            // else run same logic in job in 30 sec, try 3 times
        }

        $docTemplateIds = DocumentTemplate::whereIn('type', [
            DocumentTemplate::TPL_CONTRACT,
            DocumentTemplate::TPL_GEN_TERMS,
            DocumentTemplate::TPL_PLAN
        ])->pluck('document_template_id')->toArray();

        $attachments = [];
        $documents = $dbLoan->documents->whereIn('document_template_id', $docTemplateIds);
        $documents->each(function (Document $document) use (&$attachments) {
            $mimeContentType = mime_content_type($document->file->filepath());

            $attachments[] = [
                'file_id' => $document->file->getKey(),
                'path' => $document->file->filepath(),
                'name' => $document->getHumanFileName(),
                'mime' => $mimeContentType,
            ];
        });

        $this->emailService->sendByTemplateKeyAndLoanId(
            EmailTemplateKeyEnum::LOAN_APPROVED->value,
            $dbLoan->getKey(),
            attachments: $attachments
        );
    }
}
