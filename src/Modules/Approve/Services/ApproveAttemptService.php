<?php

namespace Modules\Approve\Services;

use Illuminate\Support\Carbon;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Approve\Repositories\ApproveAttemptRepository;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\ApproveAttempt;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Services\BaseService;
use RuntimeException;

class ApproveAttemptService extends BaseService
{
    /**
     * ApproveAttemptService constructor.
     *
     * @param ApproveAttemptRepository $approveAttemptRepository
     * @param SettingRepository $settingRepository
     */
    public function __construct(
        private readonly ApproveAttemptRepository $approveAttemptRepository = new ApproveAttemptRepository(),
        private readonly SettingRepository        $settingRepository = new SettingRepository(),
    ) {
        parent::__construct();
    }

    /**
     * @param int $loanId
     *
     * @return ApproveAttempt|null
     */
    public function getPreviousApproveAttempt(int $loanId): ?ApproveAttempt
    {
        $conditions = [
            'loan_id' => $loanId,
            'last' => 1,
            'active' => 1,
            'deleted' => 0,
        ];

        return $this->approveAttemptRepository->getByCriteria($conditions);
    }

    /**
     * @deprecated
     *
     * @param int $skipCounter
     *
     * @return bool
     */
    public function isLastDeferred(int $skipCounter): bool
    {
        $maxAllowedPostponements = $this
            ->settingRepository
            ->getSetting(SettingsEnum::max_allowed_postponements_approve);

        return $skipCounter >= (int) $maxAllowedPostponements->default_value;
    }

    /**
     * @deprecated
     *
     * @param array $newAttemptData
     * @param ApproveAttempt $previousAttempt
     *
     * @return ApproveAttempt
     */
    public function disapproveTooManyPostponements(
        array $newAttemptData,
        ApproveAttempt $previousAttempt
    ): ApproveAttempt {
        $newAttemptData['approve_decision_id'] =
            ApproveDecision::APPROVE_DECISION_ID_CANCELED;
        $newAttemptData['details'] = 'Loan is disapproved. Too many postponements.';
        $newAttemptData['approve_decision_reason_id'] =
            ApproveDecisionReason::APPROVE_DECISION_REASON_ID_OTHER;

        return $this->approveAttemptRepository->create($newAttemptData, $previousAttempt);
    }

    public function checkIfLoanIsProcessed(int $loanId): bool
    {
        $approveAttempt = $this->approveAttemptRepository->getByCriteria(
            [
                'loan_id' => $loanId,
                'last' => 1,
            ]
        );

        if (empty($approveAttempt->approve_attempt_id)) {
            return false;
        }

        $approveDecision = $approveAttempt->approveDecision;
        $type = $approveDecision->type ?? '';

        switch ($type) {
            case ApproveDecision::APPROVE_DECISION_TYPE_FINAL:
                return true;
            case ApproveDecision::APPROVE_DECISION_TYPE_WAITING:
                $attemptSkipTill = Carbon::parse($approveAttempt->skip_till);
                return $attemptSkipTill->gt(now());
            default:
                throw new RuntimeException(
                    __('approve::approveDecision.invalidTypes')
                );
        }
    }
}
