<?php

namespace Modules\Approve\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveAttempt;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Loan;
use RuntimeException;

class ApproveAttemptSeeder extends Seeder
{
    public function run(): void
    {
        $loans = Loan::all();

        $loans->each(function (Loan $loan, int $index) {
            $approveDecision = $this->getApproveDecisionId();
            $faker = Faker::create();

            $data = [
                'administrator_id' => $faker->numberBetween(5, 50),
                'office_id' => $loan->office_id,
                'approve_decision_id' => $faker->numberBetween(1, 6),
                'approve_decision_reason_id' => $approveDecision['approveDecisionReasonId'],
                'skip_time' => 30,
                'skip_till' => now(),
                'skip_counter' => $approveDecision['skipCounter'],
                'details' => $faker->paragraph(1, true),
                'start_at' => now()->addMinutes(5),
                'end_at' => now()->addMinutes(25),
                'total_time' => 20 * 60,
                'last' => 1,
                'active' => 1,
                'deleted' => 0,
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'loan_id' => $loan->getKey(),
                'waiting_time'=> $index*2,
                'processing_time'=> $index,

            ];
            $attempt = new ApproveAttempt();
            $attempt->fill($data);
            $attempt->save();
            //$loan->approveAttempt()->create($data);
            //TODO: refactor as part of https://trello.com/c/BuNxaexS/1538-migrations-upgrade
            DB::table('loan_status_history')->insert([[
                'loan_id' => $loan->getKey(),
                'loan_status_id' => 2,
                'date'=>'2022-10-10 11:11:11',
                'administrator_id'=>Administrator::DEFAULT_ADMINISTRATOR_ID],
            ]);
//            $domainStats = new AgentDecisionStats(new TmpRequestRepository());
//            $domainStats->create($loan, $attempt, $attempt);
        });
    }

    protected function getApproveDecisionId(): array
    {
        $faker = Faker::create();

        $approveDecisionId = $faker->numberBetween(1, 6);
        $approveDecisionReasonId = null;
        $skipCounter = null;

        switch ($approveDecisionId) {
            case ApproveDecision::APPROVE_DECISION_ID_CALL_LATER:
            case ApproveDecision::APPROVE_DECISION_ID_BUSY:
            case ApproveDecision::APPROVE_DECISION_ID_NO_ANSWER:
                $skipCounter = 1;
                break;
            case ApproveDecision::APPROVE_DECISION_ID_APPROVED:
                break;
            case ApproveDecision::APPROVE_DECISION_ID_CANCELED:
                $approveDecisionReasonId = $faker->numberBetween(1, 6);
                break;
            case ApproveDecision::APPROVE_DECISION_ID_WRONG_PHONE:
                $approveDecisionReasonId = 7;
                break;
            default:
                throw new RuntimeException('Invalid approve decision id');
        }

        return [
            'approveDecisionReasonId' => $approveDecisionReasonId,
            'skipCounter' => $skipCounter
        ];
    }
}