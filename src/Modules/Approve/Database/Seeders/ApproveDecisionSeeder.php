<?php

namespace Modules\Approve\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;

/**
 * php artisan db:seed --class=\\Modules\\Approve\\Database\\Seeders\\ApproveDecisionSeeder
 */
class ApproveDecisionSeeder extends Seeder
{
    public function run(): void
    {
        $approveDecisions = config('approve.approve_decisions');
        foreach ($approveDecisions as $decision) {
            $create = [
                ...$decision,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ];

            /// if decision not exists create
            if (!ApproveDecision::where('name', $decision['name'])->exists()) {
                $created = ApproveDecision::create($create);
            }
        }
    }
}
