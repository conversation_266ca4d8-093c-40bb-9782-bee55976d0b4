<?php

namespace Modules\Approve\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;

// use PhpOffice\PhpSpreadsheet\Spreadsheet;
// use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
// use PhpOffice\PhpSpreadsheet\Cell\DataType;

class ScoringStatsController extends Controller
{
    public function index(Request $request) {
        // Validate the request
        $validatedData = $this->validateCreatedAt($request);

        if (!empty($validatedData['created_at'])) {
            // Simplify date range handling
            list($fromDate, $toDate) = $this->parseDateRange($validatedData['created_at']);

            // Use the parsed dates to download scoring data
            return $this->downloadScoringDataCsv($fromDate, $toDate);
        }

        return view('approve::scoring-stats.index');
    }

    private function validateCreatedAt(Request $request) {
        $validator = Validator::make($request->all(), [
            'created_at' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $this->customDateValidation($attribute, $value, $fail);
                },
            ],
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        return $validator->validated();
    }

    private function customDateValidation($attribute, $value, $fail) {
        if (!is_null($value)) {
            $dates = explode(' - ', $value);
            foreach ($dates as $date) {
                if (!Carbon::createFromFormat('d-m-Y', $date)->format('d-m-Y') === $date) {
                    $fail("The {$attribute} must be in the format 'dd-mm-yyyy' or 'dd-mm-yyyy - dd-mm-yyyy'.");
                }
            }
            if (count($dates) > 2) {
                $fail("The {$attribute} must contain at most one range indicator '-'.");
            }
        }
    }

    private function parseDateRange($dateRange): array
    {
        $parts = explode(' - ', $dateRange);
        $fromDate = $toDate = Carbon::createFromFormat('d-m-Y H:i:s', $parts[0] . ' 00:00:00');

        if (count($parts) === 2) {
            $toDate = Carbon::createFromFormat('d-m-Y H:i:s', $parts[1] . ' 23:59:59');
        } else {
            $toDate = $fromDate->copy()->endOfDay();
        }

        return [$fromDate->format('Y-m-d H:i:s'), $toDate->format('Y-m-d H:i:s')];
    }

    public function downloadScoringDataCsv(string $fromDate, string $toDate)
    {
        try {

            $fileName = 'scoring_stats_' . time() . '.csv';
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename=' . $fileName);
            header("Pragma: no-cache"); // optional
            header("Expires: 0"); // optional

            $fp = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));

            $header = [
                'Client_ID',
                'Loan_ID',
                'Application_date',
                'Application_status',
                'Loan_amount',
                'Product_type',
                'Loan_Type',
                'Scorecard_GBR',
                'Scorecard_GB',
                'Decision_type',
                'Reject_reason',
                'Credit_Limit',
                'Refinance_flag',
                'Early_repayment_amount',
                'Original_loan_amount',
                'No_loans',
            ];
            fputcsv($fp, $header);

            $lastId = 0;
            $chunkSize = 100;
            do {
                $records = DB::select("
                    select
                        l.client_id,
                        l.loan_id,
                        l.created_at as application_date,
                        ls.name as application_status,
                        ROUND(l.amount_approved::numeric / 100, 2) as loan_amount,
                        case
                            when l.product_type_id = 1 then 'КДЗ'
                            else 'На вноски'
                        end as product_type,
                        case when (
                                select count(lll.loan_id)
                                from loan lll
                                where
                                    lll.client_id = l.client_id
                                    and lll.created_at < l.created_at
                                    and lll.loan_status_id IN (6,7,9)
                            ) > 0 then 'subsequent_loan'
                            else 'first_loan'
                        end as loan_type,
                        coalesce(aer.gbr, 'none') as scorecard_gbr,
                        coalesce(aer.gb, 'none') as scorecard_gb,
                        case
                            when l.loan_status_id = " . LoanStatus::APPROVED_STATUS_ID . " then
                                case
                                    when l.last_status_update_administrator_id = " . Administrator::SYSTEM_ADMINISTRATOR_ID . " then 'auto_approve'
                                    else 'manual'
                                end
                            when l.loan_status_id = " . LoanStatus::CANCELLED_STATUS_ID . " then
                                case
                                    when l.last_status_update_administrator_id = " . Administrator::SYSTEM_ADMINISTRATOR_ID . " then 'auto_reject'
                                    else 'manual'
                                end
                            when l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . " or l.loan_status_id = " . LoanStatus::REPAID_STATUS_ID . " then
                                case
                                    when
                                        coalesce((SELECT x.administrator_id  FROM loan_status_history x WHERE x.loan_id = l.loan_id and x.loan_status_id = 5 order by x.loan_status_history_id desc limit 1),2) = " . Administrator::SYSTEM_ADMINISTRATOR_ID . " then 'auto_approve'
                                    else 'manual'
                                end
                            else ''
                        end as decision_type,
                        case
                            when l.loan_status_id = 8 then coalesce((
                                SELECT adr.description
                                FROM approve_attempt x
                                left join approve_decision_reason adr on x.approve_decision_reason_id = adr.approve_decision_reason_id
                                WHERE x.loan_id = l.loan_id
                                order by x.approve_attempt_id desc
                                limit 1), 'undefined')
                            else ''
                        end as reject_reason,
                        coalesce(
                            (select crl.amount
                            from credit_limit crl
                            where
                                crl.loan_id = l.loan_id
                                and crl.last = 1
                                and crl.active = 1
                            order by crl.credit_limit_id DESC
                            limit 1),
                            0
                        ) as credit_limit,
                        case
                            when l.loan_status_id = " . LoanStatus::CANCELLED_STATUS_ID . " then coalesce((select sum(lr.refinanced_due_amount)/100 from loan_refinance_log lr where lr.refinancing_loan_id = l.loan_id AND lr.deleted = 0), 0)
                            else coalesce((select sum(lr.refinanced_due_amount)/100 from loan_refinance lr where lr.refinancing_loan_id = l.loan_id AND lr.deleted = 0), 0)
                        end as early_repayment_amount,
                        las.previous_loans_count,
                        COALESCE(
                            (select lph.amount_requested from loan_params_history lph where lph.loan_id = l.loan_id order by loan_params_history_id asc limit 1),
                            l.amount_requested
                        )::numeric/100 as original_loan_amount
                    from loan l
                    join loan_status ls on ls.loan_status_id = l.loan_status_id
                    join loan_actual_stats las on las.loan_id = l.loan_id
                    left join a4e_report aer on l.loan_id = aer.loan_id AND aer.last = 1
                    where
                        l.office_id = " . Office::OFFICE_ID_WEB . "
                        and l.created_at >= '" . $fromDate . "'
                        and l.created_at <= '" . $toDate . "'
                        and l.loan_id > :lastId
                    ORDER BY l.loan_id ASC
                    LIMIT :chunkSize",
                    ['lastId' => $lastId, 'chunkSize' => $chunkSize]
                );

                foreach ($records as $record) {
                    $r = [
                        $record->client_id,
                        $record->loan_id,
                        $record->application_date,
                        $record->application_status,
                        $record->loan_amount,
                        $record->product_type,
                        $record->loan_type,
                        $record->scorecard_gbr,
                        $record->scorecard_gb,
                        $record->decision_type,
                        $record->reject_reason,
                        $record->credit_limit,
                        ($record->early_repayment_amount > 0 ? 1 : 0),
                        $record->early_repayment_amount,
                        $record->original_loan_amount,
                        $record->previous_loans_count,
                    ];

                    fputcsv($fp, $r);
                }

                $lastId = end($records)->loan_id ?? 0;

            } while (count($records) == $chunkSize);

            fclose($fp);
            return ;

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error ScoringExport: ' . $msg);

            return ;
        }
    }
}
