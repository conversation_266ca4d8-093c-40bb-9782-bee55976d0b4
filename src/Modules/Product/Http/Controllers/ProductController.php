<?php

namespace Modules\Product\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Client;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductSetting;
use Modules\Docs\Services\DocumentTemplateService;
use Modules\Head\Http\Requests\ProductGroupSettingRequest;
use Modules\Product\Application\Actions\Product\ProductDataAction;
use Modules\Product\Http\Requests\ProductEditRequest;
use Modules\Product\Http\Requests\ProductSearchRequest;
use Modules\Product\Services\ProductGroupSettingService;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;
use Throwable;

class ProductController extends BaseController
{
    protected string $pageTitle = 'Product list';
    protected string $indexRoute = 'settings.product.list';

    public function __construct(
        private readonly ProductService             $productService,
        private readonly OfficeService              $officeService,
        private readonly DocumentTemplateService    $documentTemplateService,
        private readonly ProductGroupSettingService $productGroupSettingService,
        private readonly ProductTypeService         $productTypeService,
    )
    {
        parent::__construct();
    }

    public function list(
        ProductSearchRequest $productSearchRequest,
        ProductDataAction    $productDataAction
    ): View
    {
        $filters = $productSearchRequest->validated();
        $data = $productDataAction->execute($filters, $this->getPaginationLimit());

        return view('product::product.list', $data);
    }

    /**
     * @return Application|Factory|View
     *
     * @throws NotFoundException
     */
    public function create()
    {
        $productTypes = $this->productTypeService->all();
        $offices = $this->officeService->getAllOffices();
        $productSettings = $this->productGroupSettingService->all(ProductSetting::PRODUCT_SETTING_TYPE_COMMON);
        $documentTemplates = $this->documentTemplateService->getGroupedTemplates();
        $termSettings = $this->productGroupSettingService->all(ProductSetting::PRODUCT_SETTING_TYPE_TERM);
        $legalStatuses = Client::getLegalStatuses();

        return view(
            'product::product.view',
            compact(
                'productTypes',
                'offices',
                'productSettings',
                'documentTemplates',
                'termSettings',
                'legalStatuses',
            )
        );
    }

    public function store(ProductEditRequest $request): RedirectResponse
    {
        $this->productService->create($request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productCrud.productCreatedSuccessfully'));
    }

    public function duplicate(Product $product): RedirectResponse
    {
        $this->productService->duplicate($product->product_id);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productCrud.productDuplicatedSuccessfully'));
    }

    /**
     * @param Product $product
     *
     * @return Application|Factory|View
     *
     * @throws NotFoundException
     * @throws Exception
     */
    public function edit(Product $product)
    {
        $productTypes = $this->productTypeService->all();
        $offices = $this->officeService->getAllOffices();
        $productSettings = $this->productGroupSettingService->all(ProductSetting::PRODUCT_SETTING_TYPE_COMMON);
        $termSettings = $this->productGroupSettingService->all(ProductSetting::PRODUCT_SETTING_TYPE_TERM);
        $documentTemplates = $this->documentTemplateService->getGroupedTemplates();
        $productHistory = $this->getProductHistoryData($product);
        $legalStatuses = Client::getLegalStatuses();

        return view(
            'product::product.view',
            compact(
                'product',
                'productTypes',
                'offices',
                'productSettings',
                'termSettings',
                'documentTemplates',
                'productHistory',
                'legalStatuses',
            )
        );
    }

    public function update(Product $product, ProductEditRequest $request): RedirectResponse
    {
        try {
            $this->productService->edit($product, $request->validated());

            Cache::forget('all_products_id_name');
            Cache::tags(['admin_products'])->flush();

            return to_route($this->indexRoute)->with('success', __('head::productCrud.productUpdatedSuccessfully'));
        } catch (Exception $exception){
            \Log::debug($exception->getMessage());

            return back()->with('fail', __('head::productCrud.productUpdateFailed'));
        }
    }

    public function delete(Product $product): RedirectResponse
    {
        $this->productService->delete($product);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productCrud.productDeletedSuccessfully'));
    }

    public function enable(Product $product): RedirectResponse
    {
        $this->productService->enable($product);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productCrud.productEnabledSuccessfully'));
    }

    public function disable(Product $product): RedirectResponse
    {
        $this->productService->disable($product);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('head::productCrud.productDisabledSuccessfully')
            );
    }

    /**
     * @param ProductGroupSettingRequest $request
     *
     * @return array|string
     *
     * @throws NotFoundException|Exception
     */
    public function getSettingByProductGroup(ProductGroupSettingRequest $request)
    {
        $validated = $request->validated();
        $productGroupId = $validated['product_group_id'];

        $productGroup = $this->productService->getGroupById($productGroupId);

        $result = $productGroup->productSettings;

        if (!$result->count()) {
            return __('head::productSettingCrud.NoSettings');
        }

        return $result;
    }

    /**
     * @return mixed
     * @throws Exception
     */
    public function getTableData()
    {
        return $this->productService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @return mixed
     * @throws Exception
     */
    public function getProductHistoryData(Product $product)
    {
        return $this->productService->getHistory(
            parent::getTableLength(),
            $product
        );
    }

    /**
     * @param Product $product
     *
     * @return array|string
     * @throws Exception
     */
    public function refreshProductHistory(Product $product)
    {
        return view(
            'product::product.history-list-table',
            [
                'productHistory' => $this->getProductHistoryData($product),
            ]
        )->render();
    }

    /**
     * @param ProductSearchRequest $request
     *
     * @return array|string
     * @throws Throwable
     */
    public function refresh(ProductSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'product::product.list-table',
            [
                'products' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
            ]
        )->render();
    }


    public function deleteInterestRules(Product $product, string $interestType)
    {
        if ($interestType === 'interest') {
            $file = $product->interestTerms()->where('file_id', '!=', null)->first()?->file;
            $product->interestTerms()->delete();

            $file?->delete();
        }

        if ($interestType === 'penalty') {
            $file = $product->penaltyTerms()->where('file_id', '!=', null)->first()?->file;

            $product->penaltyTerms()->delete();

            $file?->delete();
        }

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productCrud.productDeletedSuccessfully'));
    }
}
