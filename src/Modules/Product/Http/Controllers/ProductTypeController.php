<?php

namespace Modules\Product\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\ProductType;
use Modules\Product\Http\Requests\ProductTypeEditRequest;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;

class ProductTypeController extends BaseController
{
    protected string $pageTitle = 'Product type list';
    protected string $indexRoute = 'settings.product-type.list';
    protected ProductService $productService;

    /**
     * ProductGroupController constructor.
     *
     * @param ProductService $productService
     *
     * @throws \ReflectionException
     */
    public function __construct(
        ProductService $productService,
        private readonly ProductTypeService $productTypeService = new ProductTypeService,
    ) {
        $this->productService = $productService;

        parent::__construct();
    }

    /**
     * @return RedirectResponse|View
     * @throws Exception
     */
    public function list()
    {
        return view(
            'product::product-type.list',
            [
                'productTypes' => $this->productTypeService->all(),
            ]
        );
    }

    /**
     *
     * @return RedirectResponse|View
     * @throws Exception
     */
    public function create()
    {
        return view(
            'product::product-type.crud',
        );
    }

    public function store(ProductTypeEditRequest $request): RedirectResponse
    {
        $this->productTypeService->create($request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productGroupCrud.productGroupCreatedSuccessfully'));
    }

    /**
     * @param ProductType $productType
     *
     * @return View
     *
     * @throws Exception
     */
    public function edit(ProductType $productType)
    {
        return view(
            'product::product-type.crud',
            compact('productType')
        );
    }

    public function update(
        ProductType $productType,
        ProductTypeEditRequest $request
    ): RedirectResponse {
        $this->productTypeService->edit($productType, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productGroupCrud.productGroupUpdatedSuccessfully'));
    }

    public function delete(ProductType $productType): RedirectResponse
    {
        $this->productTypeService->delete($productType);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productGroupCrud.productGroupDeletedSuccessfully'));
    }

    public function enable(ProductType $productType): RedirectResponse
    {
        $this->productTypeService->enable($productType);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productGroupCrud.productGroupEnabledSuccessfully'));
    }

    public function disable(ProductType $productType): RedirectResponse
    {
        $this->productService->disableGroup($productType);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('head::productGroupCrud.productGroupDisabledSuccessfully')
            );
    }
}
