<?php

namespace Modules\Product\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\ProductSetting;
use Modules\Head\Http\Requests\ProductGroupSettingEditRequest;
use Modules\Head\Http\Requests\ProductGroupSettingSearchRequest;
use Modules\Product\Services\ProductGroupSettingService;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;
use Throwable;

class ProductSettingController extends BaseController
{
    protected string $pageTitle = 'Product group setting list';
    protected string $indexRoute = 'settings.product-settings.list';
    protected ProductService $productService;

    public function __construct(
        ProductService $productService,
        private readonly ProductGroupSettingService $productGroupSettingService = new ProductGroupSettingService(),
        private readonly ProductTypeService $productTypeService = new ProductTypeService,
    ) {
        $this->productService = $productService;

        parent::__construct();
    }

    /**
     * @param ProductGroupSettingSearchRequest $request
     *
     * @return \Illuminate\Contracts\View\View
     * @throws Exception
     */
    public function list(ProductGroupSettingSearchRequest $request)
    {
        return view(
            'product::product-setting.list',
            [
                'productSettings' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
                'productGroups' => $this->productTypeService->all(),
            ]
        );
    }

    /**
     * @param ProductSetting $productSetting
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function delete(ProductSetting $productSetting): RedirectResponse
    {
        $this->productGroupSettingService->delete($productSetting);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productSettingCrud.productSettingDeletedSuccessfully'));
    }

    /**
     * @param ProductSetting $productSetting
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function enable(ProductSetting $productSetting): RedirectResponse
    {
        $this->productGroupSettingService->enable($productSetting);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productSettingCrud.productSettingEnabledSuccessfully'));
    }

    /**
     * @param ProductSetting $productSetting
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function disable(ProductSetting $productSetting): RedirectResponse
    {
        $this->productGroupSettingService->disable($productSetting);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('head::productSettingCrud.productSettingDisabledSuccessfully')
            );
    }

    /**
     * @param ProductGroupSettingSearchRequest $request
     *
     * @return RedirectResponse|void
     * @throws Exception
     */
    protected function checkForRequestParams(ProductGroupSettingSearchRequest $request)
    {
        if ($request->exists(
            [
                'name',
                'description',
                'value',
                'product_group_id',
                'active',
                'createdAt',
                'updatedAt',
            ]
        )) {
            $this->cleanFilters();
            $this->setFilters($request);
        }
    }

    /**
     * @return View
     */
    public function create()
    {
        $productSettingValueTypes = [ProductSetting::PRODUCT_SETTING_VALUE_TYPE_NUMBER];
        $productSettingTypes = ProductSetting::getProductTypes();

        return view(
            'product::product-setting.crud',
            compact('productSettingValueTypes', 'productSettingTypes')
        );
    }

    /**
     * @param ProductGroupSettingEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function store(ProductGroupSettingEditRequest $request): RedirectResponse
    {
        $this->productGroupSettingService->create($request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productSettingCrud.productSettingCreatedSuccessfully'));
    }

    /**
     * @param ProductSetting $productSetting
     *
     * @return View
     */
    public function edit(
        ProductSetting $productSetting
    ) {
        $productSettingValueTypes = [ProductSetting::PRODUCT_SETTING_VALUE_TYPE_NUMBER];
        $productSettingTypes = $productSetting::getProductTypes();

        return view(
            'product::product-setting.crud',
            compact(
                'productSetting',
                'productSettingValueTypes',
                'productSettingTypes'
            )
        );
    }

    public function update(
        ProductSetting $productSetting,
        ProductGroupSettingEditRequest $request
    ): RedirectResponse {
        $this->productGroupSettingService->edit($productSetting, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('head::productSettingCrud.UpdatedSuccessfully'));
    }

    /**
     * @param ProductGroupSettingSearchRequest $request
     *
     * @return bool|RedirectResponse
     * @throws Exception
     */
    public function setFilters(ProductGroupSettingSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    /**
     * @return mixed
     * @throws Exception
     */
    public function getTableData()
    {
        return $this->productGroupSettingService->getByFilter(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param ProductGroupSettingSearchRequest $request
     *
     * @return View
     * @throws Throwable
     */
    public function refresh(ProductGroupSettingSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'product::product-setting.list-table',
            [
                'productSettings' => $this->getTableData(),
            ]
        );
    }
}
