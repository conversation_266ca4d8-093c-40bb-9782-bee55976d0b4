<?php

namespace Modules\Product\Services;

use Modules\Common\Models\ProductSetting;
use Modules\Common\Services\BaseService;
use Modules\Product\Repository\ProductSettingRepository;

class ProductGroupSettingService extends BaseService
{
    public function __construct(
        private readonly ProductSettingRepository $productSettingRepository = new ProductSettingRepository(),
    ) {
        parent::__construct();
    }

    public function getJoins(array $data): array
    {
        return [];
    }

    public function getByFilter(
        int $limit,
        array $data
    ): mixed {
        return $this->productSettingRepository->getAll(
            $limit,
            $this->getJoins($data),
            $this->getWhereConditions($data),
            [
                'active' => 'DESC',
                'product_setting_id' => 'DESC',
            ]
        );
    }

    /**
     * @param ProductSetting $productSetting
     *
     * @return bool
     *
     */
    public function delete(ProductSetting $productSetting): bool
    {
        $this->productSettingRepository->delete($productSetting);

        return true;
    }

    public function enable(ProductSetting $productSetting): bool
    {
        if ($productSetting->isActive()) {
            throw new \RuntimeException(__('admin::settingCrud.Enable'));
        }

        $this->productSettingRepository->enable($productSetting);

        return true;
    }

    public function disable(ProductSetting $productSetting): bool
    {
        if (!$productSetting->isActive()) {
            throw new \RuntimeException(__('admin::settingCrud.Disable'));
        }

        $this->productSettingRepository->disable($productSetting);

        return true;
    }

    /**
     * @param array $data
     *
     * @return ProductSetting
     *
     */
    public function create(array $data): ProductSetting
    {
        return $this->productSettingRepository->create($data);
    }

    /**
     * @param ProductSetting $setting
     * @param array $data
     *
     * @return ProductSetting
     *
     */
    public function edit(ProductSetting $setting, array $data): ProductSetting
    {
        return $this->productSettingRepository->update($setting, $data);
    }

    public function all(?string $type = null)
    {
        $where = ['active' => 1];
        if (!empty($type) && in_array($type, ProductSetting::getProductTypes())) {
            $where['type'] = $type;
        }

        return $this->productSettingRepository->getAll(
            null,
            [],
            $where
        );
    }

}