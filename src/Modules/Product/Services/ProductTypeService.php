<?php

namespace Modules\Product\Services;

use Illuminate\Support\Collection;
use Modules\Common\Models\ProductType;
use Modules\Common\Services\BaseService;
use Modules\Product\Repository\ProductTypeRepository;

class ProductTypeService extends BaseService
{
    public function __construct(
        private readonly ProductTypeRepository $productTypeRepository = new ProductTypeRepository(),
    ) {
    }

    public function all(): array|Collection
    {
        return $this->productTypeRepository->getAll();
    }


    /**
     * @param array $data
     *
     * @return ProductType
     *
     */
    public function create(array $data): ProductType
    {
        return $this->productTypeRepository->create($data);
    }

    public function edit(ProductType $productType, array $data): ProductType
    {
        $this->productTypeRepository->edit($productType, $data);

        return $productType;
    }

    public function delete(ProductType $productType)
    {
        $this->productTypeRepository->delete($productType);
    }

    public function enable(ProductType $productType)
    {
        if ($productType->isActive()) {
            throw new \RuntimeException(__('head::productGroup.productGroupEnableForbidden'));
        }

        $this->productTypeRepository->enable($productType);
    }

}
