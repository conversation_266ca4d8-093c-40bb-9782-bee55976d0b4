<?php

namespace Modules\Product\Repository;

use Illuminate\Support\Collection;
use Modules\Common\Models\ProductSetting;
use Modules\Common\Models\ProductType;
use Modules\Common\Repositories\BaseRepository;

class ProductTypeRepository extends BaseRepository
{
    protected ProductSetting $productGroupSettings;

    public function __construct(
        protected ProductType $productType = new ProductType()
    ) {
    }

    /**
     * @param int $id
     * @return ProductType|null
     */
    public function getProductGroupById(int $id): ?ProductType
    {
        return $this->productType::where('product_group_id', $id)->first();
    }

    /**
     * @return Collection|ProductType[]
     */
    public function getAll(): Collection|array
    {
        return $this->productType::where(
            [
                'deleted' => 0,
            ]
        )->get();
    }

    /**
     * @param array $data
     *
     * @return ProductType
     */
    public function create(array $data)
    {
        $productGroup = new ProductType();
        $productGroup->fill($data);
        $productGroup->save();

        return $productGroup;
    }

    /**
     * @param ProductType $productGroup
     * @param array $data
     *
     * @return ProductType
     */
    public function edit(ProductType $productGroup, array $data)
    {
        $productGroup->fill($data);
        $productGroup->save();

        return $productGroup;
    }

    /**
     * @param ProductType $productGroup
     *
     * @throws \Exception
     */
    public function delete(ProductType $productGroup)
    {
        $productGroup->delete();
    }

    /**
     * @param ProductType $productGroup
     */
    public function enable(ProductType $productGroup)
    {
        $productGroup->enable();
    }

    /**
     * @param ProductType $productGroup
     */
    public function disable(ProductType $productGroup)
    {
        $productGroup->disable();
    }

}
