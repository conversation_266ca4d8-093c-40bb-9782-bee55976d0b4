<?php

namespace Modules\Product\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeProduct;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductDocumentTemplate;
use Modules\Common\Models\ProductHistory;
use Modules\Common\Models\ProductProductSetting;
use Modules\Common\Models\ProductSetting;
use Modules\Common\Repositories\BaseRepository;

final class ProductRepository extends BaseRepository
{
    public function __construct(
        private ProductDocumentTemplateHistoryRepository $productDocumentTemplateHistoryRepository = new ProductDocumentTemplateHistoryRepository,
    ) {
    }

    public function getProductsByAdmin(?int $adminId = null): Builder
    {
        $administrator = app(AdministratorRepository::class)->getById($adminId ?? getAdminId());
        if (!$administrator) {
            throw new \RuntimeException('Error not valid admin');
        }

        return Product::query()->where('product.active', 1)->whereHas(
            'offices',
            fn (Builder $q) => $q->whereIn(
                "{$q->getModel()->getTable()}.office_id",
                getAdminOfficeIds($administrator->getKey())
            )
        );
    }

    public function getPaginatorByFilters(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        return Product::query()->filterBy($filters)
            ->with(['creator', 'updater', 'offices'])
            ->orderBy('product_id', 'DESC')
            ->paginate($perPage);
    }


    public function getProductById(int $productId): ?Product
    {
        return Product::where(['product_id' => $productId])->first();
    }

    public function getAllProductsSimple(): Collection
    {
        return Product::query()->where([
            'deleted' => 0,
            'active' => 1,
        ])->get();
    }

    public function getByOffice(int $officeId, bool $skippMigrated = false): Collection
    {
        return $this->getByOfficeQuery($officeId, $skippMigrated)->get();
    }

    public function getByOfficeQuery(int $officeId, bool $skippMigrated = false): Builder
    {
        $where = [
            'deleted' => 0,
            'active' => 1,
        ];
        if ($skippMigrated) {
            $where['migrated'] = 0;
        }

        $productsForOffice = OfficeProduct::where('office_id', $officeId)
            ->pluck('product_id')
            ->unique()
            ->toArray();

        return Product::where($where)->whereIn('product_id', $productsForOffice);
    }

    public function getForOnlineOffice(bool $skippMigrated = false)
    {
        $where = [
            'deleted' => 0,
            'active' => 1,
        ];
        if ($skippMigrated) {
            $where['migrated'] = 0;
        }

        $productsForOffice = OfficeProduct::where('office_id', Office::OFFICE_ID_WEB)
            ->pluck('product_id')
            ->unique()
            ->toArray();

        return Product::where($where)
            ->where('is_visible_on_site', true)
            ->whereIn('product_id', $productsForOffice)
            ->get();
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        int   $limit,
        array $joins = [],
        array $where = [],
        array $order = [],
        bool  $showDeleted = false
    )
    {
        $adminOfficeIds = $this->getAdminOfficeIds();
        if (empty($adminOfficeIds)) {
            return null;
        }
        $joins['join'][] = [
            'office_product',
            'office_product.product_id',
            '=',
            'product.product_id',
        ];
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = DB::table('product');
        $builder->select(DB::raw('distinct product.product_id, product.*'));
        $this->setJoins($joins, $builder);
        if (!empty($where)) {
            $builder->where($where);
        }
        $builder->whereIn('office_product.office_id', $adminOfficeIds);
        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }

        if (empty($limit)) {
            $records = $builder->get();
            $result = Product::hydrate($records->toArray());
        } else {
            $result = $builder->paginate($limit);
            $records = Product::hydrate($result->all());
            $result->setCollection($records);
        }

        return $result;
    }

    /**
     * @param int $limit
     * @param Product $product
     * @param array $order
     *
     * @return mixed
     */
    public function getProductHistory(
        int     $limit,
        Product $product,
        array   $order = []
    )
    {
        $builder = DB::table('product_history');
        $builder->select(DB::raw('distinct product_history.product_history_id, product_history.*'));
        $builder->where('product_id', $product->getKey());

        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }

        $result = $builder->paginate($limit);
        $records = ProductHistory::hydrate($result->all());
        $result->setCollection($records);

        return $result;
    }

    /**
     * @param array $data
     *
     * @return Product
     */
    public function create(array $data)
    {
        $product = new Product();
        $product->fill($data);
        $product->save();

        return $product;
    }

    /**
     * @param Product $product
     * @param array $data
     *
     * @return Product
     */
    public function edit(Product $product, array $data)
    {
        $product->fill($data);
        $product->save();

        return $product;
    }

    /**
     * @param Product $product
     */
    public function delete(Product $product)
    {
        $product->delete();
    }

    /**
     * @param Product $product
     */
    public function enable(Product $product)
    {
        $product->enable();
    }

    /**
     * @param Product $product
     */
    public function disable(Product $product)
    {
        $product->disable();
    }

    public function getAllDefaultProductSettings()
    {
        return ProductSetting::where('active', 1)->get();
    }

    public function getDefaultProductSettingsByNames(array $names = [])
    {
        return ProductSetting::where('active', 1)
            ->whereIn('name', $names)
            ->get();
    }

    public function getProductsSettingsForAffiliates(
        int   $productId,
        array $specificKeys = []
    )
    {
        $byKey = implode('|',$specificKeys);
        return \Cache::remember("affiliate_product_settings|{$byKey}|{$productId}", now()->addHour(), function () use ($productId, $specificKeys) {
            return $this->getProductSettingsByNames($productId, $specificKeys);
        });
    }

    public function getProductSettingsByNames(
        int   $productId,
        array $specificKeys = []
    )
    {
        $builder = DB::table('product_setting');
        $builder->select(
            DB::raw(
                '
            product_setting.product_setting_id,
            product_setting.name as key,
            CASE
                WHEN product_product_setting.value IS NOT NULL
                THEN product_product_setting.value
                ELSE product_setting.default_value
            END as value,
            product_setting.default_value
        '
            )
        );
        $builder->leftJoin(
            'product_product_setting',
            function ($join) use ($productId) {
                $join->on(
                    'product_setting.product_setting_id',
                    '=',
                    'product_product_setting.product_setting_id'
                )->where(
                    'product_product_setting.product_id',
                    $productId
                );
            }
        );
        $builder->where('product_setting.active', 1);
        if (!empty($specificKeys)) {
            $builder->whereIn('product_setting.name', $specificKeys);
        }

        return $builder->get()->all();
    }

    /**
     * @param Product $product
     * @param int $id
     * @param $productSetting
     *
     * @return ProductProductSetting
     */
    public function updateOrCreateSetting(
        Product $product,
        int $id,
        mixed $productSetting
    ): ProductProductSetting {

        // Ensure product setting row exists
        $productSettingRow = ProductSetting::find($id);
        if (!$productSettingRow) {
            throw new \Exception('Product setting not found.');
        }

        $rows = ProductProductSetting::where(['product_id' => $product->getKey(), 'product_setting_id' => $id])->get();
        if ($rows->count() < 1) {
            $new = new ProductProductSetting();
            $new->product_id = $product->getKey();
            $new->product_setting_id = $id;
            $new->name = $productSettingRow->name;;
            $new->value = $productSetting;
            $new->save();

            return $new;
        }

        $cacheKey = ProductProductSetting::getCacheKey($product->getKey(), $productSettingRow->name);
        \Cache::forget($cacheKey);

        $setting = null;
        foreach ($rows as $setting) {
            $setting->name = $productSettingRow->name;
            $setting->value = $productSetting;
            $setting->save();
        }

        return $setting;
    }

    public function updateOrCreateDocumentTemplate(
        Product $product,
        string  $docType,
        int     $documentTemplateId = 0
    ): void
    {
        $oldRelation = ProductDocumentTemplate::where([
            ['product_id', '=', $product->product_id],
            ['document_template_type', '=', $docType],
            ['deleted', '=', '0'],
        ])->first();

        if ($oldRelation?->document_template_id && $oldRelation?->document_template_id === $documentTemplateId) {
            // we don't need to update
            return;
        }

        if ($documentTemplateId === 0) {
            if ($oldRelation) {
                $this->deleteProductTplRelation($oldRelation);
                $this->productDocumentTemplateHistoryRepository->createProductDocumentLog($oldRelation);
            }

            return;
        }

        $oldRelation && $this->deleteProductTplRelation($oldRelation);

        $newRelation = new ProductDocumentTemplate();
        $newRelation->product_id = $product->getKey();
        $newRelation->document_template_id = $documentTemplateId;
        $newRelation->document_template_type = $docType;
        $newRelation->save();

        if($oldRelation){
            $this->productDocumentTemplateHistoryRepository->createProductDocumentLog(
                $oldRelation, $newRelation->document_template_id, $newRelation->document_template_type
            );
        }
    }

    public function deleteProductTplRelation(ProductDocumentTemplate $relation): void
    {
        // we use this for exclude update events in "delete" method
        DB::table('product_document_template')
            ->where('product_document_template_id', $relation->product_document_template_id)
            ->delete();

        event("eloquent.forceDeleted: " . ProductDocumentTemplate::class, $relation);
    }
}
