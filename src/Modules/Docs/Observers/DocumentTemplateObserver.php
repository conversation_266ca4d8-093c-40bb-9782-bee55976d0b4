<?php

namespace Modules\Docs\Observers;

use Modules\Common\Models\DocumentTemplate;
use Modules\Docs\Repositories\DocumentTemplateRepository;
use Modules\Docs\Services\DocumentTemplateRevisionService;
use RuntimeException;

class DocumentTemplateObserver
{
    protected DocumentTemplateRevisionService $documentTemplateRevisionService;
    protected DocumentTemplateRepository $documentTemplateRepository;

    public function __construct(
        DocumentTemplateRevisionService $documentTemplateRevisionService,
        DocumentTemplateRepository      $documentTemplateRepository
    ) {
        $this->documentTemplateRevisionService = $documentTemplateRevisionService;
        $this->documentTemplateRepository = $documentTemplateRepository;
    }

    public function creating(DocumentTemplate $documentTemplate)
    {
        // check for unique name
        $this->checkForUniqueName($documentTemplate);

        // check for unique active template types
        // could not exists 2 active at same time
        $this->checkForUniqueType($documentTemplate);
    }

    public function created(DocumentTemplate $documentTemplate)
    {
        $this->documentTemplateRevisionService->createLog($documentTemplate);
    }

    public function updating(DocumentTemplate $documentTemplate)
    {
        // check for unique name
        $this->checkForUniqueName($documentTemplate);

        // check for unique active template types
        // could not exists 2 active at same time
        $this->checkForUniqueType($documentTemplate);

        if (
            $documentTemplate->isDirty('type')
            || $documentTemplate->isDirty('content')
        ) {
            $this->documentTemplateRevisionService->updateLog($documentTemplate);
        }
    }

    private function checkForUniqueName(DocumentTemplate $tpl)
    {
        // important! we dont care on active flag in this case
        $tplCheck = DocumentTemplate::where([
            ['document_template_id', '!=', $tpl->document_template_id],
            ['name', '=', $tpl->name],
            ['deleted', '=', '0'],
        ])->first();

        if (!empty($tplCheck->document_template_id)) {
            throw new RuntimeException(__('docs::documentTemplateCrud.documentTemplateNameAlreadyExists'));
        }
    }

    private function checkForUniqueType(DocumentTemplate $tpl)
    {
        if (
            !in_array($tpl->type, DocumentTemplate::PRODUCT_RELATED_TEMPLATES)
            && $tpl->type != DocumentTemplate::TPL_OTHER
        ) {
            $tplCheck = DocumentTemplate::where([
                ['document_template_id', '!=', $tpl->document_template_id],
                ['type', '=', $tpl->type],
                ['active', '=', '1'],
                ['deleted', '=', '0'],
            ])->first();

            if (!empty($tplCheck->document_template_id)) {
                throw new RuntimeException(__('docs::documentTemplateCrud.uniqueTypeTemplateOnlyOneActiveAllowed'));
            }
        }
    }
}
