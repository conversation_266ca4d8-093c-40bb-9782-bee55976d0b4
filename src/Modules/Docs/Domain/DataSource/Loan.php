<?php

namespace Modules\Docs\Domain\DataSource;

use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\CreditLimit;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office as DbOffice;
use Modules\Communication\Repositories\EmailRepository;
use Modules\Docs\Enums\PlaceholderEnum as PE;
use Modules\Head\Repositories\LoanRepository as Repo;
use Modules\Head\Services\LoanService;
use StikCredit\Calculators\Calculator;

class Loan extends DomainModel implements DataSource
{
    private DbModel $dbModel;
    private ?LoanActualStats $stats;
    private DbOffice $dbOffice;

    private $loanStats = null;
    private $dueAmounts = null;
    private $loanOffice = null;
    private $loanOfficeCity = null;
    private $refReceivedAmount = null;
    private $instTotalDueAmount = null;

    private $varDueAmount = null;
    private $varCreditLimit = null;
    private $varRefDuedAmount = null;
    private $installmentsPrincipalInterest = null;
    private array $varExtendAmounts = [];

    public function __construct(
        private readonly Repo $repo,
        private Guarantor     $guarantor,
        private Guarantor     $guarantor2
    ) {}

    public function buildFromExisting(DbModel $dbModel): self
    {
        return $this->setDbModel($dbModel)->setGuarantors()->setStats()->setDbOffice();
    }

    public function setDbModel($dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    public function setGuarantors(): self
    {
        $this->guarantor->buildFromExisting($this->dbModel->getGuarantBySeqNum());
        $this->guarantor2->buildFromExisting($this->dbModel->getGuarantBySeqNum(2));

        return $this;
    }

    public function setStats(): self
    {
        $this->stats = $this->dbModel->loanActualStats;
        return $this;
    }

    public function setDbOffice(): self
    {
        $this->dbOffice = $this->dbModel->office;

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function guarantor(bool $second = false): ?Guarantor
    {
        return $second ? $this->guarantor2 : $this->guarantor;
    }

    public function guarantorAddress(bool $second = false): string
    {
        $guarant = $this->guarantor($second)->dbModel();
        $address = $guarant->getLastAddress();
        return !empty($address->address) ? $address->address : 'не е посочен';
    }

    public function guarantorIdcard(bool $second = false)
    {
        $guarant = $this->guarantor($second)->dbModel();
        $idCard = $guarant->idCard();
        return !empty($idCard->guarant_idcard_id) ? $idCard : null;
    }

    public function hasGuarant(): string
    {
        $guarant1 = $this->guarantor()->dbModel();
        if (!empty($guarant1->guarant_id)) {
            return 'x';
        }

        $guarant1 = $this->guarantor(true)->dbModel();
        if (!empty($guarant1->guarant_id)) {
            return 'x';
        }

        return '  ';
    }

    public function getEpayMoneyTookedAt(): string
    {
        $loan = $this->dbModel;

        // if not epay => do nothing
        if (!$loan->isPaymentMethodEasypay()) {
            return '';
        }

        // get outgoing payment
        // if no outgoing payment => do nothing
        $outPayment = $loan->getOutPayment();
        if (empty($outPayment->payment_id)) {
            return '';
        }

        // if not delivered => do nothing
        if (empty($outPayment->delivered_at)) {
            return '';
        }

        return Carbon::parse($outPayment->delivered_at)->format('d.m.Y H:i');
    }

    public function stats(): ?LoanActualStats
    {
        return $this->stats;
    }

    public function dbOffice(): DbOffice
    {
        return $this->dbOffice;
    }

    public function loanTerm(): array
    {
        return [
            'approved' => $this->dbModel->period_approved,
            'period' => $this->dbModel->getPeriod()
        ];
    }

    public function loanPaymentMethod(DbClient $dbClient): array
    {
        return [
            'method' => $this->dbModel->payment_method_id,
            'iban' => $dbClient->getMainBankAccountIban()
        ];
    }

    public function loanInstallmentListPrincipalInterest(): array
    {
        if (!is_null($this->installmentsPrincipalInterest)) {
            return $this->installmentsPrincipalInterest;
        }

        $this->installmentsPrincipalInterest = $this->dbModel->orderedInstallments->map(function (Installment $installment) {
            return [
                'due_date' => $installment->due_date,
                'sum' => $installment->principal + $installment->interest,
                'sum_eur' => amountEur($installment->principal + $installment->interest, ''),
            ];
        })->toArray();

        return $this->installmentsPrincipalInterest;
    }

    // usage: FormatService::byType(REPAYMENT_HTML)
    // tpl: src/Modules/Docs/Resources/views/document-template/repayment-schedule.blade.php
    public function loanRepaymentSchedule(): array
    {
        return $this->installmentStats(
            $this->dbModel->orderedInstallments()->get()
        );
    }

    // usage: FormatService::byType(REPAYMENT_NO_PENALTY_HTML)
    // tpl: src/Modules/Docs/Resources/views/document-template/repayment-schedule-no-penalty.blade.php
    public function loanRepaymentScheduleNoPenalty(): array
    {
        return $this->installmentStats(
            $this->dbModel->orderedInstallments()->get()
        );
    }

    // usage: FormatService::byType(ACTIVE_LOANS_HTML)
    // tpl: src/Modules/Docs/Resources/views/document-template/active-loans.blade.php
    public function activeLoanTable(): array
    {
        // get active loans
        // get id, total due, total paid, total rest, yearly rep
        $client = $this->dbModel->client;
        $loans = $client->getAllActiveLoans();
        if ($loans->count() < 1) {
            return [
                'loans' => [],
                'now' => Carbon::now()->format('d.m.Y'),
            ];
        }


        $result = [];
        foreach ($loans as $loan) {

            $amounts = DbModel::getCombinedAmountsForLoanId($loan->loan_id);
            $earlyRepAmount = intToFloat($loan->getEarlyRepaymentDebtDb());

            $result[] = [
                'loan_id' => $loan->loan_id,
                'due_amount_total' => number_format($amounts['due_amount_total'], 2, '.', ' '),
                'due_amount_total_eur' => amountEur($amounts['due_amount_total'], ''),
                'repaid_amount_total' => number_format($amounts['repaid_amount_total'], 2, '.', ' '),
                'repaid_amount_total_eur' => amountEur($amounts['repaid_amount_total'], ''),
                'outstanding_amount_total' => number_format($amounts['outstanding_amount_total'], 2, '.', ' '),
                'outstanding_amount_total_eur' => amountEur($amounts['outstanding_amount_total'], ''),
                'early_repayment_amount' => number_format($earlyRepAmount, 2, '.', ' '),
                'early_repayment_amount_eur' => amountEur($earlyRepAmount, ''),
            ];
        }


        return [
            'loans' => $result,
            'now' => Carbon::now()->format('d.m.Y'),
        ];
    }

    public function getTotalAccruedAmount()
    {
        if (!empty($this->varDueAmount)) {
            return $this->varDueAmount;
        }

        $allAmounts = $this->dbModel->getCombinedAmountsForLoanId($this->dbModel->loan_id);

        $this->varDueAmount = $allAmounts['accrued_amount_total'];

        return $this->varDueAmount;
    }

    public function getTotalAccruedAmountEur()
    {
       $dueAmount = $this->getTotalAccruedAmount();
       if (empty($dueAmount)) {
            return (float) 0;
       }

        return amountEur($dueAmount, '');
    }

    // Format: банка: Първа Инвестиционна Банка, IBAN: **********************, титуляр: Стик Кредит АД
    public function getOfficeBankDetails(): string
    {
        $result = '';

        $office = $this->dbOffice();
        $bankAccounts = $office->bankAccountsByPaymentMethod();

        if (empty($bankAccounts)) {
            return $result;
        }

        $company = env('COMPANY_NAME_BG', 'Стик Кредит АД');

        foreach ($bankAccounts as $ba) {
            $result .= sprintf(
                '<div>банка: %s, IBAN: %s, титуляр: ' . $company . '</div>',
                $ba->bank_name,
                $ba->account_iban,
            );
        }

        return $result;
    }

    // Format: **********************, **********************, etc
    public function getOfficeIbans(): string
    {
        $result = '';

        $office = $this->dbOffice();
        $bankAccounts = $office->bankAccountsByPaymentMethod();

        if (empty($bankAccounts)) {
            return $result;
        }

        $ibans = [];
        foreach ($bankAccounts as $ba) {
            $ibans[] = $ba->account_iban;
        }

        return implode(', ', $ibans);
    }

    private function installmentStats(?Collection $installments): array
    {
        $data = [];
        if (!$installments) {
            return $data;
        }
        foreach ($installments as $installment) {
            $data[$installment->seq_num] = [
                'principalInterestSum' => Calculator::sum($installment->principal, $installment->interest),
                'totalSum' => Calculator::sum($installment->principal, $installment->interest, $installment->penalty),
                'principal' => $installment->principal,
                'interest' => $installment->interest,
                'penalty' => $installment->penalty,
                'dueDate' => $installment->due_date,
            ];
        }
        return $data;
    }

    public function ipApplicationSubmit(): ?string
    {
        return $this->loanStats()?->creation_ip;
    }

    public function ipApplicationSign(): ?string
    {
        return $this->loanStats()?->sign_ip;
    }

    public function applicationSignDate(): ?Carbon
    {
        $signDate = $this->dbModel->getLastHistoryForStatus(LoanStatus::SIGNED_STATUS_ID)?->date;
        if (!empty($signDate)) {
            return Carbon::parse($signDate);
        }

        return null;
    }

    public function loanStats()
    {
        if (is_null($this->loanStats)) {
            $this->loanStats = $this->dbModel->loanActualStats;
        }

        return $this->loanStats;
    }

    public function getDueAmounts(): array
    {
        if (empty($this->dueAmounts)) {
            $this->dueAmounts = $this->dbModel->getDueAmounts();
        }

        return $this->dueAmounts;
    }

    public function getLoanOffice()
    {
        if (empty($this->loanOffice)) {
            $this->loanOffice = $this->dbModel->office;
        }

        return $this->loanOffice;
    }

    public function getLoanOfficeCityName()
    {
        if (empty($this->loanOfficeCity)) {
            $office = $this->getLoanOffice();
            $this->loanOfficeCity = $office->city?->name ?? '';
        }

        return $this->loanOfficeCity;
    }

    public function getLoanOfficeAddress(): string
    {
        $office = $this->getLoanOffice();
        $city = $this->getLoanOfficeCityName();

        return trim($city . ', ' . $office->address);
    }

    public function getLoanOfficeCity(): string
    {
        return $this->getLoanOfficeCityName();
    }

    public function getCommingDueInstallmentTotalAmount(): float
    {
        if (!is_null($this->instTotalDueAmount)) {
            return $this->instTotalDueAmount;
        }

        /** @var \Modules\Common\Models\Installment $inst **/
        $inst = $this->dbModel()->getUnpaidInstallments()->first();
        $this->instTotalDueAmount = (
            !empty($inst->installment_id)
            ? (float) $inst->getPrimaryTotalRestAmount()
            : 0
        );

        if (empty($this->instTotalDueAmount) || $this->instTotalDueAmount < 0) {
            $this->instTotalDueAmount = (float) 0.0;
        }

        return $this->instTotalDueAmount;
    }

    public function getCommingDueInstallmentTotalAmountBgn()
    {
        $amount = $this->getCommingDueInstallmentTotalAmount();
        if (empty($amount)) {
            return 0.0;
        }

        return number_format((float) $amount, 2, '.', ',');
    }

    public function getCommingDueInstallmentTotalAmountEur()
    {
        $amount = (float) $this->getCommingDueInstallmentTotalAmount();
        if (empty($amount)) {
            return 0.0;
        }

        return amountEur($amount, '');
    }

    public function getInstallmentWithLowestOverdueDate()
    {
        $installments = $this->dbModel()->getUnpaidInstallments();

        $today = Carbon::today();
        $oldInstallments = $installments->filter(function ($installment) use ($today) {
            return Carbon::parse($installment->due_date)->lt($today);
        });

        $oldestInstallment = $oldInstallments->sortBy('due_date')->first();
        if (!empty($oldestInstallment->due_date)) {
            return $oldestInstallment->due_date;
        }

        return null;
    }

    public function getInstallmentWithHighestOverdueDate()
    {
        $installments = $this->dbModel()->getUnpaidInstallments();

        $today = Carbon::today();
        $oldInstallments = $installments->filter(function ($installment) use ($today) {
            return Carbon::parse($installment->due_date)->lt($today);
        });

        $newestOverdueInstallment = $oldInstallments->sortByDesc('due_date')->first();
        if (!empty($newestOverdueInstallment->due_date)) {
            return $newestOverdueInstallment->due_date;
        }

        return null;
    }

    public function getInstallmentWithClosestDueDate()
    {
        $installments = $this->dbModel()->getUnpaidInstallments();

        $today = Carbon::today();
        $futureInstallments = $installments->filter(function ($installment) use ($today) {
            return Carbon::parse($installment->due_date)->gt($today);
        });

        $firstInstallment = $futureInstallments->sortBy('due_date')->first();
        if (!empty($firstInstallment->due_date)) {
            return $firstInstallment->due_date;
        }

        return null;
    }

    public function getValueByPlaceholder(PE $placeholder): mixed
    {
        return match ($placeholder) {
            PE::GUARANTOR_1_PHONE => $this->guarantor()->dbModel()->phone ?? '',
            PE::GUARANTOR_1_EMAIL => $this->guarantor()->dbModel()->email ?? '',
            PE::GUARANTOR_1_PIN => $this->guarantor()->dbModel()->pin ?? '',
            PE::GUARANTOR_1_ID_NUMBER => $this->guarantor()->dbModel()->idcard_number ?? '',
            PE::GUARANTOR_1_FIRST_NAME => $this->guarantor()->dbModel()->first_name ?? '',
            PE::GUARANTOR_1_MIDDLE_NAME => $this->guarantor()->dbModel()->middle_name ?? '',
            PE::GUARANTOR_1_SURNAME => $this->guarantor()->dbModel()->last_name ?? '',
            PE::GUARANTOR_1_ID_ISSUE_DATE => $this->guarantorIdcard()->issue_date ?? '',
            PE::GUARANTOR_1_ID_EXPIRATION_DATE => $this->guarantorIdcard()->valid_date ?? '',
            PE::GUARANTOR_1_ID_ISSUER_NAME => $this->guarantor()->dbModel()->idCardIssued->name ?? '',
            PE::GUARANTOR_1_ADDRESS_CURRENT,
            PE::GUARANTOR_1_ADDRESS_PERMANENT => $this->guarantorAddress(),
            PE::GUARANTOR_2_PHONE => $this->guarantor(true)->dbModel()->phone ?? '',
            PE::GUARANTOR_2_EMAIL => $this->guarantor(true)->dbModel()->email ?? '',
            PE::GUARANTOR_2_PIN => $this->guarantor(true)->dbModel()->pin ?? '',
            PE::GUARANTOR_2_ID_NUMBER => $this->guarantor(true)->dbModel()->idcard_number ?? '',
            PE::GUARANTOR_2_FIRST_NAME => $this->guarantor(true)->dbModel()->first_name ?? '',
            PE::GUARANTOR_2_MIDDLE_NAME => $this->guarantor(true)->dbModel()->middle_name ?? '',
            PE::GUARANTOR_2_SURNAME => $this->guarantor(true)->dbModel()->last_name ?? '',
            PE::GUARANTOR_2_ID_ISSUE_DATE => $this->guarantorIdcard(true)->issue_date ?? '',
            PE::GUARANTOR_2_ID_EXPIRATION_DATE => $this->guarantorIdcard(true)->valid_date ?? '',
            PE::GUARANTOR_2_ID_ISSUER_NAME => $this->guarantor(true)->dbModel()->idCardIssued->name ?? '',
            PE::GUARANTOR_2_ADDRESS_CURRENT,
            PE::GUARANTOR_2_ADDRESS_PERMANENT => $this->guarantorAddress(true),
            PE::APPLICATION_ID => $this->dbModel()->getKey(),
            PE::APPLICATION_DATE,
            PE::APPLICATION_DATE_2 => Carbon::parse($this->dbModel()->created_at)->format('d.m.Y'),
            PE::APPLICATION_TIME => Carbon::parse($this->dbModel()->created_at),
            PE::LOAN_AMOUNT => $this->dbModel()->amount_approved,
            PE::LOAN_AMOUNT_EUR => $this->getLoanAmountEur(),
            PE::LOAN_AMOUNT_IN_WRITING_SIMPLE,
            PE::LOAN_AMOUNT_IN_WRITING => intToFloat($this->dbModel()->amount_approved),

            PE::LOAN_INTEREST_RATE,
            PE::LOAN_INTEREST_RATE_IN_WRITING => $this->dbModel->interest_percent,

            PE::LOAN_INTEREST_RATE_PER_DAY,
            PE::LOAN_INTEREST_RATE_IN_WRITING_PER_DAY => round($this->dbModel->interest_percent / 360, 2),

            PE::LOAN_OUTSTANDING_AMOUNT_TOTAL => $this->loanStats()->outstanding_amount_total,
            PE::LOAN_GPR,
            PE::LOAN_GPR_IN_WRITING => $this->loanStats()->rate_annual_percentage,

            PE::TOTAL_DUE_PRINCIPAL_INTEREST,
            PE::TOTAL_DUE_PRINCIPAL_INTEREST_IN_WRITING => (
                $this->getDueAmounts()['due_amount_total_principal']
                + $this->getDueAmounts()['due_amount_total_interest']
            ),

            PE::TOTAL_DUE_PRINCIPAL_INTEREST_EUR => amountEur((
                $this->getDueAmounts()['due_amount_total_principal']
                + $this->getDueAmounts()['due_amount_total_interest']
            ), ''),

            PE::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY,
            PE::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY_IN_WRITING => (
                $this->getDueAmounts()['due_amount_total_principal']
                + $this->getDueAmounts()['due_amount_total_interest']
                + $this->getDueAmounts()['due_amount_total_penalty']
            ),

            PE::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY_EUR => amountEur((
                $this->getDueAmounts()['due_amount_total_principal']
                + $this->getDueAmounts()['due_amount_total_interest']
                + $this->getDueAmounts()['due_amount_total_penalty']
            ), ''),

            PE::TOTAL_DUE_INTEREST,
            PE::TOTAL_DUE_INTEREST_IN_WRITING => $this->getDueAmounts()['due_amount_total_interest'],
            PE::TOTAL_DUE_INTEREST_EUR => amountEur($this->getDueAmounts()['due_amount_total_interest'], ''),

            PE::TOTAL_DUE_PENALTY,
            PE::LOAN_PENALTY_SUM,
            PE::LOAN_PENALTY_SUM_IN_WRITING => $this->loanStats()->due_amount_total_penalty,
            PE::LOAN_PENALTY_SUM_EUR => amountEur($this->loanStats()->due_amount_total_penalty, ''),

            PE::LOAN_TERM,
            PE::LOAN_TERM_IN_WRITING => $this->loanTerm(),
            PE::LOAN_OVERDUE_INTEREST_RATE => toFloatWithTwoDecimals($this->dbModel->getLateInterestRateValue()),
            PE::LOAN_PAYMENT_METHOD => $this->loanPaymentMethod($this->dbModel->client),

            PE::LOAN_LAST_INSTALLMENT_DATE => Carbon::parse($this->stats()->last_installment_date),
            PE::LOAN_FIRST_INSTALLMENT_DATE => Carbon::parse($this->stats()->first_installment_date),

            PE::LOAN_INSTALLMENT_SUM => $this->getCommingDueInstallmentTotalAmountBgn(),
            PE::LOAN_INSTALLMENT_SUM_EUR => $this->getCommingDueInstallmentTotalAmountEur(),
            PE::LOAN_INSTALLMENT_LIST_PRINCIPAL_INTEREST => $this->loanInstallmentListPrincipalInterest(),
            PE::LOAN_PAYMENT_PERIOD => $this->dbModel()->getPeriod(),
            PE::LOAN_INSTALLMENT_COUNT,
            PE::LOAN_INSTALLMENT_COUNT_IN_WRITING => $this->stats()->total_installments_count,
            PE::LOAN_NEXT_UNPAID_INSTALLMENT_DATE => $this->getInstallmentWithClosestDueDate(),
            PE::LOAN_LAST_UNPAID_INSTALLMENT_DATE => $this->getInstallmentWithHighestOverdueDate(),
            PE::LOAN_EARLY_REPAYMENT_AMOUNT => (float)intToFloat($this->dbModel()->getEarlyRepaymentDebtDb()),
            PE::LOAN_REPAYMENT_SCHEDULE => $this->loanRepaymentSchedule(),
            PE::LOAN_REPAYMENT_SCHEDULE_NO_PENALTY => $this->loanRepaymentScheduleNoPenalty(),
            PE::ACTIVE_LOAN_TABLE => $this->activeLoanTable(),

            PE::OFFICE_NAME => $this->dbOffice()->name ?? '',
            PE::OFFICE_PHONE => $this->dbOffice()->phone ?? '',
            PE::OFFICE_ADDRESS => $this->dbOffice()->address ?? '',
            PE::OFFICE_BANK_DETAILS => $this->getOfficeBankDetails(),
            PE::COMPANY_OFFICE_IBAN => $this->getOfficeIbans(),

            PE::TEXT_MESSAGE_HISTORY_LEGAL => $this->dbModel()->smses,
            PE::IP_APPLICATION_SUBMIT => $this->ipApplicationSubmit() ?? '',
            PE::APPLICATION_SIGN_DATE,
            PE::APPLICATION_SIGN_TIME => $this->applicationSignDate(),
            PE::IP_APPLICATION_SIGN => $this->ipApplicationSign() ?? '',

            PE::COMPANY_OFFICE_ADDRESS => $this->getLoanOfficeAddress(),
            PE::COMPANY_OFFICE_ADDRESS_SETTLEMENT => $this->getLoanOfficeCity(),

            PE::DUE_AMOUNT => $this->getTotalAccruedAmount(),
            PE::DUE_AMOUNT_EUR => $this->getTotalAccruedAmountEur(),

            PE::LOAN_HAS_GUARANT => $this->hasGuarant(),
            PE::LOAN_EPAY_MONEY_TOOKED_AT => $this->getEpayMoneyTookedAt(),
            PE::LOAN_EMAIL_HISTORY => $this->dbModel()->emails,
            PE::LOAN_EMAIL_HISTORY_FOR_LEGAL => app(EmailRepository::class)
                ->getEmailsForLegalQuery($this->dbModel()->client_id, $this->dbModel()->getKey())->get(),

            PE::CREDIT_LIMIT => $this->getLoanCreditLimitBgn(),
            PE::CREDIT_LIMIT_EUR => $this->getLoanCreditLimitEur(),

            PE::REFINANCE_RECEIVE_AMOUNT => $this->getRefReceivedAmountBgn(),
            PE::REFINANCE_RECEIVE_AMOUNT_EUR => $this->getRefReceivedAmountEur(),
            PE::REFINANCE_DUE_AMOUNT => $this->getRefDueAmountBgn(),
            PE::REFINANCE_DUE_AMOUNT_EUR => $this->getRefDueAmountEur(),
            PE::LOAN_EXTEND_AMOUNT => $this->getLoanExtendAmountBgn(),
            PE::LOAN_EXTEND_AMOUNT_EUR => $this->getLoanExtendAmountEur(),
            PE::LOAN_EXTEND_AMOUNT_15_DAYS => $this->getLoanExtendAmountBgn(15),
            PE::LOAN_EXTEND_AMOUNT_15_DAYS_EUR => $this->getLoanExtendAmountEur(15),

            PE::MODB_AMOUNT => $this->getMinIncome(),
            PE::UNLZ_AMOUNT,
            PE::UNLZ_AMOUNT_IN_WRITING => $this->getTaxExistingDueAmount(),
            PE::UNLZ_AMOUNT_EUR => amountEur($this->getTaxExistingDueAmount(), ''),
            PE::FEE_AMOUNT,
            PE::FEE_AMOUNT_IN_WRITING => $this->getTaxCollectorAmount(),
            PE::FEE_AMOUNT_EUR => amountEur($this->getTaxCollectorAmount(), ''),
            PE::MAX_FEE_AMOUNT,
            PE::MAX_FEE_AMOUNT_IN_WRITING => $this->getMaxTaxCollectorAmount(),
            PE::MAX_FEE_AMOUNT_EUR => amountEur($this->getMaxTaxCollectorAmount(), ''),
            PE::SMS_AMOUNT => $this->getSmsAmount(),
            PE::SMS_AMOUNT_EUR => amountEur($this->getSmsAmount(), ''),
            PE::PO_AMOUNT,
            PE::PO_AMOUNT_IN_WRITING => $this->getPoAmount(),
            PE::PO_AMOUNT_EUR => amountEur($this->getPoAmount(), ''),

            PE::SIGN_DOCUMENTS_LINK => $this->generateDocumentSignLink(),
        };
    }

    private function getLoanCreditLimitBgn(): string
    {
        $creditLimitObj = $this->getCreditLimitObject();

        if (!empty($creditLimitObj->amount)) {
            return number_format((float) $creditLimitObj->amount, 2, '.', ',');
        }

        return '0';
    }

    private function getLoanCreditLimitEur(): string
    {
        $creditLimitObj = $this->getCreditLimitObject();

        if (!empty($creditLimitObj->amount)) {
            return amountEur($creditLimitObj->amount, '');
        }

        return '0';
    }

    private function getCreditLimitObject()
    {
        if (!empty($this->varCreditLimit)) {
            return $this->varCreditLimit;
        }

        $this->varCreditLimit = CreditLimit::where('loan_id', $this->dbModel()->loan_id)
            ->where('active', 1)
            ->orderBy('created_at', 'DESC')
            ->first();

        return $this->varCreditLimit;
    }

    private function getRefReceivedAmount(): float
    {
        if (!is_null($this->refReceivedAmount)) {
            return $this->refReceivedAmount;
        }


        $loan = $this->dbModel();
        $creditLimitObj = $this->getCreditLimitObject();

        $this->refReceivedAmount = (float) 0;
        if (!empty($creditLimitObj->amount)) {
            $creditLimit = floatToInt($creditLimitObj->amount);

            $refinancedAmount = $loan->getTotalAmountForRefinance();
            if ($refinancedAmount > 0) {
                $refinancedAmount = getMinRefinanceAmount($refinancedAmount);
            }

            $this->refReceivedAmount = intToFloat($creditLimit - $refinancedAmount);
        }

        return $this->refReceivedAmount;
    }

    private function getRefReceivedAmountBgn(): string
    {
        $amount = $this->getRefReceivedAmount();
        if (empty($amount)) {
            return '0.00';
        }

        return number_format($amount, 2, '.', ',');
    }

    private function getRefReceivedAmountEur(): string
    {
        $amount = $this->getRefReceivedAmount();
        if (empty($amount)) {
            return '0.00';
        }

        return amountEur($amount, '');
    }

    private function getRefDueAmount(): float
    {
        if (!is_null($this->varRefDuedAmount)) {
            return $this->varRefDuedAmount;
        }

        $this->varRefDuedAmount = (float) 0;

        $amount = $this->dbModel()->getTotalAmountForRefinance();
        if (empty($amount) || $amount < 0) {
            return $this->varRefDuedAmount;
        }

        $this->varRefDuedAmount = intToFloat($amount);

        return $this->varRefDuedAmount;
    }

    private function getRefDueAmountBgn(): string
    {
        return number_format($this->getRefDueAmount(), 2, '.', ',');
    }

    private function getRefDueAmountEur(): string
    {
        return amountEur($this->getRefDueAmount(), '');
    }

    private function getLoanExtendAmount(int $extendWithDays = 30): float
    {
        if (!empty($this->varExtendAmounts[$extendWithDays])) {
            return $this->varExtendAmounts[$extendWithDays];
        }

        $val = app(LoanService::class)->calculateExtendLoanFeeAmount(
            $this->dbModel(),
            $extendWithDays
        );
        $this->varExtendAmounts[$extendWithDays] = intToFloat($val);

        return $this->varExtendAmounts[$extendWithDays];
    }

    private function getLoanExtendAmountBgn(int $extendWithDays = 30): string
    {
        $amount = $this->getLoanExtendAmount($extendWithDays);
        if (empty($amount)) {
            return '0.00';
        }

        return number_format($amount, 2, '.', ',');
    }

    private function getLoanExtendAmountEur(int $extendWithDays = 30): string
    {
        $amount = $this->getLoanExtendAmount($extendWithDays);
        if (empty($amount)) {
            return '0.00';
        }

        return amountEur($amount, '');
    }

    private function getMinIncome(): string
    {
        $minIncome = 1500;
        return $minIncome . ' лв. / <span class="eur">€</span>' . amountEur($minIncome, '');
    }

    private function getTaxExistingDueAmount()
    {
        return 10;
    }

    private function getTaxCollectorAmount()
    {
        return 10;
    }

    private function getMaxTaxCollectorAmount()
    {
        return 120;
    }

    private function getSmsAmount()
    {
        return 0.30;
    }

    private function getPoAmount()
    {
        return 400;
    }

    private function getLoanAmountEur(): string
    {
        $loanAmount = intToFloat($this->dbModel()->amount_approved);
        return amountEur($loanAmount, '');
    }

    private function generateDocumentSignLink(): string
    {
        $loan = $this->dbModel();
        $inviteUrl = $loan->client->generateInviteUrl($loan->getKey(), $loan->loan_status_id);

        return config('company.website') . "vhod-hash/{$inviteUrl->hash}";
    }

    public function isSet(): bool
    {
        return isset($this->dbModel) && $this->dbModel->amount_requested;
    }
}
