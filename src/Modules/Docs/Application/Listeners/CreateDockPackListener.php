<?php

namespace Modules\Docs\Application\Listeners;

use Carbon\Carbon;
use Modules\Collect\Domain\Events\DueDatesWereReset;
use Modules\Common\Models\DocumentTemplate;
use Modules\Docs\Jobs\DocsPackGeneratingJob;
use Modules\Sales\Domain\Events\LoanParamsUpdated;
use Modules\Sales\Domain\Events\NewLoanHasArrived;

class CreateDockPackListener
{
    public function handle(NewLoanHasArrived|LoanParamsUpdated|DueDatesWereReset $event): string
    {
        $dbLoan = $event->loan->refresh();
        $notify = ($event instanceof DueDatesWereReset ? false : true);

        if (
            !$dbLoan->isNew()
            && !$dbLoan->isSigned()
            && !$dbLoan->isProcessing()
        ) {
            return 'fail';
        }

        if ($dbLoan->isOnlineLoan()) {
            $now = Carbon::now();
            $delayInSec = 3;

            DocsPackGeneratingJob::dispatch(
                    $dbLoan,
                    DocumentTemplate::DOC_PACK_NEW_LOAN,
                    $notify
                )
                ->onQueue('docs')
                ->delay($now->addSeconds($delayInSec));

        } else {

            DocsPackGeneratingJob::dispatchSync(
                $dbLoan,
                DocumentTemplate::DOC_PACK_NEW_LOAN,
                $notify
            );
        }

        return 'success';
    }
}
