<?php

namespace Modules\Docs\Application\Listeners;

use Illuminate\Support\Facades\Log;
use Modules\CashDesk\Events\CashOperationalTransactionSaved;
use Modules\Docs\Services\DocumentService;

/**
 * Used in:
 * - IncomingCashTransaction::save() -> throws CashOperationalTransactionSaved
 * - OutgoingCashTransaction::save() -> throws CashOperationalTransactionSaved
 */
class GenerateCashDocsListener
{
    public function __construct(private DocumentService $documentService) {}

    public function handle(CashOperationalTransactionSaved $data): void
    {
        try {
            $this->documentService->generateCashDeskDocument($data->cashOperationalTransaction);
        } catch (\Throwable $e) {
            Log::debug('GenerateCashDocsListener: ' . $e->getMessage());
        }
    }
}

