<?php

namespace Modules\Docs\Http\Controllers;

use Barryvdh\Snappy\Facades\SnappyPdf;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Loan;
use Modules\Common\Models\SmsLoginCode;
use Modules\Docs\Enums\PlaceholderEnum;
use Modules\Docs\Http\Requests\DocumentCopyRequest;
use Modules\Docs\Http\Requests\DocumentTemplateEditRequest;
use Modules\Docs\Http\Requests\DocumentTemplateSearchRequest;
use Modules\Docs\Services\DocumentService;
use Modules\Docs\Services\DocumentTemplateService;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;

class DocumentTemplateController extends BaseController
{
    protected DocumentTemplateService $documentTemplateService;
    protected DocumentService $documentService;
    protected ProductService $productService;

    protected string $pageTitle = 'Document template list';
    protected string $indexRoute = 'docs.documentTemplate.list';
    protected string $editRoute = 'docs.documentTemplate.edit';

    public function __construct(
        DocumentTemplateService $documentTemplateService,
        DocumentService $documentService,
        ProductService $productService,
        private readonly ProductTypeService $productTypeService = new ProductTypeService
    ) {
        $this->documentTemplateService = $documentTemplateService;
        $this->documentService = $documentService;
        $this->productService = $productService;
        parent::__construct();
    }

    public function varsTest(Request $request)
    {
        $vars = [];
        foreach (PlaceholderEnum::cases() as $name => $value) {
            $vars[] = $value->value;
        }

        $varsValues = [];
        $loanId = (int) $request->query('loan_id');
        if (!empty($loanId)) {

            $loan = Loan::find($loanId);

            // need to prepare test var
            $code = SmsLoginCode::where('client_id', $loan->client_id)
                ->where('used', '0')
                ->first();
            if (empty($code)) {
                $code = new SmsLoginCode();
                $code->phone_number = '0891111111';
                $code->ip_requested = '***********';
                $code->browser_requested = 'Kur Browser 19.0.1';
                $code->client_id = $loan->client_id;
                $code->code = 12345;
                $code->used = 0;
                $code->save();
            }

            $varsValues = $this->documentService->getAllVars($loan);
        }

        return view(
            'docs::document-template.varTest',
            [
                'vars' => $vars,
                'varsValues' => $varsValues,
            ]
        );
    }

    public function list(DocumentTemplateSearchRequest $request)
    {
        $this->checkForRequestParams($request);

        return view(
            'docs::document-template.list',
            [
                'documentTemplates' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
                'docTempTypes' => DocumentTemplate::getTypes()
            ]
        );
    }

    public function create()
    {
        $productGroups = $this->productTypeService->all();
        $docTempTypes = DocumentTemplate::getUniqueTypes();
        $variables = PlaceholderEnum::getAllDocVarsGrouped();

        return view(
            'docs::document-template.crud',
            compact('variables', 'productGroups', 'docTempTypes')
        );
    }

    public function store(DocumentTemplateEditRequest $request): RedirectResponse
    {
        $template = $this->documentTemplateService->create($request->validated());

        return redirect()
            ->route($this->editRoute, $template->document_template_id)
            ->with('success', __('docs::documentTemplateCrud.documentTemplateCreatedSuccessfully'));
    }

    public function edit(DocumentTemplate $documentTemplate)
    {
        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $docTempTypes = DocumentTemplate::getUniqueTypes();
        $productGroups = $this->productTypeService->all();

        return view(
            'docs::document-template.crud',
            compact('variables', 'documentTemplate', 'productGroups', 'docTempTypes')
        );
    }

    public function downloadPdfTemplate(DocumentTemplate $documentTemplate)
    {
        $pdf = SnappyPdf::loadView('docs::pdf.view_snappy', ['content' => $documentTemplate->content]);
        return $pdf->download('doc_tpl_type_' . time() . '.pdf');
    }

    public function editHtml(DocumentTemplate $documentTemplate)
    {
        // return $this->downloadPdfTemplate($documentTemplate);

        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $docTempTypes = DocumentTemplate::getUniqueTypes();
        $productGroups = $this->productTypeService->all();

        return view(
            'docs::document-template.crud_html',
            compact('variables', 'documentTemplate', 'productGroups', 'docTempTypes')
        );
    }

    public function update(
        DocumentTemplate $documentTemplate,
        DocumentTemplateEditRequest $request
    ): RedirectResponse {

        $this->documentTemplateService->update($documentTemplate, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('docs::documentTemplateCrud.documentTemplateUpdatedSuccessfully'));
    }

    public function delete(DocumentTemplate $documentTemplate): RedirectResponse
    {
        $this->documentTemplateService->delete($documentTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('docs::documentTemplateCrud.documentTemplateDeletedSuccessfully'));
    }

    public function enable(DocumentTemplate $documentTemplate): RedirectResponse
    {
        $this->documentTemplateService->enable($documentTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('docs::documentTemplateCrud.documentTemplateEnabledSuccessfully'));
    }

    public function disable(DocumentTemplate $documentTemplate): RedirectResponse
    {
        $this->documentTemplateService->disable($documentTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('docs::documentTemplateCrud.documentTemplateDisabledSuccessfully')
            );
    }

    protected function checkForRequestParams(DocumentTemplateSearchRequest $request)
    {
        if ($request->exists(
            [
                'name',
                'type',
                'active',
                'created_at',
                'created_by',
                'updated_at',
                'updated_by',
            ]
        )) {
            $this->cleanFilters();
            $this->setFilters($request);
        }
    }

    public function setFilters(DocumentTemplateSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    public function getTableData()
    {
        return $this->documentTemplateService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    public function refresh(DocumentTemplateSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'docs::document-template.list-table',
            [
                'documentTemplates' => $this->getTableData(),
            ]
        )->render();
    }

    public function createCopy(DocumentTemplate $documentTemplate, DocumentCopyRequest $request): RedirectResponse
    {
        $this->documentTemplateService->createDocumentCopy($documentTemplate, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('docs::documentTemplateCrud.documentTemplateEnabledSuccessfully'));
    }
}
