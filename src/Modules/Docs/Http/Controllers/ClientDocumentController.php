<?php

namespace Modules\Docs\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Docs\Http\Requests\ClientDocumentRequest;
use Modules\Docs\Services\ClientDocumentService;

class ClientDocumentController extends BaseController
{
    public function __construct(private ClientDocumentService $clientDocumentService) {
        parent::__construct();
    }

    public function upload(ClientDocumentRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $file = $data['document'];
        $clientId = $data['client_id'];
        $loanId = $data['loan_id'] ?? null;

        $documentType = FileTypeEnum::from($data['document_type']);
        $comment = $data['comment'] ?? null;

        $this->clientDocumentService->upload($file, $clientId, $documentType, $loanId, $comment);

        return back()->with('success', 'Успешно качване на файл.')->withFragment('#clientdocuments');
    }
}
