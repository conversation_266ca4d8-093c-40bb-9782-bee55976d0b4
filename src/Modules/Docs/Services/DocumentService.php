<?php

namespace Modules\Docs\Services;

use Barryvdh\Snappy\Facades\SnappyPdf;
use Barryvdh\DomPDF\Facade\Pdf;

use App\Exceptions\FrontEndExceptions;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Repositories\CashOperationalDocumentRepository;
use Modules\Common\Enums\DiskEnum;
use Modules\Common\Helpers\OtherHelper;
use Modules\Common\Models\Document;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\File as FileEntity;
use Modules\Common\Models\FileType;
use Modules\Common\Models\Loan;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\StorageService;
use Modules\Common\Traits\ValidationTrait;
use Modules\Docs\Domain\Template;
use Modules\Docs\Enums\PlaceholderEnum as PE;
use Modules\Docs\Repositories\DocumentRepository;
use Modules\Docs\Repositories\DocumentTemplateRepository;
use Modules\Head\Repositories\FileRepository;
use RuntimeException;
use Throwable;

/**
 * - generate client docs
 * - generate law docs
 * - generate cash desk orders
 */
class DocumentService extends BaseService
{
    use ValidationTrait;

    public const LEGAL_INFO_EXPORT_FILE_NAME = 'legal_export';
    private const FILE_EXT = 'pdf';
    private const PAGE_BREAK_HTML = '<div style="page-break-after: always !important;"></div>';

    private $loanService = null;
    private $storageService = null;

    private $cashOperationalDocumentRepository = null;
    private $clientRepository = null;
    private $fileRepository = null;
    private $loanActualStatsRepository = null;
    private $mailTemplateRepository = null;

    public function __construct(
        private readonly DocumentRepository         $documentRepository,
        private readonly DocumentTemplateRepository $documentTemplateRepository
    ) {
        parent::__construct();
    }

    //////////////////////// GET already generated DOCS(Content as PDF) ////////////////////////////

    /**
     * Used in:
     * - ClientCardController::printDocuments()
     *
     * @param  Loan   $loan
     * @param  string $printAlias - could be doc template type OR doc pack name
     * @return string - url for download
     */
    public function getLoanDocs(Loan $loan, string $printAlias): string
    {
        if (!empty(DocumentTemplate::DOCUMENT_PACKS[$printAlias])) {
            return $this->getLoanDocsByLoanAndPackName(
                $loan,
                $printAlias
            );
        }

        return $this->getLoanDocByLoanAndDocType(
            $loan,
            $printAlias
        );
    }

    // used in: GenerateLegalDocsForLoansAction
    public function getFilePathForDoc(Loan $loan, string $docTplType): ?string
    {
        // get document_template_id
        $docTpl = $this->documentTemplateRepository->getByTplTypeAndProductId(
            $docTplType,
            $loan->product_id
        );
        if (empty($docTpl)) {
            return '';
        }

        $lastDocument = DB::table('document')
            ->where('loan_id', $loan->loan_id)
            ->where('document_template_id', $docTpl->document_template_id)
            ->orderBy('document_id', 'desc')
            ->first();
        if (!$lastDocument) {
            return '';
        }

        $fileData = DB::table('file')
            ->where('file_id', $lastDocument->file_id)
            ->select('file_path', 'file_name')
            ->first();
        if (!$fileData) {
            return '';
        }

        return $fileData->file_path . $fileData->file_name;
    }

    private function getLoanDocsByLoanAndPackName(
        Loan $loan,
        string $packName = DocumentTemplate::DOC_PACK_NEW_LOAN
    ): string {

        // so we know templates we need and counts
        $docTemplates = $this->getDocPackTemplates($packName);
        if (empty($docTemplates)) {
            throw new Exception('Could not find template names for doc.pack: ' . $packName);
        }


        // remove Tariff doc for online, only for offices
        if ($loan->isOnlineLoan() && isset($docTemplates[DocumentTemplate::TPL_TARIFF])) {
            unset($docTemplates[DocumentTemplate::TPL_TARIFF]);
        }


        // get last docs
        $docTypes = array_keys($docTemplates);
        $lastDocuments = $this->getLastDocsByLoanIdAndDocTypes(
            $loan->loan_id,
            $docTypes
        );
        if (empty($lastDocuments)) {
            throw new Exception('Failed to find docs for loan #' . $loan->loan_id . ', docs: ' . implode(',', $docTypes));
        }


        // prepare common string contains all docs separated by page breaks
        $contentArr = [];
        foreach ($docTemplates as $tplName => $tplCopies) {
            $docContent = '!' . $tplName . ' = NONE !';
            if (isset($lastDocuments[$tplName])) {
                $docContent = $lastDocuments[$tplName];
            }

            for ($i = 0; $i < $tplCopies; $i++) {
                $contentArr[] = $docContent;
            }
        }
        $commonContent = implode(self::PAGE_BREAK_HTML, $contentArr);


        return $this->savePdfAndReturnUrl($commonContent, 'all_docs.pdf');
    }

    private function getLoanDocByLoanAndDocType(Loan $loan, string $docType = ''): string
    {
        if (empty($docType)) {
            throw new Exception('Not provided doc type for downloading');
        }

        // get last docs
        $lastDocuments = $this->getLastDocsByLoanIdAndDocTypes(
            $loan->loan_id,
            [$docType]
        );
        if (empty($lastDocuments[$docType])) {
            throw new Exception('No doc for loan #' . $loan->loan_id . ', doc type: ' . $docType);
        }

        return $this->savePdfAndReturnUrl(
            $lastDocuments[$docType],
            $docType . '.pdf',
            useSnappy: ($docType === DocumentTemplate::TPL_LEGAL_INFO) // custom PDF lib
        );
    }

    private function getLastDocsByLoanIdAndDocTypes(
        int $loanId,
        array $docTypes
    ): array {

        $rows = DB::select("
            WITH ranked_rows AS (
                select
                    document.document_id,
                    document.content,
                    document_template.type,
                    DENSE_RANK() OVER (PARTITION BY document_template.type ORDER BY document.created_at DESC) AS row_num
                FROM document
                JOIN document_template ON document.document_template_id = document_template.document_template_id
                WHERE
                    document.loan_id = " . $loanId . "
                    AND document_template.type IN ('" . implode("','", $docTypes) . "')
            )
            SELECT content, type
            FROM ranked_rows
            WHERE row_num = 1;
        ");
        if (count($rows) < 1) {
            return [];
        }

        $result = [];
        foreach ($rows as $row) {
            $result[$row->type] = $row->content;
        }

        return $result;
    }

    /////////////////////////// GENERATE VARS ////////////////////////////

    public function getAllVars(Loan $loan): array
    {
        $varsWithValues = app(Template::class)
            ->setLoan($loan)
            ->buildForAllVars()
            ->placeholderValues();

        return $varsWithValues;
    }

    ////////////////////////// GENERATE DOCS /////////////////////////////

    /**
     * Used in:
     * - DocsPackGeneratingJob - on loan creating or params updating before approve
     * - ClientCardController::generateDocuments() - for tests only - to regenerate docs
     *
     * @param Loan $loan
     * @param string $packName
     *
     * @return array - array with created filepath(s)
     *
     * @throws Exception
     */
    public function generatePdfDocumentsPackForLoan(
        Loan   $loan,
        string $packName
    ): array {

        // get doc template names
        $docTemplates = $this->getDocPackTemplates($packName);
        if (empty($docTemplates)) {
            throw new Exception('Could not find template names for doc.pack: ' . $packName);
        }

        $productNotRelated = [];
        foreach ($docTemplates as $docTemplateName => $count) {
            if (!in_array($docTemplateName, DocumentTemplate::PRODUCT_RELATED_TEMPLATES)) {
                $productNotRelated[] = $docTemplateName;
            }
        }


        // get all templates
        $templates = $this->documentTemplateRepository->getTemplatesByLoanAndTplTypes(
            $loan,
            array_keys($docTemplates)
        );
        if (!empty($productNotRelated)) {
            foreach ($productNotRelated as $docTplName) {
                $tpl = $this->documentTemplateRepository->getByTplTypeAndProductId($docTplName);
                if (!empty($tpl->document_template_id)) {
                    $templates[] = $tpl;
                }
            }
        }
        if (empty($templates)) {
            throw new Exception('Could not find templates for doc.pack: ' . $packName);
        }


        // prepare array with values for any var
        $varsWithValues = [];
        foreach ($templates as $template) {
            $varsForOneTemplate = app(Template::class)
                ->setLoan($loan)
                ->buildFromExisting($template)
                ->placeholderValues();

            foreach ($varsForOneTemplate as $key => $value) {
                $varsWithValues[$key] = $value;
            }
        }


        // create pdf file and prepare content for downloading if allowed
        $files = [];
        foreach ($templates as $template) {
            $filepath = $this->prepareContentAndSaveFileAndDocument(
                $template,
                $loan,
                $varsWithValues
            );

            $files[] = $filepath;
        }


        return $files;
    }

    /**
     * Used in:
     * - DocumentController::generateDocument() - for tests only, when we want to check generated content
     */
    public function generatePdfDocumentForLoanByTemplateId(
        Loan $loan,
        int  $templateId,
        bool $getUrl = false
    ): string|false {

        $template = $this->documentTemplateRepository->getById($templateId);
        if (!$template) {
            return false;
        }

        return $this->generatePdfDocumentForLoanByTemplate(
            $loan,
            $template,
            $getUrl
        );
    }

    /**
     * Used in:
     * - LetterNotificationController
     * - GenerateLegalDocsForLoansAction
     */
    public function generatePdfDocumentForLoanByTemplate(
        Loan $loan,
        DocumentTemplate $template,
        bool $getUrl = false,
        array $varsCustom = []
    ): string|false {

        if (empty($template->document_template_id)) {
            return false;
        }

        // get values for all vars
        $varsWithValues = app(Template::class)
            ->setLoan($loan)
            ->buildFromExisting($template)
            ->setPredefinedValues($varsCustom)
            ->placeholderValues();


        // TODO: remove? This doesn't work (always false) because "show_dots" doesn't exist in DB
        if ($loan->show_dots) {
            $varsWithValues = $this->replaceEmptyWithDots($varsWithValues);
        }

        // prepare content
        $content = $this->generateHtmlDocumentFromTplAndVars($template, $varsWithValues);
        if (empty($content)) {
            return false;
        }

        $filePath = $this->storeContentAsPdfFile(
            $loan,
            $template,
            $content
        );

        if (empty($filePath)) {
            return false;
        }

        if ($getUrl) {
            return $this->savePdfAndReturnUrl(
                $content,
                useSnappy: ($template->type === DocumentTemplate::TPL_LEGAL_INFO) // custom PDF lib
            );
        }

        return $filePath;
    }

    /**
     * Used in:
     * - GenerateCashDocsListener - from CashOperationalTransactionSaved (incoming/outgoing)
     *
     */
    public function generateCashDeskDocument(CashOperationalTransaction $cashOperationalTransaction): array
    {
        try {
            $docTplType = $cashOperationalTransaction->transaction_type->template();

            $template = $this->documentTemplateRepository->getByTplTypeAndProductId(
                $docTplType
            );
        } catch(\Throwable $e) {
            throw new Exception('generateCashDesk: ' . $e->getMessage());
        }
        if (empty($template->document_template_id)) {
            throw new Exception('Failed to find active template for type: ' . $docTplType);
        }


        // prepare content
        try {
            // Save document with no file and content
            $newDocument = $this->getCashOperDocRepo()->create([
                'document_template_id' => $template->getKey(),
                'cash_operational_transaction_id' => $cashOperationalTransaction->cash_operational_transaction_id,
            ]);

            // prepare vars
            $vars = $this->getVarsForCashDeskDoc(
                $cashOperationalTransaction,
                $template,
                $newDocument->cash_operational_documents_id
            );

            $html = $this->processHtmlForTransactionDocument(
                $template,
                $vars
            );


        } catch (\Throwable $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.storeDocumentProblem') . '(1)', $e);
        }


        // create directory
        try {
            $dir = $this->getStorageService()->makeDir(
                $cashOperationalTransaction->transaction_type->value
            );
        } catch (\Throwable $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.dirForDocsNotCreateSuccessfully'), $e);
        }


        // Save file
        $fileName =
            $cashOperationalTransaction->cash_operational_transaction_id . '_' .
            $cashOperationalTransaction->transaction_type->value . '_' . time();
        $fileNameFull = $fileName . '.' . self::FILE_EXT;

        try {
            $filePath = $this->getStorageService()->generate(
                $fileName,
                ['content' => $html, 'client_id' => '9999999',],
                // TODO: remove client_id after getPdfContent() done in Storage service
                self::FILE_EXT,
                $dir
            );
            if (!file_exists(storage_path($dir) . $fileNameFull)) {
                throw new RuntimeException(__('Failed to save file: ' . $fileNameFull));
            }
        } catch (\Throwable $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.pdfNotStoreSuccessfully'), $e);
        }

        // Save file relation
        try {
            $file = $this->saveFileEntity(
                $dir,
                $fileNameFull,
                $template->type
            );
        } catch (\Throwable $e) {
            throw new RuntimeException('Failed to save file entity, file: ' . $fileNameFull, $e);
        }

        try {
            $newDocument->file_id = $file->file_id;
            $newDocument->save();
        } catch (\Throwable $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.storeDocumentProblem') . '(2)', $e);
        }


        $data = [];
        $data['cash_operational_document'] = $newDocument;
        $data['html'] = $html;

        return $data;
    }

    /////////////////////////// HELPER METHODS ////////////////////////////

    private function getVarsForCashDeskDoc(
        CashOperationalTransaction $cashOperationalTransaction,
        DocumentTemplate           $template,
        int                        $cashOperationalDocumentId
    ): array {

        $varsEur = [
            // EUR
            PE::CD_PAYMENT_AMOUNT_EUR->value => amountEur((float) intToFloat($cashOperationalTransaction->amount)), // payment_amount_eur
            PE::CD_PAID_PRINCIPLE_EUR->value => 0, // paid_principal_eur
            PE::CD_PAID_INTEREST_EUR->value => 0, // paid_interest_eur
            PE::CD_PAID_PENALTY_EUR->value => 0, // paid_penalty_eur
            PE::CD_PAID_LATE_INTEREST_EUR->value => 0, // paid_late_interest_late_penalty_eur
            PE::CD_PAID_TAXES_EUR->value => 0, // paid_taxes_eur
        ];

        $vars = [
            // common for all trasactions
            PE::CD_ORDER_NUMBER->value => $cashOperationalDocumentId, // order_number
            PE::CD_TRANSAC_DATE->value => Carbon::parse($cashOperationalTransaction->created_at)->format('d-m-Y'), // transaction_date
            PE::CD_BASIS->value => $cashOperationalTransaction->basis, // basis
            PE::CD_PAYMENT_AMOUNT->value => amount($cashOperationalTransaction->amount), // payment_amount
            PE::CD_PAYMENT_AMOUNT_IN_WRITING->value => numberToBgWords((float) intToFloat($cashOperationalTransaction->amount)), // payment_amount_in_writing

            // payment delivery
            PE::CD_PAID_PRINCIPLE->value => 0, // paid_principal
            PE::CD_PAID_INTEREST->value => 0, // paid_interest
            PE::CD_PAID_PENALTY->value => 0, // paid_penalty
            PE::CD_PAID_LATE_INTEREST->value => 0, // paid_late_interest_late_penalty
            PE::CD_PAID_TAXES->value => 0, // paid_taxes

            // client related
            PE::CLIENT_PIN->value => '',
            PE::CLIENT_ID_NUMBER->value => '',
            PE::CLIENT_FIRST_NAME->value => '',
            PE::CLIENT_MIDDLE_NAME->value => '',
            PE::CLIENT_SURNAME->value => '',
            PE::CLIENT_ID_ISSUE_DATE->value => '',
            PE::CLIENT_ID_ISSUER_NAME->value => '',
            PE::CLIENT_PERMANENT_ADDRESS_FULL->value => '',

            // company related
            PE::COMPANY_PIN->value => config('company.bulstat'), // company_name
            PE::COMPANY_NAME->value => config('company.name_bg'), // company_pin

            // these vars are always empty!
            PE::CD_REPRESENTOR_OR_FIRM->value => '', // representator_or_firm
            PE::CD_ATTORNEY_NUMBER->value => '', // attorney_number
            PE::CD_ATTORNEY_DATE->value => '', // attorney_date
        ];

        // ПКО разпределяне на сума по: главница, лихва и тн
        if ($cashOperationalTransaction->payment) {
            $payment = $cashOperationalTransaction->payment;

            if (!empty($payment->delivery)) {
                if (is_string($payment->delivery)) {
                    $payment->delivery = json_decode($payment->delivery);
                }

                foreach ($payment->delivery as $item) {
                    $key = $this->getPaymentColumnNameByAlias($item['column']);
                    $rKey = 'paid_' . $key;

                    $amount = (float) intToFloat($item['amount']);
                    if ($amount > 0) {
                        $vars[$rKey] = $amount;
                    }
                }

                if (!empty($vars['paid_late_penalty'])) {
                    $vars[PE::CD_PAID_TAXES->value] += $vars['paid_late_penalty'];
                }
            }
        }

        // add eur values
        foreach ($varsEur as $keyEur => $valEur) {
            if (substr($keyEur, -4) !== '_eur') {
                continue;
            }

            if (empty($valEur)) {
                $keyEurBase = substr($keyEur, 0, -4);
                $valEur = amountEur($vars[$keyEurBase], '');
            }

            $vars[$keyEur] = $valEur;
        }

        // Сетване на клиентски данни
        if ($cashOperationalTransaction->loan) {
            $client = $cashOperationalTransaction?->loan?->client;

            if (!empty($client->client_id)) {
                $vars['client_pin'] = $client->pin;
                $vars['client_id_number'] = $client->idcard_number;
                $vars['client_first_name'] = $client->first_name;
                $vars['client_middle_name'] = $client->middle_name;
                $vars['client_surname'] = $client->last_name;
                $vars['client_permanent_address_full'] = $this->clientAddressFull($client);

                $idCard = $client->clientLastIdCard();
                if (!empty($idCard->issue_date)) {
                    $vars['client_id_issue_date'] = $idCard->issue_date;
                    $vars['client_id_issuer_name'] = $idCard?->idCardIssued?->name ?? 'МВР';
                }
            }
        }

        return $vars;
    }

    private function getPaymentColumnNameByAlias(string $alias): string
    {
        return match ($alias) {
            'outstandingPrincipleAmount' => 'principal',
            'outstandingInterestAmount' => 'interest',
            'outstandingPenaltyAmount' => 'penalty',
            'outstandingLateInterestAmount' => 'late_interest',
            'outstandingLatePenaltyAmount' => 'late_penalty',
            default => 'taxes',
        };
    }

    public function clientAddressFull($client): string
    {
        $properties = [
            'municipality' => 'общ.',
            'district' => 'обл.',
            'location' => null,
            'building_number' => null,
            'building_entrance' => 'вх.',
            'building_floor' => 'ет.',
            'building_apartment' => 'ап.',
        ];

        $lastAddress = $client->clientLastAddressIdcard();

        $finalArray = [];
        foreach ($properties as $propertyKey => $propertyValue) {
            if (!empty($lastAddress->$propertyKey)) {
                $finalArray[$propertyKey] = (!empty($propertyValue) ? $propertyValue : '') . $lastAddress->$propertyKey;
            }
        }

        // if nothing in location, but we have an address, let use it
        if (empty($finalArray['location']) && !empty($lastAddress->address)) {
            return $lastAddress->address;
        }

        if (empty($finalArray)) {
            return '';
        }

        return implode(', ', $finalArray);
    }

    private function processHtmlForTransactionDocument(
        DocumentTemplate $template,
        array            $data
    ): string {
        return $this->replaceVariables(
            $template->content,
            $data
        );
    }

    private function prepareContentAndSaveFileAndDocument(
        DocumentTemplate $template,
        Loan             $loan,
        array            $varsWithValues
    ): ?string {

        $content = $this->generateHtmlDocumentFromTplAndVars(
            $template,
            $varsWithValues
        );
        if (empty($content)) {
            return null;
        }

        $filePath = $this->storeContentAsPdfFile(
            $loan,
            $template,
            $content,
            $this->getVarsFromAllVars(
                $template->variables(),
                $varsWithValues
            )
        );

        return $filePath;
    }

    private function replaceEmptyWithDots(array $array): array
    {
        foreach ($array as $i => $val) {
            if (!$val) {
                $array[$i] = '................................';
            }
        }

        return $array;
    }

    private function replaceVariables(string $documentTemplateContent, array $variables): string
    {
        $data = collect(Arr::dot($variables))->mapWithKeys(static function ($item, $key) {
            return ['{' . $key . '}' => $item];
        })->toArray();

        return strtr($documentTemplateContent, $data);
    }

    private function generateHtmlDocumentFromTplAndVars(
        DocumentTemplate $template,
        array            $varsWithValues
    ): string {
        try {
            $content = $template->content ?? '';
            if (empty($content)) {
                return '';
            }
        } catch (Throwable $e) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateNotFound'),
                $e
            );
        }

        return $this->replaceVariables(
            $content,
            $varsWithValues
        );
    }

    private function getDocPackTemplates(string $docPack): array
    {
        if (empty($docPack)) {
            return [];
        }

        if (empty(DocumentTemplate::DOCUMENT_PACKS[$docPack])) {
            return [];
        }

        return DocumentTemplate::DOCUMENT_PACKS[$docPack];
    }

    private function getVarsFromAllVars(
        array $varKeys,
        array $varsWithValues
    ): array {
        if (empty($varKeys)) {
            return [];
        }

        $result = [];
        foreach ($varKeys as $varKey) {
            if (isset($varsWithValues[$varKey])) {
                $result[$varKey] = $varsWithValues[$varKey];
            }
        }

        return $result;
    }

    private function saveFileEntity(
        string $filePath,
        string $fileDocName,
        string $templateType,
        Loan   $loan = null
    ): FileEntity {
        $size = Storage::disk('local')->size($filePath . $fileDocName); // TODO!!!!!!!!!!
        $fileType = FileType::where(['name' => $templateType, 'active' => '1'])->first();

        if (empty($fileType->file_type_id)) {
            $fileType = new FileType;
            $fileType->name = $templateType;
            $fileType->save();
        }

        $createFileData = [
            'file_storage_id' => Document::DOCUMENT_FILE_STORAGE_ID,
            'file_type_id' => $fileType->file_type_id,
            'hash' => $fileDocName,
            'file_path' => $filePath,
            'file_size' => $size,
            'file_type' => $fileType->name,
            'file_name' => $fileDocName,
        ];

        $file = $this->getFileRepo()->create($createFileData);
        if (empty($file->file_id)) {
            throw new RuntimeException('Failed to save a file: ' . $fileDocName);
        }

        return $file;
    }

    /**
     * Receive template and prepared content as string
     * Safe PDF file in storage
     * Save file entity in DB
     * Save document entity in DB
     * Return filepath to saved file
     *
     * use FileService->create instead like in GetLawDocsForLoansAction
     */
    public function storeContentAsPdfFile(
        Loan             $loan,
        DocumentTemplate $template,
        string           $content,
        array            $vars = []
    ): string {

        $filePath = '';

        // define vars for saving a file
        try {
            $path = $template->type;
            $dir = $this->getStorageService()->makeDir(
                $path
            );
            $fileName = $loan->client_id . '_' . $loan->getKey() . '_' . $template->name . '_' . time();
            $fileNameFull = $fileName . '.' . self::FILE_EXT;
        } catch (Exception $e) {
            throw new FrontEndExceptions(__('docs::documentTemplateCrud.dirForDocsNotCreateSuccessfully'), $e);
        }

        // save file in storage
        $filePath = $this->getStorageService()->generate(
            $fileName,
            ['content' => $content],
            self::FILE_EXT,
            $dir,
            ($template->type === DocumentTemplate::TPL_LEGAL_INFO) // custom PDF lib
        );

        if (!file_exists(storage_path($dir) . '/' . $fileNameFull)) {
            throw new RuntimeException(__('Failed to save file: ' . $fileNameFull));
        }

        // save file entity in DB
        try {
            $file = $this->saveFileEntity(
                $dir,
                $fileNameFull,
                $template->type,
                $loan
            );
            if (empty($file->file_id)) {
                throw new RuntimeException(__('docs::document.documentFileStoreFail'));
            }
        } catch (Exception $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.pdfNotStoreSuccessfully'), $e);
        }

        // save document entity in DB
        try {
            $document = new Document();
            $document->fill([
                'document_template_id' => $template->document_template_id,
                'file_id' => $file->file_id,
                'loan_id' => $loan->getKey(),
                'client_id' => $loan->client_id,
                'content' => $content,
                'variables' => json_encode($vars),
            ]);
            $this->documentRepository->save($document);
        } catch (Exception $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.storeDocumentProblem'), $e);
        }

        return $filePath;
    }

    public function checkUndefinedVars(string $content, bool $exception = true): array
    {
        $matches = OtherHelper::getVariablesFromText($content);

        if (count($matches) && $exception) {
            throw new FrontEndExceptions(
                'In document template, variables are not defined: ' . implode(', ', $matches)
            );
        }

        return $matches;
    }

    private function savePdf(
        string $content,
        string $filename = 'document.pdf',
        string $pathToView = StorageService::PATH_TO_PDF_TEMPLATE,
        bool $useSnappy = false,
        DiskEnum $disk = DiskEnum::DocumentsPdf,
    ): string {
        $this->checkUndefinedVars($content);
        // $content = preg_replace("/mso-.+?:\s*?.*?[;\"']/s", '', $content); // Can be useful in the future

        $filename = hrtime(true) . "-$filename";
        $storage = Storage::disk($disk->value);

        if ($useSnappy) {
            $pdf = SnappyPdf::loadView(
                StorageService::PATH_TO_PDF_SNAPPY_TEMPLATE,
                ['content' => $content],
            );
            $pdf->save($storage->path($filename));
        } else {
            $pdf = PDF::loadView(
                $pathToView,
                ['content' => $content],
            );
            $pdf->save($filename, $disk->value);
        }

        if (!$storage->fileExists($filename)) {
            throw new RuntimeException(__('Failed to save file: ' . $storage->path($filename)));
        }

        return $filename;
    }

    private function savePdfAndReturnUrl(
        string $content,
        string $filename = 'document.pdf',
        string $pathToView = StorageService::PATH_TO_PDF_TEMPLATE,
        bool $useSnappy = false,
        DiskEnum $disk = DiskEnum::DocumentsPdf,
    ): string {
        return Storage::disk($disk->value)->url($this->savePdf($content, $filename, $pathToView, $useSnappy, $disk));
    }

    /////////////////////////// Fix document template vars  ////////////////////////////////

    // Used in:
    // - src/Modules/Docs/Console/FixDocumentVariables - manual correction of variables in templates
    // - src/Modules/Docs/Services/DocumentTemplateService::create()/update() - auto correction of existing vars
    public function fixDocumentTemplateVariables(DocumentTemplate $template): void
    {
        $variables = $this->checkUndefinedVars($template->content, false);
        $template->variables = json_encode(array_values($variables), JSON_THROW_ON_ERROR);
        $template->save();
    }

    /////////////////////////////// GETTERS //////////////////////////////

    private function getStorageService(): StorageService
    {
        if (null === $this->storageService) {
            $this->storageService = app(StorageService::class);
        }

        return $this->storageService;
    }

    private function getCashOperDocRepo(): CashOperationalDocumentRepository
    {
        if (null === $this->cashOperationalDocumentRepository) {
            $this->cashOperationalDocumentRepository = app(CashOperationalDocumentRepository::class);
        }

        return $this->cashOperationalDocumentRepository;
    }

    private function getFileRepo(): FileRepository
    {
        if (null === $this->fileRepository) {
            $this->fileRepository = app(FileRepository::class);
        }

        return $this->fileRepository;
    }
}
