<?php

namespace Modules\Docs\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\FileStorage;
use Modules\Common\Models\FileType;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\StorageService;
use Modules\Common\Models\ClientDocument;
use Modules\Docs\Repositories\ClientDocumentRepository;
use Modules\Head\Repositories\FileRepository;
use Modules\Head\Services\LoanService;
use RuntimeException;
use Throwable;

class ClientDocumentService extends BaseService
{
    public function __construct(
        private ClientDocumentRepository $clientDocumentRepository,
        private StorageService $storageService,
        private FileRepository $fileRepository,
        private LoanService $loanService
    ) {
        parent::__construct();
    }

    public function uploadVeriffVideo(
        $stream,
        int $clientId,
        FileTypeEnum $documentType,
        int $loanId = null,
        ?string $mediaId = null,
        ?string $comment = null
    ): ClientDocument {
        try {
            $path = StorageService::CLIENT_DOC_PATH . $clientId . '/';
            $name = 'verif_image_' . $mediaId . '.webm';

            Storage::put($path . $name, $stream);

            $fileData = [];
            $fileData['hash'] = md5(time());
            $fileData['file_path'] = $path;
            $fileData['file_size'] = filesize(storage_path($path . $name));
            $fileData['file_type'] = mime_content_type(storage_path($path . $name));
            $fileData['file_name'] = $name;
            $fileData['file_type_id'] = FileType::FILETYPE_ID_CARD_ID;
            $fileData['file_storage_id'] = FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID;
            $fileData['comment'] = $comment;

            $savedFile = $this->fileRepository->create($fileData);
            if (empty($savedFile->file_id)) {
                throw new RuntimeException('Failed to save file: ' . $path . $name);
            }

            $loan = null;
            if ($loanId) {
                $loan = $this->loanService->getLoanById($loanId);
            }

            $document['loan_id'] = $loan?->loan_id;
            $document['client_id'] = $clientId;
            $document['file_id'] = $savedFile->file_id;
            $document['verif_doc_id'] = $mediaId;

            return $this->clientDocumentRepository->create($document);

        } catch (Throwable $e) {
            $msg = $e->getMessage() . ', file: ' . $e->getFile() . ':' . $e->getLine();
            \Log::error($msg);

            throw new RuntimeException(__('docs::documentTemplateCrud.storeDocumentProblem'), $e);
        }
    }

    public function uploadVerifDocuments(
        $stream,
        int $clientId,
        FileTypeEnum $documentType,
        int $loanId = null,
        ?string $mediaId = null,
        ?string $comment = null
    ): ClientDocument {
        try {
            $path = StorageService::CLIENT_DOC_PATH . $clientId . '/';
            $name = 'verif_image_' . $mediaId . '.jpg';

            Storage::put($path . $name, $stream);

            $fileData = [];
            $fileData['hash'] = md5(time());
            $fileData['file_path'] = $path;
            $fileData['file_size'] = filesize(storage_path($path . $name));
            $fileData['file_type'] = mime_content_type(storage_path($path . $name));
            $fileData['file_name'] = $name;
            $fileData['file_type_id'] = FileType::getIdFromCode($documentType);
            $fileData['file_storage_id'] = FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID;
            $fileData['comment'] = $comment;

            $savedFile = $this->fileRepository->create($fileData);
            if (empty($savedFile->file_id)) {
                throw new RuntimeException('Failed to save file: ' . $path . $name);
            }

            $loan = null;
            if ($loanId) {
                $loan = $this->loanService->getLoanById($loanId);
            }

            $document['loan_id'] = $loan?->loan_id;
            $document['client_id'] = $clientId;
            $document['file_id'] = $savedFile->file_id;
            $document['verif_doc_id'] = $mediaId;

            return $this->clientDocumentRepository->create($document);
        } catch (Throwable $e) {
            $msg = $e->getMessage() . ', file: ' . $e->getFile() . ':' . $e->getLine();
            \Log::error($msg);

            throw new RuntimeException(__('docs::documentTemplateCrud.storeDocumentProblem'), $e);
        }
    }

    public function alreadyUploaded(int $client_id, string $verifDocId): bool
    {
        return ClientDocument::where('client_id', $client_id)->where('verif_doc_id', $verifDocId)->exists();
    }

    public function upload(
        UploadedFile $file,
        int $clientId,
        FileTypeEnum $documentType,
        int $loanId = null,
        ?string $comment = null
    ): ?ClientDocument {

        try {
            $fileStore = $this->storageService->uploadClientDoc($clientId, $file);
        } catch (Throwable $e) {
            throw new RuntimeException(__('docs::documentTemplateCrud.storeDocumentProblem'), $e);
        }

        $fileData = [];
        $fileData['hash'] = Hash::make($file->getClientOriginalName());
        $fileData['file_path'] = $fileStore[0];
        $fileData['file_size'] = $file->getSize();
        $fileData['file_type'] = $file->getClientMimeType();
        $fileData['file_name'] = $fileStore[1];
        $fileData['file_type_id'] = FileType::getIdFromCode($documentType);
        $fileData['file_storage_id'] = FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID;
        $fileData['comment'] = $comment;

        $savedFile = $this->fileRepository->create($fileData);
        if (empty($savedFile->file_id)) {
            throw new RuntimeException('Failed to save file: ' . $fileStore[1]);
        }

        $document = [];
        $document['client_id'] = $clientId;
        $document['loan_id'] = $loanId;
        $document['file_id'] = $savedFile->file_id;

        return $this->clientDocumentRepository->create($document);
    }
}
