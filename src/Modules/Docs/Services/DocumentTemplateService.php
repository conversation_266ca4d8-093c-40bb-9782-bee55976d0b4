<?php

namespace Modules\Docs\Services;

use Illuminate\Support\Str;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\WherePipeline\DataWrapper;
use Modules\Common\Services\WherePipeline\WherePipeline;
use Modules\Common\Traits\ValidationTrait;
use Modules\Docs\Repositories\DocumentTemplateRepository;
use HTMLPurifier;
use HTMLPurifier_Config;
use RuntimeException;

class DocumentTemplateService extends BaseService
{
    use ValidationTrait;


    public function __construct(
        private readonly DocumentTemplateRepository $documentTemplateRepository,
        private readonly DocumentService $documentService
    ) {
        parent::__construct();
    }

    public function getById(int $id): DocumentTemplate
    {
        $documentTemplate = $this->documentTemplateRepository->getById($id);
        if (!$documentTemplate) {
            throw new RuntimeException(__('docs::documentTemplateCrud.documentTemplateNotFound'));
        }

        return $documentTemplate;
    }
    /**
     * @return DocumentTemplate
     */
    public function create(array $data)
    {
        if (isset($data['key'])) {
            $data['key'] = Str::of($data['key'])->slug('_');
        }

        try {

            $documentTemplate = $this->documentTemplateRepository->create($data);
            $this->documentService->fixDocumentTemplateVariables($documentTemplate);

        } catch (\Exception $e) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateCreationFailed'),
                $e
            );
        }

        return $documentTemplate;
    }

    /**
     * @param array $data
     */
    public function update(DocumentTemplate $documentTemplate, array $data)
    {
        if (isset($data['key'])) {
            $data['key'] = Str::of($data['key'])->slug('_');
        }

        if (!empty($data['content'])) {
            $data['content'] = trim($data['content']);

//            $config = HTMLPurifier_Config::createDefault();
//            $purifier = new HTMLPurifier($config);
//            $data['content'] = $purifier->purify($data['content']);
        }

        try {
            $this->documentTemplateRepository->edit($documentTemplate, $data);
            $this->documentService->fixDocumentTemplateVariables($documentTemplate);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateUpdateFailed'),
                $e
            );
        }
    }

    public function delete(DocumentTemplate $documentTemplate)
    {
        try {
            $this->documentTemplateRepository->delete($documentTemplate);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateDeletionFailed'),
                $e
            );
        }
    }

    public function enable(DocumentTemplate $documentTemplate)
    {
        if ($documentTemplate->isActive()) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateEnableForbidden')
            );
        }

        try {
            $this->documentTemplateRepository->enable($documentTemplate);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateEnableFailed'),
                $e
            );
        }
    }

    public function disable(DocumentTemplate $documentTemplate)
    {
        if (!$documentTemplate->isActive()) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateDisableForbidden')
            );
        }

        try {
            $this->documentTemplateRepository->disable($documentTemplate);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('docs::documentTemplateCrud.documentTemplateDisableFailed'),
                $e
            );
        }
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     *
     * @throws \Exception
     */
    public function getByFilters(int $limit, array $data)
    {
        return $this->documentTemplateRepository->getAll(
            (new WherePipeline(new DataWrapper($data), DocumentTemplate::class))->run(),
            $limit,
            $this->getJoins($data),
            [
                'active' => 'DESC',
                'document_template_id' => 'DESC',
            ]
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getGroupedTemplates(): array
    {
        $allTemplates = [];

        foreach (DocumentTemplate::PRODUCT_RELATED_TEMPLATES as $type) {
            $allTemplates[$type] = $this->documentTemplateRepository->getGroupedByType($type);
        }

        return $allTemplates;
    }

    public function createDocumentCopy(DocumentTemplate $documentTemplate, array $data): DocumentTemplate
    {
        $data['type'] = $documentTemplate->type;
        $data['description'] = $documentTemplate->description;
        $data['content'] = $documentTemplate->content;
        $data['variables'] = $documentTemplate->variables;
        $data['product_type_id'] = $documentTemplate->product_type_id;

        return $this->documentTemplateRepository->createCopy($data);
    }
}
