<?php

namespace Modules\Docs\Services;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\BankAccount;
use Modules\Common\Models\Email;
use Modules\Common\Models\PaymentMethod;

class FormatService
{
    public static function byType(mixed $value, string $type)
    {
        return match ($type) {
            'READY_AMOUNT' => $value,
            'PROPER_NAME' => ucfirst($value),
            'DATE_INTL' => formatDate($value, 'd.m.Y') ?? '',
            'DATE_BG' => showDateBg(is_string($value) ? $value : $value->format('Y-m-d')),
            'TIME' => $value?->format('H:i:s') ?? '',
            'AMOUNT' => amount($value, ''),
            'NUMBER_WRITING' => numberToText((float)$value) ?? '',
            'NUMBER_OF_LEV' => numberToBgWords((float)$value) ?? '',
            'WORDED_PERCENT' => floatToWord($value) . ' ' . (checkIfEndsWithOne($value) ? __('docs::document.percentOne') : __('docs::document.percent')),
            'REPAYMENT_HTML' => view('docs::document-template.repayment-schedule', ['data' => $value])->render(),
            'REPAYMENT_NO_PENALTY_HTML' => view('docs::document-template.repayment-schedule-no-penalty', ['data' => $value])->render(),
            'ACTIVE_LOANS_HTML' => view('docs::document-template.active-loans', ['data' => $value])->render(),
            'IMAGE' => self::getImage($value),
            'ID_CARD_HTML' => self::getIdCardBothSides($value),
            'EMAIL_HISTORY_HTML' => self::getLoanEmailHistoryHtml($value),
            'EMAIL_HISTORY_FOR_LEGAL_HTML' => self::getLoanEmailHistoryHtmlForLegal($value),
            'text_message_history_legal' => view('docs::document-template.sms-history', ['smses' => $value])->render(),
            'loan_term' => $value['approved'] . ' ' . trans_choice('product::product.keys.' . $value['period'], $value['approved'], [], 'bg'),
            'loan_term_in_writing' => numberToText($value['approved']) . ' ' . trans_choice('product::product.keys.' . $value['period'], $value['approved'], [], 'bg'),
            'loan_payment_method' => self::loanPaymentMethod($value),
            'loan_instalment_list_principal_interest' => self::loanInstallmentListPrincipalInterest($value),
            'loan_payment_period' => __('docs::document.periods.' . $value),
            'loan_instalment_count_in_writing' => numberToText($value, false, true),
            'office_bank_details' => $value,
            'application_sign_date' => $value?->format('Y-m-d') ?? '',
            'client_id_card_picture', 'client_id_card_signature' => $value ? 'data:image/png;base64, ' . $value : '',
            'poll' => self::poll(),
            default => $value,
        };
    }

    /**
     * @param Collection|Email[] $emails
     * @return string
     */
    private static function getLoanEmailHistoryHtml($emails): string
    {
        if ($emails->count() < 1) {
            return '-';
        }

        $html = '';
        foreach ($emails as $email) {
            $html .= '<p>Дата: ' . $email->created_at . '</p>';
            $html .= '<p>Заглавие: ' . $email->title . '</p>';
            $html .= '<p>' . $email->body . '</p>';
            $html .= '<p>&nbsp;</p>';
        }

        return $html;
    }

    /**
     * @param Collection|Email[] $emails
     * @return string
     */
    private static function getLoanEmailHistoryHtmlForLegal(Collection $emails): string
    {
        if ($emails->count() < 1) {
            return '-';
        }

        $html = <<<HTML
<style>
    iframe {
        width: 100%;
    }
    body{
        zoom:1.33;
    }
</style>
<script>
    function resizeIframe(id) {
        let iframe = document.getElementById(id);
        iframe.style.height = (iframe.contentWindow.document.body.scrollHeight - 450) + 'px';
    }
</script>
HTML;
        $counter = 1;
        foreach ($emails as $email) {
            $frame = 'email-frame-' . $counter;
            $html .= "<p>Имейл $counter - \"$email->title\" - Изпратен на " . $email->created_at->format('d.m.Y в H:i:s') . " часа</p>";

            $html .= "<iframe style='border: none;' scrolling='no' id='$frame' onLoad='resizeIframe(\"$frame\")' srcdoc=\"" . htmlentities($email->body) . "\"></iframe>";

            if ($email->emailFiles()->exists()) {
                $content = '<div style="text-align: left"><p>Прикачени файлове</p><ul>';

                /** @var \Modules\Common\Models\File $attachment **/
                foreach ($email->emailFiles as $attachment) {
                    $content .= "<p>{$attachment->getCustomDocument()->getHumanFileName()}</p>";
                }

                $content .= '</ul></div>';

                $html .= $content;
            }

            if ($counter < $emails->count()) {
                $html .= '<div class="page-break"></div>';
            }
            $counter++;
        }

        return $html;
    }

    private static function getIdCardBothSides($value): string
    {
        return '
            <style>
                .mvr-card {
                    background-color: #f2e9ea;
                    border-radius: 10px;
                    padding: 15px;
                }
                .front {
                    margin: 15px 0;
                }
                .mvr-card-img {
                    max-width: 180px;
                }
                .image-container {
                    display: inline-block;
                    vertical-align: middle;
                }
                .image-container img {
                    height: auto;
                }
                .mvr-card-info-wrapper {
                    display: inline-block;
                    vertical-align: top;
                    padding-left: 20px;
                }
                .mvr-card-value {
                    display: inline-block;
                    font-weight: 700;
                }
                .mvr-card-label {
                    display: inline-block;
                    color: #6b6967;
                    font-size: 13px;
                    margin-right: 10px;
                }
                .mvr-card-img-signiture {
                    max-width: 180px;
                    margin-left: 20px;
                }
            </style>
            <div style="display: block">
                <div class="mvr-card front">
                    <div class="image-container">
                    ' . (
                        !empty($value['client_id_card_picture'])
                        ? '<img src="data:image/png;base64, ' . $value['client_id_card_picture'] . '" class="mvr-card-img">'
                        :  ''
                    ) . '
                    </div>
                    <div class="mvr-card-info-wrapper">
                        <div>
                            <div class="mvr-card-label">Фамилия</div>
                            <div class="mvr-card-value">' . ($value['client_surname'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Surname</div>
                            <div class="mvr-card-value">' . ($value['client_surname_latin'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Име</div>
                            <div class="mvr-card-value">' . ($value['client_first_name'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Name</div>
                            <div class="mvr-card-value">' . ($value['client_first_name_latin'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Презиме</div>
                            <div class="mvr-card-value">' . ($value['client_middle_name'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Father`s name</div>
                            <div class="mvr-card-value">' . ($value['client_middle_name_latin'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">ЕГН/Personal No</div>
                            <div class="mvr-card-value">' . ($value['client_pin'] ?? '') . '</div>
                            <div class="mvr-card-label" style="margin-left:30px">Пол/Sex</div>
                            <div class="mvr-card-value">' . ($value['client_sex'] ?? '') . '/' . ($value['client_sex_latin'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Гражданство/Nationality</div>
                            <div class="mvr-card-value">' . ($value['client_nationality'] ?? '') . '/' . ($value['client_nationality_latin'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Дата на раждане/Date of birth</div>
                            <div class="mvr-card-value">' . ($value['client_birth_day'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Валидност/Date of expiry</div>
                            <div class="mvr-card-value">' . ($value['client_id_expiration_date'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Номер на документа/Document number</div>
                            <div class="mvr-card-value">' . ($value['client_id_number'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Подпис/Signature</div>
                            <br/>
                            ' . (
                                !empty($value['client_id_card_picture'])
                                ? '<img src="data:image/png;base64, ' . $value['client_id_card_signature'] . '" class="mvr-card-img-signiture">'
                                :  ''
                            ) . '
                        </div>
                    </div>
                </div>
                <div class="mvr-card back">
                    <div class="mvr-card-info-wrapper">
                        <div>
                            <div class="mvr-card-label">Фамилия/Surname</div>
                            <div class="mvr-card-value">' . ($value['client_surname'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Място на раждане/Place of birth</div>
                            <div class="mvr-card-value">' . ($value['client_birthplace'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label" style="vertical-align: top;">Постоянен адрес/Residence</div>
                            <div class="mvr-card-value" style="display: block;">' . ($value['client_permanent_address'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Ръст/Height</div>
                            <div class="mvr-card-value">' . ($value['client_height'] ?? '') . '</div>
                            <div class="mvr-card-label" style="margin-left: 30px">Цвят на очите/Color of eyes</div>
                            <div class="mvr-card-value">' . ($value['client_eye_color'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Издаден от/ Authority</div>
                            <div class="mvr-card-value">' . ($value['client_id_card_authority'] ?? '') . '</div>
                        </div>
                        <div>
                            <div class="mvr-card-label">Дата на издаване/Date of issue</div>
                            <div class="mvr-card-value">' . ($value['client_id_issue_date'] ?? '') . '</div>
                        </div>
                    </div>
                </div>
            </div>
        ';
    }

    private static function getImage(string $path): string
    {
        return '<img src="' . $path . '" />';
    }

    private static function officeBankDetails(?BankAccount $officeBank): string
    {
        return $officeBank->bank_account_id
            ? "<p>Име: $officeBank->name<br>IBAN: $officeBank->account_iban<br>Телефон: $officeBank->phone</p>"
            : '';
    }

    private static function loanInstallmentListPrincipalInterest(array $values): string
    {
        $str = '';
        foreach ($values as $vals) {
            $str .= '<div>'
                 . $vals['due_date']->format('d.m.Y') . ' '
                 . $vals['sum'] . ' лв.'
                 . '/ <span class="eur">€</span>' .$vals['sum_eur'] . '</span>'
                 . '</div>';
        }

        return $str;
    }

    private static function loanPaymentMethod(array $values): string
    {
        return match ($values['method']) {
            PaymentMethod::PAYMENT_METHOD_BANK => __('docs::document.paymentMethodBank', ['iban' => $values['iban']]),
            PaymentMethod::PAYMENT_METHOD_EASYPAY => __('docs::document.paymentMethodEasypay'),
            PaymentMethod::PAYMENT_METHOD_CASH => __('docs::document.paymentMethodCash'),
        };
    }

    private static function poll(): string
    {
        return view('docs::document-template.poll', [
            'hash' => null,
            'length' => env('POLL_LENGTH') ?? 10,
            'websiteUrl' => env('POLL_WEBSITE_URL') ?? ''
        ])->render();
    }
}
