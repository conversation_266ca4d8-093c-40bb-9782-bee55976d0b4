<?php

namespace Modules\Common\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Role;
use Modules\Common\Observers\ModelObserver;
use Schema;

/**
 * @property Administrator|null $creator
 * @property Administrator|null $updater
 * @property Administrator|null $processedBy
 * @property Administrator|null $handler
 * @property Administrator|null $deleter
 * @property int $deleted_by
 * @property int $updated_by
 * @property int $disabled_by
 * @property int $enabled_by
 * @property int $deleted
 * @property int $active
 * @property Carbon $deleted_at
 * @property Carbon $updated_at
 * @property Carbon $disabled_at
 * @property Carbon $enabled_at
 */
trait BaseModelTrait
{
    protected $traitCasts = [
        'active' => 'boolean',
        'deleted' => 'boolean',
        'created_at' => 'datetime:d-m-Y H:i',
        'updated_at' => 'datetime:d-m-Y H:i',
        'deleted_at' => 'datetime:d-m-Y H:i',
        'enabled_at' => 'datetime:d-m-Y H:i',
        'disabled_at' => 'datetime:d-m-Y H:i',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
        'enabled_by' => 'integer',
        'disabled_by' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        self::observe(ModelObserver::class);
    }


    public function delete(): bool
    {
        // Check if isForceDeleting method exists (only available with SoftDeletes trait)
        $isForceDeleting = method_exists($this, 'isForceDeleting') ? $this->isForceDeleting() : false;
        if ($isForceDeleting || !Schema::hasColumn($this->getTable(), 'deleted_by')) {
            return parent::delete();
        }

        $this->disable(false);
        $this->deleted_by = getAdminId();
        $this->deleted_at = Carbon::now();
        $this->deleted = 1;
        $this->active = 0;

        return $this->save();
    }

    public function disable(bool $save = true): bool
    {
        if (Schema::hasColumn($this->getTable(), 'updated_by')) {
            $this->updated_by = getAdminId();
            $this->updated_at = Carbon::now();
        }

        $this->disabled_at = Carbon::now();
        $this->disabled_by = getAdminId();
        $this->active = 0;

        if ($save) {
            $this->save();
        }

        return true;
    }

    public function enable(): void
    {
        $this->updated_by = getAdminId();
        $this->updated_at = Carbon::now();
        $this->enabled_at = Carbon::now();
        $this->enabled_by = getAdminId();
        $this->active = 1;
        $this->save();
    }

    public function isActive(): bool
    {
        return (1 == $this->active);
    }

    public function isActiveLabel(): string
    {
        return $this->isActive() ? __('table.Active') : __('table.NotActive');
    }

    public function isInactive(): bool
    {
        return !$this->isActive();
    }

    public function isDeleted(): bool
    {
        return (1 == $this->deleted);
    }

    public function newCollection(array $models = [])
    {
        return new CustomEloquentCollection($models);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'created_by', 'administrator_id')->withTrashed();
    }

    /**
     * Get the last admin that updated the item.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'updated_by', 'administrator_id');
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'processed_by', 'administrator_id');
    }

    public function handler(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'handled_by', 'administrator_id');
    }

    public function deleter(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'deleted_by', 'administrator_id');
    }

    public function getCreateAdmin()
    {
        return $this->getAdminName('creator');
    }

    public function getUpdateAdmin()
    {
        return $this->getAdminName('updater');
    }

    public function getHandleAdmin(): string
    {
        return $this->getAdminName('handler');
    }

    public function getDeleteAdmin()
    {
        return $this->getAdminName('deleter');
    }

    public function getAdminName(string $type = 'creator'): string
    {
        return (is_null($this->{$type}) ? '' : $this->{$type}->first_name . ' ' . $this->{$type}->last_name);
    }

    public static function getAdminId(): int
    {
        $admin = \Auth::user();
        if (empty($admin->administrator_id)) {
            return Administrator::SYSTEM_ADMINISTRATOR_ID;
        }

        return $admin->administrator_id;
    }

    public function getMaxPriority(): int
    {
        $admin = getAdmin();
        $prirority = $admin?->getPriority() ?? 0;

        return !empty($prirority) ? $prirority : Role::PRIORITY_MIN;
    }

    public function adopt(string $relation, $ids, $detaching = true)
    {
        if (!method_exists($this, $relation)) {
            throw new NotFoundException(
                'The relation ' . $relation . ' do not exists.', 500
            );
        }

        $changes = $this->$relation()->sync($ids, $detaching);
        if (empty($changes['attached']) && empty($changes['detached']) && empty($changes['updated'])) {
            return;
        }

        return $changes;
    }

    public static function getTableName(): string
    {
        /** @var static $instance */
        $instance = app(static::class);

        return $instance->getTable();
    }
}
