<?php

namespace Modules\Common\Tests\Traits;

use Faker\Factory;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Testing\TestResponse;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanActivationSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\BankAccount;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\Contact;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\ProductSetting;
use Modules\Head\Services\ExtendLoanService;
use Modules\Head\Services\LoanService;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;
use Modules\Product\Repository\ProductRepository;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;

trait CreateTestLoanTrait
{
    public NewAppAction $newAppAction;
    public ?Loan $loan;

    public function createTestLoan(array $data = []): self
    {
        $productRepository = app(ProductRepository::class);
        $faker = Factory::create();

        $officeId = Office::OFFICE_ID_WEB;
        if (!empty($data['office_id'])) {
            $officeId = $data['office_id'];
        }

        if ($officeId == Office::OFFICE_ID_WEB) {
            $productId = $faker->numberBetween(1, 2);
        } else {
            $productId = $faker->numberBetween(3, 4);
        }

        if (!empty($data['product_id'])) {
            $productId = $data['product_id'];
        }

        $product = $productRepository->getProductById($productId);
        $minAmount = $product->getSettingValueByKey(ProductSetting::MIN_AMOUNT_KEY);
        $maxAmount = $product->getSettingValueByKey(ProductSetting::MAX_AMOUNT_KEY);

        $minPeriod = $product->getSettingValueByKey(ProductSetting::MIN_PERIOD_KEY);
        if (!$minPeriod) {
            $minPeriod = 3;
        }
        $maxPeriod = $product->getSettingValueByKey(ProductSetting::MAX_PERIOD);
        if (!$maxPeriod) {
            $maxPeriod = 12;
        }
        $loanPeriod = $faker->numberBetween(intval($minPeriod), intval($maxPeriod));
        if (!empty($data['loan_period'])) {
            $loanPeriod = $data['loan_period'];
        }

        //// create loan data
        $idCardData = ClientIdCard::factory(1)->make();

        $clientSeqData = new Sequence(
            ['phone' => [$faker->numerify('0999######'), $faker->numerify('0999######')]]
        );
        $clientData = Client::factory(1)->make($clientSeqData);
        echo 'Process pin: ' . $clientData->first()->pin . "\n\n";
        $clientAddressData = ClientAddress::factory(1)->make();
        $clientGuarantData = Guarant::factory(1)->make();
        $clientContactData = Contact::factory(2)->make();

        $data = [
            "loan" => [
                "source_id" => $faker->numberBetween(1, 5),//old
                "channel_id" => $faker->numberBetween(1, 4),//old
                "administrator_id" => 1,//old
                "product_id" => $productId,
                "loan_sum" => floatToInt($faker->randomElement(range((int) $minAmount, (int) $maxAmount, 50))),
                "loan_period" => $loanPeriod,
                "discount_percent" => 0, ///$faker->numberBetween(1, 15),
                "office_id" => Office::OFFICE_ID_WEB,
                "payment_method" => PaymentMethod::PAYMENT_METHOD_EASYPAY,
                "iban" => $faker->iban('BG'),
                "client_id" => null,
                "comment" => "kpomentariy test"
            ],
            'client' => $clientData->first()->toArray(),
            'client_idcard' => $idCardData->first()->toArray(),
            'client_address' => $clientAddressData->first()->toArray(),
            'guarant' => $clientGuarantData->toArray(),
            'contact' => $clientContactData->toArray(),
            "clientMetaAction" => "identification_mvr_valid_date",
            "attempt" => [
                "start_at" => "2022-09-29 12:31:15"
            ],
        ];

        ///@todo ovveride data params with incommin $data

        $this->newAppAction = app(NewAppAction::class);
        $dto = (new DtoSerializerNewClientLoan())->createClientDto($data);
        $this->newAppAction->execute($dto);
        $this->loan = $this->newAppAction->getDbLoan();

        return $this;
    }

    public function signTestLoan(): self
    {
        app(SignLoanAction::class)->execute($this->newAppAction->getDbLoan());

        return $this;
    }

    public function paidTestLoan(): self
    {
        app(SignLoanAction::class)->execute($this->loan->refresh())->dbModel();
        app(ProcessLoanAction::class)->execute($this->loan->refresh())->dbModel();
        app(ApproveLoanAction::class)->execute(
            new DecisionDto(
                $this->loan->loan_id,
                1,
                ApproveDecision::APPROVE_DECISION_APPROVED,
                ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
                '',
                null
            )
        );
        /** @var Payment $firstLoanMainPayment */
        $loanPayment = $this->loan->payments()->orderByDesc('payment_id')->first();

        if (!$loanPayment) {
            throw new \Exception('Error no payment found');
        }

        $this->loan->refresh();
        if (!$this->loan->isActive()) {
            app(ConfirmEasyPaySendingAction::class)->execute(
                $loanPayment
            );
        }

        return $this;
    }

    public function makeTestLoanInOverdue(int $overdueDays, ?int $loanId = null): self
    {
        if ((!$loanId && !$this->loan) || $overdueDays <= 0) {
            throw new \Exception('Error invalid params');
        }

        if (!empty($loanId)) {
            $this->loan = Loan::find($loanId);
        }

        Artisan::call('script:make-loan-overdue', [
            'loanId' => $this->loan?->loan_id,
            'days' => $overdueDays
        ]);

        return $this;
    }

    public function createPaymentForTestLoan(
        int $loanId,
        int $amount,
        PaymentDeliveryEnum $paymentDeliveryEnum = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT,
        int $paymentMethod = PaymentMethod::PAYMENT_METHOD_BANK,
    ): TestResponse {
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));

        $postData = [
            "clientIds" => 1,
            "selectedLoanIds" => [AfterLoanActivationSeeder::LOAN_ID => AfterLoanActivationSeeder::LOAN_ID],
            "totalAmountForDelivery" => $amount,
            "loanPaymentAmount" => [
                $loanId => $amount
            ],
            "loanAction" => [$loanId => $paymentDeliveryEnum->value],
            "office_id" => 33,
            "payment_method_id" => $paymentMethod,
            "bank_account_id" => BankAccount::first()?->getKey(),
            "payment_amount" => $amount,
            "receive_cash" => $amount,
            "document_number" => fake()->numerify('########'),
            "description" => fake()->numerify('########'),
            "loans" => [$loanId],
        ];

        return $this->post(route('payment.manual-payment.storeManualPayment'), $postData);
    }

    public function extendTestLoan(
        int $extendDays = 15,
        ?int $loanId = null,
    ): Loan {
        /// validate params
        if (!$this->loan && !$loanId) {
            throw new \Exception('Error invalid params');
        }

        if (!empty($loanId)) {
            $this->loan = Loan::findOrFail($loanId);
        }

        $extendLoanService = app(ExtendLoanService::class);
        $loanService = app(LoanService::class);

        /// calculate extend fee amount
        $extendFeeAmount = $loanService->calculateExtendLoanFeeAmount($this->loan, $extendDays);

        /// extend loan
        $wasExtended = $extendLoanService->run($loanId, $extendFeeAmount, $extendDays);
        if (!$wasExtended) {
            throw new \Exception('Error processing extended loan');
        }

        return $this->loan->refresh();
    }
}
