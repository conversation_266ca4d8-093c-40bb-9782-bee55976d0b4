<?php

namespace Modules\Common\Tests\Integration\Presentation\Console;

use Illuminate\Console\OutputStyle;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Collect\Database\Seeders\Test\OverdueLoanSeeder;
use Modules\Common\Console\OverdueSmsSender;
use Modules\Communication\Services\SmsService;
use Modules\Head\Repositories\Loan\LoanActualStatsRepository;
use Symfony\Component\Console\Helper\ProgressBar;
use Tests\TestCase;

class OverdueSmsSenderTest extends TestCase
{
    use DatabaseTransactions;
    private OverdueSmsSender $sut;
    private LoanActualStatsRepository $repo;
    private OutputStyle $output;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repo = new LoanActualStatsRepository();
        $smsService = $this->createMock(SmsService::class);
        $this->sut = new OverdueSmsSender($smsService, $this->repo);
        $this->output = $this->createMock(OutputStyle::class);
        $this->output->method('createProgressBar')->willReturn(new ProgressBar($this->output));
        $this->sut->setOutput($this->output);
    }

    public function testNothingSent()
    {
        $this->sut->handle();
        $this->assertEquals(0, $this->sut->smsSent);
        $this->assertEquals(0, $this->sut->totalCount);
    }

    public function testHappyPath()
    {
        $this->seed(OverdueLoanSeeder::class);
        $this->assertCount(14, $this->repo->getActiveOverdueLoanStats());
        $this->sut->handle();
        $this->assertEquals(14, $this->sut->smsSent);
        $this->assertEquals(14, $this->sut->totalCount);
    }

    public function testSomeExceptions()
    {
        $this->seed(OverdueLoanSeeder::class);
        $smsService = $this->createMock(SmsService::class);
        $smsService->method('sendByTemplateKeyAndLoan')
            ->willThrowException(
                new \RuntimeException('xx', new \Exception(''))
            );
        $sut = new OverdueSmsSender($smsService, $this->repo);
        $sut->setOutput($this->output);
        $sut->handle();
        $this->assertEquals(0, $sut->smsSent);
        $this->assertEquals(14, $sut->totalCount);
    }
}