<?php

declare(strict_types=1);

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Models\Area;
use Modules\Common\Models\Municipality;

final class MunicipalityFactory extends Factory
{
    protected $model = Municipality::class;

    public function definition(): array
    {
        $area = Area::factory()->create();

        return [
            'code' => $area->code . $this->faker->numberBetween(1, 99),
            'name' => $this->faker->city(),
            'area_id' => $area,
        ];
    }
}
