<?php

declare(strict_types=1);

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Models\Area;

final class AreaFactory extends Factory
{
    protected $model = Area::class;

    public function definition(): array
    {
        return [
            'code' => strtoupper($this->faker->lexify('???')),
            'name' => $this->faker->city(),
        ];
    }
}
