<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Common\Enums\ClientCardBoxGroupEnum;
use Modules\Common\Models\ClientCardBox;
use Modules\Common\Models\ClientCardBoxToGroup;

/**
 * Reuse when we need add new client card boxes
 * php artisan db:seed --class=Modules\\Common\\Database\\Seeders\\ClientCardBoxToGroupSeederTableSeeder
 */
class ClientCardBoxToGroupSeederTableSeeder extends Seeder
{
    public function run(): void
    {
        $clientCardBoxes = ClientCardBox::all();
        $clientCardBoxGroups = ClientCardBoxGroupEnum::cases();
        $createdCount = 0;

        foreach ($clientCardBoxGroups as $clientCardBoxGroup) {
            /** @var \Modules\Common\Models\ClientCardBox $clientCardBox **/
            foreach ($clientCardBoxes as $key => $clientCardBox) {
                if (in_array($clientCardBox->name, $clientCardBoxGroup->boxes())) {
                    $created = $clientCardBox->clientCardBoxToGroup()->firstOrCreate(
                        ['group_name' => $clientCardBoxGroup->value],
                        ['ord' => $key]
                    );

                    if ($created->wasRecentlyCreated) {
                        $createdCount++;
                    }
                }
            }
        }

        if (property_exists($this, 'command') && $this->command) {
            $this->command->info("ClientCardBoxToGroupSeederTableSeeder completed.");
            $this->command->info("New records created: {$createdCount}");
        }
    }
}
