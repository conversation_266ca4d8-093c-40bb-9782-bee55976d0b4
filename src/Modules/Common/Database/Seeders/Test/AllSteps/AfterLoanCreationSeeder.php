<?php

namespace Modules\Common\Database\Seeders\Test\AllSteps;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AfterLoanCreationSeeder extends Seeder
{
    use WithoutModelEvents;

    const LOAN_ID = 10039;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('client')->insert(self::client());
        DB::table('client_actual_stats')->insert(self::clientActualStats());
        DB::table('client_address')->insert(self::clientAddress());
        DB::table('client_email')->insert(self::clientEmail());
        DB::table('client_history')->insert(self::clientHistory());
        DB::table('client_idcard')->insert(self::clientIdCard());
        DB::table('client_name')->insert(self::clientName());
        DB::table('client_phone')->insert(self::clientPhone());
        DB::table('client_picture')->insert(self::clientPicture());
        DB::table('contact')->insert(self::contact());
        DB::table('representor')->insert(self::representative());
        DB::table('client_representor')->insert(self::clientRepresentative());
        DB::table('loan')->insert(self::loan());
        DB::table('client_office')->insert(self::clientOffice());
        DB::table('installment')->insert(self::installment());
        DB::table('loan_actual_stats')->insert(self::loanActualStats());
        DB::table('loan_address')->insert(self::loanAddress());
        DB::table('loan_client_name')->insert(self::loanClientName());
        DB::table('loan_contact_actual')->insert(self::loanContactActual());
        DB::table('loan_email')->insert(self::loanEmail());
        DB::table('loan_history')->insert(self::loanHistory());
        DB::table('loan_idcard')->insert(self::loanIdCard());
        DB::table('loan_meta')->insert(self::loanMeta());
        DB::table('loan_phone')->insert(self::loanPhone());
        DB::table('loan_product_setting')->insert(self::loanProductSetting());
        DB::table('loan_status_history')->insert(self::loanStatusHistory());
        DB::table('notification_setting')->insert(self::notificationSetting());
    }

    public static function client(): array
    {
        return json_decode('
          {
            "client_id": 1,
            "pin": "9104188750",
            "idcard_number": "9104188750",
            "first_name": "Калоян",
            "middle_name": "Патлеев",
            "last_name": "Илиев",
            "phone": "0896667788",
            "email": "",
            "new": 1,
            "dead": 0,
            "blocked": 0,
            "type": "real",
            "active": 1,
            "deleted": 0,
            "created_at": "'.now().'",
            "created_by": 2,
            "updated_at":  "'.now().'",
            "legal_status": "company",
            "citizenship_type": "unknown",
            "legal_status_code": "437",
            "economy_sector_code": "9",
            "industry_code": "96",
            "registered_in_ccr": 0,
            "need_ccr_sync": 1,
            "first_name_latin": "Kaloyan",
            "middle_name_latin": "Patleev",
            "last_name_latin": "Iliev"
          }', true);
    }

    public static function clientActualStats(): array
    {
        return json_decode('{
    "client_actual_stats_id": 1434,
    "client_id": 1,
    "applications_count": 1,
    "approved_loans_count": 0,
    "disapproved_loans_count": 0,
    "repaid_loans_count": 0,
    "days_without_loan": 0,
    "current_overdue_days": 0,
    "max_overdue_days": 0,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "current_overdue_amount": 0,
    "max_overdue_amount": 0
  }', true);
    }

    public static function clientAddress(): array
    {
        return json_decode('[
  {
    "client_address_id": 67,
    "client_id": 1,
    "type": "id_card",
    "city_id": 1,
    "post_code": "",
    "address": "Soedinenie",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "address_is_match": "yes"
  },
  {
    "client_address_id": 68,
    "client_id": 1,
    "type": "current",
    "city_id": 1,
    "post_code": "",
    "address": "Soedinenie",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "address_is_match": "yes"
  }
]', true);
    }

    public static function clientEmail(): array
    {
        return json_decode('[{
    "client_email_id": 36,
    "client_id": 1,
    "email": "",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }]', true);
    }

    public static function clientHistory(): array
    {
        return json_decode('[
  {
    "client_history_id": 1642,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.pin",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1643,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.idcard_number",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1644,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.phone",
    "value_from": null,
    "value_to": "0896667788",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1645,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.email",
    "value_from": null,
    "value_to": "",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1646,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.legal_status",
    "value_from": null,
    "value_to": "company",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1647,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.citizenship_type",
    "value_from": null,
    "value_to": "unknown",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1648,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.legal_status_code",
    "value_from": null,
    "value_to": "437",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1649,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.economy_sector_code",
    "value_from": null,
    "value_to": "9",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1650,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.industry_code",
    "value_from": null,
    "value_to": "96",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1651,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.first_name",
    "value_from": null,
    "value_to": "Калоян",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1652,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.middle_name",
    "value_from": null,
    "value_to": "Патлеев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1653,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.last_name",
    "value_from": null,
    "value_to": "Илиев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1654,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.first_name_latin",
    "value_from": null,
    "value_to": "Kaloyan",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1655,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.middle_name_latin",
    "value_from": null,
    "value_to": "Patleev",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1656,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.last_name_latin",
    "value_from": null,
    "value_to": "Iliev",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1657,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.need_ccr_sync",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1658,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.created_by",
    "value_from": null,
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1659,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.address",
    "value_from": null,
    "value_to": "Soedinenie",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1660,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.city_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1661,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.post_code",
    "value_from": null,
    "value_to": "",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1662,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.type",
    "value_from": null,
    "value_to": "id_card",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1663,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1664,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.address",
    "value_from": null,
    "value_to": "Soedinenie",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1665,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.city_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1666,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.post_code",
    "value_from": null,
    "value_to": "",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1667,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.type",
    "value_from": null,
    "value_to": "current",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1668,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
'//  {
//    "client_history_id": 1669,
//    "client_id": 1,
//    "administrator_id": 2,
//    "field": "client_email.email",
//    "value_from": null,
//    "value_to": "",
//    "ip": "127.0.0.1",
//    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
//    "active": 1,
//    "deleted": 0,
//    "created_at": "'.now().'",
//    "created_by": 2,
//    "updated_at": "'.now().'"
//  },
//  {
//    "client_history_id": 1670,
//    "client_id": 1,
//    "administrator_id": 2,
//    "field": "client_email.last",
//    "value_from": null,
//    "value_to": "1",
//    "ip": "127.0.0.1",
//    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
//    "active": 1,
//    "deleted": 0,
//    "created_at": "'.now().'",
//    "created_by": 2,
//    "updated_at": "'.now().'"
//  },
        .'
  {
    "client_history_id": 1671,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.first_name",
    "value_from": null,
    "value_to": "Калоян",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1672,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.middle_name",
    "value_from": null,
    "value_to": "Патлеев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1673,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.last_name",
    "value_from": null,
    "value_to": "Илиев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1674,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1675,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.pin",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1676,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.idcard_number",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1677,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.idcard_issued_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1678,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.issue_date",
    "value_from": null,
    "value_to": "2011-03-09",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1679,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.valid_date",
    "value_from": null,
    "value_to": "2024-03-09",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1680,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.city_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1681,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.address",
    "value_from": null,
    "value_to": "Soedinenie",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1682,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1683,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_phone.number",
    "value_from": null,
    "value_to": "0896667788",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1684,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_phone.seq_num",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1685,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_phone.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function clientIdCard(): array
    {
        return json_decode('[
  {
    "client_idcard_id": 33,
    "client_id": 1,
    "city_id": 1,
    "idcard_issued_id": 1,
    "pin": "9104188750",
    "idcard_number": "9104188750",
    "issue_date": "2011-03-09",
    "valid_date": "2024-03-09",
    "post_code": null,
    "address": "Soedinenie",
    "sex": null,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function clientName(): array
    {
        return json_decode('[
  {
    "client_name_id": 35,
    "client_id": 1,
    "first_name": "Калоян",
    "middle_name": "Патлеев",
    "last_name": "Илиев",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function clientOffice(): array
    {
        return json_decode('[
  {
    "client_id": 1,
    "office_id": 33,
    "loan_id": '.self::LOAN_ID.'
  }
]', true);
    }

    public static function clientPhone(): array
    {
        return json_decode('[
  {
    "client_phone_id": 49,
    "client_id": 1,
    "number": "0896667788",
    "seq_num": 1,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function clientPicture(): array
    {
        return json_decode('[
  {
    "client_picture_id": 35,
    "client_id": 1,
    "type": "mvr",
    "base64": "SomeLongBase64String",
    "source": "funky",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "pin": "9104188750"
  }
]', true);
    }

    public static function contact(): array
    {
        return json_decode('[
  {
    "contact_id": 393,
    "pin": null,
    "phone": "0896667701",
    "email": null,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "name": "James Karter"
  },
  {
    "contact_id": 394,
    "pin": null,
    "phone": "0896667702",
    "email": null,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "name": "Boby Oneal"
  }
]', true);
    }

    public static function representative(): array
    {
        return json_decode('[
  {
    "representor_id": 27,
    "pin": null,
    "first_name": "1234567",
    "middle_name": "тестович",
    "last_name": "test company",
    "phone": "0896667701",
    "phone_additional": "0896667702",
    "email": "<EMAIL>",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function clientRepresentative(): array
    {
        return json_decode('[
  {
    "client_representor_id": 23,
    "client_id": 1,
    "representor_id": 27,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2
  }
]', true);
    }


    public static function loan(): array
    {
        $loanData = json_decode('[
  {
    "loan_id": '.self::LOAN_ID.',
    "client_id": 1,
    "product_id": 3,
    "product_type_id": 1,
    "loan_type_id": 1,
    "discount_percent": 0.00,
    "amount_requested": 20000,
    "amount_approved": 20000,
    "installments_requested": 1,
    "installments_approved": 1,
    "currency_id": 1,
    "period_requested": 7,
    "period_approved": 7,
    "period_grace": null,
    "loan_status_id": 2,
    "last_status_update_administrator_id": 2,
    "last_status_update_date": "'.now().'",
    "payment_method_id": 3,
    "source_id": 1,
    "channel_id": 1,
    "office_id": 33,
    "hash": "333b9597fe0a644fb474eb620489d78e",
    "comment": null,
    "juridical": 0,
    "cession": 0,
    "fraud": 0,
    "loan_changed_at": "'.now().'",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "grace_updated": 0,
    "amount_rest": 200.00,
    "a4e_performance": 1,
    "registered_in_ccr": 0,
    "need_ccr_sync": 0,
    "extended": 0,
    "ccr_finished": 0,
    "interest_percent": 3600,
    "penalty_percent": 21309,
    "installment_modifier": "+1 days",
    "approve_tasks": ["identification_new_client","identification_mvr_no_response","manual_approve"],
    "period_final": 7
  }
]', true);
        $loanData[0]['approve_tasks'] = '["identification_new_client","identification_mvr_no_response","manual_approve"]';
        return $loanData;
    }

    public static function installment(): array
    {
        return json_decode('[
  {
    "installment_id": 46,
    "client_id": 1,
    "loan_id": '.self::LOAN_ID.',
    "seq_num": 1,
    "due_date": "'.now()->addWeek()->toDateString().'",
    "accrued_total_amount": 200.00,
    "total_amount": 209.68,
    "principal": 200.00,
    "paid_principal": 0.00,
    "accrued_interest": 0.00,
    "interest": 1.40,
    "late_interest": 0.00,
    "paid_accrued_interest": 0.00,
    "paid_interest": 0.00,
    "rest_interest": 0.00,
    "paid_late_interest": 0.00,
    "accrued_penalty": 0.00,
    "penalty": 8.28,
    "late_penalty": 0.00,
    "paid_accrued_penalty": 0.00,
    "paid_penalty": 0.00,
    "rest_penalty": 0.00,
    "paid_late_penalty": 0.00,
    "overdue_days": 0,
    "overdue_amount": 0.00,
    "max_overdue_days": 0,
    "max_overdue_amount": 0.00,
    "status": "scheduled",
    "paid": 0,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanActualStats(): array
    {
        return json_decode('[
  {
    "loan_stats_id": 307,
    "loan_id": '.self::LOAN_ID.',
    "date": "'.now()->toDateString().'",
    "loan_limit": 10,
    "profit": 9.68,
    "approved": 0,
    "first_loan": 1,
    "has_payment": 0,
    "total_installments_count": 1,
    "paid_installments_count": 0,
    "unpaid_installments_count": 1,
    "current_installment": 1,
    "current_installment_amount": 0.00,
    "current_installment_date": "'.now()->addWeek()->toDateString().'",
    "first_installment_date": "'.now()->addWeek()->toDateString().'",
    "next_installment_date": "'.now()->addWeek()->toDateString().'",
    "last_installment_date": "'.now()->addWeek()->toDateString().'",
    "previous_loans_count": 0,
    "previous_applications_count": 1,
    "days_after_previous_loan": 0,
    "rate_overdue_interest": 0.00,
    "repaid_amount_principal": 0.00,
    "repaid_amount_interest": 0.00,
    "repaid_amount_penalty": 0.00,
    "repaid_amount_taxes": 0.00,
    "repaid_amount_total": 0.00,
    "due_amount_total": 209.68,
    "due_amount_total_interest": 1.40,
    "due_amount_total_penalty": 8.28,
    "due_amount_total_taxes": 0.00,
    "outstanding_amount_total": 209.68,
    "outstanding_amount_principal": null,
    "outstanding_amount_interest": 1.40,
    "outstanding_amount_penalty": 8.28,
    "outstanding_amount_taxes": 0.00,
    "accrued_amount_total": 0.00,
    "accrued_amount_principal": 0.00,
    "accrued_amount_interest": 0.00,
    "accrued_amount_penalty": 0.00,
    "accrued_amount_taxes": 0.00,
    "current_overdue_days": 0,
    "current_overdue_amount": 0.00,
    "max_overdue_days": 0,
    "max_overdue_amount": 0.00,
    "overdue_amount_principal": 0.00,
    "overdue_amount_interest": 0.00,
    "overdue_amount_penalty": 0.00,
    "overdue_amount_taxes": 0.00,
    "overdue_amount_total": 0.00,
    "overdue_installments": 0,
    "last_paid_date": null,
    "contract_end_date": "'.now()->addWeek()->toDateString().'",
    "repayment_date": "'.now()->addWeek()->toDateString().'",
    "credit_limit": 10,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "total_overdue_penalty": 0.00,
    "total_overdue_interest": 0.00,
    "total_overdue_principal": 0.00,
    "rate_annual_percentage": 0.36,
    "total_interest": 1.40,
    "total_penalty": 0.00,
    "first_repayment_date": "'.now()->addWeek()->toDateString().'",
    "prev_repayment_date": "'.now()->addWeek()->toDateString().'"
  }
]', true);
    }

    public static function loanAddress(): array
    {
        return json_decode('[
  {
    "loan_id": '.self::LOAN_ID.',
    "client_address_id": 68,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "type": "id_card"
  },
  {
    "loan_id": '.self::LOAN_ID.',
    "client_address_id": 67,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "type": "id_card"
  }
]', true);
    }

    public static function loanClientName(): array
    {
        return json_decode('[
  {
    "loan_id": '.self::LOAN_ID.',
    "client_name_id": 35,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanContactActual(): array
    {
        return json_decode('[
  {
    "loan_contact_actual_id": 51,
    "client_id": 1,
    "loan_id": '.self::LOAN_ID.',
    "contact_id": 393,
    "contact_type_id": 1,
    "seq_num": 1,
    "created_at": "'.now().'",
    "created_by": 2,
    "active": 1,
    "deleted": 0
  },
  {
    "loan_contact_actual_id": 52,
    "client_id": 1,
    "loan_id": '.self::LOAN_ID.',
    "contact_id": 394,
    "contact_type_id": 1,
    "seq_num": 2,
    "created_at": "'.now().'",
    "created_by": 2,
    "active": 1,
    "deleted": 0
  }
]', true);
    }

    public static function loanEmail(): array
    {
        return json_decode('[
  {
    "loan_id": '.self::LOAN_ID.',
    "client_email_id": 36,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanHistory(): array
    {
        return json_decode('[
  {
    "loan_history_id": 1679,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.client_id",
    "value_to": "31",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1680,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.office_id",
    "value_to": "33",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1681,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.loan_status_id",
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1682,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.last_status_update_administrator_id",
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1683,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.last_status_update_date",
    "value_to": "'.now().'",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1684,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.loan_changed_at",
    "value_to": "'.now().'",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1685,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.product_id",
    "value_to": "3",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1686,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.product_type_id",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1687,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.payment_method_id",
    "value_to": "3",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1688,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.amount_requested",
    "value_to": "20000",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1689,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.amount_approved",
    "value_to": "20000",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1690,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.loan_type_id",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1691,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.amount_rest",
    "value_to": "20000",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1692,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.discount_percent",
    "value_to": "0",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1693,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.period_requested",
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1694,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.period_approved",
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1696,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.period_final",
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1697,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.interest_percent",
    "value_to": "36.00",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1698,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.penalty_percent",
    "value_to": "213.09",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1699,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.installment_modifier",
    "value_to": "+1 days",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1700,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.installments_requested",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1701,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.installments_approved",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1702,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.approve_tasks",
    "value_to": "[\"identification_new_client\",\"identification_mvr_no_response\",\"manual_approve\"]",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1703,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.currency_id",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1704,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.source_id",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1705,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.channel_id",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1706,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan.hash",
    "value_to": "333b9597fe0a644fb474eb620489d78e",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1707,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_address.client_address_id",
    "value_to": "68",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1708,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_address.last",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1709,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_address.client_address_id",
    "value_to": "67",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1710,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_address.last",
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1711,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_phone.client_phone_id",
    "value_to": "49",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
    {
    "loan_history_id": 1712,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_email.client_email_id",
    "value_to": "36",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1713,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_idcard.client_idcard_id",
    "value_to": "33",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "loan_history_id": 1714,
    "loan_id": '.self::LOAN_ID.',
    "field": "loan_client_name.client_name_id",
    "value_to": "35",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanIdCard(): array
    {
        return json_decode('[
  {
    "loan_id": '.self::LOAN_ID.',
    "client_idcard_id": 33,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanMeta(): array
    {
        return json_decode('[
  {
    "meta_id": 75,
    "loan_id": '.self::LOAN_ID.',
    "key": "discount_options_created",
    "value": "{\"loan_discount\":0,\"admin_discount\":0,\"client_discount\":null,\"sale_task_discount\":null,\"client_discount_actual_id\":null,\"administrator_id\":null,\"product_ids\":null}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanPhone(): array
    {
        return json_decode('[
  {
    "loan_id": '.self::LOAN_ID.',
    "client_phone_id": 49,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }

    public static function loanProductSetting(): array
    {
        $data = '[
  {
    "loan_product_setting_id": 42,
    "loan_id": 10039,
    "product_id": 3,
    "active": 1,
    "deleted": 0,
    "created_by": 2,
    "period": "1"
  }
]';
        $decoded = json_decode($data, true);
        $decoded[0]['settings'] = '{"min_amount":"200","max_amount":"600","default_amount":"200","default_period":"7","period":"1","min_period":"3","max_period":"24","amount_step":"50","period_step":"1","extension":"1","grace_period":"1","grace_period_days":"30","send_email":"0","send_mail":"1","send_sms":"0","late_interest":"10.01","late_penalty":"10.01","late_penalty_days":"0","collector_tax":"0","collector_tax_days":"0","sms_tax":"0","mail_tax":"0"}';
        return $decoded;
    }

    public static function loanStatusHistory(): array
    {
        return json_decode('[
  {
    "loan_status_history_id": 148,
    "loan_id": '.self::LOAN_ID.',
    "loan_status_id": 2,
    "date": "'.now().'",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2
  }
]', true);
    }

    public static function notificationSetting(): array
    {
        return json_decode('[
  {
    "notification_setting_id": 3143,
    "client_id": 1,
    "type": "system",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3144,
    "client_id": 1,
    "type": "system",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3145,
    "client_id": 1,
    "type": "system",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3147,
    "client_id": 1,
    "type": "information",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3148,
    "client_id": 1,
    "type": "information",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3149,
    "client_id": 1,
    "type": "information",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3151,
    "client_id": 1,
    "type": "marketing",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3152,
    "client_id": 1,
    "type": "marketing",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3153,
    "client_id": 1,
    "type": "marketing",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3155,
    "client_id": 1,
    "type": "sales",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3156,
    "client_id": 1,
    "type": "sales",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3157,
    "client_id": 1,
    "type": "sales",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3159,
    "client_id": 1,
    "type": "approve",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3160,
    "client_id": 1,
    "type": "approve",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3161,
    "client_id": 1,
    "type": "approve",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3163,
    "client_id": 1,
    "type": "collect",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3164,
    "client_id": 1,
    "type": "collect",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3165,
    "client_id": 1,
    "type": "collect",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]', true);
    }
}
