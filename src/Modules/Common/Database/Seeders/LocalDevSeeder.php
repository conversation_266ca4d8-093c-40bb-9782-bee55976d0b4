<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Admin\Database\Seeders\OfficeFiscalDeviceTableSeeder;
use Modules\Approve\Database\Seeders\ApproveAttemptSeeder;
use Modules\CashDesk\Database\Seeders\CashOperationalTransactionSeeder;
use Modules\Collect\Database\Seeders\BucketTaskSeeder;
use Modules\Common\Database\Seeders\CashLoanSteps\ActiveCashLoanSeeder;
use Modules\Communication\Database\Seeders\CommunicationCommentSeeder;
use Modules\Communication\Database\Seeders\EmailSeeder;
use Modules\Communication\Database\Seeders\SmsSeeder;
use Modules\Discounts\Database\Seeders\ClientDiscountActualSeeder;
use Modules\Sales\Database\Seeders\SaleTaskSeeder;
use Modules\ThirdParty\Database\Seeders\A4EReportSeeder;
use Modules\ThirdParty\Database\Seeders\CcrReportSeeder;
use Modules\ThirdParty\Database\Seeders\MvrReportSeeder;
use Modules\ThirdParty\Database\Seeders\NoiReportSeeder;


class LocalDevSeeder extends Seeder
{
    public function run(): void
    {
        if (!isLocal()) {
            return;
        }
        $this->call(
            [
                OfficeFiscalDeviceTableSeeder::class,
                GuarantSeeder::class,
                FileSeeder::class,
                ClientLoanSeeder::class,
                PaymentSeeder::class,
                LoanContactActualSeeder::class,
                LoanGuarantActualSeeder::class,
                LoanContactHistorySeeder::class,
                LoanGuarantHistorySeeder::class,
                ApproveAttemptSeeder::class,
                BucketTaskSeeder::class,
                SaleTaskSeeder::class,
                SmsSeeder::class,
                EmailSeeder::class,
                CommunicationCommentSeeder::class,
                ClientDiscountActualSeeder::class,
                MvrReportSeeder::class,
                NoiReportSeeder::class,
                CcrReportSeeder::class,
                A4EReportSeeder::class,
                CashOperationalTransactionSeeder::class,

                ActiveEasyPayLoanSeeder::class,
                ActiveCashLoanSeeder::class,
                ActiveBankLoanSeeder::class,
                ApproveAgentStatsSeeder::class,

                ClientCardBoxesSeeder::class,
                ClientCardBoxToGroupSeederTableSeeder::class
            ]
        );
    }
}
