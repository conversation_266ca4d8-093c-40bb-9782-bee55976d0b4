<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Office;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;

class OfficeEmailSmsSeederTableSeeder extends Seeder
{
    public function run()
    {
        DB::table('office_email_template')->truncate();
        DB::table('office_sms_template')->truncate();

        $emailTemplates = EmailTemplate::all();
        $smsTemplates = SmsTemplate::all();

        foreach ($emailTemplates as $emailTemplate) {
            $officeTemplateArray = [
                'office_id' => Office::OFFICE_ID_WEB,
                'email_template_id' => $emailTemplate->getKey(),
            ];
            DB::table('office_email_template')->insert($officeTemplateArray);
        }

        foreach ($smsTemplates as $smsTemplate) {
            $officeTemplateArray = [
                'office_id' => Office::OFFICE_ID_WEB,
                'sms_template_id' => $smsTemplate->getKey(),
            ];
            DB::table('office_sms_template')->insert($officeTemplateArray);
        }
    }
}
