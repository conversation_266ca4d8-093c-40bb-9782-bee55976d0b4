<?php

use Modules\Common\Models\SettingType;

return [
    'topButtons' => [
        'type' => 'buttons',
        'items' => [
            [
                'route' => 'sales.newApplicationReset',
                'title' => 'menu.NewApplication',
                'class' => 'btn btn-primary',
            ],
            [
                'route' => 'payment.manual-payment.index',
                'title' => 'menu.NewPayment',
                'class' => 'btn btn-primary',
            ],
        ],
    ],
    'Sales' => [
        'title' => 'menu.Sales',
        'items' => [
            [
                'route' => 'sales.saleTask.list',
                'title' => 'menu.Tasks',
            ],
            [
                'route' => 'head.discountsClients.list',
                'title' => 'menu.Discounts',
            ],
            [
                'route' => 'head.pre-approved.list',
                'title' => 'menu.PreApprovedClients',
            ],
            [
                'route' => 'head.interest-free-offer.list',
                'title' => 'menu.InterestFreeOffers',
            ],
            [
                'route' => 'sales.clients.without-profile',
                'title' => 'menu.ClientsWithoutProfile',
            ],
            [
                'route' => 'sales.clients.who-have-never-had-active-loans',
                'title' => 'menu.ClientsWhoHaveNeverHadActiveLoans',
            ],
            [
                'route' => 'head.clients.without-active-loan',
                'title' => 'menu.ClientsWithoutActiveLoan',
            ],
            [
                'route' => 'head.clients.offices.without-active-loan',
                'title' => 'menu.ClientsWithoutActiveLoanFromOffices',
            ],
            [
                'route' => 'discounts.by-phones',
                'title' => 'menu.DiscountByPhone',
            ],
            [
                'route' => 'sales.veriff-stats.list',
                'title' => 'menu.VeriffStats',
            ],
        ]
    ],
    'Approve' => [
        'title' => 'menu.Approve',
        'items' => [
            [
                'route' => 'head.approveLoans.list',
                'title' => 'menu.Tasks',
            ],
            [
                'route' => 'head.autoProcessRules.list',
                'title' => 'menu.AutoProcessRules',
            ],
            [
                'route' => 'head.creditLimitRules.list',
                'title' => 'menu.CreditLimit',
            ],
            [
                'route' => 'head.manualCreditLimitRules.list',
                'title' => 'menu.ManualCreditLimitRules',
            ],
        ]
    ],
    'Collect' => [
        'title' => 'menu.Collect',
        'items' => [
            [
                'route' => 'collect.bucket-tasks.list',
                'title' => 'menu.Tasks',
            ],
            [
                'route' => 'collect.loan-buckets.list',
                'title' => 'menu.Loans',
            ],
            [
                'route' => 'collect.installments-in-due.list',
                'title' => 'menu.InstallmentsInDue',
            ],
            [
                'route' => 'collect.outer-collector.list',
                'title' => 'menu.OuterCollector',
            ],
        ]
    ],
    'Payments' => [
        'title' => 'menu.PaymentsMain',
        'items' => [
            [
                'route' => 'payment.paymentsTasks.list',
                'title' => 'menu.Tasks',
            ],
            [
                'route' => 'payment.payments.list',
                'title' => 'menu.Payments',
            ],
            [
                'route' => 'payment.unclaimed_money.list',
                'title' => 'menu.UnclaimedMoney',
            ],
            [
                'route' => 'payment.cashDesk.selectOffice',
                'title' => 'menu.CashDesk',
            ],
        ]
    ],
    'Communication' => [
        'title' => 'menu.Communication',
        'items' => [
            [
                'route' => 'communication.manual-sms.index',
                'title' => 'menu.ManualSms',
            ],
            [
                'route' => 'communication.sms.list',
                'title' => 'menu.SmsSend',
            ],
            [
                'route' => 'communication.email.list',
                'title' => 'menu.EmailSend',
            ],
            [
                'route' => 'communication.smsTemplate.list',
                'title' => 'menu.SmsTemplates',
            ],
            [
                'route' => 'communication.emailTemplate.list',
                'title' => 'menu.EmailTemplates',
            ],
            [
                'route' => 'head.client-communication.checkSettings',
                'title' => 'menu.CheckCommunicationSettings',
            ],
            [
                'route' => 'communication.messages-with-sending-errors',
                'title' => 'menu.MessagesWithSendingErrors',
            ],
            [
                'route' => 'communication.manual-mailings-stats',
                'title' => 'menu.TemplateStats',
            ],
        ]
    ],
    'Accounting' => [
        'title' => 'menu.Accounting',
        'items' => [
            [
                'route' => 'acc.auto-payments.list',
                'title' => 'menu.AutoPayments',
            ],
            [
                'route' => 'acc.stats.index',
                'title' => 'menu.Statistics',
            ],
            [
                'route' => 'acc.daily-reports.index',
                'title' => 'menu.DailyReports',
            ],
            [
                'route' => 'acc.accounting-operations.list',
                'title' => 'menu.AccountingOperations',
            ],
        ]
    ],
    'CCR' => [
        'title' => 'menu.CcrReports',
        'items' => [
            [
                'route' => 'head.ccrReports.list',
                'title' => 'menu.Reports',
            ],
            [
                'route' => 'head.ccrReports.stats',
                'title' => 'menu.CcrRequestStats',
            ],
            [
                'route' => 'head.ccrReports.import',
                'title' => 'menu.CcrReportImport',
            ],
        ]
    ],
    'ReportStats' => [
        'title' => 'menu.ReportStats',
        'items' => [
            [
                'route' => 'head.telemarketing.list',
                'title' => 'menu.Telemarketing',
            ],
            [
                'route' => 'received-payments.index',
                'title' => 'menu.ReceivedPayments',
            ],
            [
                'route' => 'remaining-principals.index',
                'title' => 'menu.RemainingPrincipals',
            ],
            [
                'route' => 'approve.agent-statistics',
                'title' => 'menu.ApproveAgentStats',
            ],
            // [
            //     'route' => 'approve.client-actual-stats.list',
            //     'title' => 'menu.ClientActualStats',
            // ],
            // [
            //     'route' => 'approve.loan-actual-stats.list',
            //     'title' => 'menu.LoanActualStats',
            // ],
            [
                'route' => 'approve.scoring.index',
                'title' => 'menu.ScoringStats',
            ],
            [
                'route' => 'thirdparty.a4e-reports-check',
                'title' => 'menu.A4eReportsCheck',
            ],
            [
                'route' => 'thirdparty.stats',
                'title' => 'menu.ThirdPartyChecks',
            ],
            [
                'route' => 'head.portfolio.index',
                'title' => 'menu.PortfolioSnapshots',
            ],
            [
                'route' => 'payments.deleted-expense-snapshots',
                'title' => 'menu.DeletedExpenseSnapshots',
            ],
            [
                'route' => 'payment.outer-collector-loans.index',
                'title' => 'menu.OuterCollectorLoans',
            ],
            [
                'route' => 'collect.completed-tasks',
                'title' => 'menu.CompletedCollectTasks',
            ],
            [
                'route' => 'head.loans.discounted',
                'title' => 'menu.DiscountedLoans',
            ],
            [
                'route' => 'collect.collected-installments.list',
                'title' => 'menu.CollectedInstallments',
            ],
            [
                'route' => 'head.duplicate-loans',
                'title' => 'menu.DuplicateLoans',
            ],
            [
                'route' => 'head.clients.overdue-stats-physical-offices',
                'title' => 'menu.OverdueStatsPhysicalOffices',
            ],
            [
                'route' => 'head.custom-noi-reports.list',
                'title' => 'menu.CustomNoiReports',
            ],
            [
                'route' => 'head.refer-friend-stats.list',
                'title' => 'menu.ReferFriend',
            ],
            [
                'route' => 'head.reports-bir-cho',
                'title' => 'menu.ReportBirCho',
            ],
            [
                'route' => 'head.loan-repayment-data',
                'title' => 'menu.LoanRepaymentData',
            ],
            [
                'route' => 'head.affiliate-stats.list',
                'title' => 'menu.AffiliateStats',
            ],
        ]
    ],
    'Settings' => [
        'title' => 'menu.Settings',
        'items' => [
            [
                'route' => 'head.bankAccount.list',
                'title' => 'menu.BankAccounts',
            ],
            [
                'title' => 'menu.Document',
                'items' => [
                    [
                        'route' => 'docs.documentTemplate.list',
                        'title' => 'menu.DocumentTemplates',
                    ],
                    [
                        'route' => 'docs.documentTemplate.varsTest',
                        'title' => 'menu.DocumentVars',
                    ],
                ]
            ],
            [
                'route' => 'settings.product.list',
                'title' => 'menu.Product',
            ],
            [
                'route' => 'common.client-card-boxes.index',
                'title' => 'menu.ClientCardBoxes',
            ],
        ],
    ],
    'Administration' => [
        'title' => 'menu.Administration',
        'items' => [
            [
                'route' => 'admin.administrators.list',
                'title' => 'menu.Administrators',
            ],
            [
                'route' => 'admin.roles.list',
                'title' => 'menu.Roles & Permissions',
            ],
            [
                'route' => 'admin.offices.list',
                'title' => 'menu.Offices',
            ],
            [
                'route' => 'admin.branches.list',
                'title' => 'menu.Branches',
            ],
            [
                'route' => 'head.consultants.list',
                'title' => 'menu.Consultants',
            ],
        ]
    ],
    'Operational' => [
        'title' => 'menu.Operational',
        'items' => [
            [
                'route' => 'sales.tmpRequests.list',
                'title' => 'menu.TmpRequests',
            ],
            [
                'title' => 'menu.Loans',
                'route' => 'head.loans.list',
            ],
            [
                'title' => 'menu.ClientBlackList',
                'route' => 'head.client-black-list.list',
            ],
        ]
    ],
    'Logs' => [
        'title' => 'menu.PaymentsInfo',
        'items' => [
            [
                'route' => 'terminal-log.index',
                'title' => "menu.TerminalLog",
            ],
            [
                'route' => 'payments.easypay.listRequest',
                'title' => 'menu.EasypayRequestLog',
            ],
        ]
    ],
]

?>
