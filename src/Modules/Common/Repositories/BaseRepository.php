<?php

namespace Modules\Common\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Services\WherePipeline\DataWrapper;
use PDO;

/**
 * @method Model getDbModel()
 */
abstract class BaseRepository
{
    public const NOT_DEL_ACTIVE = [
        'deleted' => 0,
        'active' => 1,
    ];

    protected ?array $loadRelations = null;

    /**
     * @param  string[]  $relations
     * @return static
     */
    public function loadRelations(array $relations): static
    {
        $this->loadRelations = $relations;

        return $this;
    }

    public function getFirstByConditions(array $conditions): ?Model
    {
        if (!method_exists($this, 'getDbModel')) {
            throw new \Exception('You need to implement getDbModel(); in to: ' . static::class);
        }

        return $this->getDbModel()->where($conditions)->first();
    }

    public function getRowsByConditions(array $conditions): Collection
    {
        if (!method_exists($this, 'getDbModel')) {
            throw new \Exception('You need to implement getDbModel(); in to: ' . static::class);
        }

        return $this->getDbModel()
            ->where($conditions)
            ->orderBy($this->getDbModel()->getKeyName(), 'DESC')
            ->get();
    }

    public function getRowsFilterBy(
        array  $filters = [],
        int    $perPage = 10,
        string $order = 'DESC'
    ): LengthAwarePaginator
    {
        if ($this->loadRelations) {
            /** @phpstan-ignore-next-line */
            return $this->getDbModel()
                ->with($this->loadRelations)
                ->filterBy($filters)
                ->orderBy($this->getDbModel()->getKeyName(), $order)
                ->paginate($perPage);
        }
        /** @phpstan-ignore-next-line */
        return $this->getDbModel()
            ->filterBy($filters)
            ->orderBy($this->getDbModel()->getKeyName(), $order)
            ->paginate($perPage);
    }

    public function getPaginationLimit(): int
    {
        return (int)session(
            BaseController::getControllerRouteName(),
            config('view.paginationLimit')
        );
    }

    protected function getAdminOfficeIds(?int $adminId = null): array
    {
        $loggedAdminId = $adminId ?: Auth::id();

        $query = DB::getPdo()->query(
            "
                SELECT office_id
                FROM administrator_office
                WHERE administrator_id = $loggedAdminId
            "
        );
        $data = $query->fetchAll(PDO::FETCH_ASSOC | PDO::FETCH_COLUMN);

        return $data;
    }

    /**
     * @param array $order
     *
     * @return array
     */
    protected function prepareOrderStatement(array $order)
    {
        $orderByFields = [];
        array_walk(
            $order,
            function ($value, $key) use (&$orderByFields) {
                $orderByFields[] = $key . ' ' . $value;
            }
        );

        return $orderByFields;
    }

    /**
     * @param array $where
     * @param bool $showDeleted
     * @param string $prefix
     *
     * @return array
     */
    protected function checkForDeleted(array $where, bool $showDeleted, string $prefix = '')
    {
        if (!$showDeleted) {
            $where[] = [($prefix !== '' ? $prefix . '.' : '') . 'deleted', '=', '0'];
        }

        return $where;
    }

    /**
     * @param array $joins
     * @param \Illuminate\Database\Eloquent\Builder | Builder $builder
     *
     * @return Builder
     */
    protected function setJoins(array $joins, $builder)
    {
        if (!empty($joins)) {
            foreach ($joins as $joinType => $joinVars) {
                foreach ($joinVars as $key => $joinArgs) {
                    $builder->{$joinType}(
                        $joinArgs[0], // reference table
                        $joinArgs[1], // reference column
                        $joinArgs[2],  // sign
                        $joinArgs[3]  // reference condition
                    );
                }
            }
        }

        return $builder;
    }

    /**
     * [getOrdersFromArray description]
     * @deprecated todo delete ?
     */
    protected function getOrdersFromArray(array $orders): string
    {
        return implode(
            ', ',
            array_map(
                function ($v, $k) {
                    return $k . ' ' . $v;
                },
                $orders,
                array_keys($orders)
            )
        );
    }

    /**
     * [getOrdersFromArray description]
     * @deprecated  todo delete ?
     */
    protected function getWhereConditionsFromArray(array $where): string
    {
        return '(' . implode(
                ') AND (',
                array_map(
                    function ($row) {
                        return $row[0] . " " . $row[1] . " '" . $row[2] . "'";
                    },
                    $where
                )
            ) . ')';
    }

    /**
     * @param array $order
     * @param Builder | \Illuminate\Database\Eloquent\Builder $builder
     *
     * @return Builder | \Illuminate\Database\Eloquent\Builder
     */
    protected function addOrder(array $order, $builder)
    {
        if (!empty($order)) {
            foreach ($order as $field => $direction) {
                $builder->orderBy($field, $direction);
            }
        }

        return $builder;
    }

    /**
     * @param Builder | \Illuminate\Database\Eloquent\Builder $builder
     * @param DataWrapper $dataWrapper
     */
    protected function setWheres($builder, DataWrapper $dataWrapper)
    {
        $builder->where($dataWrapper->getWhere());
        if (!empty($whereIn = $dataWrapper->getWhereIn())) {
            foreach ($whereIn as $column => $values) {
                $builder->whereIn($column, $values);
            }
        }
    }

    /**
     * @param Collection $collection
     * @param int $perPage
     * @param string $pageName
     * @param string|null $fragment
     *
     * @return LengthAwarePaginator
     */
    protected function paginateCollection(
        Collection $collection,
        int        $perPage,
        string     $pageName = 'page',
        string     $fragment = null
    ): LengthAwarePaginator
    {
        $currentPage = LengthAwarePaginator::resolveCurrentPage($pageName);
        $currentPageItems = $collection->slice(($currentPage - 1) * $perPage, $perPage);
        parse_str(request()->getQueryString(), $query);
        unset($query[$pageName]);

        return new LengthAwarePaginator(
            $currentPageItems,
            $collection->count(),
            $perPage,
            $currentPage,
            [
                'pageName' => $pageName,
                'path' => LengthAwarePaginator::resolveCurrentPath(),
                'query' => $query,
                'fragment' => $fragment,
            ]
        );
    }
}
