<?php

declare(strict_types=1);

namespace Modules\Common\Imports;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Validators\Failure;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Docs\Services\ClientDocumentService;
use RuntimeException;

final class ClientLoanFilesImport implements ToCollection, WithChunkReading, WithValidation, WithHeadingRow,
                                             SkipsOnFailure, WithEvents
{
    use Importable, RegistersEventListeners;

    private const PATH_ON_OLD_SERVER = '/home/<USER>/public_html/public/uploads/media-manager/app-modules-credits-models-credit/';
    private const TABLE_NAME = 'tmp_transfer_clients_files';
    private const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.JPG', '.JPEG', '.PNG', '.GIF', '.BMP'];

    private int $chunkProcessed = 0;
    private ?int $provisionUserId = null;
    private ?int $provisionCreditId = null;

    public function __construct(private readonly ClientDocumentService $documentService) {
        if (!env('SCP_USER')) {
            throw new RuntimeException('SCP_USER is not set');
        }
        if (!env('SCP_HOST')) {
            throw new RuntimeException('SCP_HOST is not set');
        }
        if (!env('SCP_PASS')) {
            throw new RuntimeException('SCP_PASS is not set');
        }
    }

    public function setClientAndLoan(?int $clientId = null, ?int $loanId = null)
    {
        if (!empty($clientId)) {
            $client = Client::where('client_id', $clientId)
                ->first();

            if (empty($client->client_id)) {
                throw new \Exception('Not found a user.');
            }

            $this->provisionUserId = (int) $client->migration_provision_id;
        }

        if (!empty($loanId)) {
            $loan = Loan::where('loan_id', $loanId)
                ->where('migration_db', 'provision')
                ->first();

            if (empty($loan->loan_id)) {
                throw new \Exception('Not found a loan.');
            }

            $this->provisionCreditId = (int) $loan->migration_provision_id;
        }

        return $this;
    }

    public function beforeImport(BeforeImport $event): void
    {
        $this->getConsoleOutput()->progressStart($event->getReader()->getTotalRows()['Worksheet'] - 1);
    }

    public function afterImport(AfterImport $event): void
    {
        $this->getConsoleOutput()->progressFinish();
    }

    public function collection(Collection $collection): void
    {
        foreach ($collection as $row) {

            if (!empty($this->provisionUserId) && $this->provisionUserId != $row['provision_user_id']) {
                continue;
            }

            if (!empty($this->provisionCreditId) && $this->provisionCreditId != $row['provision_credit_id']) {
                continue;
            }

            // if filename contains only extension, they in common have 2 files:
            // - '_.jpg'
            // - 'A_.jpg'
            // so in that case we try to find these to files and download them
            if (in_array($row['file_name'], self::IMAGE_EXTENSIONS, true)) {
                $done1 = $this->transferFile(
                    $row['provision_user_id'],
                    $row['provision_credit_id'],
                    $row['file_folder'],
                    '_' . $row['file_name'],
                );
                if (!$done1) {
                    $this->transferFile(
                        $row['provision_user_id'],
                        $row['provision_credit_id'],
                        $row['file_folder'],
                        'A_' . $row['file_name'],
                    );
                }

                continue;
            }

            $this->transferFile(
                $row['provision_user_id'],
                $row['provision_credit_id'],
                $row['file_folder'],
                $row['file_name']
            );

            $this->getConsoleOutput()->progressAdvance();
        }
    }

    private function transferFile(
        int $provisionUserId,
        int $provisionCreditId,
        int $fileFolder,
        string $fileName,
        bool $strict = true,
    ): bool {

        // chec if row already handled
        $log = $this->getLog($provisionUserId, $provisionCreditId, $fileFolder, $fileName);
        if ($log?->is_processed) {
            return true;
        }

        $loan = Loan::where([
            'migration_provision_id' => $provisionCreditId,
            'migration_db' => 'provision',
        ])->first();
        if (empty($loan->loan_id)) {
            return false;
        }
        $clientId = $loan->client_id;
        $loanId = $loan->loan_id;


        $logId = $log->id ?? DB::table(self::TABLE_NAME)->insertGetId([
            'provision_user_id' => $provisionUserId,
            'provision_credit_id' => $provisionCreditId,
            'file_folder' => $fileFolder,
            'file_name' => $fileName,
            'is_processed' => false,
        ]);

        $relativePath = "$provisionCreditId/$fileFolder/$fileName";
        $pathToFile = self::PATH_ON_OLD_SERVER . $relativePath;
        $pathToSave = storage_path('imports/file-transfer/' . $fileName);


        $directoryPath = dirname($pathToSave);
        if (!is_dir($directoryPath)) {
            mkdir($directoryPath, 0777, true); // Create the directory recursively with permissions
        }


        if (!$this->downloadFile($pathToFile, $pathToSave, $strict)) {
            $this->setLogTransferred($logId, true); // Only for '_.' and 'A_.' files
        }

        $doc = $this->documentService->upload(
            new UploadedFile($pathToSave, $fileName),
            $clientId,
            FileTypeEnum::ID_CARD,
            $loanId,
            'Migrated from provision'
        );

        if (empty($doc->client_document_id)) {
            return false;
        }


        unlink($pathToSave); // Delete file after successful upload

        $this->setLogTransferred($logId);

        return true;
    }

    private function getLog(int $provisionUserId, int $provisionCreditId, int $fileFolder, string $fileName): ?object
    {
        return DB::table(self::TABLE_NAME)
            ->where([
                'provision_user_id' => $provisionUserId,
                'provision_credit_id' => $provisionCreditId,
                'file_folder' => $fileFolder,
                'file_name' => $fileName
            ])->first();
    }

    private function downloadFile(string $pathToFile, string $pathToSave, bool $strict = true): bool
    {
        if (!file_exists($pathToSave)) {
            $userName = env('SCP_USER');
            $host = env('SCP_HOST');
            $pass = env('SCP_PASS');

            exec("sshpass -p '$pass' scp $userName@$host:$pathToFile $pathToSave");

            $exists = file_exists($pathToSave);

            if ($strict && !$exists) {
                throw new RuntimeException('Failed to download file from old server: ' . $pathToSave);
            }

            return $exists;
        }

        return false;
    }

    private function setLogTransferred(int $logId, bool $skipped = false): void
    {
        DB::table(self::TABLE_NAME)->where('id', $logId)->update([
            'is_processed' => true,
            'is_skipped' => $skipped,
        ]);
    }

    public function chunkSize(): int
    {
        return 200;
    }

    public function rules(): array
    {
        return [
            'provision_user_id' => 'required|numeric',
            'provision_credit_id' => 'required|numeric',
            'file_folder' => 'required|numeric',
            'file_name' => 'required',
            'string',
            'not_regex:/NULL/',
        ];
    }

    public function onFailure(Failure ...$failures): void
    {
        foreach ($failures as $failure) {
            foreach ($failure as $message) {
                echo $message;
            }
        }
    }
}
