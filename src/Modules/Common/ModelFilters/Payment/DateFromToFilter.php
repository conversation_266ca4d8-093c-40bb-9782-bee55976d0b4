<?php

namespace Modules\Common\ModelFilters\Payment;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\ModelFilters\ModelFilterAbstract;

final class DateFromToFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        // Split into at most two parts; if only one date, both $from and $to become the same
        [$from, $to] = array_pad(explode(' - ', $filterValue, 2), 2, $filterValue);

        // Parse dates and force start/end of day
        $dateFrom = Carbon::parse($from)->startOfDay();
        $dateTo   = Carbon::parse($to)->endOfDay();

        $this->query->whereBetween(DB::raw('COALESCE(payment.deleted_at, payment.created_at)'), [
            $dateFrom->toDateTimeString(),
            $dateTo->toDateTimeString(),
        ]);
    }
}
