<?php

namespace Modules\Common\ModelFilters\Payment;

use Modules\Common\ModelFilters\ModelFilterAbstract;

class PaymentIsActiveFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        if (intval($filterValue) === 0) {
            // Check if the model uses SoftDeletes trait before calling onlyTrashed()
            if (method_exists($this->query, 'onlyTrashed')) {
                $this->query->onlyTrashed()->where('active', $filterValue);
            } else {
                $this->query->where('active', $filterValue);
            }

        } else {
            $this->query->where('active', $filterValue);
        }
    }
}
