<?php

namespace Modules\Common\Listeners\CustomNoiReports;

use Modules\Common\Events\CustomNoiReports\CustomNoiReportReadyEvent;
use Modules\Head\Models\CustomNoiReports;
use Modules\Head\Services\CustomNoiReportsService;
use RuntimeException;

class CreateExportForUpdatedLoans
{
    public function handle(CustomNoiReportReadyEvent $event): void
    {
        $customNoiReport = CustomNoiReports::where('custom_noi_report_id', $event->customNoiReportId)->first();
        if (!$customNoiReport) {
            throw new RuntimeException('Error invalid custom noi report ID');
        }

        app(CustomNoiReportsService::class)->createExportFileForUpdatedNoiReports(
            $customNoiReport
        );
    }
}