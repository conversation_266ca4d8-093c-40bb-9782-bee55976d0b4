<?php

namespace Modules\Common\Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\Client;
use Modules\Common\Models\CommunicationComment;
use Modules\Common\Models\Email;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Sms;

/**
 * @property int $communication_id
 * @property int $client_id
 * @property int $loan_id
 * @property string $communication_type
 * @property string $communication_key
 * @property Email|Sms|CommunicationComment $model
 * @property Email $email
 * @property Sms $sms
 * @property Loan $loan
 * @property Client $client
 * @mixin IdeHelperCommunicationPivot
 */
class CommunicationPivot extends BaseModel
{
    protected $fillable = [
        'communication_id',
        'communication_type',
        'communication_key',
        'client_id',
        'classification',
        'loan_id',
        'created_at',
        'created_by',
    ];

    public function model(): Email|Sms|CommunicationComment
    {
        return $this->{$this->communication_type};
    }

    public function comment(): BelongsTo
    {
        return $this->belongsTo(CommunicationComment::class, 'communication_id', 'communication_comment_id');
    }

    public function sms(): BelongsTo
    {
        return $this->belongsTo(Sms::class, 'communication_id', 'sms_id');
    }

    public function email(): BelongsTo
    {
        return $this->belongsTo(Email::class, 'communication_id', 'email_id');
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id', 'loan_id');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'client_id');
    }
}
