<?php

namespace Modules\Common\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Traits\FilterModelTrait;

/**
 * @method static Builder|DelExpenseSnapshot filterBy()
 * @mixin IdeHelperDelExpenseSnapshot
 */
final class DelExpenseSnapshot extends Model
{
    use FilterModelTrait;

    const UPDATED_AT = null;

    protected $table = 'del_expense_snapshot';
    protected $primaryKey = 'id';

    protected $fillable = [
        'loan_id',
        'amount',
        'manual',
        'loan',
        'installments',
        'taxes',
        'loan_stats',
        'client_stats',
        'created_at',
        'created_by',
        'reverted_at',
        'reverted_by',
        'deleted_data',
    ];

    protected $casts = [
        'loan' => 'json',
        'taxes' => 'json',
        'loan_stats' => 'json',
        'client_stats' => 'json',
        'installments' => 'json',
        'deleted_data' => 'array',
    ];

    public function loanRelation(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id', 'loan_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'created_by', 'administrator_id');
    }
}
