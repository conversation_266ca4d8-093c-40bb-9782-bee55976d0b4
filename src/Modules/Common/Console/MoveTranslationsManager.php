<?php

namespace Modules\Common\Console;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Common\Services\StorageService;
use RuntimeException;
use StikCredit\Calculators\Calculator;
use Throwable;

class MoveTranslationsManager extends CommonCommand
{
    private const TRANSLATIONS_BACKUP_PATH = '/backups/translations/';

    private const DEFAULT_LOCALES = [
        'bg',
        'en',
    ];

    private const MODULE_LANG_DIR =
        DIRECTORY_SEPARATOR . 'Resources' .
        DIRECTORY_SEPARATOR . 'lang';

    /**
     * @var string
     */
    protected $name = 'translations:move';

    /**
     * @var string
     */
    protected $signature = 'translations:move';

    /**
     * @var string
     */
    protected $description = 'Move all translations from the modules to the app main lang folder.';

    /**
     * @var array
     */
    private array $locales;

    /**
     * @var StorageService
     */
    private StorageService $storageService;

    /**
     * MoveTranslationsManager constructor.
     *
     * @param StorageService $storageService
     */
    public function __construct(StorageService $storageService)
    {
        $this->storageService = $storageService;
        $this->locales = config(
            'translations.locales',
            self::DEFAULT_LOCALES
        );

        parent::__construct();
    }

    /**
     * @return bool
     */
    public function handle(): bool
    {
        $start = $this->currentMicroTime();
        $nameWithoutNamespace = $this->getClassName();
        $this->log("Starting $nameWithoutNamespace");

        try {
            $modulesAndPaths = $this->getAllModuleNamesAndPaths();

            $this->processModules($modulesAndPaths);
        } catch (Throwable $t) {
            $msg = 'Move translations failed. Error: ' . $t->getMessage()
                . ', file: ' . $t->getFile()
                . ', line: ' . $t->getLine();
            $this->log($msg);

            return false;
        }

        $end = $this->currentMicroTime();
        $this->log('Execution time: ' . Calculator::round(($end - $start), 5) . ' seconds');

        return true;
    }

    private function processModules(Collection $modulesAndPaths)
    {
        $backupPath = $this->createBackupDir();

        if (!$backupPath) {
            $msg = 'Failed to create backup directory';
            $this->log($msg);

            throw new RuntimeException($msg);
        }

        foreach ($modulesAndPaths as $moduleName => $modulePath) {
            $moduleLangPath = $modulePath . self::MODULE_LANG_DIR;

            if (!is_dir($moduleLangPath)) {
                $this->log("Warning: $moduleName module does not have a lang folder");
                continue;
            }

            $locales = $this->getFiles($moduleLangPath); // bg, en, de
            $this->processLocales($moduleName, $moduleLangPath, $locales, $backupPath);
        }
    }

    /**
     * @param string $moduleName
     * @param string $moduleLangPath
     * @param array $locales
     * @param string $baseBackupPath
     */
    private function processLocales(
        string $moduleName,
        string $moduleLangPath,
        array $locales,
        string $baseBackupPath
    ) {
        foreach ($locales as $locale) {
            if (!in_array($locale, $this->locales)) {
                continue;
            }

            $translationsLocation = $moduleLangPath . DIRECTORY_SEPARATOR . $locale;
            $translationFiles = $this->getFiles($translationsLocation);
            $filteredTransFiles = Arr::where(
                $translationFiles,
                function ($fileName) {
                    return Str::endsWith($fileName, '.php');
                }
            );
            $fromPaths = array_map(
                function ($el) use ($translationsLocation) {
                    return $translationsLocation . DIRECTORY_SEPARATOR . $el;
                },
                $filteredTransFiles
            );
            $toPaths = array_map(
                function ($fromPath) use ($moduleName, $locale) {
                    return Str::replaceLast(
                        implode(DIRECTORY_SEPARATOR, ['Modules', $moduleName, 'Resources', 'lang', $locale]),
                        implode(DIRECTORY_SEPARATOR, ['resources', 'lang', $locale, $moduleName]),
                        $fromPath
                    );
                },
                $fromPaths
            );
            $backupPaths = array_map(
                function ($fromPath) use ($baseBackupPath) {
                    return Str::replaceFirst(
                        DIRECTORY_SEPARATOR . 'Modules',
                        DIRECTORY_SEPARATOR . 'storage' . $baseBackupPath,
                        $fromPath
                    );
                },
                $fromPaths
            );

            // Backup lang files
            $this->createNewDirs($backupPaths);
            $this->copyTranslationFiles($fromPaths, $backupPaths);

            // Copy lang files
            $this->createNewDirs($toPaths);
            $this->copyTranslationFiles($fromPaths, $toPaths);
        }
    }

    /**
     * @param array $fromPaths
     * @param array $toPaths
     */
    private function copyTranslationFiles(
        array $fromPaths,
        array $toPaths
    ) {
        foreach ($fromPaths as $index => $from) {
            $to = $toPaths[$index];
            $this->storageService->copyFile($from, $to);
        }
    }

    /**
     * @param array $toPaths
     */
    private function createNewDirs(array $toPaths)
    {
        foreach ($toPaths as $toPath) {
            $newDirPath = Str::beforeLast($toPath, DIRECTORY_SEPARATOR);
            if (!$this->storageService->isDirectory($newDirPath)) {
                $this->storageService->makeDirectory($newDirPath);
            }
        }
    }

    /**
     * @return string|null
     */
    private function createBackupDir(): ?string
    {
        $backupPath = self::TRANSLATIONS_BACKUP_PATH . time();
        $isSaved = Storage::disk('public')->makeDirectory($backupPath, 0777, true, true);

        return $isSaved ? $backupPath : null;
    }
}
