<?php

namespace Modules\Common\Console;

use Carbon\Carbon;
use Modules\Common\Models\Client;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\ClvStats;
use Modules\Common\Models\ClvStatsDaily;
use Modules\Common\Models\LoanStatus;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Command\Command;

class CLVTracker extends CommonCommand
{
    const DEF_OVERDUE = 90;

    protected $name = 'script:clv-tracker';
    protected $signature = 'script:clv-tracker';
    protected $description = 'Based on client overdue calculate clv stats';

    public function handle()
    {
        $this->handleCLV90();
        $this->handleCLV90Yearly();
    }

    /**
     * Updates clv_stats_daily ON DAILY BASIS
     * @return bool
     */
    public function handleCLV90(): bool
    {
        $this->startLog($this->getClassName().'::'.__FUNCTION__, "--- START (clv90)".$this->getClassName() . '_clv90');

        $todo = 0;
        $done = 0;

        DB::table('client_actual_stats')
            ->where('current_overdue_days', '<=', self::DEF_OVERDUE)
            ->chunkById(
                200,
                function ($rows) use (&$todo, &$done) {

                    $todo += $rows->count();

                    foreach ($rows as $row) {
                        $done += (int) $this->statsToClv($row);
                    }
                },
                'client_actual_stats_id'
            );


        $msg = 'Processed: ' . $done . ', Total: ' . $todo;
        $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

        return Command::SUCCESS;
    }

    /**
     * Updates clv_stats_daily ON X YEAR(S) PASSED(event)
     * @return bool
     */
    public function handleCLV90Yearly(): bool
    {
        $this->startLog($this->getClassName().'::'.__FUNCTION__, "--- START (clv yearly)".$this->getClassName() . '_clvYear');

        $todo = 0;
        $done = 0;
        $trackedYears = [1 ,2, 3];

        foreach ($trackedYears as $yearsAfter) {

            $createdAtStart = Carbon::now()
                ->subYears($yearsAfter)
                ->startOfDay();
            $createdAtEnd = Carbon::now()
                ->subYears($yearsAfter)
                ->endOfDay();

            Client::where('created_at', '<=', $createdAtEnd)
                ->where('created_at', '>=', $createdAtStart)
                ->chunkById(
                    100,
                    function ($clients) use (&$todo, &$done, $yearsAfter) {

                        $todo += $clients->count();

                        foreach ($clients as $client) {

                            try {
                                $clientStats = $client->clientActualStats;

                                // Save clv_12m, clv_24m, clv_36m
                                $key = 'clv_' . (12 * $yearsAfter) . 'm';
                                $val = $clientStats->lifetime_value_total;
                                $this->saveClvStats($client->client_id, $key, $val);

                                if ($clientStats->current_overdue_days <= self::DEF_OVERDUE) {
                                    $key2 = 'clv_' . self::DEF_OVERDUE . 'dpd_' . (12 * $yearsAfter) . 'm';

                                    $unpaidPrincipal = floatToInt(
                                        $this->getUnpaidPrincipalOfLoanWithOverdueLowerThen(
                                            $client->client_id
                                        )
                                    );
                                    $totalClv = floatToInt($clientStats->lifetime_value_total);
                                    $val2 = intToFloat(($totalClv + $unpaidPrincipal));

                                    // Save clv_90dpd_12m, clv_90dpd_24m, clv_90dpd_36m
                                    $this->saveClvStats($client->client_id, $key2, $val2);
                                }

                                $done++;

                            } catch (\Throwable $e) {
                                dump('Exception(clv90yearly', $e);
                            }
                        }
                    }
                );
        }

        $msg = 'Processed: ' . $done . ', Total: ' . $todo;
        $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

        return Command::SUCCESS;
    }

    private function statsToClv($row): bool
    {
        try {

            $key = 'clv_below_' . self::DEF_OVERDUE . 'dpd';

            $unpaidPrincipal = floatToInt(
                $this->getUnpaidPrincipalOfLoanWithOverdueLowerThen(
                    $row->client_id
                )
            );
            $totalClv = floatToInt($row->lifetime_value_total);
            $val = intToFloat(($totalClv + $unpaidPrincipal));

            // save clv_below_90dpd
            return $this->saveClvStatsDaily(
                $row->client_id,
                $key,
                $val
            );

        } catch (\Throwable $e) {
            dump('Exception(clv90)', $e);
            return false;
        }
    }

    private function getUnpaidPrincipalOfLoanWithOverdueLowerThen(int $clientId): float
    {
        return (float) LoanActualStats::join('loan', 'loan_actual_stats.loan_id', '=', 'loan.loan_id')
            ->where('loan.client_id', '=', $clientId)
            ->where('loan.loan_status_id', '=', LoanStatus::ACTIVE_STATUS_ID)
            ->where('loan_actual_stats.current_overdue_days', '<=', self::DEF_OVERDUE)
            ->sum('loan_actual_stats.outstanding_amount_principal');
    }

    private function saveClvStatsDaily(
        int $clientId,
        string $key,
        $val,
    ): bool  {
        ClvStatsDaily::updateOrInsert(
            ['client_id' => $clientId, 'key' => $key],
            ['val' => $val]
        );

        return true;
    }

    private function saveClvStats(
        int $clientId,
        string $key,
        $val,
    ): bool  {
        ClvStats::updateOrInsert(
            ['client_id' => $clientId, 'key' => $key],
            ['val' => $val]
        );

        return true;
    }
}
