<?php

namespace Modules\Common\Console;

use Modules\Communication\Models\EmailTemplate;

class ConvertEmailTemplateHtmlToText extends CommonCommand
{
    protected $name = 'script:email-template:to-html';

    protected $description = 'Command description.';

    public function handle(): void
    {
        EmailTemplate::all()->each(function (EmailTemplate $emailTemplate) {
            $isUpdated = $emailTemplate->update([
                'body' => $emailTemplate->htmlToPlainText($emailTemplate->text)
            ]);

            $this->info("Updated: [{$emailTemplate->getKey()}][" . intval($isUpdated) . "]");
        });
    }
}
