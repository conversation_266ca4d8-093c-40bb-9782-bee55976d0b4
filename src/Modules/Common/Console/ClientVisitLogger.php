<?php

namespace Modules\Common\Console;

use Carbon\Carbon;
use Modules\Common\Models\ClientVisitLog;
use Modules\Common\Models\RawRequest;
use Symfony\Component\Console\Command\Command;

class ClientVisitLogger extends CommonCommand
{
    protected $name = 'script:client-visit-logger';
    protected $signature = 'script:client-visit-logger';
    protected $description = 'Create stats for client site visiting based on raw_requests';

    protected int $total;
    protected int $processed;

    public function handle(): bool
    {
        $this->startLog();


        $done = 0;
        $todo = 0;
        $previousDate = Carbon::now()->subDay()->format('Y-m-d');
        $logDate = Carbon::now()->subDay()->format('Y-m-d');


        // Retrieve distinct client IDs from raw_request for the last 24 hours
        $clientIds = RawRequest::whereDate('created_at', $previousDate)
            ->whereNotNull('client_id')
            ->pluck('client_id')
            ->unique();

        // Exit if nothing to analyze
        if ($clientIds->count() < 1) {
            $msg = 'Processed: ' . $done . ', Total: ' . $todo;
            $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

            return Command::FAILURE;
        }

        $todo = $clientIds->count();
        foreach ($clientIds as $clientId) {

            try {
                // Prepare and save ClientVisitLog entry for each distinct client ID
                $clientLog = new ClientVisitLog();
                $clientLog->client_id = $clientId;
                $clientLog->date = $logDate;

                // Calculate logins count and logins_raw_requests
                $logins = RawRequest::whereDate('created_at', $previousDate)
                    ->where('url', 'LIKE', '%api/v1/login%')
                    ->where('client_id', $clientId)
                    ->get();

                $clientLog->logins_count = $logins->count();
                $clientLog->logins_raw_requests = $logins->map(function ($login) {
                    $success = false;
                    if (preg_match('/"success":[\s+]?["]?(true)["]?/', $login->response, $matches)) {
                        $success = true;
                    }

                    return [
                        'raw_request_id' => $login->raw_request_id,
                        'url' => $login->url,
                        'ip' => $login->ip,
                        'browser' => $login->browser,
                        'longitude' => $login->longitude,
                        'latitude' => $login->latitude,
                        'success' => $success,
                        'created_at' => $login->created_at,
                    ];
                });

                // Calculate pages visited count and pages_visited_raw_requests
                $pagesVisited = RawRequest::whereDate('created_at', $previousDate)
                    ->where('client_id', $clientId)
                    ->where('type', 'api')
                    ->get();

                $clientLog->pages_visited_count = $pagesVisited->count();
                $clientLog->pages_visited_raw_requests = $pagesVisited->map(function ($pageVisited) {
                    $success = false;
                    if (preg_match('/"success":[\s+]?["]?(true)["]?/', $pageVisited->response, $matches)) {
                        $success = true;
                    }

                    return [
                        'raw_request_id' => $pageVisited->raw_request_id,
                        'url' => $pageVisited->url,
                        'ip' => $pageVisited->ip,
                        'browser' => $pageVisited->browser,
                        'longitude' => $pageVisited->longitude,
                        'latitude' => $pageVisited->latitude,
                        'success' => $success,
                        'created_at' => $pageVisited->created_at,
                    ];
                });

                // Retrieve distinct IPs for the client within the last 24 hours
                $ips = $pagesVisited->pluck('ip')->unique();
                $clientLog->ips = $ips;



                // Calculate timespent_total and timespent_avg
                $prevRow = null;
                $startAt = null;
                $timespents = [];
                foreach ($pagesVisited as $vp) {

                    if (is_null($startAt)) {
                        $startAt = $vp->created_at;
                        $prevRow = $vp;
                        continue;
                    }

                    if (!preg_match('/\/login/', $vp->url)) {
                        $prevRow = $vp;
                        continue;
                    }

                    // update timestamps
                    $timespent = $this->getTimespent($prevRow, $startAt);
                    if ($timespent) {
                        $timespents[] = $timespent;
                    }


                    // start new cycle
                    $startAt = $vp->created_at;
                    $prevRow = $vp;
                }

                if ($startAt && $prevRow) {
                    $timespent = $this->getTimespent($prevRow, $startAt);
                    if ($timespent) {
                        $timespents[] = $timespent;
                    }
                }

                if (!empty($timespent)) {
                    $clientLog->timespent_total = array_sum($timespents);
                    $clientLog->timespent_avg = (
                        count($timespents) > 0
                        ? round((array_sum($timespents) / count($timespents)))
                        : 0
                    );
                }

                $clientLog->save();
                $done++;

            } catch (\Throwable $e) {
                dump($e);
            }
        }


        $msg = 'Processed: ' . $done . ', Total: ' . $todo;
        $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

        return Command::SUCCESS;
    }

    private function getTimespent($prevRow, $startAt): int
    {
        // calc seconds between 2 borders
        $lastActionAt = $prevRow->created_at;

        return (int) $lastActionAt->diffInSeconds($startAt);
    }
}
