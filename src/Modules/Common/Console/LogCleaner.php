<?php

namespace Modules\Common\Console;

use Symfony\Component\Console\Command\Command;

class Log<PERSON>leaner extends CommonCommand
{
    public const PROTECTED_LOGS = [
        'stack',
        'single',
        'emergency',
        'log_cleaner',
    ];

    public const PATH_NAME = 'path';

    protected $name = 'script:logs:clear';
    protected $signature = 'script:logs:clear {log?}';
    protected $description = 'Clear log';
    protected string $logChannel = 'log_cleaner';


    public function handle(): int
    {
        $this->startLog();

        $logArg = $this->argument('log');

        $channels = config('logging.channels');
        $logsToDelete = self::getLogPaths($channels, $logArg);

        $deletedLogs = $this->deleteLogs($logsToDelete);
        $deletedLogs += $this->deleteHorizonLogs();
        $deletedLogs += $this->deleteLaravelLogs();
        $deletedLogs += $this->deleteNewAppLogs();

        $msg = 'Logs cleaned.';
        $logMessages = [
            $msg,
            'Deleted count: ' . $deletedLogs,
            $this->executionTimeString()
        ];

        $this->finishLog($logMessages, count($logsToDelete), $deletedLogs, $msg);

        return Command::SUCCESS;
    }

    /**
     * @param array $channels
     * @param null|string $log
     *
     * @return array
     */
    public static function getLogPaths(array $channels, string $log = null): array
    {
        $result = [];

        if ($log !== null) {
            if (
                !in_array($log, self::PROTECTED_LOGS)
                && array_key_exists($log, $channels)
                && array_key_exists(self::PATH_NAME, $channels[$log])
            ) {
                $result[] = $channels[$log][self::PATH_NAME];
            }

            return $result;
        }

        foreach ($channels as $key => $channel) {
            if (!in_array($key, self::PROTECTED_LOGS) && array_key_exists(self::PATH_NAME, $channel)) {
                $result[] = $channel[self::PATH_NAME];
            }
        }

        return $result;
    }

    protected function deleteLogs(array $logsToDelete): int
    {
        $deleted = 0;

        foreach ($logsToDelete as $path) {
            if (file_exists($path)) {
                file_put_contents($path, '');
                $this->log('Deleted: ' . $path);
                $deleted++;
            }
        }

        return $deleted;
    }

    private function deleteHorizonLogs(): int
    {
        $folderPath = '/var/www/credit-hunter/src/storage/logs';

        $deleted = 0;
        for ($number = 1; $number <= 20; $number++) {

            $fileName = "horizon.log.$number";
            $filePath = $folderPath . '/' . $fileName;

            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    $deleted++;
                }
            }
        }

        $logArg = $this->log('Deleted horizon logs: ' . $deleted);

        return $deleted;
    }

    private function deleteLaravellogs(): int
    {
        $sevenDaysAgo = strtotime('-7 days');
        $folderPath = '/var/www/credit-hunter/src/storage/logs';
        $files = scandir($folderPath);

        $deleted = 0;
        foreach ($files as $file) {
            $filePath = $folderPath . '/' . $file;

            preg_match('/laravel-(\d{4}-\d{2}-\d{2})/', $file, $matches);

            if (is_file($filePath) && isset($matches[1])) {
                $fileDate = strtotime($matches[1]);

                if ($fileDate < $sevenDaysAgo) {
                    if (unlink($filePath)) {
                        $deleted++;
                    }
                }
            }
        }

        $logArg = $this->log('Deleted laravel logs: ' . $deleted);

        return $deleted;
    }

    private function deleteNewAppLogs(): int
    {
        $sevenDaysAgo = strtotime('-7 days');
        $folderPath = '/var/www/credit-hunter/src/storage/logs';
        $files = scandir($folderPath);

        $deleted = 0;
        foreach ($files as $file) {
            $filePath = $folderPath . '/' . $file;

            preg_match('/new-app-(\d{4}-\d{2}-\d{2})/', $file, $matches);

            if (is_file($filePath) && isset($matches[1])) {
                $fileDate = strtotime($matches[1]);

                if ($fileDate < $sevenDaysAgo) {
                    if (unlink($filePath)) {
                        $deleted++;
                    }
                }
            }
        }

        $logArg = $this->log('Deleted new-app logs: ' . $deleted);

        return $deleted;
    }
}
