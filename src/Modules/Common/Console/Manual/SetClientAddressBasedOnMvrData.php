<?php

namespace Modules\Common\Console\Manual;


use DB;
use Illuminate\Support\Collection;
use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\MvrReport;
use Modules\Common\Models\LoanAddress;
use Modules\Common\Console\CommonCommand;
use Modules\ThirdParty\Services\MvrService;

class SetClientAddressBasedOnMvrData extends CommonCommand
{
    protected $name = 'script:set-addresses-based-on-mvr-data';
    protected $signature = 'script:set-addresses-based-on-mvr-data {client_id?}';
    protected $description = 'Corrections on client addresses based on mvr reports';

    public function __construct(private readonly MvrService $mvrService)
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $totalRows = 0;
        $processedAddresses = [];
        $processedMvrPivots = [];

        $clientId = (int) $this->argument('client_id');

        Client::where('active', 1)
            ->where('deleted', 0)
            ->when($clientId > 0, function ($query) use ($clientId) {
                $query->where('client.client_id', $clientId);
            })
            ->orderBy('client_id')
            ->chunkById(
                200,
                function ($clients) use (&$totalRows, &$processedAddresses, &$processedMvrPivots) {

                    $totalRows += $clients->count();
                    $this->info('-- chunk: ' . $totalRows);

                    foreach ($clients as $client) {
                        /** @var Client $client */

                        $mvrReport = $client->getLastMvrReport();
                        if (empty($mvrReport->mvr_report_id)) {
                            continue;
                        }
                        $mvrParsedData = json_decode($mvrReport->parsed_data ?? '[]', true);
                        if (empty($mvrParsedData)) {
                            continue;
                        }

                        $address = $client->clientLastAddressIdcard();
                        $address2 = $client->clientLastAddressCurrent();
                        $addressPivots = $address?->getLastPivots(['id', 'loan_id']) ?? new Collection();
                        $addressPivots2 = $address2?->getLastPivots(['id', 'loan_id']) ?? new Collection();
                        [$city, $settlementCode, $locationCode] = $this->extractForUpdate($mvrReport);


                        // handle mvr_report.parsed_data
                        if (!empty($settlementCode)) {
                            $mvrParsedData['client_address']['settlement_code'] = $settlementCode;
                        }
                        if (!empty($locationCode)) {
                            $mvrParsedData['client_address']['location_code'] = $locationCode;
                        }
                        $mvrReport->parsed_data = json_encode($mvrParsedData);
                        $mvrReport->saveQuietly();


                        // handle ID card address - change city if different from mvr
                        if (!empty($city->city_id) && !empty($address->city_id) && $city->city_id != $address->city_id) {

                            // create new with fixed city
                            $copyAddress = $address->replicate();
                            $copyAddress->city_id = $city->city_id;
                            $copyAddress->last = 1;
                            $copyAddress->created_at = now();
                            $copyAddress->created_by = 2;
                            $copyAddress->saveQuietly();

                            // make old current one
                            $address->last = 0;
                            $address->saveQuietly();

                            // handle address loan pivots
                            if ($addressPivots->count() > 0) {
                                foreach ($addressPivots as $addressPivot) {

                                    DB::transaction(static function () use ($addressPivot, $copyAddress) {
                                        $addressPivot->last = 0;
                                        $addressPivot->save();

                                        $copyAddressPivot = new LoanAddress();
                                        $copyAddressPivot->loan_id = $addressPivot->loan_id;
                                        $copyAddressPivot->client_address_id = $copyAddress->client_address_id;
                                        $copyAddressPivot->last = 1;
                                        $copyAddressPivot->type = AddressTypeEnum::IdCard->value;
                                        $copyAddressPivot->save();
                                    });
                                }
                            }

                            // handle current address
                            if (
                                $address2->city_id == $address->city_id
                                && $address2->address == $address->address
                            ) {
                                // create new with fixed city
                                $copyAddress2 = $address2->replicate();
                                $copyAddress2->city_id = $city->city_id;
                                $copyAddress2->last = 1;
                                $copyAddress2->created_at = now();
                                $copyAddress2->created_by = 2;
                                $copyAddress2->save();

                                // make old current one
                                $address2->last = 0;
                                $address2->save();

                                // handle address loan pivots
                                if ($addressPivots2->count() > 0) {
                                    foreach ($addressPivots2 as $addressPivot2) {

                                        DB::transaction(static function () use ($addressPivot2, $copyAddress2) {
                                            $addressPivot2->last = 0;
                                            $addressPivot2->save();

                                            $copyAddressPivot2 = new LoanAddress();
                                            $copyAddressPivot2->loan_id = $addressPivot2->loan_id;
                                            $copyAddressPivot2->client_address_id = $copyAddress2->client_address_id;
                                            $copyAddressPivot2->last = 1;
                                            $copyAddressPivot2->type = AddressTypeEnum::Current->value;
                                            $copyAddressPivot2->save();
                                        });

                                    }
                                }
                            }

                            $processedAddresses[] = $client->client_id;
                        }

                        $mvrPivots = $mvrReport->getLastPivots();
                        if ($mvrPivots->count() > 0 && (!empty($settlementCode) || !empty($locationCode))) {
                            foreach ($mvrPivots as $mvrPivot) {
                                $mvrPivot->settlement_code = $settlementCode;
                                $mvrPivot->location_code = $locationCode;
                                $mvrPivot->saveQuietly();
                            }

                            $processedMvrPivots[] = $client->client_id;
                        }
                    }
                },
                'client.client_id',
                'client_id'
            );

        $this->info("--- Finito ---");
        $this->info("Total clients: {$totalRows}");
        $this->info("Processed addresses: " . count($processedAddresses));
        $this->info("Processed mvr pivots: " . count($processedMvrPivots));
    }

    private function extractForUpdate(MvrReport $mvrReport): array
    {
        $obj = json_decode($mvrReport->data);

        $city = $this->mvrService->getCityFromRawData($obj);

        $settlementCode = null;
        if (!empty($obj->PermanentAddress->SettlementCode)) {
            $settlementCode = $obj->PermanentAddress->SettlementCode;
        }

        $locationCode = null;
        if (!empty($obj->PermanentAddress->LocationCode)) {
            $locationCode = $obj->PermanentAddress->LocationCode;
        }

        return [$city, $settlementCode, $locationCode];
    }
}
