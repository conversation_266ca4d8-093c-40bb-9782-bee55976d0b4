<?php

namespace Modules\Common\Console\Manual;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Events\CustomNoiReports\CustomNoiReportReadyEvent;
use Modules\Common\Models\Loan;
use Modules\Common\Models\NoiReportPivot;
use Modules\Head\Models\CustomNoiReports;
use Modules\ThirdParty\Services\NoiService;
use Modules\ThirdParty\Services\ReportService;
use RuntimeException;

// runs: php artisan script:custom-noi-report noi_stats_for_old
class CustomNoiUpdate extends CommonCommand
{
    protected $name = 'script:custom-noi-report';
    protected $signature = 'script:custom-noi-report {report_type?} {loan_id?} {custom_noi_report_id?} {admin_id?}'; // noi2, noi7, noi51, noi_stats_for_old
    protected $description = 'Update noi reports for specific loans';

    public function handle()
    {
        /// for loans from imported file
        if ($this->hasArgument('custom_noi_report_id')) {
            $this->getReportsForLoans(
                intval($this->argument('custom_noi_report_id')),
                intval($this->argument('admin_id')),
            );

            return;
        }

        $this->info("--- CustomNoiUpdate ---");
        $startedAt = microtime(true);


        $noiRepService = app(ReportService::class);
        $loanIdParam = (int) $this->argument('loan_id');
        $reportType = $this->argument('report_type') ?? 'noi2';


        $loanIds = [];
        if (!empty($loanIdParam)) {
            $loanIds = [$loanIdParam];
        }


        $loans = Loan::with(['client'])->whereIn('loan_id', $loanIds)->get();
        if ($loans->count() < 1) {
            $this->info("No loans found, exit.");
            return;
        }


        if ('noi_stats_for_old' == $reportType) {
            $totalRows = $loans->count();
            $processedRows = $this->makeNoiStats($loans);

            $finishedAt = microtime(true);
            $this->info("---- The END (" . round(($finishedAt - $startedAt), 2) . "sec) ----");
            $this->info("Total rows: {$totalRows}");
            $this->info("Processed rows: {$processedRows}");

            return;
        }


        $totalRows = 0;
        $processedRows = 0;
        foreach ($loans as $loan) {
            $this->info("- processing: {$loan->loan_id}");

            $totalRows++;

            $noiReport = $noiRepService->saveAndGetNoiReport(
                $loan->client,
                $reportType,
                'manual-report-cmd',
                $loan
            );

            if (!empty($noiReport->noi_report_id)) {
                $processedRows++;
            }
        }


        $finishedAt = microtime(true);
        $this->info("---- The END (" . round(($finishedAt - $startedAt), 2) . "sec) ----");
        $this->info("Total rows: {$totalRows}");
        $this->info("Processed rows: {$processedRows}");
    }

    private function makeNoiStats($loans): int
    {
        $noiService = app(NoiService::class);

        $processedRows = 0;
        foreach ($loans as $loan) {
            $this->info("- processing: {$loan->loan_id}");

            // get last noi report
            $noiReport = $loan->getLastNoiReport('noi2');
            if (empty($noiReport->noi_report_id)) {
                $this->info("--- skipped: {$loan->loan_id} - no noi2 report");
                continue;
            }

            // save stats for last noi report
            $noiStats = $noiService->saveNoiStats($noiReport, $loan->client_id, $loan->loan_id);
            if (empty($noiStats->id)) {
                $this->info("--- skipped: {$loan->loan_id} - failed to extract noi stats");
                continue;
            }

            $processedRows++;
        }

        return $processedRows;
    }

    private function getReportsForLoans(int $customNoiReportId, int $adminId): void
    {
        $this->info("--- CustomNoiUpdate ---");
        $startedAt = microtime(true);

        $customNoiReports = CustomNoiReports::whereCustomNoiReportId($customNoiReportId)->first();
        if (!$customNoiReports) {
            throw new RuntimeException('Invalid custom noi reports id.');
        }

        $noiRepService = app(ReportService::class);
        $loans = Loan::with(['client'])->whereIn('loan_id', $customNoiReports->imported_loan_ids)->get();

        if ($loans->count() < 1) {
            $this->info("No loans found, exit.");
            return;
        }

        $totalRows = 0;
        $processedRows = 0;
        foreach ($loans as $loan) {
            $this->info("- processing: {$loan->loan_id}");

            $totalRows++;

            $row = NoiReportPivot::where('loan_id', $loan->loan_id)
                ->where('created_at', '>=', Carbon::now()->format('Y-m-d 00:00:00'))
                ->where('name', 'noi2')
                ->first();

            if (!empty($row->noi_report_pivot_id)) {
                $processedRows++;
                continue;
            }

            $noiReport = $noiRepService->saveAndGetNoiReport(
                $loan->client,
                'noi2',
                'manual-report-cmd',
                $loan
            );

            if (!empty($noiReport->noi_report_id)) {
                $processedRows++;
            }
        }


        // finalize custom report
        $customNoiReports->setAttribute('processed', true);
        $customNoiReports->setAttribute('processed_at', now());
        $customNoiReports->setAttribute('processed_by', $adminId);
        $customNoiReports->saveQuietly();


        // after all reports is updated trigger event to create export file
        event(new CustomNoiReportReadyEvent($customNoiReportId));

        $finishedAt = microtime(true);
        $this->info("---- The END (" . round(($finishedAt - $startedAt), 2) . "sec) ----");
        $this->info("Total rows: {$totalRows}");
        $this->info("Processed rows: {$processedRows}");
    }
}
