<?php

declare(strict_types=1);

namespace Modules\Common\Console\Manual;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\LoanAddress;
use Modules\Common\Models\ClientMeta;
use Modules\Common\Services\ClientAddressService;

/**
 * php artisan script:common:deduplicate-client-address {client_id? : Optional specific client ID to process}
 */
final class DeduplicateClientAddressCommand extends CommonCommand
{
    protected $signature = 'script:common:deduplicate-client-address {client_id? : Optional specific client ID to process}';
    protected $description = 'Remove duplicate client addresses and update loan address links';

    public function __construct(
        private readonly ClientAddressService $clientAddressService,
    ) {
        parent::__construct();
    }

    public function handle(): void
    {
        $this->startLog(
            $this->description,
            'Starting deduplication of client addresses...'
        );

        $clientId = $this->argument('client_id');

        if ($clientId) {
            $client = Client::find($clientId, ['client_id']);

            if (!$client) {
                $this->error("Client with ID {$clientId} not found.");

                return;
            }

            DB::transaction(function () use ($client) {
                $this->deduplicateClientAddresses($client);
                $this->markClientAsProcessed($client);
            });
        } else {
            Client::select(['client_id'])
                ->where('client_id', '!=', 0)
                ->whereDoesntHave('metas', static function ($query) {
                    $query->where('key', 'addresses_were_fixed')
                        ->where('value', '1');
                })
                ->chunk(2000, function (Collection $clients) {
                    $this->info('--- chunk');

                    foreach ($clients as $client) {
                        DB::transaction(function () use ($client) {
                            $this->deduplicateClientAddresses($client);
                            $this->markClientAsProcessed($client);
                        });
                    }
                });
        }

        $this->finishLog(['Deduplication completed.']);
    }

    private function markClientAsProcessed(Client $client): void
    {
        ClientMeta::updateOrCreate([
            'client_id' => $client->getKey(),
            'key' => 'addresses_were_fixed',
            'value' => '1',
        ]);
    }

    private function deduplicateClientAddresses(Client $client): void
    {
        $addresses = ClientAddress::where('client_id', $client->getKey())
            ->select(['client_address_id', 'type', 'created_at', 'last', 'city_id', 'post_code', 'address'])
            ->orderBy('created_at')
            ->get();


        // Group by (type + semantic hash) to avoid N²
        $groups = $addresses->groupBy(function (ClientAddress $addr) {
            return $addr->type . '|' . md5(
                strtolower($addr->address . '|' . $addr->post_code . '|' . $addr->city_id)
            );
        });

        $toDelete = [];
        /** @var Collection $group */
        foreach ($groups as $group) {
            if ($group->count() == 1) {
                continue;

            }

            // sort by created_at ascending
            $sorted = $group->sortBy('created_at')->values();

            // pick first as “keeper” and temporary set it as not last
            $keeper = $sorted->first();

            // update other records
            foreach ($sorted as $duplicate) {
                if ($duplicate->getKey() == $keeper->getKey()) {
                    continue;
                }

                $this->updateLoanAddressLinks($duplicate, $keeper);
                $toDelete[] = $duplicate;
            }
        }

        $this->removeDuplicateAddresses($toDelete);
        $this->removeDuplicateLoanAddressLinks($client);
        $this->setProperLastValue($client);
    }

    private function updateLoanAddressLinks(ClientAddress $duplicate, ClientAddress $keeper): void
    {
        LoanAddress::where('client_address_id', $duplicate->getKey())
            ->update(['client_address_id' => $keeper->getKey()]);
    }

    private function removeDuplicateAddresses(array $toBeDeleted = []): void
    {
        if (count($toBeDeleted) > 0) {
            $idsToDelete = collect($toBeDeleted)->map(fn($addr) => $addr->getKey())->toArray();
            ClientAddress::whereIn('client_address_id', $idsToDelete)->forceDelete();
        }
    }

    private function removeDuplicateLoanAddressLinks(Client $client): void
    {
        $loanAddresses = LoanAddress::whereIn(
                'client_address_id',
                static function ($query) use ($client) {
                    $query->select('client_address_id')
                        ->from('client_address')
                        ->where('client_id', $client->getKey());
                }
            )
            ->orderBy('created_at')
            ->get(['id', 'loan_id', 'client_address_id', 'last']);

        $groupedByLoanAndAddress = $loanAddresses->groupBy(
            static fn(LoanAddress $item) => $item->loan_id . '-' . $item->client_address_id
        );

        $idsToDelete = [];
        foreach ($groupedByLoanAndAddress as $group) {
            $primaryLink = $group->firstWhere('last', 1) ?? $group->first();

            /** @var LoanAddress $link */
            foreach ($group as $link) {
                if ($link->getKey() !== $primaryLink->getKey()) {
                    $idsToDelete[] = $link->getKey();
                }
            }
        }

        if (count($idsToDelete) > 0) {
            LoanAddress::whereIn('id', $idsToDelete)->forceDelete();
        }
    }

    private function setProperLastValue(Client $client): void
    {
        $address = ClientAddress::where('client_id', $client->client_id)
            ->where('active', 1)
            ->where('type', 'id_card')
            ->orderBy('created_at', 'desc')
            ->first();
        if ($address) {
            $address->last = 1;
            $address->save();

            ClientAddress::where('client_id', $client->client_id)
                ->where('client_address_id', '!=', $address->client_address_id)
                ->where('type', 'id_card')
                ->update(['last' => 0]);

        } else {
            $this->info('No id_card address for #' . $client->client_id);
        }

        $address2 = ClientAddress::where('client_id', $client->client_id)
            ->where('active', 1)
            ->where('type', 'current')
            ->orderBy('created_at', 'desc')
            ->first();
        if ($address2) {
            $address2->last = 1;
            $address2->save();

            ClientAddress::where('client_id', $client->client_id)
                ->where('client_address_id', '!=', $address2->client_address_id)
                ->where('type', 'current')
                ->update(['last' => 0]);

        } else {
            $this->info('No current address for #' . $client->client_id);
        }
    }
}
