<?php

namespace Modules\Common\Console\Manual;

use Modules\Common\Models\Loan;
use Modules\Common\Models\MvrReport;
use Modules\Common\Models\MvrReportPivot;
use Modules\Common\Console\CommonCommand;

class SetMvrParsedData extends CommonCommand
{
    protected $name = 'script:set-mvr-parsed_data';
    protected $description = 'Set SettlementCode & LocationCode for mvr_pivot';

    public function handle()
    {
        $totalRows = 0;
        $skippedRows = 0;
        $processedRows = 0;

        Loan::select('loan.*')
            ->leftJoin('mvr_report_pivot as mrp', function($join){
                $join->on('mrp.loan_id', 'loan.loan_id')
                     ->where('mrp.last', 1);
            })
            // ->where('loan_id', 499296)
            ->whereIn('loan.loan_status_id', [6, 9])
            ->where(function($q){
                $q->whereNull('mrp.loan_id')
                  ->orWhereNull('mrp.municipality')
                  ->orWhere('mrp.municipality', '');
            })
            ->orderBy('loan.loan_id')
            ->chunkById(
                5000,
                function ($loans) use (&$totalRows, &$skippedRows, &$processedRows) {

                    $totalRows += $loans->count();
                    $this->info('-- chunk: ' . $totalRows);

                    foreach ($loans as $loan) {

                        $pivots = MvrReportPivot::where('loan_id', $loan->loan_id)->orderBy('created_at')->get();

                        // if no pivot -> create it
                        if ($pivots->isEmpty()) {
                            $client = $loan->client;
                            if (!$client) {
                                $skippedRows++;
                                $this->info('No client for loan #' . $loan->loan_id);
                                continue;
                            }

                            $mvrRep = MvrReport::where('pin', $client->pin)
                                ->whereRaw("(parsed_data IS NOT NULL AND parsed_data != '[]')")  // fixed
                                ->orderBy('created_at', 'desc')
                                ->first();

                            if ($mvrRep && !empty($mvrRep->data)) {
                                try {
                                    $data = json_decode($mvrRep->data, true);

                                    // Extract SettlementCode and LocationCode
                                    $settlementCode = data_get($data, 'PermanentAddress.SettlementCode');
                                    $locationCode = data_get($data, 'PermanentAddress.LocationCode');
                                    $municipality = data_get($data, 'PermanentAddress.MunicipalityName');
                                    $district = data_get($data, 'PermanentAddress.DistrictName');

                                    $mvrReportPivot = new MvrReportPivot();
                                    $mvrReportPivot->mvr_report_id = $mvrRep->mvr_report_id;
                                    $mvrReportPivot->client_id = $client->client_id;
                                    $mvrReportPivot->loan_id = $loan->loan_id;
                                    $mvrReportPivot->last = 1;
                                    $mvrReportPivot->created_at = $loan->created_at;
                                    $mvrReportPivot->settlement_code = $settlementCode;
                                    $mvrReportPivot->location_code = $locationCode;
                                    $mvrReportPivot->municipality = $municipality;
                                    $mvrReportPivot->district = $district;
                                    $mvrReportPivot->save();

                                    $processedRows++;

                                } catch(\Throwable $e) {
                                    $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
                                    $this->info('Failed to create mvr pivot, loan #' . $loan->loan_id . ' - ' . $msg);
                                }
                            }

                            continue;
                        }

                        $lastPivot = $pivots->last();
                        // if (!empty($pivot->municipality)) {
                        //     continue; // already handled
                        // }

                        foreach ($pivots as $pivot) {

                            // Set all rows' `last` field to 0 except the last one
                            $pivot->last = $pivot->is($lastPivot) ? 1 : 0;

                            if (
                                $pivot->is($lastPivot)
                                && (
                                    empty($pivot->settlement_code)
                                    || empty($pivot->location_code)
                                    || empty($pivot->municipality)
                                    || empty($pivot->district)
                                )
                            ) {
                                $mvrReport = MvrReport::find($pivot->mvr_report_id);
                                if ($mvrReport && !empty($mvrReport->data)) {
                                    $settlementCode = null;
                                    $locationCode = null;
                                    $municipality = null;
                                    $district = null;

                                    try {
                                        $data = json_decode($mvrReport->data, true);

                                        // Extract SettlementCode and LocationCode
                                        $settlementCode = data_get($data, 'PermanentAddress.SettlementCode');
                                        $locationCode = data_get($data, 'PermanentAddress.LocationCode');
                                        $municipality = data_get($data, 'PermanentAddress.MunicipalityName');
                                        $district = data_get($data, 'PermanentAddress.DistrictName');
                                    } catch(\Throwable $e) {
                                        $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
                                        $this->info('Failed to create mvr pivot, loan #' . $loan->loan_id . ' - ' . $msg);
                                    }

                                    $pivot->settlement_code = $settlementCode;
                                    $pivot->location_code = $locationCode;
                                    $pivot->municipality = $municipality;
                                    $pivot->district = $district;
                                }
                            }

                            $pivot->saveQuietly();
                        }

                        $processedRows++;
                    }
                },
                'loan.loan_id',
                'loan_id'
            );

        $this->info("--- SetMvrParsedData ---");
        $this->info("Total rows: {$totalRows}");
        $this->info("Skipped rows: {$skippedRows}");
        $this->info("Processed rows: {$processedRows}");
    }
}

