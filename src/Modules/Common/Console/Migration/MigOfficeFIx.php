<?php

namespace Modules\Common\Console\Migration;

ini_set('max_execution_time', 3000);
ini_set('memory_limit', '1024M');

use Mockery\Exception;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeProduct;

class MigOfficeFIx extends CommonCommand
{
    use MigrationTrait;

    private string $migLogDbName = '';

    protected $name = 'script:mig-office-fix';
    protected $signature = 'script:mig-office-fix {office_db} {loan_id?}'; // nefin_office1, nefin_office2, nefin_online
    protected $description = 'Set proper office ids for migrated loans';

    public function __construct()
    {
        parent::__construct();
    }

    private static function validateConnection(): void
    {
        $connectionParams = [
            'NEFIN_MS_URL',
            'NEFIN_MS_TOKEN',
        ];

        foreach ($connectionParams as $paramName) {
            if (empty(env($paramName))) {
                echo 'Missing db connection: ' . $paramName;
                exit;
            }
        }
    }

    public function handle(): void
    {
        $this->validateConnection();

        $this->log("--- START ---");
        $start = microtime(true);
        $this->startLog($this->getClassName().'::'.__FUNCTION__);


        $db = $this->argument('office_db');
        if (empty($db)) {
            echo 'No db param provided';
            return;
        }

        $this->migLogDbName = $db;

        $loanId = (int) $this->argument('loan_id');
        if ($loanId) {
            $this->loanId = $loanId;
        }


        $todo = [];
        $done = [];
        if ($loanId) {

            $todo = [$loanId];

            $loan = Loan::where('loan_id', $loanId)
                ->where('migration_db', $this->migLogDbName)
                ->first();

            if ($this->proceedLoan($loan)) {
                $done = [$loanId];
            }

        } else {

            Loan::where('migration_db', $this->migLogDbName)
                ->where('office_id', '=', 1)
                ->orderBy('loan_id')
                ->chunk(200, function ($loans) use(&$todo, &$done) {
                    foreach ($loans as $loan) {
                        $todo = [$loan->loan_id];

                        if ($this->proceedLoan($loan)) {
                            $done = [$loan->loan_id];
                        }
                    }
                });
        }

        $msg = 'Processed: ' . count($done) . ', Total: ' . count($todo);
        $this->finishLog([], count($todo), count($done), $msg);
        $this->log($msg);
        $this->log('Exec.time: ' . round((microtime(true) - $start), 2) . ' second(s)');
    }

    private function proceedLoan(Loan $loan)
    {
        // get proper nefin credit and office name
        $credit = $this->getNefinCredit($loan);

        // get office
        $office = $this->getOffice($credit);

        // set propert office
        $loan->office_id = $office->office_id;
        $loan->save();

        // add product office relation
        $op = OfficeProduct::where([
            'office_id'  => $office->office_id,
            'product_id' => $loan->product_id,
        ])->first();
        if (empty($op->office_id)) {
            $op = new OfficeProduct();
            $op->fill([
                'office_id'  => $office->office_id,
                'product_id' => $loan->product_id,
            ]);
            $op->save();
        }

        return true;
    }

    private function getNefinCredit(Loan $loan)
    {
        $query = "
                SELECT credit.*, office.OFFICE_NAME
                FROM BizCreditHeaders credit
                JOIN BizOffices office ON office.OFFICE_ID = credit.OFFICE_ID
                WHERE credit.CREDIT_ID = '" . (int) $loan->migration_nefin_id ."'
        ";
        $nfRes = $this->getNefinDataFromMS($query, $loan->migration_db);
        if (!empty($nfRes['error'])) {
            throw new Exception(__METHOD__ . '(): ' . $nfRes['error']);
        }

        return (object) $nfRes['data'][0];
    }

    private function getOffice($credit): Office
    {
        if (empty($credit->OFFICE_NAME)) {
            throw new Exception('Office without name found!');
        }

        if ($credit->OFFICE_NAME == 'Офис Интернет') {
            $credit->OFFICE_NAME = 'офис Централата'; // mapping with our db
        }

        $offName = preg_replace('/(офис|Офис|Оф.)/i', '', $credit->OFFICE_NAME);
        if ('Офс Берковица' == $credit->OFFICE_NAME) {
            $offName = 'Берковица';
        }

        $office = Office::where('name', 'ILIKE', '%' . $offName . '%')->first();
        if (!empty($office->office_id)) {
            return $office;
        }

        $office = new Office();
        $office->office_type_id = 1;
        $office->name = $credit->OFFICE_NAME;
        $office->save();

        return $office;
    }
}
