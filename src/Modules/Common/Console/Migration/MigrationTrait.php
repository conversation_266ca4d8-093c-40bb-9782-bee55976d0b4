<?php

namespace Modules\Common\Console\Migration;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\AdministratorOffice;
use Modules\Common\Models\AdministratorSetting;
use Modules\Common\Models\Consultant;
use Modules\Common\Models\ConsultantOffice;
use Modules\Common\Models\Office;
use Modules\Head\Repositories\CityRepository;

trait MigrationTrait
{
    public function getNefinDataFromMS(string $query, string $db = '', bool $debug = false)
    {
        $url = env('NEFIN_MS_URL');
        $token = env('NEFIN_MS_TOKEN');

        if (empty($url) || empty($token)) {
            return ;
        }

        $trimmedQuery = trim($query);
        $finalQuery = str_replace("\r\n", " ", $trimmedQuery);
        $finalQuery = str_replace("\n", " ", $finalQuery);
        $finalQuery = preg_replace('/\s+/', ' ', $finalQuery);


        // Append the query parameter to the URL
        $urlWithParam = $url . '?query=' . urlencode($finalQuery);
        if (!empty($db)) {
            $urlWithParam .= '&db=' . $db;
        }

        // Initialize cURL
        $ch = curl_init();

        // Set the cURL options
        curl_setopt($ch, CURLOPT_URL, $urlWithParam);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');

        $headers = array();
        $headers[] = 'Authorization: ' . $token;
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Execute the cURL request
        $response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            curl_close($ch);
            return array('error' => $error_msg);
        }

        // Close the cURL handle
        curl_close($ch);

        // Decode the JSON response
        $decodedResponse = json_decode($response, true);

        // Check for JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            return array('error' => 'JSON decode error: ' . json_last_error_msg());
        }

        // Check for the 'error' key in the decoded response
        if (isset($decodedResponse['error'])) {
            return array('error' => $decodedResponse['error']);
        }

        // Check for the 'data' key in the decoded response
        if (isset($decodedResponse['data'])) {
            return ['data' => $decodedResponse['data']];
        }

        // If neither 'error' nor 'data' keys are present
        return array('error' => 'Unexpected response format');
    }

    private function extractAddressValuesCommon(
        string $cityStr,
        ?string $addressStr = null
    ): array {
        $parsedData = [
            'city' => null,
            'city_id' => null,
            'street' => null,
            'post_code' => null,
            'municipality' => null,
            'district' => null,
            'building_number' => null,
            'building_entrance' => null,
            'building_floor' => null,
            'building_apartment' => null,
        ];

        $testString = trim($cityStr); // should contains full address
        $parsedData['address'] = $testString;
        $parsedData['address_full'] = $testString;

        // 1. check for one-two words only, so we can assume it's a city
        $wordsCount = count(explode(' ', $testString));
        if (preg_match('/^[\p{L}]+\s?[\p{L}]*$/u', $testString) && $wordsCount <= 2) {
            if ($wordsCount > 1) {
                $pattern = '/\b(гр\.?\s?|град\s?|с\.?\s?|село\s?)\b/u';
                $replacedString = trim(preg_replace($pattern, '', $testString));
                $parsedData['city'] = $replacedString;
            } else {
                $parsedData['city'] = $testString;
            }
        }

        // try to exract: city, municipality, district if they are separated by comma
        $parts = explode(',', $testString);
        if (count($parts) > 1) {
            foreach ($parts as $part) {
                $part = trim($part);

                // first search for "село" because of case: обл. Благоевград общ. Петрич с. Кърналово ул. Шипка 6
                // where we founc city = град общ
                preg_match('/\b(с|село)[\.\s]{1,3}(\w+)/iu', $part, $parseRes);
                if (!empty($parseRes[2])) {
                    $parsedData['city'] = $parseRes[2];
                }

                // if no "село" found, search for "град"
                if (empty($parsedData['city'])) {
                    preg_match('/\b(гр|град)[\.| ][\s]{0,3}(\w+)/iu', $part, $parseRes);
                    if (!empty($parseRes[2])) {
                        $parsedData['city'] = $parseRes[2];
                    }
                }

                if (empty($parsedData['municipality'])) {
                    preg_match('/(общ|Обш|Община|Обшина)[\.| ][\s]{0,3}(\w+)/iu', $part, $parseRes);
                    if (!empty($parseRes[2])) {
                        $parsedData['municipality'] = $parseRes[0];
                    }
                }

                if (empty($parsedData['district'])) {
                    preg_match('/(обл|област|Обл )(.|\s)[\s]{0,3}(\w+)/iu', $part, $parseRes);
                    if (!empty($parseRes[2])) {
                        $parsedData['district'] = $parseRes[0];
                    }
                }
            }
        }

        // if something terribly wrong lets parse full address
        preg_match('/(?:^|,|.|\s+)(с|село)[\.| ][\s]{0,3}(\w+)/iu', $testString, $cityRes);
        if (!empty($cityRes[2]) && (empty($parsedData['city']) || $parsedData['city'] != $cityRes[2])) {
            if (!preg_match('/^(село|блок|град|[0-9]+)$/i', $cityRes[2])) {
                $parsedData['city'] = $cityRes[2];
            }
        }
        preg_match('/\b(с|село)[\.| ][\s]{0,3}(\w+(?:\s\w+)?)/iu', $testString, $cityRes);
        if (!empty($cityRes[2]) && !preg_match('/^(село|блок|град)$/i', $cityRes[2])) {
            $parsedData['city2'] = $cityRes[2];
        }
        if (
            empty($parsedData['city'])
            || preg_match('/общ/i', $parsedData['city'])
            || preg_match('/Херсон/i', $parsedData['city'])
            || preg_match('/\b(гр|град)[\.| ][\s]{0,3}(\w+)/iu', $testString)
        ) {
            preg_match('/\b(гр|град)[\.| ][\s]{0,3}(\w+)/iu', $testString, $cityRes);
            if (!empty($cityRes[2]) && !preg_match('/^(село|блок|град|[0-9]+)$/i', $cityRes[2])) {
                $parsedData['city'] = $cityRes[2];
            }

            preg_match('/(гр|град)[\.| ][\s]{0,3}(\w+(?:\s\w+)?)/iu', $testString, $cityRes);
            if (!empty($cityRes[2]) && !preg_match('/^(село|блок|град|[0-9]+)$/i', $cityRes[2])) {
                $parsedData['city2'] = $cityRes[2];
            }
        }
        if (empty($parsedData['municipality'])) {
            preg_match('/(общ|Обш|Община|Обшина)[\.| ][\s]{0,3}(\w+)/iu', $testString, $munRes);
            if (!empty($munRes[2])) {
                $parsedData['municipality'] = $munRes[0];
            }
        }
        if (empty($parsedData['district'])) {
            preg_match('/(обл|област|Обл )(.|\s)[\s]{0,3}(\w+)/iu', $testString, $distRes);
            if (!empty($distRes[2])) {
                $parsedData['district'] = $distRes[0];
            }
        }

        if (preg_match('/(булевард|бул |бул.|улица|ул |ул\.?)\s*([\p{L}\p{M}]+)\s*(\d+)?/iu', $testString, $matches)) {
            $parsedData['street'] = trim($matches[1]) . ' ' .trim($matches[2]);

            if (isset($matches[3])) {
                $parsedData['building_number'] = trim($matches[3]);
            }
        }

        if (preg_match('/(вх\.?|вход\.?)[\s]*([0-9A-Zа-яА-Я]+)/iu', $testString, $matches)) {
            $parsedData['building_entrance'] = trim($matches[2]);
        }

        if (preg_match('/(ет|ет\.|етаж\.?)\s*(\d+)/iu', $testString, $matches)) {
            $parsedData['building_floor'] = trim($matches[2]);
        }

        if (preg_match('/(ап|ап\.|апарт\.?|апартамент\.?)\s*(\d+)/iu', $testString, $matches)) {
            $parsedData['building_apartment'] = trim($matches[2]);
        }

        if (!empty($addressStr)) {
            $words = preg_split('/\s+/', trim($addressStr));
            foreach ($words as $index => $word) {

                // Handle street names that have space in them
                if (empty($parsedData['street']) && !preg_match('/\d+/', $word) && (!isset($words[$index + 1]) || !preg_match('/\d+/', $words[$index + 1]))) {
                    $parsedData['street'] = isset($parsedData['street']) ? $parsedData['street'] . ' ' . $word : $word;
                    continue;
                }

                if (empty($parsedData['building_number']) && preg_match('/\d+/', $word)) {
                    $parsedData['building_number'] = preg_replace('/[^a-z0-9]*$/i', '', $word);

                }

                if (empty($parsedData['building_entrance']) && preg_match('/вх\.?/iu', $word) || preg_match('/вход/iu', $word)) {
                    $parsedData['building_entrance'] = isset($words[$index + 1]) ? $words[$index + 1] : null;
                }

                if (empty($parsedData['building_floor']) && preg_match('/ет\.?/iu', $word) || preg_match('/етаж/iu', $word)) {
                    $parsedData['building_floor'] = isset($words[$index + 1]) ? preg_replace('/[^a-z0-9]*$/i', '', $words[$index + 1]) : null;
                }

                if (empty($parsedData['building_apartment']) && preg_match('/ап\.?/iu', $word) || preg_match('/апартамент/iu', $word)) {
                    $parsedData['building_apartment'] = isset($words[$index + 1]) ? preg_replace('/[^a-z0-9]*$/i', '', $words[$index + 1]) : null;
                }
            }
        }

        // poredna totalka: manual corrections
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Благоеврад') {
            $parsedData['city'] = 'Благоевград';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Далгопол') {
            $parsedData['city'] = 'Дългопол';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Костенкоци') {
            $parsedData['city'] = 'Костенковци';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Давитково') {
            $parsedData['city'] = 'Давидково';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Елховец477') {
            $parsedData['city'] = 'Елховец';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Чепелае') {
            $parsedData['city'] = 'Чепеларе';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Рудзоем') {
            $parsedData['city'] = 'Рудозем';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Касък') {
            $parsedData['city'] = 'Доспат';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Eтрополе') { // yeah baby, they use english "E" as first letter
            $parsedData['city'] = 'Етрополе';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Ряка') {
            $parsedData['city'] = 'Река';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Сингурларе') {
            $parsedData['city'] = 'Сунгурларе';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'мьрчево') {
            $parsedData['city'] = 'Марчево';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Недлино') {
            $parsedData['city'] = 'Неделино';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Слиивница') {
            $parsedData['city'] = 'Сливница';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Алиговкса') {
            $parsedData['city'] = 'Алиговска';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Пролеж') {
            $parsedData['city'] = 'Пролеша';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Дрангово240') {
            $parsedData['city'] = 'Дрангово';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'ЗАГОРЧЕ') {
            $parsedData['city'] = 'Загориче';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Каоливово') {
            $parsedData['city'] = 'Каолиново';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Синивир') {
            $parsedData['city'] = 'Сини вир';
            $parsedData['city_id'] = '5059';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Нефит') {
            $parsedData['city'] = 'Неофит Рилски';
            $parsedData['city_id'] = '593';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Браночово') {
            $parsedData['city'] = 'Браничево';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Аксакова') {
            $parsedData['city'] = 'Аксаково';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'ДОБРОТОЦА') {
            $parsedData['city'] = 'Добротица';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Чврник') {
            $parsedData['city'] = 'Черник';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Сретковец') {
            $parsedData['city'] = 'Средковец';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Искьр') {
            $parsedData['city'] = 'Искър';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Браничово') {
            $parsedData['city'] = 'Браничево';
        }
        if (!empty($parsedData['city']) && ($parsedData['city'] == 'Орлиак' || $parsedData['city'] == '0рляк')) {
            $parsedData['city'] = 'Орляк';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Лудогогорци') {
            $parsedData['city'] = 'Лудогорци';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Друменво') {
            $parsedData['city'] = 'Друмево';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Струйно') {
            $parsedData['city'] = 'Струино';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Осeновец') {
            $parsedData['city'] = 'Осеновец';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Чврноглавци') {
            $parsedData['city'] = 'Черноглавци';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Дълкопол') {
            $parsedData['city'] = 'Дългопол';
        }
        if (!empty($parsedData['city']) && $parsedData['city'] == 'Порайно') {
            $parsedData['city'] = 'Поройно';
        }
        if (!empty($parsedData['city']) && ($parsedData['city'] == 'Такач' || $parsedData['city'] == 'Тикач')) {
            $parsedData['city'] = 'Тъкач';
        }
        if (empty($parsedData['city']) && preg_match('/ц.Миген/i', $testString)) {
            $parsedData['city'] = 'Мъглен';
        }
        //Ex: грт. София,ж.к. Дружба 271, вход Б, ет.6, апт. 44
        if (preg_match('/(град|гр|грт\.|гр.)\s+(София|СОГФИЯ)/ui', $testString)) {
            $parsedData['city'] = 'София';
        }
        if (preg_match('/(с\. Смилян ул)/ui', $testString)) {
            $parsedData['city'] = 'Смилян';
        }
        if (preg_match('/(гр \.Асеновград)/ui', $testString)) {
            $parsedData['city'] = 'Асеновград';
        }
        if (preg_match('/(грПловдив)/ui', $testString)) {
            $parsedData['city'] = 'Пловдив';
        }
        if (preg_match('/(Върбицаул)/ui', $testString)) {
            $parsedData['city'] = 'Върбица';
        }
        if (preg_match('/(с.Бяля Река)/ui', $testString)) {
            $parsedData['city'] = 'Бяла Река';
        }
        if (preg_match('/(Шумегр.Шумен)/ui', $testString)) {
            $parsedData['city'] = 'Шумен';
        }
        if (preg_match('/(Шуменгр.Шумен)/ui', $testString)) {
            $parsedData['city'] = 'Шумен';
        }
        if (preg_match('/(с.МЪГЛЕН)/ui', $testString)) {
            $parsedData['city'] = 'Мъглен';
        }
        if (preg_match('/(с.Зайцино ореше)/ui', $testString)) {
            $parsedData['city'] = 'Зайчино ореше';
        }
        if (preg_match('/(гр.Новин пазар)/ui', $testString)) {
            $parsedData['city'] = 'Нови пазар';
        }

        //Example: "Крапец, общ. Мездра, ул. Враца 62"
        if (
            empty($parsedData['city'])
            && !preg_match('/^(гр\.|град\s|c\.|село\s)/iu', $testString)
            && preg_match('/^(\p{L}+)/iu', $testString, $matches)
            && preg_match('/^(?!обл|обш|общ).*/iu', $testString)
        ) {
            $parsedData['city'] = trim($matches[1]);
        }


        // turbo stupid cleaning, Ex: "обл.Благоевград общ.Сатовча с."
        if (isset($parsedData['city2']) && $parsedData['city2'] == "общ") {
            $parsedData['city2'] = null;
        }


        // if no city found , lets use община and set city based on it
        if (empty($parsedData['city']) && empty($parsedData['city2'])) {
            // Ex: обл.Смолян общ. Смолян ул.Оборище 8
            if (!empty($parsedData['district'])) {
                preg_match('/(обл\.?|област)[\s]{0,3}(\w+)/iu', $parsedData['district'], $matches);
                if (!empty($matches[2])) {
                    $parsedData['city'] = $matches[2];
                } else {
                    $parsedData['city'] = $parsedData['district'];
                }
            }
            // Ex: община лом ул. шести септември 44 вх в ет. 4 ап. 8
            if (!empty($parsedData['municipality'])) {
                preg_match('/\b(общ\.?|обш\.?|община|обшина)[\s]*(\w+)/iu', $parsedData['municipality'], $matches);

                if (!empty($matches[2])) {
                    $parsedData['city'] = $matches[2];
                } else {
                    $parsedData['city'] = $parsedData['municipality'];
                }
            }
        }


        if (!empty($parsedData['city'])) {
            $cityRepo = \App::make(CityRepository::class);
            $city = $cityRepo->getCachedCityByName($parsedData['city']);
            if (empty($city->city_id) && !empty($parsedData['city2'])) {
                $city = $cityRepo->getCachedCityByName($parsedData['city2']);
            }
            if (!empty($city->city_id)) {
                $parsedData['city_id'] = $city->city_id;
            }
        }


        // remove unused keys: city, street
        unset($parsedData['city']);
        unset($parsedData['street']);
        if (isset($parsedData['city2'])) {
            unset($parsedData['city2']);
        }


        return $parsedData;
    }

    private function prepareNefinPayments(array $payments): array
    {
        $result = [];

        foreach ($payments as $res) {
            // ebaniy pizdec
            $res['ROW_TYPE'] = trim($res['ROW_TYPE']);
            $res['LINE_TYPE'] = !empty($res['LINE_TYPE']) ? trim($res['LINE_TYPE']) : $res['LINE_TYPE'];

            // ebaniy pizdec2,  Ex: php artisan script:mig-office nefin_office1 7712138798, where amount = ".0100"
            if (!is_null($res['APPLIED_CAPITAL_PAYMENT']) && $res['APPLIED_CAPITAL_PAYMENT'][0] == '.') {
                $res['APPLIED_CAPITAL_PAYMENT'] = '0' . $res['APPLIED_CAPITAL_PAYMENT'];
            }
            if (!is_null($res['APPLIED_INTEREST_PAYMENT']) && $res['APPLIED_INTEREST_PAYMENT'][0] == '.') {
                $res['APPLIED_INTEREST_PAYMENT'] = '0' . $res['APPLIED_INTEREST_PAYMENT'];
            }
            if (!is_null($res['APPLIED_FORFEIT_PAYMENT']) && $res['APPLIED_FORFEIT_PAYMENT'][0] == '.') {
                $res['APPLIED_FORFEIT_PAYMENT'] = '0' . $res['APPLIED_FORFEIT_PAYMENT'];
            }

            $nefPayment = (object) $res;
            $principal = floatToInt(floatval($nefPayment->APPLIED_CAPITAL_PAYMENT));
            $interest = floatToInt(floatval($nefPayment->APPLIED_INTEREST_PAYMENT));
            $penalty = floatToInt(floatval($nefPayment->APPLIED_FORFEIT_PAYMENT));

            if ($principal <= 0 && $interest <= 0 && $penalty <= 0) {
                continue;
            }

            $result[] = $nefPayment;
        }

        return $result;
    }

    private function filterNefinLate(
        array $lateRows,
        array $installments,
        bool $hasJoinedInstallments
    ): array {

        if (empty($lateRows)) {
            return [];
        }

        if (
            $hasJoinedInstallments
            || (count($installments) == 1 && count($lateRows) > 1)
        ) {
            $tmpLateRow = null;

            foreach ($lateRows as $lateRow) {
                if (empty($tmpLateRow)) {
                    $tmpLateRow = $lateRow;
                    continue;
                }

                $tmpLateRow->DUE_AMOUNT += $lateRow->DUE_AMOUNT;
                $tmpLateRow->CAPITAL += $lateRow->CAPITAL;
                $tmpLateRow->INTEREST += $lateRow->INTEREST;
                $tmpLateRow->CAPITAL_PAYMENT += $lateRow->CAPITAL_PAYMENT;
                $tmpLateRow->INTEREST_PAYMENT += $lateRow->INTEREST_PAYMENT;
                $tmpLateRow->AMOUNT_PAYMENT += $lateRow->AMOUNT_PAYMENT;
                $tmpLateRow->REST_CAPITAL += $lateRow->REST_CAPITAL;
                $tmpLateRow->REST_INTEREST += $lateRow->REST_INTEREST;
                $tmpLateRow->REST_AMOUNT += $lateRow->REST_AMOUNT;
                $tmpLateRow->FORFEIT += $lateRow->FORFEIT;
                $tmpLateRow->FORFEIT_PAYMENT += $lateRow->FORFEIT_PAYMENT;
                $tmpLateRow->FORFEIT_REST += $lateRow->FORFEIT_REST;

            }

            $lateRows = [];
            $lateRows[] = $tmpLateRow;

        } else {
            // sometimes there are several late interest/penalty lines for one row, we need to join them

            $tmpLateRows = [];

            foreach ($lateRows as $lateRow) {
                if (empty($tmpLateRows[$lateRow->ROW_ID])) {
                    $tmpLateRows[$lateRow->ROW_ID] = $lateRow;
                    continue;
                }

                $tmpLateRows[$lateRow->ROW_ID]->DUE_AMOUNT += $lateRow->DUE_AMOUNT;
                $tmpLateRows[$lateRow->ROW_ID]->CAPITAL += $lateRow->CAPITAL;
                $tmpLateRows[$lateRow->ROW_ID]->INTEREST += $lateRow->INTEREST;
                $tmpLateRows[$lateRow->ROW_ID]->CAPITAL_PAYMENT += $lateRow->CAPITAL_PAYMENT;
                $tmpLateRows[$lateRow->ROW_ID]->INTEREST_PAYMENT += $lateRow->INTEREST_PAYMENT;
                $tmpLateRows[$lateRow->ROW_ID]->AMOUNT_PAYMENT += $lateRow->AMOUNT_PAYMENT;
                $tmpLateRows[$lateRow->ROW_ID]->REST_CAPITAL += $lateRow->REST_CAPITAL;
                $tmpLateRows[$lateRow->ROW_ID]->REST_INTEREST += $lateRow->REST_INTEREST;
                $tmpLateRows[$lateRow->ROW_ID]->REST_AMOUNT += $lateRow->REST_AMOUNT;
                $tmpLateRows[$lateRow->ROW_ID]->FORFEIT += $lateRow->FORFEIT;
                $tmpLateRows[$lateRow->ROW_ID]->FORFEIT_PAYMENT += $lateRow->FORFEIT_PAYMENT;
                $tmpLateRows[$lateRow->ROW_ID]->FORFEIT_REST += $lateRow->FORFEIT_REST;
            }

            $lateRows = $tmpLateRows;
        }

        return $lateRows;
    }

    private function getNefinCreatorData($nefinCredit, string $db): array
    {
        if (
            empty($nefinCredit->CREATED_DATE)
            || empty($nefinCredit->CrUserID)
            || intval($nefinCredit->CrUserID) < 1
        ) {
            return [];
        }

        return [
            'id'          => $nefinCredit->CrUserID,
            'first_name'  => $nefinCredit->CrFirstName,
            'middle_name' => $nefinCredit->CrSurName,
            'last_name'   => $nefinCredit->CrLastName,
            'date'        => Carbon::parse($nefinCredit->CREATED_DATE),
            'db'          => $db,
            'username'    => $nefinCredit->CrUserName,
            'password'    => Hash::make($nefinCredit->CrPassword),
            'password_raw'=> $nefinCredit->CrPassword,
        ];
    }

    private function getNefinModifierData($nefinCredit, string $db): array
    {
        if (
            empty($nefinCredit->MODIFIED_BY)
            || empty($nefinCredit->UserID)
            || intval($nefinCredit->MODIFIED_BY) < 1
        ) {
            return [];
        }

        return [
            'id'          => $nefinCredit->UserID,
            'first_name'  => $nefinCredit->FirstName,
            'middle_name' => $nefinCredit->SurName,
            'last_name'   => $nefinCredit->LastName,
            'date'        => Carbon::parse($nefinCredit->MODIFIED_DATE),
            'db'          => $db,
            'username'    => $nefinCredit->UserName,
            'password'    => Hash::make($nefinCredit->Password),
            'password_raw'=> $nefinCredit->Password,
        ];
    }

    private function getOrCreateAdmin(array $modData, ?int $officeId = null)
    {
        $admin = DB::selectOne(DB::raw("select * from administrator where migration_db = '" . $modData['db'] . "' AND migration_id = '" . $modData['id'] . "' LIMIT 1"));
        if (empty($admin->administrator_id)) {

            $rawPass = 12345;
            $username = 'migrated_admin_' . $modData['id'] . time();
            $password = '$2y$10$iLX8SP0ljoM8QUyPvxXFA.Oa6OKUsQWkWys6mGR0NCQJA7ys9xnIK'; // $rawPass

            if (!empty($modData['username']) && !empty($modData['password'])) {
                $username = $modData['username'] . '_' . $modData['db'];
                $password = $modData['password'];
            }

            // check if username is used
            $admin = DB::selectOne(DB::raw("select * from administrator where username = '" . $username . "' LIMIT 1"));
            if (!empty($admin->administrator_id)) {
                $username = $modData['username'] . '_2_' . $modData['db'];

                // check if username is used
                $admin = DB::selectOne(DB::raw("select * from administrator where username = '" . $username . "' LIMIT 1"));
                if (!empty($admin->administrator_id)) {
                    $username = $modData['username'] . '_3_' . $modData['db'];

                    $admin = DB::selectOne(DB::raw("select * from administrator where username = '" . $username . "' LIMIT 1"));
                    if (!empty($admin->administrator_id)) {
                        $username = $modData['username'] . '_4_' . $modData['db'];

                        $admin = DB::selectOne(DB::raw("select * from administrator where username = '" . $username . "' LIMIT 1"));
                        if (!empty($admin->administrator_id)) {
                            $username = $modData['username'] . '_5_' . $modData['db'];

                            $admin = DB::selectOne(DB::raw("select * from administrator where username = '" . $username . "' LIMIT 1"));
                            if (!empty($admin->administrator_id)) {
                                $username = $modData['username'] . '_6_' . $modData['db'];

                                $admin = DB::selectOne(DB::raw("select * from administrator where username = '" . $username . "' LIMIT 1"));
                                if (!empty($admin->administrator_id)) {
                                    $username = $modData['username'] . '_7_' . $modData['db'];
                                }
                            }
                        }
                    }
                }
            }

            $admin = new Administrator();
            $admin->fill([
                'username' => $username,
                'password' => $password,
                'first_name' => $modData['first_name'],
                'middle_name' => !empty($modData['middle_name']) ? $modData['middle_name'] : '',
                'last_name' => !empty($modData['last_name']) ? $modData['last_name'] : 'No last name',
                'migration_db' => $modData['db'],
                'migration_id' => $modData['id'],
                'remember_token' => (!empty($modData['password_raw']) ? $modData['password_raw'] : $rawPass),
            ]);
            $admin->save();


            $adminSet = new AdministratorSetting();
            $adminSet->administrator_id = $admin->administrator_id;
            $adminSet->setting_key = 'max_discount_percent_administrator';
            $adminSet->value = 0;
            $adminSet->save();


            $adminSet = new AdministratorSetting();
            $adminSet->administrator_id = $admin->administrator_id;
            $adminSet->setting_key = 'count_of_reports_per_month_for_a_given_client_administrator';
            $adminSet->value = 20;
            $adminSet->save();
        } else {

        }

        if (!empty($officeId)) {
            $ao = AdministratorOffice::where([
                'administrator_id' => $admin->administrator_id,
                'office_id' => $officeId,
            ])->first();

            if (empty($ao->office_id)) {
                $adminOf = new AdministratorOffice();
                $adminOf->administrator_id = $admin->administrator_id;
                $adminOf->office_id = $officeId;
                $adminOf->save();
            }
        }

        return $admin;
    }

    private function getNefinConsultantData($nefinCredit, string $db): array
    {
        if (empty($nefinCredit->ADVISER_ID)) {
            return [];
        }

        return [
            'id'   => $nefinCredit->ADVISER_ID,
            'db'   => $db,
            'name' => $nefinCredit->ADVISER_NAME ?? 'Unknown #' . $nefinCredit->ADVISER_ID,
            'phone' => $nefinCredit->PHONE,
        ];
    }

    private function getOrCreateConsultant($consultantDataNefin, ?int $officeId = null)
    {
        $cons = $this->getConsultantByArray($consultantDataNefin);
        if (empty($cons->consultant_id)) {
            $cons = $this->createConsultant($consultantDataNefin);
        }

        if (!empty($officeId)) {
            $this->attachConsultantToOffice($cons, $officeId);
        }

        return $cons;
    }

    private function getConsultantByArray(array $consultantDataNefin)
    {
        return Consultant::where(function($query) use ($consultantDataNefin) {
                $query->where('migration_db', $consultantDataNefin['db'])
                    ->where('migration_id', $consultantDataNefin['id']);
            })
            ->orWhere('name', trim($consultantDataNefin['name']))
            ->first();
    }

    private function createConsultant(array $consultantData)
    {
        $cons = new Consultant();
        $cons->fill([
            'name'         => $consultantData['name'],
            'phone'        => $consultantData['phone'],
            'migration_db' => $consultantData['db'],
            'migration_id' => $consultantData['id'],
        ]);
        $cons->save();

        return $cons;
    }

    private function attachConsultantToOffice($consultant, int $officeId)
    {
        if (!empty($consultant->consultant_id)) {

            $co = ConsultantOffice::where([
                'consultant_id' => $consultant->consultant_id,
                'office_id'     => $officeId,
            ])->first();

            if (empty($co->consultant_id)) {
                $consOffice = new ConsultantOffice();
                $consOffice->consultant_id = $consultant->consultant_id;
                $consOffice->office_id     = $officeId;
                $consOffice->save();
            }
        }
    }

}
