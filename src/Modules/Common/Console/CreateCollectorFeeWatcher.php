<?php

namespace Modules\Common\Console;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Tax;
use Modules\Payments\Repositories\TaxRepository;
use Modules\Product\Services\ProductSettingsService;
use Symfony\Component\Console\Command\Command;

class CreateCollectorFeeWatcher extends CommonCommand
{
    protected $name = 'script:create-collector-fee';
    protected $signature = 'script:create-collector-fee {loan_id?}';
    protected $description = 'Watch loan overdue days when is past with settings in product create collector fee';

    public function handle(): int
    {
        $this->startLog();


        $loanId = (int)$this->argument('loan_id');
        $productId = null;

        if (!empty($loanId)) {
            $loan = Loan::find($loanId);
            if (!empty($loan->product_id)) {
                $productId = (int)$loan->product_id;
            }

            if($loan->isJuridical()){
                Log::debug(__METHOD__ . ' You cant create collector fee for juridical loan');
                return Command::SUCCESS;
            }
        }


        $productSettingsService = app(ProductSettingsService::class);


        // format: product_id => days
        $settingsDays = $productSettingsService->getProductSettingsForCollectorDays($productId);
        if (empty($settingsDays)) {
            $msg = 'No product settings: collector_tax_days';
            $this->finishLog([], null, null, $msg);
            return Command::SUCCESS;
        }
        $productIds = array_keys($settingsDays);


        // format: product_id => percent
        $settingsPercents = $productSettingsService->getProductSettingsForCollectorPercents($productIds);
        if (empty($settingsPercents)) {
            $msg = 'No product settings: collector_tax';
            $this->finishLog([], null, null, $msg);
            return Command::SUCCESS;
        }


        $installments = self::getInstallmentsForUpdate($productIds, $settingsDays);


        $skipped = [
            'error' => [],
            'amount' => [],
            'percent' => [],
        ];

        $skippedWrongDays = 0;
        $todo = count($installments);
        $done = 0;

        $taxRepository = app(TaxRepository::class);

        foreach ($installments as $installment) {
            // since we take multipple overdues days we need to check the proper one of our product
            $productId = (int) $installment->product_id;
            $productOverdueDays = $settingsDays[$productId];
            if ($productOverdueDays != $installment->overdue_days) {
                $skippedWrongDays++;
                continue;
            }


            $collectorTaxPercent = 0;
            if (!empty($settingsPercents[$installment->product_id])) {
                $collectorTaxPercent = $settingsPercents[$installment->product_id];
            }

            if (empty($collectorTaxPercent)) {
                $skipped['percent'][] = [
                    'percent' => $collectorTaxPercent,
                    'loan_id' => $installment->loan_id,
                    'installment_id' => $installment->installment_id,
                    'product_id' => $installment->product_id,
                    'overdue_amount' => $installment->overdue_amount_primary,
                ];

                continue;
            }

            $collectorFeePercent = number_format($collectorTaxPercent, 2, '.', '');
            $collectorFeePercent = $collectorFeePercent / 100;
            $collectorFeeAmount = round($installment->overdue_amount_primary * $collectorFeePercent, 2);

            if (empty($collectorFeeAmount)) {
                $skipped['amount'][] = [
                    'amount' => $collectorFeeAmount,
                    'percent' => $collectorTaxPercent,
                    'collector_amount' => $collectorFeeAmount,
                    'loan_id' => $installment->loan_id,
                    'installment_id' => $installment->installment_id,
                    'product_id' => $installment->product_id,
                    'overdue_amount' => $installment->overdue_amount_primary,
                ];

                continue;
            }


            try {
                $data = [
                    'installment_id' => $installment->installment_id,
                    'toInstallment' => $installment->seq_num,
                    'loan_id' => $installment->loan_id,
                    'client_id' => $installment->client_id,
                    'type' => Tax::TAX_TYPE_COLLECTOR,
                    'amount' => floatToInt($collectorFeeAmount),
                    'paid_amount' => 0,
                    'rest_amount' => floatToInt($collectorFeeAmount),
                    'date_from' => date('Y-m-d'),
                    'comment' => __("head::clientCard.collectorFee", ['overdueAmount' => $installment->overdue_amount_primary]),
                    'currency_id' => Currency::BGN_CURRENCY_ID,
                    'status' => Tax::TAX_STATUS_SCHEDULED,
                ];

                $taxRepository->create($data);

                $done++;

            } catch (\Throwable $e) {

                $error = $e->getMessage() . ", " . $e->getFile() . ":" . $e->getLine();

                $skipped['amount'][] = [
                    'error' => $error,
                    'amount' => $collectorFeeAmount,
                    'percent' => $collectorTaxPercent,
                    'collector_amount' => $collectorFeeAmount,
                    'loan_id' => $installment->loan_id,
                    'installment_id' => $installment->installment_id,
                    'product_id' => $installment->product_id,
                    'overdue_amount' => $installment->overdue_amount_primary,
                ];

                Log::debug("CollectorTax adding failed: " . $error);
            }
        }


        // actualize todo, remove skipped by wrong days
        $todo = $todo - $skippedWrongDays;


        $msg = 'Total: ' . $todo . ', done: ' . $done;
        $this->finishLog([], $todo, $done, $msg);


        return Command::SUCCESS;
    }

    public static function getInstallmentsForUpdate(array $productIds, array $settingsDays): array
    {
        return DB::select(DB::raw("
            select
                i.installment_id,
                i.seq_num,
                i.loan_id,
                (i.rest_principal + i.rest_interest + i.rest_penalty) as overdue_amount_primary,
                l.client_id,
                l.product_id,
                i.overdue_days
            from installment i
            join loan l on (
                    l.loan_id = i.loan_id
                    and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    and l.juridical = 0
                    and l.product_id IN (" . implode(',', $productIds) . ")
                )
            left join tax t on (
                t.installment_id = i.installment_id
                and t.loan_id = i.loan_id
                and t.type = 'collector'
                and t.created_at >= current_date
                and t.created_at < current_date + interval '1 day'
            )
            where
                i.paid = 0
                and i.overdue_days IN (" . implode(',', $settingsDays) . ")
                and t.tax_id IS NULL
        "));
    }
}
