<?php

namespace Modules\Common\Console;

use Illuminate\Support\Carbon;
use Modules\Common\Models\RawRequest;
use Symfony\Component\Console\Command\Command;

final class RawRequestLocation extends CommonCommand
{
    protected $name = 'script:raw-request-location-update';
    protected $signature = 'script:raw-request-location-update {ip?}';
    protected $description = 'Update location data for raw_request by ip';

    protected int $total;
    protected int $processed;

    public function handle(): bool
    {
        $this->startLog();


        $ipSearch = null;
        if (!empty($this->argument('ip'))) {
            $ipSearch = $this->argument('ip');
        }


        $builder = RawRequest::select('ip')
            ->whereNotNull('ip')
            ->whereNull('coords_updated_at');

        if ($ipSearch) {
            $builder->where('ip', $ipSearch);
        }

        $requestForUpdate = $builder->groupBy('ip')->get();

        $todo = $requestForUpdate->count();
        if ($todo < 1) {
            $msg = 'No requests for updated';
            $logMessages = [$msg, $this->executionTimeString()];
            $this->finishLog($logMessages, $todo, 0, $msg);

            return Command::SUCCESS;
        }
        $this->info('To be updated: ' . $todo);

        $done = 0;
        $now = Carbon::now();

        foreach ($requestForUpdate as $req) {
            $ip = $req->ip;

            $data = getLocationDataByIp($ip);
            // dump($ip . ' - ', $data);

            if (empty($data)) {
                RawRequest::where('ip', $ip)
                    ->whereNull('coords_updated_at')
                    ->update(['coords_updated_at' => $now]);

                continue;
            }

            RawRequest::where('ip', $ip)
                ->whereNull('coords_updated_at')
                ->update([
                    'coords_updated_at' => $now,
                    'country' => $data['country'] ?: null,
                    'city' => $data['city'] ?: null,
                    'latitude' => $data['latitude'] ?: null,
                    'longitude' => $data['longitude'] ?: null,
                ]);

            $done++;
        }


        $msg = 'Processed ip(s): ' . $done . ', Total: ' . $todo;
        $logMessages = [$msg, $this->executionTimeString()];
        $this->finishLog($logMessages, $todo, $done, $msg);

        return Command::SUCCESS;
    }
}
