<?php

namespace Modules\Common\Providers;

use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use Modules\Api\Http\Commands\UpdateEmailAndNotificationsFromApiCommand;
use Modules\Common\CommandBus\CommandBus;
use Modules\Common\CommandBus\IlluminateCommandBus;
use Modules\Common\Console\CleanRawRequestsCommand;
use Modules\Common\Console\ClearSpecialPaymentAgreementCommand;
use Modules\Common\Console\ClientDownloadLogger;
use Modules\Common\Console\ClientVisitLogger;
use Modules\Common\Console\CLVTracker;
use Modules\Common\Console\ConvertEmailTemplateHtmlToText;
use Modules\Common\Console\ConvertGuarantorToClient;
use Modules\Common\Console\CreateA4ePerformanceDirsCommand;
use Modules\Common\Console\CreateCollectorFeeWatcher;
use Modules\Common\Console\CreateConsultantStatsForMonth;
use Modules\Common\Console\CronLogCleaner;
use Modules\Common\Console\DailyArchiver;
use Modules\Common\Console\DeduplicateClientIdCardCommand;
use Modules\Common\Console\DeleteCancelLoanFilesCommand;
use Modules\Common\Console\EasypayAutoRefundCommand;
use Modules\Common\Console\EsIndexClientsCommand;
use Modules\Common\Console\FixNumberOfLastClientAddressesCommand;
use Modules\Common\Console\FixWrittenOfLoansCommand;
use Modules\Common\Console\LoanStatsLocation;
use Modules\Common\Console\LogCleaner;
use Modules\Common\Console\LogMonitor;
use Modules\Common\Console\LostVeriffOffers;
use Modules\Common\Console\Manual\CustomNoiUpdate;
use Modules\Common\Console\Manual\DeduplicateClientAddressCommand;
use Modules\Common\Console\Manual\FixVeriffData;
use Modules\Common\Console\Manual\RefreshPaymentActiveStatusForDeletedPayments;
use Modules\Common\Console\Manual\SetDocTemplatesToProductsCommand;
use Modules\Common\Console\Manual\SetRelationLoanToSaleTasks;
use Modules\Common\Console\Manual\SetMvrParsedData;
use Modules\Common\Console\Manual\SetClientAddressBasedOnMvrData;
use Modules\Common\Console\Manual\TransferClientsFilesFromOldServerCommand;
use Modules\Common\Console\Migration\MigAddClientNotifSet;
use Modules\Common\Console\Migration\MigAdminsAndConsultants;
use Modules\Common\Console\Migration\MigCommentsFIx;
use Modules\Common\Console\Migration\MigConsultantFIx;
use Modules\Common\Console\Migration\MigDispatcher;
use Modules\Common\Console\Migration\MigExceptions;
use Modules\Common\Console\Migration\MigFixOnCcr;
use Modules\Common\Console\Migration\MigOffice;
use Modules\Common\Console\Migration\MigOfficeFIx;
use Modules\Common\Console\Migration\MigSite;
use Modules\Common\Console\ModulesStatsRefresh;
use Modules\Common\Console\MoveTranslationsManager;
use Modules\Common\Console\OverdueSmsSender;
use Modules\Common\Console\PaymentDeliveryColumnFixCommand;
use Modules\Common\Console\ProcessOldLoanRefinance;
use Modules\Common\Console\RawRequestLocation;
use Modules\Common\Console\ReferralSmsTracker;
use Modules\Common\Console\RefinanceStatisticsForMigratedLoansCommand;
use Modules\Common\Console\RollbackTranslationsManager;
use Modules\Common\Console\PortfolioSnapshotCommand;
use Modules\Common\Console\SendFailedLoginAttemptReportCommand;
use Modules\Common\Console\UpdateConsultantStatsCommand;
use Modules\Common\Console\WeeklyCleanerSmsLoginCodeCommand;
use Modules\Common\Facades\BankDetails;
use Modules\Head\Http\Commands\UpdateClientAddressesCommand;
use Modules\Head\Http\Commands\UpdateClientPhonesCommand;
use Modules\Head\Http\Commands\UpdateNotificationSettingsCommand;
use Modules\Sales\Application\Reactions\UpdateClientAddressesReaction;
use Modules\Sales\Application\Reactions\UpdateClientPhonesReaction;
use Modules\Sales\Application\Reactions\UpdateEmailAndNotificationsFromApiReaction;
use Modules\Sales\Application\Reactions\UpdateNotificationSettingsReaction;

class CommonServiceProvider extends ServiceProvider
{
    /**
     * @var string $moduleName
     */
    protected $moduleName = 'Common';

    /**
     * @var string $moduleNameLower
     */
    protected $moduleNameLower = 'common';

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'Database/Migrations'));
        $this->registerCommands();
    }

    public function registerCommands(): void
    {
        $this->commands([
            ClientDownloadLogger::class,
            ClientVisitLogger::class,
            DailyArchiver::class,
            CronLogCleaner::class,
            OverdueSmsSender::class,
            LoanStatsLocation::class,
            LogCleaner::class,
            LogMonitor::class,
            MoveTranslationsManager::class,
            RawRequestLocation::class,
            RollbackTranslationsManager::class,
            PortfolioSnapshotCommand::class,
            ModulesStatsRefresh::class,
            CreateA4ePerformanceDirsCommand::class,
            CLVTracker::class,
            CreateCollectorFeeWatcher::class,

            SetDocTemplatesToProductsCommand::class,
            ConvertEmailTemplateHtmlToText::class,

            CleanRawRequestsCommand::class,
            FixNumberOfLastClientAddressesCommand::class,
            EsIndexClientsCommand::class,
            PaymentDeliveryColumnFixCommand::class,

            // migration commands
            MigAddClientNotifSet::class,
            MigAdminsAndConsultants::class,
            MigCommentsFIx::class,
            MigConsultantFIx::class,
            MigDispatcher::class,
            MigExceptions::class,
            MigFixOnCcr::class,
            MigOffice::class,
            MigOfficeFIx::class,
            MigSite::class,
            CustomNoiUpdate::class,
            RefreshPaymentActiveStatusForDeletedPayments::class, /// todo delete after run on prod
            SendFailedLoginAttemptReportCommand::class,
            DeleteCancelLoanFilesCommand::class,
            ConvertGuarantorToClient::class,
            ProcessOldLoanRefinance::class,
            RefinanceStatisticsForMigratedLoansCommand::class,
            EasypayAutoRefundCommand::class,
            UpdateConsultantStatsCommand::class,
            CreateConsultantStatsForMonth::class,
            SetMvrParsedData::class,
            SetClientAddressBasedOnMvrData::class,
            SetRelationLoanToSaleTasks::class,
            TransferClientsFilesFromOldServerCommand::class,

            WeeklyCleanerSmsLoginCodeCommand::class,
            DeduplicateClientIdCardCommand::class,
            DeduplicateClientAddressCommand::class,
            ReferralSmsTracker::class,
            LostVeriffOffers::class,
            FixVeriffData::class,
            FixWrittenOfLoansCommand::class,
            ClearSpecialPaymentAgreementCommand::class,
        ]);
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        $this->app->singleton(CommandBus::class, IlluminateCommandBus::class);
        $this->app->bind('bank-details', function () {
            return app(BankDetails::class);
        });
        /** @var CommandBus $bus */
        $bus = $this->app->make(CommandBus::class);
        $bus->map([
            UpdateClientPhonesCommand::class => UpdateClientPhonesReaction::class,
            UpdateClientAddressesCommand::class => UpdateClientAddressesReaction::class,
            UpdateNotificationSettingsCommand::class => UpdateNotificationSettingsReaction::class,
            UpdateEmailAndNotificationsFromApiCommand::class => UpdateEmailAndNotificationsFromApiReaction::class,
        ]);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes(
            [
                module_path($this->moduleName, 'Config/config.php') => config_path($this->moduleNameLower . '.php'),
            ],
            'config'
        );
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/config.php'),
            $this->moduleNameLower
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);

        $sourcePath = module_path($this->moduleName, 'Resources/views');

        $this->publishes(
            [
                $sourcePath => $viewPath,
            ],
            ['views', $this->moduleNameLower . '-module-views']
        );

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'Resources/lang'), $this->moduleNameLower);
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!isProd() && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path($this->moduleName, 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }

        return $paths;
    }
}
