<?php

declare(strict_types=1);

namespace Modules\Common\Services;

use Carbon\CarbonInterface;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ClientDiscountActual;
use Modules\Common\Models\Product;
use RuntimeException;

final class ClientDiscountActualService
{
    public function createByProducts(
        int $clientId,
        array $productIds,
        int $discount,
        CarbonInterface $validFrom,
        CarbonInterface $validTo,
        bool $isInterestFree = false,
    ): array {

        $forbiddenProductIds = $this->getProductIdsForbiddenDiscount();

        return DB::transaction(static function () use (
            $productIds, $clientId, $discount, $validFrom, $validTo, $isInterestFree, $forbiddenProductIds
        ) {
            $ids = [];

            foreach ($productIds as $productId) {

                if (!empty($forbiddenProductIds) && in_array($productId, $forbiddenProductIds)) {
                    continue;
                }

                $model = new ClientDiscountActual();
                $model->fill([
                    'client_id' => $clientId,
                    'product_id' => $productId,
                    'percent' => $discount,
                    'valid_from' => $validFrom->startOfDay()->toDateTimeString(),
                    'valid_till' => $validTo->endOfDay()->toDateTimeString(),
                    'created_at' => now(),
                    'created_by' => getAdminId(),
                    'is_interest_free' => $isInterestFree,
                    'active' => 1,
                    'deleted' => 0,
                ]);

                if (!$model->save()) {
                    throw new RuntimeException('Failed to create client discount actual');
                }

                $ids[] = $model->getKey();
            }

            return $ids;
        });
    }

    public function createForClientsByProducts(
        array $clientIds,
        array $productIds,
        int $discount,
        CarbonInterface $validFrom,
        CarbonInterface $validTo,
        bool $isInterestFree = false,
    ): void {
        $inserts = [];
        $now = now();

        $forbiddenProductIds = $this->getProductIdsForbiddenDiscount();

        foreach ($clientIds as $clientId) {
            foreach ($productIds as $productId) {

                if (!empty($forbiddenProductIds) && in_array($productId, $forbiddenProductIds)) {
                    continue;
                }

                $inserts[] = [
                    'client_id' => $clientId,
                    'product_id' => $productId,
                    'percent' => $discount,
                    'valid_from' => $validFrom->startOfDay()->toDateTimeString(),
                    'valid_till' => $validTo->endOfDay()->toDateTimeString(),
                    'created_at' => $now,
                    'created_by' => getAdminId(),
                    'is_interest_free' => $isInterestFree,
                    'active' => 1,
                    'deleted' => 0,
                ];
            }
        }

        if ($inserts && !ClientDiscountActual::insert($inserts)) {
            throw new RuntimeException('Failed to create client discount actual');
        }
    }

    private function getProductIdsForbiddenDiscount(): array
    {
        $ids = Product::where('client_related', true)
            ->where('active', 1)
            ->get()
            ->pluck('product_id')
            ->toArray();

        if (empty($ids)) {
            return [];
        }

        return $ids;
    }
}
