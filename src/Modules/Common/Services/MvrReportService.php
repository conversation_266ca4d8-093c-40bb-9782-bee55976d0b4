<?php

declare(strict_types=1);

namespace Modules\Common\Services;

use Carbon\Carbon;

final class MvrReportService
{
    // used in: Modules/Docs/Domain/DataSource/Client.php
    public static function parseIdCardData(array $data): array
    {
        $permanentAddress = self::getPermanentAddress($data['PermanentAddress']);
        if (empty($permanentAddress)) {
            $permanentAddress = '';
        }

        $res = [
            // Client id card front
            'client_id_card_picture' => $data['Picture'] ?? null,
            'client_id_card_signature' => $data['IdentitySignature'] ?? '',

            'client_surname' => $data['PersonNames']['FamilyName'] ?? '',
            'client_surname_latin' => $data['PersonNames']['LastNameLatin'] ?? '',
            'client_first_name' => $data['PersonNames']['FirstName'] ?? '',
            'client_first_name_latin' => $data['PersonNames']['FirstNameLatin'] ?? '',
            'client_middle_name' => $data['PersonNames']['Surname'] ?? '',
            'client_middle_name_latin' => $data['PersonNames']['SurnameLatin'] ?? '',
            'client_pin' => $data['EGN'] ?? '',
            'client_sex' => $data['GenderName'] ?? '',
            'client_sex_latin' => $data['GenderNameLatin'] ?? '',
            'client_nationality' => $data['NationalityList']['Nationality']['NationalityName'] ?? '',
            'client_nationality_latin' => $data['NationalityList']['Nationality']['NationalityCode'] ?? '',
            'client_birth_day' => !empty($data['BirthDate']) ? Carbon::parse($data['BirthDate'])->format('d.m.Y') : '',
            'client_id_expiration_date' => !empty($data['ValidDate']) ? Carbon::parse($data['ValidDate'])->format('d.m.Y') : '',
            'client_id_number' => $data['IdentityDocumentNumber'] ?? '',

            // Client id card back
            'client_birthplace' => $data['BirthPlace']['MunicipalityName'] ?? '',
            'client_permanent_address' => $permanentAddress,
            'client_height' => $data['Height'] ?? '',
            'client_eye_color' => $data['EyesColor'] ?? '',
            'client_id_card_authority' => $data['IssuerName'] ?? '',
            'client_id_issue_date' => !empty($data['IssueDate']) ? Carbon::parse($data['IssueDate'])->format('d.m.Y') : '',
        ];

        // stik, #1102402
        // Valentin Rizov (11:44 AM): 1102402 - server error при опит за генериране на съдебни док(Стик - лайф) - Влади ми писа за него, проверих и други, този случай е изключение,
        foreach ($res as &$val) {
            if (is_array($val)) {
                $val = '';
            }
        }

        return $res;
    }

    // used in: @here->parseIdCardData()
    public static function getPermanentAddress(array $permanentAddress): string
    {
        $result = (
                !empty($permanentAddress['SettlementName']) && is_string($permanentAddress['SettlementName'])
                    ? $permanentAddress['SettlementName']
                    : ''
            ) . ' ' . (
                !empty($permanentAddress['LocationName']) && is_string($permanentAddress['LocationName'])
                    ? $permanentAddress['LocationName']
                    : ''
            ) . ' ';

        $buildingNumber = '';
        $entrance = '';
        $floor = '';
        $apartment = '';
        if (!empty($permanentAddress['BuildingNumber'])) {
            $buildingNumber = $permanentAddress['BuildingNumber'] . ' ';
        }

        if (!empty($permanentAddress['Entrance'])) {
            $entrance = 'ВХ.' . $permanentAddress['Entrance'] . ' ';
            $buildingNumber = 'БЛ.' . $buildingNumber;
        }

        if (!empty($permanentAddress['Floor'])) {
            $floor = 'ET.' . $permanentAddress['Floor'] . ' ';
        }

        if (!empty($permanentAddress['Apartment'])) {
            $apartment = 'АП.' . $permanentAddress['Apartment'];
        }


        return $result . $buildingNumber . $entrance . $floor . $apartment;
    }
}
