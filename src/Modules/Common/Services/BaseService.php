<?php

namespace Modules\Common\Services;

use DateTime;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Collection;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Common\Traits\DateBuilderTrait;
use Modules\Common\Traits\StringFormatterTrait;
use stdClass;

class BaseService
{
    use DateBuilderTrait;
    use StringFormatterTrait;

    public const CONCAT_KEY = 'name';
    public const STATUS_KEY = 'status';
    public const STATUS_KEYS = [
        'active',
        'blocked',
        'deleted',
    ];

    /**
     * BaseService constructor.
     */
    public function __construct(
        private readonly AdministratorRepository $administratorRepository = new AdministratorRepository
    ) {}

    public function whereCond($data): array
    {
        return $this->getWhereConditions($data);
    }

    /**
     * @param array $data
     * @param array|string[] $names
     * @param string $prefix
     *
     * @return array
     */
    protected function getWhereConditions(
        array $data,
        array $names = ['name'],
        string $prefix = ''
    ){
        $where = [];
        foreach ($data as $key => $item) {
            if (!isset($item)) {
                continue;
            }

            if (!is_string($item) && !is_numeric($item)) {
                continue;
            }

            if ($key == self::STATUS_KEY && in_array($item, self::STATUS_KEYS)) {
                $where[] = [
                    $prefix . ($prefix != '' ? '.' : '') . $item,
                    '=',
                    '1',
                ];
                continue;
            }
            if (self::CONCAT_KEY === $key && !empty($names)) {
                $where[] = [
                    DB::raw("CONCAT_WS(' ', " . implode(', ', $names) . ")"),
                    'ILIKE',
                    "%{$item}%",
                ];
                continue;
            }

            if (is_numeric($item) && !preg_match('/pin|phone|value/', $key)) {
                $where[] = [
                    $prefix . ($prefix != '' ? '.' : '') . $this->fmtCamelCaseToSnakeCase($key),
                    '=',
                    "{$item}",
                ];
                continue;
            }
            if (preg_match(self::$dateRangeRegex, $item)) {
                $extractedDates = $this->extractDates($item);
                $where[] = [
                    $prefix . ($prefix != '' ? '.' : '') . $this->fmtCamelCaseToSnakeCase($key),
                    '>=',
                    $extractedDates['from'],
                ];
                $where[] = [
                    $prefix . ($prefix != '' ? '.' : '') . $this->fmtCamelCaseToSnakeCase($key),
                    '<=',
                    $extractedDates['to'],
                ];
                continue;
            }

            if (preg_match(self::$dateRegex, $item)) {
                $where[] = [
                    $prefix . ($prefix != '' ? '.' : '') . $this->fmtCamelCaseToSnakeCase($key),
                    '>=',
                    $this->fmt($item, DateBuilderTrait::$formatDateRangeBegin),
                ];
                $where[] = [
                    $prefix . ($prefix != '' ? '.' : '') . $this->fmtCamelCaseToSnakeCase($key),
                    '<=',
                    $this->fmt($item, DateBuilderTrait::$formatDateRangeEnd),
                ];
                continue;
            }

            $where[] = [
                $prefix . ($prefix != '' ? '.' : '') . $this->fmtCamelCaseToSnakeCase($key),
                'ILIKE',
                "%{$item}%",
            ];
        }

        return $where;
    }

    /**
     * [adminCanChangeOffice description]
     *
     * @param int $officeId
     *
     * @return bool
     */
    public function adminCanChangeOffice(int $officeId): bool
    {
        $admin = Auth::user();
        if ($admin->isSuperAdmin()) {
            return true;
        }

        return $this->administratorRepository->isAdminFromOffice(Auth::id(), $officeId);
    }

    /**
     * [adminBelongsToClientOffices description]
     *
     * @param array $officeIds
     *
     * @return bool
     */
    public function adminBelongsToClientOffices(array $officeIds): bool
    {
        return $this->administratorRepository->adminBelongsToOffices($officeIds);
    }

    /**
     * @param array $data
     *
     * @return array
     */
    protected function getOrderConditions(
        array $data
    ) {
        $order = [];
        if (!empty($data['order'])) {
            foreach ($data['order'] as $key => $variable) {
                if (is_array($variable)) {
                    foreach ($variable as $keySub => $variableSub) {
                        $order = [
                            $key . '.' . $keySub => strtoupper($variableSub),
                        ];
                    }
                } else {
                    $order = [
                        $key => strtoupper($variable),
                    ];
                }
            }
        }

        return $order;
    }

    /**
     * @param $items
     *
     * @return Collection
     */
    protected function convertArrayToCollection($items): Collection
    {
        if (gettype($items) !== 'array') {
            return $items;
        }

        return collect($items)->map(
            function ($item) {
                return (object) $item;
            }
        );
    }

    /**
     * @param string $fullyName
     * @param null $collection
     *
     * @return Collection
     */
    function collect(string $fullyName, $collection = null): Collection
    {
        return new $fullyName($collection);
    }

    /**
     * @param array $whereConditions
     * @param string $old
     * @param string $new
     * @param string $field
     */
    protected function replaceTableName(
        array &$whereConditions,
        string $old,
        string $new,
        string $field
    ) {
        array_walk(
            $whereConditions,
            function (&$element) use ($old, $new, $field) {
                if ("$old.$field" == $element[0]) {
                    $element[0] = "$new.$field";
                }
            }
        );
    }

    /**
     * @param array $array
     * @param string $separator
     * @param string $lastSeparator
     *
     * @return string|null
     */
    public static function naturalLanguageImplode(
        array $array,
        string $separator = ', ',
        string $lastSeparator = ' and '
    ): ?string {
        $last = array_pop($array);

        if ($array) {
            return implode($separator, $array) . $lastSeparator . $last;
        }

        return $last;
    }

    /**
     * @param string $value
     *
     * @return string
     */
    function convertSnakeToCamel(string $value): string
    {
        $wordsUpper = ucwords(str_replace('_', ' ', $value));
        $pascalCase = str_replace(' ', '', $wordsUpper);

        return lcfirst($pascalCase);
    }

    /**
     * @param stdClass $model
     * @param string $standardColumn
     * @param null|string $exceptionColumn
     * @param int $minutesDelay
     *
     * @return stdClass
     * @throws Exception
     */
    protected function addTimerParams(
        $model,
        string $standardColumn,
        ?string $exceptionColumn = null,
        int $minutesDelay = 5
    ) {
        $targetDate = new Datetime($model->{$standardColumn});

        // If the loan has approve_attempt we use skip_till to calc timer params
        if (!empty($model->{$exceptionColumn})) {
            $targetDate = new DateTime($model->{$exceptionColumn});
        }

        $timeTargetDate = $targetDate->getTimestamp();
        $nowTime = (new Datetime)->getTimestamp();
        $diffSeconds = $nowTime - $timeTargetDate;
        $diffMinutes = $diffSeconds / 60;
        $hours = floor($diffSeconds / 3600);
        $minutes = floor(($diffMinutes) % 60);
        $seconds = $diffSeconds % 60;
        $params = [$minutes, $seconds];
        $format = '%02d:%02d';

        if ($hours != 0) {
            $format = '%02d:' . $format;
            array_unshift($params, $hours);
        }

        $model->timer = sprintf($format, ...$params);
        $model->is_admin_late = $diffMinutes >= $minutesDelay;

        return $model;
    }

    protected function getWhereConditionFromTo(
        array &$data,
        array $where,
        string $dataKey,
        ?string $dbField = null
    ): array {
        $value = $data[$dataKey] ?? null;
        unset($data[$dataKey]);
        if (!$value) {
            return $where;
        }

        $from = $value['from'] ?? null;
        if ($from) {
            $where[] = [$dbField ?: $dataKey, '>=', $from];
        }

        $to = $value['to'] ?? null;
        if ($to) {
            $where[] = [$dbField ?: $dataKey, '<=', $to];
        }

        return $where;
    }

    protected function getWhereConditionEqual(
        array &$data,
        mixed $where,
        string $dataKey,
        ?string $dbField = null,
        mixed $default = null
    ): array {
        $value = $data[$dataKey] ?? null;
        unset($data[$dataKey]);

        if ($value) {
            $where[] = [$dbField ?: $dataKey, '=', $value];
        } elseif ($default) {
            $where[] = [$dbField ?: $dataKey, '=', $default];
        }

        return $where;
    }

    protected function getWhereConditionILikeRight(
        array &$data,
        mixed $where,
        string $dataKey,
        ?string $dbField = null,
        mixed $default = null
    ) {
        $value = $data[$dataKey] ?? null;
        unset($data[$dataKey]);

        if ($value) {
            $where[] = [$dbField ?: $dataKey, 'ILIKE', $value . '%'];
        } elseif ($default) {
            $where[] = [$dbField ?: $dataKey, 'ILIKE', $default . '%'];
        }

        return $where;
    }
}
