<?php

namespace Modules\Common\Services;

use Illuminate\Support\Facades\Storage;
use ZipArchive;

class ZipArchiveService
{
    public const ZIP_DIRECTORY = 'zips';
    private const ZIP_EXT = 'zip';

    public function makeZipFile(
        array $files,
        string $fileName,
        string $dir = self::ZIP_DIRECTORY
    ): string {

        if (!Storage::disk('local')->exists($dir)) {
            Storage::disk('local')->makeDirectory($dir);
        }

        if (empty($files)) {
            throw new \RuntimeException(__('collect::bucketTask.noDocumentsForArchive'));
        }

        $zip_file = storage_path('/' . $dir . '/' . $fileName . '_' . time() . '.' . self::ZIP_EXT);

        $zip = new ZipArchive();
        $zip->open($zip_file, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        // foreach ($files as $file) {
        //     $explodedFile = explode('/', $file);
        //     $fileNameOnly = array_pop($explodedFile);

        //     $zip->addFile(storage_path($file), $fileNameOnly);
        // }

        foreach ($files as $loanId => $loanFiles) {
            foreach ($loanFiles as $file) {
                $subfolder = "Loan_$loanId";

                // Extract the file name without the path
                $explodedFile = explode('/', $file);
                $fileNameOnly = array_pop($explodedFile);

                // Create the subfolder if it doesn't exist
                $zip->addEmptyDir($subfolder);

                // Add the file to the subfolder within the archive
                $zip->addFile(storage_path($file), "$subfolder/$fileNameOnly");
            }
        }

        $zip->close();

        return $zip_file;
    }
}
