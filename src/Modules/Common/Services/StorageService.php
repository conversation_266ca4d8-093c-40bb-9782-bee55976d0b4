<?php

namespace Modules\Common\Services;

use Barryvdh\DomPDF\Facade\Pdf;
use Barryvdh\Snappy\Facades\SnappyPdf;

use Exception;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Exceptions\ExportWrongFormatException;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\File as StikCreditFile;
use Modules\Common\Models\FileStorage;
use Modules\Common\Models\FileType;
use Modules\Head\Repositories\FileRepository;
use RuntimeException;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

/**
 * Class StorageService
 * Responsible for:
 * - storing files
 * - downloading files
 * - generating exports
 * - importing files
 *
 * @package Modules\Core\Services
 */
class StorageService extends BaseService
{
    const AVATAR_ALIAS = 'ava_';
    const AVATAR_EXTENSION = 'jpg';
    const AVATAR_PATH = 'avatars/';
    const DEFAULT_AVATAR_PATH = '/images/avatars/default.jpg';
    const DEFAULT_STORAGE_PATH = '/storage/';
    const PATH_TO_EXPORTS = '/exports/';
    const PATH_TO_DIRECT_SERVICE = 'direct_service/';
    const PATH_TO_CCR = 'ccr_reports/';
    const PATH_TO_CCR_BORR = 'ccr_reports/borr/';
    const PATH_TO_CCR_CRED = 'ccr_reports/cred/';
    const PATH_TO_CCR_CUCR = 'ccr_reports/cucr/';
    const PATH_TO_A4E_PERFORMANCE_FINAL = 'a4e_performance/final';
    const PATH_TO_A4E_PERFORMANCE_TMP = 'a4e_performance/tmp';
    const PATH_TO_A4E_PERFORMANCE_TMP_FOLDER = 'a4e_performance/tmp/monthly-update';
    const PATH_TO_PDF_TEMPLATE = 'docs::pdf.view'; // TODO: move to core
    const PATH_TO_PDF_SNAPPY_TEMPLATE = 'docs::pdf.view_snappy'; // TODO: move to core
    const PATH_TO_DISCOUNT_IMPORTS = 'imports/discounts/';
    const PATH_TO_INTEREST_TERM_IMPORTS = 'imports/terms/';
    const PATH_TO_LOAN_APPROVED = 'loan/approved/';
    const CLIENT_DOC_PATH = 'client-doc/';
    const LANDING_DOC_PATH = 'landing-doc/';
    const PORTFOLIO_PATH = 'portfolio/';

    const OUTER_COLLECTION_PATH = 'outer-collector/';

    protected const FILE_FORMATS = [
        'xls',
        'xlsx',
        'csv',
        'pdf',
    ];

    private const IMPORT_FORMATS = [
        'xls',
        'xlsx',
        'ods',
        'csv',
    ];

    public const VIEW_FILE_FORMATS = [
        'png',
        'jpeg',
        'gif',
        'bmp',
        'pdf',
    ];

    public const DOWNLOAD_FILE_FORMATS = [
        'xls',
        'xlsx',
        'csv',
        'pdf',
        'png',
        'jpeg',
    ];

    private $repo = null;

    /**
     * @param Administrator $admin
     * @param UploadedFile $file
     *
     * @return string
     */
    public function uploadAvatar(
        Administrator $admin,
        UploadedFile $file
    ): string {
        $fileNameNew = self::getAdminAvatarPath($admin->getKey());
        Storage::disk('public')->put($fileNameNew, file_get_contents($file));
        return $fileNameNew;
    }

    /**
     * [getAdminAvatarPath description]
     *
     * @param int $adminId
     *
     * @return string
     */
    public static function getAdminAvatarPath(int $adminId): string
    {
        return sprintf(
            '%s%s%d.%s',
            self::AVATAR_PATH,
            self::AVATAR_ALIAS,
            $adminId,
            self::AVATAR_EXTENSION
        );
    }

    /**
     * @param string $path
     *
     * @return bool
     */
    public static function hasFile(string $path, $disc = 'public'): bool
    {
        return Storage::disk($disc)->fileExists($path);
    }

    public function makeDir(string $documentTemplateKey): string
    {
        $path = config('docs.filePath') . $documentTemplateKey . '/';
        if (
            !empty(Auth::user()->administrator_id)
            && (
                Auth::user()->administrator_id
                == Administrator::DEFAULT_UNIT_TEST_USER_ID
            )
        ) {
            $path = config('docs.filePath')
                . config('docs.filePathTesting') . '/'
                . $documentTemplateKey . '/';
        }

        if (!self::hasFile($path)) {
            Storage::disk('local')->makeDirectory($path, 0777, true, true);
        }

        return $path;
    }

    /**
     * @param string $fileName
     * @param array $config
     * @param string $format
     * @param string $dirToSave
     *
     * @return StreamedResponse
     *
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function download(
        string $fileName,
        array $config,
        string $format = 'xlsx',
        string $dirToSave = self::PATH_TO_EXPORTS
    ) {
        $filePath = $this->generate(
            $fileName,
            $config,
            $format,
            $dirToSave
        );

        if (!$filePath) {
            throw new Exception('Empty download path');
        }

        return $this->downloadFromStorage($filePath, 'public'); // TODO: check why public?
    }

    /**
     * Important! Works only with relative path
     *
     * @param string $filePath
     *
     * @return StreamedResponse
     */
    public function downloadFromStorage(string $filePath, string $disk = 'local')
    {
        return Storage::disk($disk)->download($filePath);
    }

    /**
     * @param string $fileName
     * @param array $config
     * @param string $format
     * @param string $dirToSave
     *
     * @return string - relative path to file
     *
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function generate(
        string $fileName,
        array $config,
        string $format = 'xlsx',
        string $dirToSave = self::PATH_TO_EXPORTS,
        bool $useSnappy = false
    ): string {

        if (!in_array($format, self::FILE_FORMATS)) {
            throw new ExportWrongFormatException($format . ' is not allowed.');
        }

        $downloadPath = $dirToSave . $fileName . '.' . $format;
        switch ($format) {
            case 'csv':
            case 'xls':
            case 'xlsx':
                if (empty($config['collectionClass'])) {
                    throw new Exception('Empty collection for ' . $format . ' export');
                }

                Excel::store(
                    $config['collectionClass'],
                    $downloadPath,
                    'public'
                );
                break;
            case 'pdf':
                if (empty($config['content'])) {
                    throw new Exception('No content for pdf export');
                }

                if ($useSnappy) {
                    $pdf = SnappyPdf::loadView(self::PATH_TO_PDF_SNAPPY_TEMPLATE, $config);
                } else {
                    $pdf = PDF::loadView(self::PATH_TO_PDF_TEMPLATE, $config);
                    $pdf->setWarnings(false);
                }

                $path = storage_path() . '/' . $downloadPath;
                $pdf->save($path);
                if (!file_exists($path)) {
                    throw new RuntimeException(__('Failed to save file: ' . $path));
                }
        }

        return $downloadPath;
    }

    /**
     * @return bool
     */
    public function importDiscounts(UploadedFile $file)
    {
        try {
            $ext = $file->getClientOriginalExtension();
            $this->isCorrectFormat($ext, self::IMPORT_FORMATS);

            $originNameWithExt = $file->getClientOriginalName();
            $originName = pathinfo($originNameWithExt, PATHINFO_FILENAME);
            $newFileName = self::PATH_TO_DISCOUNT_IMPORTS . $originName . '_' . time() . '.' . $ext;
            $content = file_get_contents($file);
            $isSaved = $this->storeFile($newFileName, $content);

            if ($isSaved) {
                // Create file log history
                $newFileData = [
                    'file_storage_id' => FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID,
                    'file_type_id' => FileType::getIdFromCode(FileTypeEnum::DISCOUNT_IMPORT),
                    'hash' => $newFileName,
                    'file_path' => self::PATH_TO_DISCOUNT_IMPORTS,
                    'file_size' => $file->getSize(),
                    'file_type' => $ext,
                    'file_name' => $newFileName,
                ];

                $this->getFileRepository()->create($newFileData);
            }
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::storageService.FileProblemImporting'),
                $e->getMessage()
            );
        }

        return true;
    }

    public function savePortfolioDataInFile($writer, string $filename): StikCreditFile
    {
        try {
            $directoryPath = self::PORTFOLIO_PATH;

            $firstTime = false;
            if (!self::hasFile($directoryPath, 'public')) {
                $firstTime = true;
                Storage::disk('public')->makeDirectory($directoryPath, 0777, true, true);
            }

            // saved file in storage
            $fullPath = $directoryPath . $filename;
            $absolutePath = storage_path($directoryPath) . $filename;
            // $writer->save($absolutePath);

            ob_start();
            $writer->save('php://output');
            $content = ob_get_contents();
            ob_end_clean();

            Storage::disk('public')->put($fullPath, $content);


            if ($firstTime) {
                \Cache::forget('t_file_type_getIdFromCode');
            }

            // Create file log history
            $newFileData = [
                'file_storage_id' => FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID,
                'file_type_id' => FileType::getIdFromCode(FileTypeEnum::PORTFOLIO_SNAPSHOT),
                'hash' => $fullPath,
                'file_path' => $fullPath,
                'file_size' => filesize($absolutePath),
                'file_type' => 'xlsx',
                'file_name' => $filename,
            ];

            return $this->getFileRepository()->create($newFileData);

        } catch (Throwable $e) {
            report($e);
            throw new RuntimeException(
                __('head::storageService.FileIsNotSaved'),
                $e->getMessage()
            );
        }
    }

    /**
     * @param string $fileName
     * @param string $content
     * @param string $disk
     * @return bool|string
     */
    public function storeFile(string $fileName, string $content, $disk = 'local')
    {
        $isSaved = Storage::disk($disk)
            ->put($fileName, $content);

        if (!$isSaved) {
            throw new RuntimeException(
                __('head::storageService.FileIsNotSaved')
            );
        }

        return $isSaved;
    }

    /**
     * @return bool
     */
    private function isCorrectFormat(string $ext, array $formats): bool
    {
        if (!in_array($ext, $formats)) {
            throw new RuntimeException(
                __('head::storageService.InvalidImportFormat') .
                implode(', ', $formats)
            );
        }

        return true;
    }

    public function importTerms(
        UploadedFile $file,
        string $type = 'interest'
    ): StikCreditFile {
        try {
            $fileTypeId = null;
            if ('interest' === $type) {
                $fileTypeId = FileType::getIdFromCode(FileTypeEnum::INTEREST_TERM_IMPORT);
            }
            if ('penalty' === $type) {
                $fileTypeId = FileType::getIdFromCode(FileTypeEnum::PENALTY_TERM_IMPORT);
            }
            if ($fileTypeId === null) {
                throw new RuntimeException('Wrong import terms type provided');
            }

            $ext = $file->getClientOriginalExtension();
            $this->isCorrectFormat($ext, self::IMPORT_FORMATS);

            $originNameWithExt = $file->getClientOriginalName();
            $originName = pathinfo($originNameWithExt, PATHINFO_FILENAME);
            $newFileName = $originName . '_' . time() . '.' . $ext;
            $content = file_get_contents($file);
            $isSaved = $this->storeFile(self::PATH_TO_INTEREST_TERM_IMPORTS . $newFileName, $content);

            if (!$isSaved) {
                throw new Exception('File is not stored.');
            }

            // Create file log history
            $newFileData = [
                'file_storage_id' => FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID,
                'file_type_id' => $fileTypeId,
                'hash' => $newFileName,
                'file_path' => self::PATH_TO_INTEREST_TERM_IMPORTS,
                'file_size' => $file->getSize(),
                'file_type' => $ext,
                'file_name' => $newFileName,
            ];

            return $this->getFileRepository()->create($newFileData);
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::storageService.FileProblemImporting'),
                $e->getMessage()
            );
        }
    }

    /**
     * @param $from
     * @param $to
     */
    public function copyFile(string $from, string $to)
    {
        File::copy($from, $to);
    }

    /**
     * @param string $dirPath
     *
     * @return bool
     */
    public function isDirectory(string $dirPath): bool
    {
        return File::isDirectory($dirPath);
    }

    /**
     * @param string $path
     * @return bool
     */
    public function fileExists(string $path): bool
    {
        return File::exists($path);
    }

    /**
     * @param string $dirPath
     */
    public function makeDirectory(string $dirPath)
    {
        File::makeDirectory($dirPath, 0777, true, true);
    }

    /**
     * @param string $dirToRemove
     *
     * @return bool
     */
    public function deleteDirectory(string $dirToRemove): bool
    {
        return File::deleteDirectory($dirToRemove);
    }

    public function clearStorageDirectory(string $dirName): bool
    {
        return (new Filesystem)->cleanDirectory('storage/' . $dirName);
    }

    /**
     * @return string[]
     */
    public function uploadClientDoc(int $clientId, UploadedFile $file)
    {
        $path = self::CLIENT_DOC_PATH . $clientId . '/';

        if (!self::hasFile($path)) {
            Storage::disk('public')->makeDirectory($path);
        }

        $newFileName = 'clientDoc' . $clientId . '_' . time() . '.' . $file->extension();

        $this->storeFile(($path . $newFileName), file_get_contents($file), 'public');

        return [$path, $newFileName];
    }

    /**
     * @return string[]
     */
    public function uploadLandingDoc(UploadedFile $file)
    {
        $path = self::LANDING_DOC_PATH;

        if (!self::hasFile($path)) {
            Storage::disk('public')->makeDirectory($path, 0777, true, true);
        }

        $newFileName = FileTypeEnum::LANDING_DOC->value . '_' . time() . '.' . $file->extension();

        $storageState = $this->storeFile(($path . $newFileName), file_get_contents($file), 'public');

        return [$path, $newFileName];
    }

    /**
     * @param StikCreditFile $file
     * @param array $extensions
     * @return string
     */
    public function isValidFileExtension(StikCreditFile $file, array $extensions = []): string
    {
        if (in_array($this->getDocumentFileTypeExtension($file->file_name), $extensions, true)) {
            return true;
        }
        return false;
    }

    /**
     * @param string $fileName
     * @return string
     */
    public function getDocumentFileTypeExtension(string $fileName): string
    {
        return pathinfo($fileName, PATHINFO_EXTENSION);
    }

    /**
     * @param StikCreditFile $file
     * @param string|null $fileExtension
     * @return string
     */
    public function buildDocumentLink(
        StikCreditFile $file,
        string $fileExtension = null
    ): string
    {
        $fileName = $file->file_name;

        if (!is_null($fileExtension) && !strpos($file->file_name, $fileExtension)) {
            $fileName = $file->file_name . '.' . $fileExtension;
        }

        $downloadPath = $file->file_path . $fileName;

        if (!self::hasFile($downloadPath)) {
            throw new RuntimeException('File not found.');
        }
        return Storage::disk('public')->path($downloadPath);
    }

    // also used in: LandingDocService
    public function getFileRepository(): FileRepository
    {
        if (null === $this->repo) {
            $this->repo = app(FileRepository::class);
        }

        return $this->repo;
    }
}
