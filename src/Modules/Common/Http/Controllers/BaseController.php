<?php

namespace Modules\Common\Http\Controllers;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Models\Administrator;
use Modules\Common\Services\SessionService;
use Modules\Common\Services\StorageService;
use ReflectionClass;

class BaseController extends Controller
{
    public const HTTP_INTERNAL_SERVER_ERROR = 500;
    public const HTTP_BAD_REQUEST = 400;
    public const HTTP_OK = 200;
    public const HTTP_NOT_FOUND = 404;
    public const DEFAULT_TABLE_ROWS_COUNT = 10;
    public const PAGE_TYPE_LIST = 'list';
    public const PAGE_TYPE_CREATE = 'create';
    public const PAGE_TYPE_EDIT = 'edit';
    public const PAGE_TYPE_DELETE = 'delete';
    public const DATE_FORMAT = 'd-m-Y';

    protected ?SessionService $sessionService = null;
    protected ?StorageService $storageService = null;
    protected string $cacheKey = '';
    protected string $pageTitle = '';
    protected ?string $keyForCachedData = null;

    public function __construct()
    {
        $this->setCacheKey();
        $this->setPageTitle($this->pageTitle);
    }

    public function backError(string $msg, array $replace = [], array|null $data = null): RedirectResponse
    {
        if ($data) {
            return back()
                ->withInput($data)
                ->with('fail', __($msg, $replace));
        }

        return back()->with('fail', __($msg, $replace));
    }

    public function backSuccess(string $msg, array $replace = []): RedirectResponse
    {
        return back()->with('success', __($msg, $replace));
    }

    public function getStorageService()
    {
        if ($this->storageService === null) {
            $this->storageService = new StorageService();
        }

        return $this->storageService;
    }

    public function setPageTitle($pageTitle)
    {
        view()->share('pageTitle', $pageTitle);
    }

    public function setCacheKey()
    {
        if (empty($this->cacheKey)) {
            $class = (new ReflectionClass($this))->getShortName();
            $this->cacheKey = sprintf(
                "%s.%s.%s",
                'filters',
                strtolower(str_replace('Controller', '', $class)),
                'list'
            );
        }
    }

    public function getFilters()
    {
        return session($this->cacheKey);
    }

    public function getFiltersSpecifyCacheKey($cacheKey)
    {
        return session($cacheKey);
    }

    public function cleanFilters(): bool
    {
        session()->forget($this->cacheKey);

        return true;
    }

    public function setFiltersFromRequest(
        BaseRequest $request,
                    $cacheKey = null
    ): bool
    {
        $key = $cacheKey ?: $this->cacheKey;

        session()->put(
            $key,
            $request->validated()
        );

        return true;
    }

    private function arrayToString(array $params): string
    {
        $paramString = '';
        $arrayKeys = array_keys($params);
        $last_key = end($arrayKeys);
        foreach ($params as $key => $value) {
            $paramString .= "$key => $value";
            if ($key != $last_key) {
                $paramString .= ', ';
            }
        }

        return $paramString;
    }

    /**
     * @return int
     * @todo delete
     */
    public function getTableLength()
    {
        return (int)session($this->cacheKey . '.length', self::DEFAULT_TABLE_ROWS_COUNT);
    }

    public function getPaginationLimit(?string $routeName = null): int
    {
        $routeName = request()->route()->getName();
        $routeName = explode('.', $routeName);
        $routeName[array_key_last($routeName)] = '*';
        $routeName = implode('.', $routeName);


        return (int)session($routeName, self::DEFAULT_TABLE_ROWS_COUNT);
    }

    protected function getKeyForCache()
    {
        if ($this->keyForCachedData == null) {
            $sessionKeys = session($this->cacheKey, []);
            $sessionKeys['page'] = request()->get('page');
            $sessionKeys['controller'] = static::class;
            $this->keyForCachedData = md5(json_encode($sessionKeys));
        }

        return $this->keyForCachedData;
    }

    protected function getAdministrator(): ?Administrator
    {
        return Auth::user();
    }

    protected function getAdministratorOrFail(): Administrator
    {
        $admin = $this->getAdministrator();
        if (!$admin) {
            throw new ModelNotFoundException();
        }

        return $admin;
    }

    public static function getControllerRouteName(): string
    {
        $routeName = request()->route()->getName();
        $routeName = explode('.', $routeName);
        $routeName[array_key_last($routeName)] = '*';
        return implode('.', $routeName);
    }
}
