<?php

namespace Modules\Common\Models;

use Illuminate\Auth\Authenticatable as AuthenticatableTrait;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use Laravel\Sanctum\HasApiTokens;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Enums\InviteUrlActionEnum;
use Modules\Common\Enums\NotificationChannel;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Enums\VeriffProviderStatusEnum;
use Modules\Common\Interfaces\HistoryInterface;
use Modules\Common\Repositories\InviteUrlRepository;
use Modules\Common\Services\EasyPay;
use Modules\Common\Traits\PinTrait;
use Modules\Communication\Interfaces\CommunicationInterface;
use PDO;
use Throwable;

/**
 * Modules\Common\Models\Client
 *
 * @property int $client_id
 * @property string $pin
 * @property string $idcard_number
 * @property string $first_name
 * @property string|null $middle_name
 * @property string|null $last_name
 * @property string|null $phone
 * @property string|null $email
 * @property int $new
 * @property int $dead
 * @property bool $blocked
 * @property string $type
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property string|null $latin_names
 * @property string|null $gender
 * @property string $legal_status
 * @property string $citizenship_type
 * @property string|null $legal_status_code
 * @property string|null $economy_sector_code
 * @property string|null $industry_code
 * @property int $registered_in_ccr
 * @property int $need_ccr_sync
 * @property string|null $registered_in_ccr_at
 * @property string|null $set_need_ccr_sync_at
 * @property string|null $unset_need_ccr_sync_at
 * @property int|null $verified
 * @property string|null $birth_date
 * @property string|null $first_name_latin
 * @property string|null $middle_name_latin
 * @property string|null $last_name_latin
 * @property-read CustomEloquentCollection|Loan[] $loans
 * @property-read CustomEloquentCollection|ClientAddress[] $addresses
 * @property-read int|null $addresses_count
 * @property-read ClientActualStats|null $clientActualStats
 * @property-read CustomEloquentCollection|ClientBankAccount[] $clientBankAccounts
 * @property-read int|null $client_bank_accounts_count
 * @property-read CustomEloquentCollection|ClientBlockHistory[] $clientBlockHistories
 * @property-read int|null $client_block_histories_count
 * @property-read CustomEloquentCollection|Contact[] $clientContacts
 * @property-read int|null $client_contacts_count
 * @property-read CustomEloquentCollection|Contact[] $clientContactsHistory
 * @property-read int|null $client_contacts_history_count
 * @property-read CustomEloquentCollection|ClientDeleteHistory[] $clientDeleteHistories
 * @property-read int|null $client_delete_histories_count
 * @property-read CustomEloquentCollection|ClientDiscountActual[] $clientDiscountActuals
 * @property-read int|null $client_discount_actuals_count
 * @property-read CustomEloquentCollection|ClientEmail[] $clientEmails
 * @property-read int|null $client_emails_count
 * @property-read CustomEloquentCollection|Guarant[] $clientGuarants
 * @property-read int|null $client_guarants_count
 * @property-read CustomEloquentCollection|Guarant[] $clientGuarantsHistory
 * @property-read int|null $client_guarants_history_count
 * @property-read CustomEloquentCollection|ClientHistory[] $clientHistory
 * @property-read int|null $client_history_count
 * @property-read CustomEloquentCollection|ClientIdCard[] $clientIdCards
 * @property-read int|null $client_id_cards_count
 * @property-read CustomEloquentCollection|ClientName[] $clientNames
 * @property-read int|null $client_names_count
 * @property-read CustomEloquentCollection|ClientPhone[] $clientPhones
 * @property-read int|null $client_phones_count
 * @property-read CustomEloquentCollection|ClientPicture[] $clientPictures
 * @property-read int|null $client_pictures_count
 * @property-read CustomEloquentCollection|ClientRepresentor[] $clientRepresentors
 * @property-read int|null $client_representors_count
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read CustomEloquentCollection|Sms[] $email_pivot
 * @property-read int|null $email_pivot_count
 * @property-read CustomEloquentCollection|File[] $file
 * @property-read int|null $file_count
 * @property-read Administrator|null $handler
 * @property-read ?Loan $latestLoan
 * @property-read int|null $loans_count
 * @property-read CustomEloquentCollection|NotificationSetting[] $notificationSettings
 * @property-read int|null $notification_settings_count
 * @property-read Administrator|null $processedBy
 * @property-read CustomEloquentCollection|Sms[] $smses
 * @property-read int|null $smses_count
 * @property-read CustomEloquentCollection|TmpRequest[] $tmpRequests
 * @property-read int|null $tmp_requests_count
 * @property-read Administrator|null $updater
 * @property-read ClientPhone $lastClientPhone
 * @property-read ?Loan $lastRepaidLoan
 * @property-read CustomEloquentCollection|Loan[] $activeLoans
 * @mixin IdeHelperClient
 */
class Client extends BaseModel implements HistoryInterface, Authenticatable
{
    use PinTrait, HasFactory, HasApiTokens, AuthenticatableTrait, Sortable;

    const UNREG_SALE_CLIENT_ID = 0;

    const LOGIN_TOKEN_KEY = 'WS:LoginToken';

    // legal_status options
    const LS_INDV = 'individual';
    const LS_COMP = 'company';

    // citizenship_type options
    const CT_LOCAL = 'local';
    const CT_FOREIGNER = 'foreigner';
    const CT_UNKNOWN = 'unknown';


    // gender options
    const GENDER_MALE = 'male';
    const GENDER_FEMALE = 'female';
    const GENDER_UNKNOWN = 'unknown';

    // legal_status_code options
    const LS_CODE_LOCAL_INVD = 1998; // местно физическо лице
    const LS_CODE_FOREIGN_INVD = 1999; // чуждестранно физическо лице
    const LS_CODE_MERCHANT = 437; // търговец по ТЗ

    // economy_sector_code options
    const ES_CODE_INVD = 3312; // домакинства - население
    const ES_CODE_COMP = 9; // некласифицирани по сектор

    // indusrtry_code options
    const IND_CODE_INVD = '00'; // физическо лице
    const IND_CODE_COMP = 96; // други персонални услуги

    // ccr person type
    const CPT_INDV = 1; // физическо лице и няма флаг = чуждестранен гражданин
    const CPT_COMP = 2; // юридическо лице
    const CPT_FRGN = 3; // физическо лице и има флаг = чуждестранен гражданин
    const CPT_UKNW = 5; // неизвестно лице

    const CLIENT_DEFAULT_PIN_LENGTH = 10;

    const CCR_BORR_TRIGER_PROPS = [
        'first_name',
        'middle_name',
        'last_name',
        'first_name_latin',
        'middle_name_latin',
        'last_name_latin',
        'legal_status_code',
        'economy_sector_code',
        'industry_code',
    ];

    protected $historyClass = ClientHistory::class;
    protected $table = 'client';
    protected $primaryKey = 'client_id';
    protected $fillable = [
        'pin',
        'idcard_number',
        'first_name',
        'middle_name',
        'last_name',
        'phone',
        'email',
        'type',
        'dead',
        'blocked',
        'blocked_to_date',
        'latin_names',
        'gender',
        'legal_status',
        'citizenship_type',
        'legal_status_code',
        'economy_sector_code',
        'industry_code',
        'registered_in_ccr',
        // CCR: BORR
        'need_ccr_sync',
        'registered_in_ccr_at',
        'set_need_ccr_sync_at',
        'unset_need_ccr_sync_at',
        'birth_date',
        'acc_reg',
        'acc_reg_at',
        'first_name_latin',
        'middle_name_latin',
        'last_name_latin',
        'migration_db',
        'migration_provision_id',
        'migration_nefin_id',
        'new',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',

        /// veriff
        'verif_skipped',
        /// (default value null whn no action from user  | when user skip verification is set to true | when user start verification is set to false.)
        'verif_processed_at',
        'verif_processed_loan_id',
        'verif_provider_session_id',
        'verif_provider_url',
        'verif_provider_status',
        'verif_provider_status_updated_at',
        'verif_provider_data',
        'verif_provider_get_images_at',
        'verif_fails_count',
        'docs_verified_at',
        'verif_offered',
        'verif_offered_at',
        'verif_skip_type',
    ];

    protected $casts = [
        'blocked' => 'boolean',
        'verif_provider_status' => VeriffProviderStatusEnum::class,
    ];

    public $sortable = ['full_name'];

    public function hasVeriffAction(): bool
    {
        if (
            /// when not has client action or
            is_null($this->verif_skipped) ||
            /// veriff is started but not finished
            in_array($this->verif_provider_status?->value, [
                VeriffProviderStatusEnum::VERIF_STATUS_CREATED->value,
                VeriffProviderStatusEnum::VERIF_STATUS_WAITING->value,
                VeriffProviderStatusEnum::VERIF_STATUS_STARTED->value,
            ])
        ) {
            return false;
        }

        return true;
    }

    public function clientCreditLimit(): HasOne
    {
        return $this->hasOne(ClientCreditLimit::class, 'client_id', 'client_id')
            ->where('active', 1)
            ->whereNull('deleted_at');
    }

    public function guarantorByPin(): BelongsTo
    {
        return $this->belongsTo(Guarant::class, 'pin', 'pin');
    }

    public function failedLoginAttempt(): HasMany
    {
        return $this->hasMany(FailedLoginAttempt::class, 'client_id', 'client_id');
    }

    public function clientComment(): HasMany
    {
        return $this
            ->hasMany(ClientComment::class, 'client_id', 'client_id')
            ->orderBy('id', 'DESC');
    }

    public function generateInviteUrl(int $loanId, int $loanStatusId): InviteUrl
    {
        $payload = [
            'now' => now(),
            'loan_id' => $loanId,
            'loan_status_id' => $loanStatusId,
            'admin_id' => getAdminId()
        ];

        /// check if hash already generated & valid
        $hasGeneratedHash = app(InviteUrlRepository::class)->hasValidHash(
            InviteUrlActionEnum::signLink,
            $this->getKey()
        );
        if ($hasGeneratedHash) {
            return $hasGeneratedHash;
        }

        return app(InviteUrlRepository::class)->create(
            $this->getKey(),
            $loanId,
            InviteUrlActionEnum::signLink,
            app(SettingRepository::class)->getSetting(
                SettingsEnum::expire_sign_link
            )->default_value,
            $payload
        );
    }

    public static function getCitizenshipTypes(): array
    {
        return [
            self::CT_LOCAL,
            self::CT_FOREIGNER,
            self::CT_UNKNOWN,
        ];
    }

    public static function getLegalStatuses(): array
    {
        return [
            self::LS_INDV,
            self::LS_COMP,
        ];
    }

    public static function getLegalStatusCodes(): array
    {
        return [
            self::LS_CODE_LOCAL_INVD,
            self::LS_CODE_FOREIGN_INVD,
            self::LS_CODE_MERCHANT,
        ];
    }

    public static function getEconomySectorCodes(): array
    {
        return [
            self::ES_CODE_INVD,
            self::ES_CODE_COMP,
        ];
    }

    public static function getIndustryCodes(): array
    {
        return [
            self::IND_CODE_INVD,
            self::IND_CODE_COMP,
        ];
    }

    public static function getGenders(): array
    {
        return [
            self::GENDER_MALE,
            self::GENDER_FEMALE,
            self::CT_UNKNOWN,
        ];
    }

    public static function getCcrPersonTypes(): array
    {
        return [
            self::CPT_INDV,
            self::CPT_COMP,
            self::CPT_FRGN,
            self::CPT_UKNW,
        ];
    }

    public function isLocal(): bool
    {
        return (self::CT_LOCAL == $this->citizenship_type);
    }

    public function isCompany(): bool
    {
        return (self::LS_COMP == $this->legal_status);
    }

    public function isIndividual(): bool
    {
        return (self::LS_INDV == $this->legal_status);
    }


    public function getCcrPersonType(): int
    {
        if ($this->isCompany()) {
            return self::CPT_COMP;
        }

        if ($this->isIndividual()) {
            if ($this->isLocal()) {
                return self::CPT_INDV;
            }

            return self::CPT_FRGN;
        }

        return self::CPT_UKNW;
    }

    /** RELATIONSHIPS START **/

    public function activeLoans(): HasMany
    {
        return $this->hasMany(Loan::class, 'client_id', 'client_id')
            ->where('active', 1)
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
    }

    /**
     * @return HasMany
     * @deprecated
     */
    public function activeAndRepaidLoans(): HasMany
    {
        return $this
            ->hasMany(
                Loan::class,
                'client_id',
                'client_id',
            )
            ->where([
                ['active', '=', 1]
            ])
            ->whereIn('loan_status_id', [
                LoanStatus::ACTIVE_STATUS_ID,
                LoanStatus::REPAID_STATUS_ID,
            ])
            ->orderBy('loan_id', 'DESC');
    }

    public function loans(): HasMany
    {
        return $this->hasMany(Loan::class, 'client_id', 'client_id')
            ->orderBy('created_at', 'DESC');
    }

    public function clientActualStats(): HasOne
    {
        return $this->hasOne(
            ClientActualStats::class,
            'client_id',
            'client_id'
        );
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(
            ClientAddress::class,
            'client_id',
            'client_id'
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_address_id', 'DESC');
    }

    public function idCardAddress(): HasOne
    {
        return $this
            ->hasOne(
                ClientAddress::class,
                'client_id',
                'client_id'
            )
            ->where([
                'last' => 1,
                'active' => 1,
                'type' => ClientAddress::TYPE_ID_CARD
            ]);
    }

    public function currentAddress(): HasOne
    {
        return $this
            ->hasOne(
                ClientAddress::class,
                'client_id',
                'client_id'
            )
            ->where([
                'last' => 1,
                'active' => 1,
                'type' => ClientAddress::TYPE_CURRENT
            ]);
    }

    public function clientHistory(): HasMany
    {
        return $this->hasMany(
            ClientHistory::class,
            'client_id',
            'client_id'
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_history_id', 'DESC');
    }

    public function clientBankAccounts(): HasMany
    {
        return $this->hasMany(
            ClientBankAccount::class,
            'client_id',
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_bank_account_id', 'DESC');
    }

    public function getRepesentor(): ?Representor
    {
        return $this->clientRepresentors->first()?->representor;
    }

    public function clientRepresentors(): HasMany
    {
        return $this
            ->hasMany(
                ClientRepresentor::class,
                'client_id',
            )
            ->where('active', 1)
            ->orderBy('client_representor_id', 'DESC');
    }

    public function clientGuarants(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Guarant::class,
                'loan_guarant_actual',
                'client_id',
                'guarant_id'
            )
            ->withPivot(['guarant_type_id', 'seq_num'])
            ->where([
                ['guarant.active', '=', 1],
                ['loan_guarant_actual.active', '=', 1],
            ])
            ->orderBy('seq_num');
    }

    public function clientGuarantsHistory(): BelongsToMany
    {
        return $this->belongsToMany(
            Guarant::class,
            'loan_guarant_history',
            'client_id',
            'guarant_id'
        )->withPivot(['guarant_type_id'])
            ->where(
                [
                    ['guarant.active', '=', 1],
                    ['loan_guarant_history.active', '=', 1],
                ]
            )
            ->orderBy('seq_num');
    }

    public function clientContacts(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Contact::class,
                'loan_contact_actual',
                'client_id',
                'contact_id'
            )
            ->withPivot(['contact_type_id', 'seq_num', 'loan_contact_actual_id'])
            ->where([
                ['contact.active', '=', 1],
                ['loan_contact_actual.active', '=', 1],
            ])
            ->orderBy('seq_num');
    }

    public function clientContactsHistory(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Contact::class,
                'loan_contact_history',
                'client_id',
                'contact_id'
            )
            ->withPivot(['contact_type_id'])
            ->where([
                ['contact.active', '=', 1],
                ['loan_contact_history.active', '=', 1],
            ])
            ->orderBy('seq_num');
    }

    public function clientBlockHistories(): HasMany
    {
        return $this->hasMany(
            ClientBlockHistory::class,
            'client_id',
            'client_id',
        )
            ->where('active', 1)
            ->orderBy('client_block_history_id', 'DESC');
    }

    public function clientUnblockBlockHistories(): HasMany
    {
        return $this->hasMany(
            ClientUnBlockHistory::class,
            'client_id',
            'client_id',
        )
            ->where('active', 1)
            ->orderBy('client_unblock_history_id', 'DESC');
    }

    public function clientDeleteHistories(): HasMany
    {
        return $this->hasMany(
            ClientDeleteHistory::class,
            'client_id',
            'client_id',
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_delete_history_id', 'DESC');
    }

    public function clientEmails(): HasMany
    {
        return $this->hasMany(
            ClientEmail::class,
            'client_id'
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_email_id', 'DESC');
    }

    public function clientIdCards(): HasMany
    {
        return $this
            ->hasMany(
                ClientIdCard::class,
                'client_id',
                'client_id'
            )
            ->where('active', 1)
            ->orderBy('client_idcard_id', 'DESC');
    }

    public function clientPictures(): HasMany
    {
        return $this->hasMany(
            ClientPicture::class,
            'client_id',
            'client_id'
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_picture_id', 'DESC');
    }

    public function clientNames(): HasMany
    {
        return $this->hasMany(
            ClientName::class,
            'client_id',
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_name_id', 'DESC');
    }

    public function getMainClientPhone(): ?ClientPhone
    {
        return ClientPhone::where([
            ['client_id', '=', $this->client_id],
            ['number', '=', $this->phone],
        ])
            ->orderBy('seq_num')
            ->orderBy('client_phone_id', 'DESC')
            ->first();
    }

    public function clientPhones(): HasMany
    {
        return $this
            ->hasMany(
                ClientPhone::class,
                'client_id',
                'client_id'
            )
            ->where('active', 1)
            ->where('last', 1)
            ->orderBy('seq_num')
            ->orderBy('client_phone_id', 'DESC');
    }

    public function allClientPhonesExceptMain(int $limit = 10)
    {
        return ClientPhone::where([
            ['client_id', '=', $this->client_id],
            ['number', '!=', $this->phone],
        ])
            ->select('client_phone_id', 'number', 'seq_num')
            ->distinct('number')
            ->limit($limit)
            ->get();
    }

    public function tmpRequests(): HasMany
    {
        return $this->hasMany(
            TmpRequest::class,
            'client_id',
            'client_id',
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('tmp_request_id', 'DESC');
    }

    public function clientDiscountActuals(): HasMany
    {
        return $this->hasMany(
            ClientDiscountActual::class,
            'client_id',
            'client_id'
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('client_discount_actual_id', 'DESC');
    }

    /**
     * TODO: unify with getClientDiscountWithProduct
     * @see \Modules\Discounts\Repositories\ClientDiscountActualRepository::getClientDiscountWithProduct
     */
    public function getDiscountForProduct(
        int $productId
    ): ?ClientDiscountActual {
        return ClientDiscountActual::where([
            ['client_id', '=', $this->client_id],
            ['product_id', '=', $productId],
            ['valid_from', '<=', now()],
            ['valid_till', '>=', now()],
            ['client_discount_actual.active', '=', '1'],
            ['client_discount_actual.deleted', '=', '0'],
        ])
            ->orderBy('percent', 'DESC')
            ->first();
    }

    // returns an array in format:
    // [
    //   'product_id' => [
    //      'discount' => xxx,
    //      'from' => xxx,
    //      'till' => xxx,
    //   ]
    // ]
    public function getActualDiscountsForProducts(array $productIds): array
    {
        $rows = ClientDiscountActual::where([
                ['client_id', '=', $this->client_id],
                ['valid_from', '<=', now()],
                ['valid_till', '>=', now()],
                ['client_discount_actual.active', '=', '1'],
                ['client_discount_actual.deleted', '=', '0'],
            ])
            ->whereIn('product_id', $productIds)
            ->orderBy('percent', 'DESC')
            ->get();

        if ($rows->count() < 1) {
            return [];
        }

        $res = [];
        foreach ($rows as $row) {

            // first time add
            if (!isset($res[$row->product_id])) {
                $res[$row->product_id] = [
                    'discount' => $row->percent,
                    'from' => $row->valid_from,
                    'till' => $row->valid_till,
                ];
                continue;
            }

            // if we have another offer for same product and better discount -> use it
            if ($res[$row->product_id]['discount'] < $row->percent) {
                $res[$row->product_id] = [
                    'discount' => $row->percent,
                    'from' => $row->valid_from,
                    'till' => $row->valid_till,
                ];
            }
        }

        return $res;
    }

    public function notificationSettings(): HasMany
    {
        return $this->hasMany(
            NotificationSetting::class,
            'client_id',
            'client_id',
        )->where(
            [
                ['active', '=', 1],
            ]
        )->orderBy('notification_setting_id');
    }

    /**
     * @param string $type information, approve, collect, marketing, system, etc
     * @param NotificationChannel $channel sms, email, mail, etc
     * @return bool
     *
     * @see CommunicationInterface
     * @see NotificationChannel
     */
    public function allowedSendingForTypeAndChanel(string $type, NotificationChannel $channel): bool
    {
        if ($type !== CommunicationInterface::TEMPLATE_TYPE_MARKETING) {
            $type = CommunicationInterface::TEMPLATE_TYPE_SYSTEM;
        }

        $value = NotificationSetting::where([
            ['client_id', '=', $this->client_id],
            ['type', '=', $type],
            ['channel', '=', $channel->value],
        ])->value('value');


        // if no such setting we're allowed sending
        if ($value === null) {
            return true;
        }

        return (bool) $value;
    }

    /* Relationships end */

    public function isBlocked(): bool
    {
        // blocked is not null. If you have an error, check the data received from the db for this model
        return $this->blocked;
    }

    public function isDead(): bool
    {
        return (1 == $this->dead);
    }

    public function checkIsNewByLoans(): bool
    {
        $repaidLoans = $this->clientActualStats->repaid_loans_count;
        $activeLoans = $this->getActiveLoansCount(); /// return active loans count

        if ($activeLoans >= 1 && $repaidLoans === 0) {
            return true;
        }

        return false;
    }

    public function isNew(): bool
    {
        return (1 == $this->new);
    }

    public function isNewLabel(): string
    {
        return $this->isNew() ? __('table.New') : __('table.Old');
    }

    public function getMainBankAccount(): ?ClientBankAccount
    {
        return $this->clientBankAccounts()
            ->where('last', '=', 1)
            ->first();
    }

    public function getMainBankAccountIban(): string
    {
        $bankAccount = $this->getMainBankAccount();
        if (!empty($bankAccount->iban)) {
            return $bankAccount->iban;
        }

        return '';
    }

    public function latestLoan(): HasOne
    {
        return $this->hasOne(Loan::class, 'client_id', 'client_id')
            ->where(['loan.active' => 1])->orderBy('loan_id', 'DESC');
    }

    public function latestActiveLoan(): HasOne
    {
        return $this->latestLoan()->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
    }

    public function latestActiveLoanByWebOffice(): HasOne
    {
        return $this->latestLoan()
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->orderByRaw('loan.office_id =' . Office::OFFICE_ID_WEB . ' desc');
    }

    public function getClientLoanById(int $loanId): ?Loan
    {
        return Loan::where([
            'client_id' => $this->client_id,
            'loan_id' => $loanId,
        ])->first();
    }

    public function getLatestLoan(): ?Loan
    {
        return $this->loans()->first();
    }


    public function clientLastEmail()
    {
        return $this->clientEmails()->where('last', '=', 1)->first();
    }

    public function clientLastEmailAddress(): string
    {
        if (!empty($clientEmail = $this->clientLastEmail())) {
            return $clientEmail->email;
        }

        return '';
    }

    public function lastClientPhone(): HasOne
    {
        return $this->hasOne(
            ClientPhone::class,
            'client_id',
            'client_id'
        )->where(['last' => 1]);
    }

    public function getLastClientPhone(): ?ClientPhone
    {
        return $this->lastClientPhone;
    }

    public function getClientBlockDeleteReason(): mixed
    {
        if ($this->isDeleted()) {
            return $this->clientDeleteHistories->where('last', '=', 1)->first();
        }

        if ($this->isBlocked()) {
            return $this->clientBlockHistories->where('last', '=', 1)->first();
        }

        return null;
    }

    public function clientOffices(): array
    {
        $query = DB::getPdo()->query(
            "
                SELECT
                    DISTINCT(l.office_id)
                FROM
                    loan AS l
                WHERE
                    l.client_id = " . $this->client_id . "
            "
        );

        return $query->fetchAll(PDO::FETCH_ASSOC | PDO::FETCH_COLUMN);
    }

    public function clientLastIdCard(): ?ClientIdCard
    {
        return $this->clientIdCards()
            ->where('last', '=', 1)
            ->first();
    }

    public function clientLastAddress(string $type = '', $with = false)
    {
        $where = [];
        $where[] = ['last', '=', 1];
        if (!empty($type)) {
            $where[] = ['type', '=', $type];
        }

        $relation = $this->addresses();

        // Some cases we need eager loading for serializing the relation too.
        if ($with !== false) {
            $relation->with($with);
        }

        return $relation->where($where)
            ->first();
    }

    public function clientLastAddressIdcard($with = false): ?ClientAddress
    {
        return $this->clientLastAddress(ClientAddress::TYPE_ID_CARD, $with);
    }

    public function clientLastAddressCurrent($with = false)
    {
        return $this->clientLastAddress(ClientAddress::TYPE_CURRENT, $with);
    }

    public function getLastClientName()
    {
        return $this->clientNames()->where('last', '=', 1)->first();
    }

    public function getLastApprovedLoan(): ?Loan
    {
        return $this->getLastLoan(LoanStatus::APPROVED_STATUS_ID);
    }

    public function getLastLoan(int $statusId = 0): ?Loan
    {
        $results = DB::select(
            DB::raw(
                "
            SELECT l.*
            FROM loan l
            " . (
                $statusId > 0
                    ? "JOIN loan_status_history as lsh ON (
                    lsh.loan_id = l.loan_id
                    and lsh.loan_status_id = " . $statusId . "
                )"
                    : ""
                ) . "
            WHERE l.client_id = " . $this->client_id . "
            ORDER BY " . ($statusId > 0 ? "lsh.date" : "l.created_at") . " DESC
            LIMIT 1;
        "
            )
        );

        if (!empty($results)) {
            $collection = Loan::hydrate([(array) $results[0]]);

            return $collection->first();
        }

        return null;
    }

    public function getLastLoanByStatuses(
        $statuses = [
            LoanStatus::NEW_STATUS_ID,
            LoanStatus::PROCESSING_STATUS_ID,
            LoanStatus::SIGNED_STATUS_ID,
            LoanStatus::APPROVED_STATUS_ID,
            LoanStatus::ACTIVE_STATUS_ID
        ]
    ): ?Loan {
        return Loan::where([
            ['client_id', '=', $this->client_id],
            ['active', '=', '1'],
        ])
            ->whereIn('loan_status_id', $statuses)
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    public function hasWaitingRequest(): bool
    {
        return ($this->tmpRequests()->count() > 0);
    }

    public function getAllLoansCount(): int
    {
        return (int) DB::table('loan')->where(
            [
                'client_id' => $this->client_id,
            ]
        )->count();
    }

    public function getActiveLoansCount(): int
    {
        return $this->activeLoans()->count();
    }

    public function getMaxCurrentOverdueOfActiveLoans(): int
    {
        $maxOverdueDays = LoanActualStats::whereIn('loan_id', function ($query) {
            $query->select('loan_id')
                ->from('loan')
                ->where('client_id', $this->client_id)
                ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
        })->max('current_overdue_days');

        return (int) $maxOverdueDays;
    }

    public function hasActiveLoan(): bool
    {
        $activeLoansCount = $this->getActiveLoansCount();

        return ($activeLoansCount > 0);
    }

    public function hasRepaidLoans(): bool
    {
        return ($this->getRepaidLoansCount() > 0);
    }

    public function getRepaidLoansCount(): int
    {
        return DB::table('loan')->where(
            [
                'client_id' => $this->client_id,
                'loan_status_id' => LoanStatus::REPAID_STATUS_ID,
            ]
        )->count();
    }

    public function hasNoLoans(): bool
    {
        $activeLoansCount = DB::table('loan')
            ->where('client_id', $this->client_id)
            ->whereIn(
                'loan_status_id',
                [
                    LoanStatus::ACTIVE_STATUS_ID,
                    LoanStatus::REPAID_STATUS_ID,
                ]
            )
            ->count();

        return ($activeLoansCount > 0);
    }

    public function hasUnsingedLoans(): bool
    {
        $activeLoansCount = DB::table('loan')
            ->where('client_id', $this->client_id)
            ->whereIn(
                'loan_status_id',
                [
                    LoanStatus::NEW_STATUS_ID,
                ]
            )
            ->count();

        return ($activeLoansCount > 0);
    }

    public function hasUnreceivedEasyPayMoney(): bool
    {
        $loansIds = $this->activeLoans()->pluck('loan_id');

        if ($loansIds->isEmpty()) {
            return false;
        }

        return Payment::query()
            ->whereIn('loan_id', $loansIds)
            ->where([
                'payment_method_id' => PaymentMethodEnum::EASY_PAY->id(),
                'status' => PaymentStatusEnum::EASY_PAY_SENT,
                'active' => 1,
                'direction' => PaymentDirectionEnum::OUT->value,
            ])
            ->where('sent_at', '>=', now()->subDays(EasyPay::MAX_DAYS_FOR_UNRECEIVED_MONEY))
            ->exists();
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'client_id', 'client_id');
    }

    public function getUnreceivedMoneyDataFromEasyPay(?int $loanId = null): array
    {
        $queryBuilder = $this->payments()->where([
            ['direction', '=', PaymentDirectionEnum::OUT->value],
            ['status', '=', PaymentStatusEnum::EASY_PAY_SENT],
            ['active', '=', '1'],
        ]);

        if ($loanId) {
            $queryBuilder->where('loan_id', $loanId);
        }
        $payments = $queryBuilder->get(['sent_at', 'amount', 'loan_id', 'payment_id']);

        if ($payments->isEmpty()) {
            return [];
        }

        $result = [];
        foreach ($payments as $payment) {
            $result[$payment->payment_id] = [
                'date' => (Carbon::parse($payment->sent_at))->format('d.m.Y'),
                'amount' => intToFloat($payment->amount) . ' лв.',
                'loan_id' => $payment->loan_id,
            ];
        }

        return $result;
    }

    public function getLastActiveOrRepaidOfficeLoan(): ?Loan
    {
        $lastActiveLoan = $this->getLastActiveLoan();
        if (!empty($lastActiveLoan->loan_id)) {
            return $lastActiveLoan;
        }

        return $this->getLastRepaidLoanFromOffice();
    }

    public function getLastActiveOrRepaidLoan(): ?Loan
    {
        $lastActiveLoan = $this->getLastActiveLoan();
        if (!empty($lastActiveLoan->loan_id)) {
            return $lastActiveLoan;
        }

        return $this->getLastRepaidLoan();
    }

    public function lastRepaidLoan(): HasOne
    {
        return $this->hasOne(
            Loan::class,
            'client_id',
            'client_id',
        )
            ->where('loan_status_id', LoanStatus::REPAID_STATUS_ID)
            ->orderBy('repaid_at', 'DESC');
    }

    public function getLastRepaidLoan(): ?Loan
    {
        return Loan::where(
            [
                'client_id' => $this->client_id,
                'loan_status_id' => LoanStatus::REPAID_STATUS_ID,
            ]
        )
            ->orderBy('repaid_at', 'DESC')
            ->first();
    }

    public function getLastRepaidLoanFromOffice(): ?Loan
    {
        return Loan::where(
            [
                'client_id' => $this->client_id,
                'loan_status_id' => LoanStatus::REPAID_STATUS_ID,
            ]
        )
            ->where('office_id', '!=', Office::OFFICE_ID_WEB)
            ->orderBy('repaid_at', 'DESC')
            ->first();
    }

    public function getLastActiveLoan(): ?Loan
    {
        return Loan::where(
            [
                'client_id' => $this->client_id,
                'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
            ]
        )
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    /**
     * @return Collection<int, \Modules\Common\Models\Loan>
     */
    public function getAllActiveLoans(): Collection
    {
        return Loan::where([
            'client_id' => $this->client_id,
            'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
        ])
            ->orderBy('created_at', 'DESC')
            ->get();
    }

    public function getLastContactsCollection(?int $limit = null): Collection
    {
        $sql = "
            SELECT
                c.*,
                cca.seq_num,
                ct.contact_type_id,
                ct.name as contact_type_name
            FROM contact c
            JOIN loan_contact_actual as cca
                ON cca.contact_id = c.contact_id
                AND cca.client_id = $this->client_id
                AND cca.active = 1
            JOIN contact_type ct
                ON ct.contact_type_id =  cca.contact_type_id
            WHERE c.deleted_at IS NULL
            ORDER BY c.created_at DESC
        ";

        if ($limit) {
            $sql .= " LIMIT $limit";
        }

        $rows = DB::select(DB::raw($sql));

        if ($rows) {
            $rows = array_filter($rows, static function ($item) {
                static $values = [];

                if (!isset($values[$item->phone])) {
                    $values[$item->phone] = [$item->name];

                    return true;
                }

                if (!in_array($item->name, $values[$item->phone], true)) {
                    $values[$item->phone][] = $item->name;

                    return true;
                }

                return false;
            });

            return Contact::hydrate($rows);
        }

        return new Collection();
    }

    public function previousLoansCount(): int
    {
        return DB::table('loan')
            ->where('client_id', '=', $this->client_id)
            ->where('active', '=', 0)
            ->where('loan_status_id', '=', LoanStatus::REPAID_STATUS_ID)
            ->count();
    }

    public function hydrateCustom(array $items)
    {
        $instance = $this->newModelInstance();

        return $instance->newCollection(
            array_map(
                function ($item) use ($instance) {
                    $obj = $instance->newFromBuilder($item);
                    $obj->loans_total_count = $item->loans_total_count;

                    return $obj;
                },
                $items
            )
        );
    }

    public function getFullName(): string
    {
        return trim(
            $this->first_name
            . (!empty($this->middle_name) ? ' ' . $this->middle_name : '')
            . ' ' . $this->last_name
        );
    }

    public function fullNameSortable(Builder $query, string $direction): Builder
    {
        return $query->orderBy('first_name', $direction);
    }

    public function mappedNotificationSettings(): array
    {
        $settings = $this->notificationSettings;

        $result = [];

        foreach ($settings as $setting) {
            $result[$setting->type][$setting->channel] = [
                'notification_setting_id' => $setting->notification_setting_id,
                'value' => $setting->value,
            ];
        }

        return $result;
    }

    public function getPrettyBirthDay()
    {
        try {
            $birthday = $this->parse($this->pin)['birthday_text'];
        } catch (Throwable $t) {
            $birthday = null;
        }

        return $birthday;
    }

    public function getLastPicture(string $type = 'mvr'): ?ClientPicture
    {
        return ClientPicture::where(
            [
                ['client_id', '=', $this->client_id],
                ['last', '=', 1],
                ['active', '=', 1],
                ['deleted', '=', 0],
            ]
        )
            ->orderBy('client_picture_id', 'DESC')
            ->first();
    }

    public function getMvrPictures(): array
    {
        $obj1 = ClientPicture::where(
            [
                ['client_id', '=', $this->client_id],
                ['last', '=', 1],
                ['active', '=', 1],
                ['deleted', '=', 0],
                ['type', '=', ClientPicture::TYPE_MVR],
                ['source', '=', ClientPicture::SOURCE_MVR_PICT],
            ]
        )->orderBy('client_picture_id', 'DESC')->first();

        $obj2 = ClientPicture::where(
            [
                ['client_id', '=', $this->client_id],
                ['last', '=', 1],
                ['active', '=', 1],
                ['deleted', '=', 0],
                ['type', '=', ClientPicture::TYPE_MVR],
                ['source', '=', ClientPicture::SOURCE_MVR_SIGN],
            ]
        )->orderBy('client_picture_id', 'DESC')->first();

        $clientPics = [];
        if (!empty($obj1->client_picture_id)) {
            $clientPics[] = $obj1;
        }
        if (!empty($obj2->client_picture_id)) {
            $clientPics[] = $obj2;
        }

        //if nothing found, lets just take the last one
        if (empty($clientPics)) {
            $pic = $this->getLastPicture();
            if (!empty($pic->client_picture_id)) {
                $clientPics[] = $pic;
            }
        }

        return $clientPics;
    }

    public function getMvrPicture(): ?ClientPicture
    {
        return ClientPicture::where(
            [
                ['client_id', '=', $this->client_id],
                ['active', '=', 1],
                ['deleted', '=', 0],
                ['type', '=', ClientPicture::TYPE_MVR],
                ['source', '=', ClientPicture::SOURCE_MVR_PICT],
            ]
        )->orderBy('client_picture_id', 'DESC')->first();
    }

    public function getManualUploadedIdCardFiles(int $limit = 1): Collection
    {
        return File::select('file.*')
            ->join('client_document as cd', 'cd.file_id', '=', 'file.file_id')
            ->where('cd.client_id', $this->client_id)
            ->where('file.file_type_id', FileType::FILETYPE_ID_CARD_ID)
            ->orderBy('cd.client_document_id', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function getLatestSmses(int $limit = 5): Collection
    {
        return $this->smses()->limit($limit)->get();
    }

    public function smses(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Sms::class,
                'communication_pivots',
                'client_id',
                'communication_id',
            )
            ->withPivot('loan_id')
            ->where([
                ['sms.active', '=', 1],
            ])
            ->orderBy('sms.sms_id', 'DESC');
    }

    public function addMeta(string $key, string $value = ''): bool
    {
        if (empty($key)) {
            return false;
        }

        $meta = $this->getMeta($key);
        if (!empty($meta->client_meta_id)) {
            $meta->value = $value;
            $meta->save();

            return true;
        }

        $meta = new ClientMeta();
        $meta->key = $key;
        $meta->value = $value;
        $meta->client_id = $this->getKey();
        $meta->save();

        return true;
    }

    public function getMeta(string $key = ''): ?ClientMeta
    {
        if (empty($key)) {
            return null;
        }

        return ClientMeta::where(
            [
                'client_id' => $this->client_id,
                'key' => $key,
                'active' => '1',
                'deleted' => '0',
            ]
        )->first();
    }

    /**
     * Return value of client meta key
     * @param string $key
     * @return mixed: null|string|array
     */
    public function getMetaValue(string $key = '')
    {
        $meta = $this->getMeta($key);

        if (empty($meta->client_meta_id)) {
            return null;
        }

        if (empty($meta->value)) {
            return null;
        }

        $data = json_decode($meta->value, true);
        if (json_last_error() == JSON_ERROR_NONE) {
            return $data;
        }

        return $meta->value;
    }

    public function getHeaderData(): array
    {
        $data = $this->getMetaValue(ClientMeta::KEY_HEADER);
        if (empty($data) || !is_array($data)) {
            return [];
        }

        return $data;
    }

    public function addHeaderDataToMeta(array $headerData): bool
    {
        return $this->addMeta(ClientMeta::KEY_HEADER, json_encode($headerData));
    }

    /**
     * @return BelongsToMany
     * @deprecated
     */
    public function file()
    {
        return $this->belongsToMany(
            File::class,
            'client_file',
            'client_id',
            'file_id'
        );
    }

    public function getLastA4EReport(int $loanId = null): ?A4EReport
    {
        $where = [
            ['client_id', '=', $this->client_id],
            ['last', '=', '1'],
            ['active', '=', '1'],
            ['deleted', '=', '0'],
        ];

        if (!empty($loanId)) {
            $where[] = ['loan_id', '=', $loanId];
        }

        return A4EReport::where($where)
            ->orderBy('a4e_report_id', 'DESC')
            ->first();
    }

    public function getLastCcrReport(): ?CcrReport
    {
        return CcrReport::where([
            ['pin', '=', $this->pin],
            ['last', '=', '1'],
            ['active', '=', '1'],
            ['deleted', '=', '0'],
        ])->orderBy('ccr_report_id', 'DESC')->first();
    }

    public function getLastNoiReports(array $reportNames): array
    {
        $res = [];

        foreach ($reportNames as $reportName) {
            $rep = NoiReport::where([
                ['pin', '=', $this->pin],
                ['active', '=', '1'],
                ['deleted', '=', '0'],
                // ['last', '=', '1'],
            ])
                ->where('name', $reportName)
                ->orderBy('created_at', 'DESC')
                ->limit(1)
                ->first();

            if (!empty($rep->noi_report_id)) {
                $res[] = $rep;
            }
        }

        return $res;
    }

    public function getActualStats(): ?ClientActualStats
    {
        return ClientActualStats::where([
            ['client_id', '=', $this->client_id],
            ['active', '=', '1'],
            ['deleted', '=', '0'],
        ])->first();
    }

    public function getMaxDaysOverdueForCreditLimit(): int
    {
        if ($this->hasActiveLoan()) {
            return $this->getMaxDaysOverdueForActiveLoans();
        }

        $lastRepaidLoan = $this->getLastRepaidLoan();
        if (empty($lastRepaidLoan->loan_id)) {
            return 0;
        }

        $stats = $lastRepaidLoan->loanActualStats()->first();
        if (empty($stats->loan_id)) {
            return 0;
        }

        return (int) $stats->max_overdue_days;
    }

    public function getMaxDaysOverdueForActiveLoans(): int
    {
        return DB::selectOne(
            "
            SELECT COALESCE(MAX(las.current_overdue_days), 0) as overdue_days
            FROM loan_actual_stats las
            JOIN loan l ON l.loan_id = las.loan_id
            WHERE
                l.client_id = " . $this->client_id . "
                AND l.loan_status_id = '" . LoanStatus::ACTIVE_STATUS_ID . "'
                AND las.active = '1';
        "
        )->overdue_days;
    }

    public function getCreditLimitOfLastActiveLoan(): ?float
    {
        $res = DB::selectOne(
            "
            SELECT las.credit_limit
            FROM loan_actual_stats las
            JOIN loan l ON l.loan_id = las.loan_id AND l.client_id = " . $this->client_id . "
            JOIN loan_status_history lsh ON l.loan_id = lsh.loan_id AND lsh.loan_status_id = '" . LoanStatus::ACTIVE_STATUS_ID . "'
            WHERE
                l.active = '1'
                AND lsh.active = '1'
                AND las.active = '1'
            ORDER BY lsh.created_at DESC
            LIMIT 1;
        "
        );

        if (empty($res->credit_limit)) {
            return null;
        }

        return $res->credit_limit;
    }

    public function getClientCreditLimitLastUpdatedDate(): ?string
    {
        $data = $this->getClientCreditLimitStats();
        if (empty($data['credit_limit_updated_at'])) {
            return null;
        }

        return $data['credit_limit_updated_at'];
    }

    public function getClientCreditLimitStats(): array
    {
        $row = DB::selectOne(
            "
            SELECT
                cas.credit_limit,
                cas.credit_limit_updated_at
            FROM client_actual_stats cas
            WHERE
                cas.client_id = " . $this->client_id . "
                AND cas.active = '1'
            ORDER BY cas.created_at DESC
            LIMIT 1;
        "
        );

        if (!isset($row->credit_limit)) {
            return [];
        }

        return (array) $row;
    }

    public function getLastMvrReport(): ?MvrReport
    {
        return MvrReport::where([
            ['pin', '=', $this->pin],
            ['last', '=', '1'],
            ['active', '=', '1'],
            ['deleted', '=', '0'],
        ])
            ->orderBy('mvr_report_id', 'DESC')
            ->first();
    }

    public function getLastComment(): string
    {
        $obj = CommunicationComment::where('client_id', $this->client_id)
            ->orderBy('communication_comment_id', 'desc')
            ->first();

        if (empty($obj->text)) {
            return '';
        }

        return $obj->text;
    }

    public function hadOnlineLoans(): bool
    {
        foreach ($this->loans as $loan) {
            if ($loan->isOnlineLoan()) {
                return true;
            }
        }

        return false;
    }

    public function getMigratedContractNumbers(): array
    {
        $rows = Loan::where('client_id', $this->client_id)
            ->where('migration_db', 'provision')
            ->get();

        $result = [];
        foreach ($rows as $r) {
            $result[] = $r->contract_number;
        }

        return $result;
    }

    public function installments(): HasMany
    {
        return $this->hasMany(Installment::class, 'client_id', 'client_id');
    }

    public function getLastPayment(): ?Payment
    {
        $results = DB::select(
            DB::raw(
                "
            SELECT p.*
            FROM payment p
            WHERE
                p.client_id = " . $this->client_id . "
                AND p.active = 1
                AND p.status = 'delivered'
                AND p.direction = 'in'
            ORDER BY p.created_at
            LIMIT 1;
        "
            )
        );

        if (!empty($results)) {
            $collection = Payment::hydrate([(array) $results[0]]);

            return $collection->first();
        }

        return null;
    }

    public function metas(): HasMany
    {
        return $this->hasMany(ClientMeta::class, 'client_id', 'client_id');
    }

    public function getClientMainData(): array
    {
        return [
            'client_id'      => $this->client_id,
            'pin'            => $this->pin,
            'idcard_number'  => $this->idcard_number,
            'first_name'     => $this->first_name,
            'middle_name'    => $this->middle_name,
            'last_name'      => $this->last_name,
            'phone'          => $this->phone,
            'email'          => $this->clientLastEmailAddress(),
            'gender'         => $this->gender,
        ];
    }
}
