<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;

/**
 * @property int $bucket_id
 * @property string $name
 * @property string $description
 * @property string $type
 * @property int $overdue_days_from
 * @property int $overdue_days_to
 * @property Carbon|null $created_at
 * @property-read CustomEloquentCollection|BucketHistory[] $historyEntries
 * @property-read CustomEloquentCollection|BucketHistory[] $loans
 * @mixin IdeHelperBucket
 */
class Bucket extends Model
{
    const UPDATED_AT = null;

    public const BUCKET_0_ID = 0;
    public const BUCKET_1_ID = 1;
    public const BUCKET_2_ID = 2;
    public const BUCKET_3_ID = 3;
    public const BUCKET_4_ID = 4;
    public const BUCKET_5_ID = 5;
    public const BUCKET_LEGAL_ACTION_ID = 6;
    public const BUCKET_SELLING_ID = 7;
    public const BUCKET_WRITTEN_OFF_ID = 8;

    public const TYPE_STATIC = 'static';
    public const TYPE_DYNAMIC = 'dynamic';

    /**
     * @var string
     */
    protected $table = 'bucket';

    /**
     * @var string
     */
    protected $primaryKey = 'bucket_id';

    /**
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'overdue_days_from',
        'overdue_days_to',
    ];

    public function loans(): HasMany
    {
        return $this->hasMany(
            Loan::class,
            'bucket_id',
            'bucket_id'
        );
    }

    public static function selectOptions(string $value, string $key, $builder = false): static|array
    {
        if ($builder) {
            /** @var static $instance */
            $instance = app(static::class);
            return $instance;
        }

        return static::all()->pluck($value, $key)->toArray();
    }

    public function historyEntries(): HasMany
    {
        return $this->hasMany(
            BucketHistory::class,
            'bucket_id',
            'bucket_id'
        );
    }
}
