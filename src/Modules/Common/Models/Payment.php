<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Modules\Accounting\Models\AccountingPayment;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Entities\DelExpenseSnapshot;
use Modules\Common\Entities\PaymentSnapshot;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\TaskStatusEnum;

/**
 * \Modules\Common\Models\Payment
 *
 * @property int $payment_id
 * @property int|null $client_id
 * @property int|null $loan_id
 * @property int|null $installment_id
 * @property int|null $office_id
 * @property int $payment_method_id
 * @property int $currency_id
 * @property PaymentDirectionEnum $direction
 * @property PaymentDeliveryEnum|null $delivery_type
 * @property PaymentPurposeEnum|null $purpose
 * @property PaymentSourceEnum|null $source
 * @property PaymentProblemEnum|null $problem
 * @property PaymentStatusEnum $status
 * @property string|null $date
 * @property int|null $amount_received
 * @property int|null $amount_resto
 * @property int $amount
 * @property int $manual
 * @property int $correction
 * @property int $bank_account_id
 * @property string|null $document_number
 * @property string|null $description
 * @property array|null $delivery
 * @property string|null $handled_at
 * @property int|null $handled_by
 * @property string|null $relation_key
 * @property int|null $processing_by
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $delivered_at
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property int|null $imported_payment_id
 * @property int|null $cash_operational_transaction_id
 * @property int|null $easy_pay_api_id
 * @property int|null $parent_payment_id
 * @property string|null $sent_at
 * @property int|null $days_on_books
 * @property int|null $months_on_books
 * @property int|null $payment_passed_days
 * @property int|null $payment_overdue_days
 * @property string|null $payment_overdue_amount
 * @property int $skip_easypay_sending
 * @property-read CashOperationalTransaction|null $cashOperationalTransaction
 * @property-read CustomEloquentCollection<int, CashOperationalTransaction> $cashOperationalTransactions
 * @property-read EasyPayAttempt|null $easyPayAttempt
 * @property-read CustomEloquentCollection<int, EasyPayAttempt> $easyPayAttempts
 * @property-read Client|null $client
 * @property-read CustomEloquentCollection<int, Payment> $childPayments
 * @property-read Payment|null $parentPayment
 * @property-read Administrator|null $creator
 * @property-read Currency $currency
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read ImportedPayment|null $importedPayment
 * @property-read Installment|null $installment
 * @property-read Loan|null $loan
 * @property-read Office|null $office
 * @property-read PaymentMethod $paymentMethod
 * @property-read PaymentTask|null $paymentTask
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @property-read BankAccount|null $bankAccount
 * @property string|null $migration_db
 * @property int|null $migration_id
 * @property string|null $refund_state
 * @property-read Collection<int, PaymentDistribution> $paymentDistribution
 * @method static CustomEloquentCollection<int, static> all($columns = ['*'])
 * @method static \Modules\Common\Database\factories\PaymentFactory factory($count = null, $state = [])
 * @method static CustomEloquentCollection<int, static> get($columns = ['*'])
 * @method static Builder|Payment newModelQuery()
 * @method static Builder|Payment newQuery()
 * @method static Builder|Payment ofMyOffices()
 * @method static Builder|Payment onlyTrashed()
 * @method static Builder|Payment query()
 * @method static Builder|Payment whereActive($value)
 * @method static Builder|Payment whereAmount($value)
 * @method static Builder|Payment whereAmountReceived($value)
 * @method static Builder|Payment whereAmountResto($value)
 * @method static Builder|Payment whereBankAccountId($value)
 * @method static Builder|Payment whereCashOperationalTransactionId($value)
 * @method static Builder|Payment whereClientId($value)
 * @method static Builder|Payment whereCorrection($value)
 * @method static Builder|Payment whereCreatedAt($value)
 * @method static Builder|Payment whereCreatedBy($value)
 * @method static Builder|Payment whereCurrencyId($value)
 * @method static Builder|Payment whereDate($value)
 * @method static Builder|Payment whereDaysOnBooks($value)
 * @method static Builder|Payment whereDeleted($value)
 * @method static Builder|Payment whereDeletedAt($value)
 * @method static Builder|Payment whereDeletedBy($value)
 * @method static Builder|Payment whereDeliveredAt($value)
 * @method static Builder|Payment whereDelivery($value)
 * @method static Builder|Payment whereDeliveryType($value)
 * @method static Builder|Payment whereDescription($value)
 * @method static Builder|Payment whereDirection($value)
 * @method static Builder|Payment whereDisabledAt($value)
 * @method static Builder|Payment whereDisabledBy($value)
 * @method static Builder|Payment whereDocumentNumber($value)
 * @method static Builder|Payment whereEasyPayApiId($value)
 * @method static Builder|Payment whereEnabledAt($value)
 * @method static Builder|Payment whereEnabledBy($value)
 * @method static Builder|Payment whereHandledAt($value)
 * @method static Builder|Payment whereHandledBy($value)
 * @method static Builder|Payment whereImportedPaymentId($value)
 * @method static Builder|Payment whereInstallmentId($value)
 * @method static Builder|Payment whereLoanId($value)
 * @method static Builder|Payment whereManual($value)
 * @method static Builder|Payment whereMigrationDb($value)
 * @method static Builder|Payment whereMigrationId($value)
 * @method static Builder|Payment whereMonthsOnBooks($value)
 * @method static Builder|Payment whereOfficeId($value)
 * @method static Builder|Payment whereParentPaymentId($value)
 * @method static Builder|Payment wherePaymentId($value)
 * @method static Builder|Payment wherePaymentMethodId($value)
 * @method static Builder|Payment wherePaymentOverdueAmount($value)
 * @method static Builder|Payment wherePaymentOverdueDays($value)
 * @method static Builder|Payment wherePaymentPassedDays($value)
 * @method static Builder|Payment whereProblem($value)
 * @method static Builder|Payment whereProcessingBy($value)
 * @method static Builder|Payment wherePurpose($value)
 * @method static Builder|Payment whereRefundState($value)
 * @method static Builder|Payment whereRelationKey($value)
 * @method static Builder|Payment whereSentAt($value)
 * @method static Builder|Payment whereSkipEasypaySending($value)
 * @method static Builder|Payment whereSource($value)
 * @method static Builder|Payment whereStatus($value)
 * @method static Builder|Payment whereUpdatedAt($value)
 * @method static Builder|Payment whereUpdatedBy($value)
 * @method static Builder|Payment withTrashed()
 * @method static Builder|Payment withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperPayment
 */
class Payment extends BaseModel
{
    public const PAYMENT_DIRECTION_IN = 'in';
    public const PAYMENT_DIRECTION_OUT = 'out';

    public const PAYMENT_SOURCE_BANK = 'bank';
    public const PAYMENT_SOURCE_EASYPAY = 'easypay';
    public const PAYMENT_SOURCE_CASH = 'cash';
    public const PAYMENT_SOURCE_REFINANCE = 'refinance';
    public const PAYMENT_SOURCE_INTERCEPTION = 'interception'; // прихващане
    public const DELIVERY_LOAN_EXTENSION = 'loan_extension';

    public const EASYPAY_REFUND_DONE = 'refunded';
    public const EASYPAY_REFUND_PROCESSING = 'processing';

    protected $casts = [
        'direction' => PaymentDirectionEnum::class,
        'status' => PaymentStatusEnum::class,
        'delivery_type' => PaymentDeliveryEnum::class,
        'source' => PaymentSourceEnum::class,
        'purpose' => PaymentPurposeEnum::class,
        'problem' => PaymentProblemEnum::class
    ];

    protected $table = 'payment';
    protected $primaryKey = 'payment_id';
    protected $traitCasts = [
        'active' => 'boolean',
        'deleted' => 'boolean',
        'created_at' => 'datetime:d-m-Y H:i',
        'updated_at' => 'datetime:d-m-Y H:i',
        'deleted_at' => 'datetime:d-m-Y H:i',
        'enabled_at' => 'datetime:d-m-Y H:i',
        'disabled_at' => 'datetime:d-m-Y H:i',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
        'enabled_by' => 'integer',
        'disabled_by' => 'integer',
        'delivery' => 'array',
    ];

    protected $fillable = [
        'client_id', 'loan_id', 'installment_id', 'office_id', 'payment_method_id', 'currency_id', 'direction',
        'delivery_type', 'purpose', 'source', 'problem', 'status', 'amount_received', 'amount_resto', 'amount',
        'manual', 'correction', 'bank_account_id', 'document_number', 'description', 'delivery', 'handled_at',
        'handled_by', 'relation_key', 'processing_by', 'active', 'deleted', 'delivered_at', 'created_at', 'created_by', 'updated_by',
        'deleted_by', 'enabled_at', 'enabled_by', 'disabled_at', 'disabled_by', 'imported_payment_id',
        'cash_operational_transaction_id', 'easy_pay_api_id', 'parent_payment_id', 'sent_at', 'days_on_books',
        'months_on_books', 'payment_passed_days', 'payment_overdue_days', 'payment_overdue_amount',
        'skip_easypay_sending', 'migration_db', 'migration_id', 'refund_state', 'del_expense_snapshot_id',
    ];

    public function getPaymentType(): string
    {
        if (is_null($this->delivery_type) || in_array($this->delivery_type, [PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT, PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION])) {
            return 'default';
        }
        return 'closing';
    }

    public function payConfirm(): BelongsTo
    {
        return $this->belongsTo(PayConfirm::class, 'document_number', 'tid');
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class, 'bank_account_id', 'bank_account_id');
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function importedPayment(): HasOne
    {
        return $this->hasOne(
            ImportedPayment::class,
            'imported_payment_id',
            'imported_payment_id'
        );
    }

    public function cashOperationalTransaction(): HasOne
    {
        return $this->hasOne(
            CashOperationalTransaction::class,
            'payment_id',
            'payment_id'
        );
    }

    public function cashOperationalTransactions(): HasMany
    {
        return $this->hasMany(
            CashOperationalTransaction::class,
            'payment_id',
            'payment_id'
        );
    }

    public function paymentDistribution(): HasMany
    {
        return $this->hasMany(
            PaymentDistribution::class,
            'payment_id',
            'payment_id'
        );
    }

    public function easyPayAttempt(): HasOne
    {
        return $this->hasOne(
            EasyPayAttempt::class,
            'easy_pay_attempt_id',
            'easy_pay_api_id'
        );
    }

    public function easyPayAttempts(): HasMany
    {
        return $this->hasMany(
            EasyPayAttempt::class,
            'payment_id',
            'payment_id'
        );
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(
            Currency::class,
            'currency_id',
            'currency_id'
        );
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(
            PaymentMethod::class,
            'payment_method_id',
            'payment_method_id'
        );
    }

    public static function getAllPaymentSources(): array
    {
        return [
            self::PAYMENT_SOURCE_BANK,
            self::PAYMENT_SOURCE_EASYPAY,
            self::PAYMENT_SOURCE_CASH,
            self::PAYMENT_SOURCE_REFINANCE,
            self::PAYMENT_SOURCE_INTERCEPTION,
        ];
    }


    public function isIncoming(): bool
    {
        return $this->direction == PaymentDirectionEnum::IN;
    }

    public function installment(): BelongsTo
    {
        return $this->belongsTo(
            Installment::class,
            'installment_id',
            'installment_id'
        );
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(
            Office::class,
            'office_id',
            'office_id'
        );
    }

    public function childPayments(): HasMany
    {
        return $this->hasMany(
            Payment::class,
            'parent_payment_id',
            'payment_id'
        );
    }

    public function parentPayment(): BelongsTo
    {
        return $this->belongsTo(
            Payment::class,
            'payment_id',
            'parent_payment_id'
        );
    }

    public function canBeEdited(): bool
    {
        if ($this->isDeleted()) {
            return false;
        }
        if (!$this->isActive()) {
            return false;
        }
        if (!$this->isIncoming()) {
            return false;
        }
        if (!Carbon::today()->equalTo($this->created_at->startOfDay())) {
            return false;
        }
        if ($this->payment_method_id == PaymentMethod::PAYMENT_METHOD_CASH) {
            return false;
        }

        return true;
    }

    public function canBeDeletedLabel(): string
    {
        if ($this->isDeleted()) {
            return 'изтрито';
        }
        if (!$this->isActive()) {
            return 'не е активно';
        }
        if (!$this->isIncoming()) {
            return 'не е входящо';
        }
        // can not delete incoming payments from refinance
        if (!empty($this->parent_payment_id)) {
            return 'има парент пеймент';
        }
        if ($this->status != PaymentStatusEnum::DELIVERED) {
            return 'не е деливерд';
        }
        if ($this->purpose == PaymentPurposeEnum::REFUND) {
            return 'рефунднато';
        }
        if (!Carbon::today()->equalTo($this->created_at->startOfDay())) {
            return 'грешна дата';
        }

        // check if refinanced: loan is repaid + record in loan_refinance + refinancing loan is active
        $loan = $this->loan;
        $refinancingLoan = $loan->getRefinancingLoan();
        if (
            $loan->isRepaid()
            && !empty($refinancingLoan->loan_id)
            // && $refinancingLoan->isActive()
        ) {
            return 'изплатен с рефинанс';
        }
        // check if refinance process started
        if (
            $loan->isActive()
            && !empty($refinancingLoan->loan_id)
            && $refinancingLoan->isInStatusBeforeActivation()
        ) {
            return 'започнат рефинанс';
        }

        return true;
    }

    public function canBeDeleted(): bool
    {
        // if ($this->loan_id == 63419) return true;

        if ($this->isDeleted()) {
            return false;
        }
        if (!$this->isActive()) {
            return false;
        }
        if (!$this->isIncoming()) {
            return false;
        }
        // can not delete incoming payments from refinance
        if (!empty($this->parent_payment_id)) {
            return false;
        }
        if ($this->status != PaymentStatusEnum::DELIVERED) {
            return false;
        }
        if ($this->purpose == PaymentPurposeEnum::REFUND) {
            return false;
        }

        if (!$this->isLast()) {
            return false;
        }

        // check for closed day for physical office
        if (!$this->isOnlineOffice()) {
            $office = $this->office;
            if ($office->isClosedCashDesk()) {
                return false;
            }
        }

        $admin = getAdmin();
        if (
            $admin->isSuperAdmin()
            || $admin->isAccountant()
            || $admin->isSpecUser()
        ) {
            if (
                !Carbon::parse($this->created_at)->isSameYear(Carbon::now())
                && Carbon::parse($this->created_at)->diffInDays(Carbon::now()) > 150
            ) {
                return false;
            }

        } else {
            /// if today greater than created_at +3 days return false
            if (Carbon::today()->gt($this->created_at->modify('+3 days')->startOfDay())) {
                return false;
            }
        }

        // check if refinanced: loan is repaid + record in loan_refinance + refinancing loan is active
        $loan = $this->loan;
        $refinancingLoan = $loan->getRefinancingLoan();
        if (
            $loan->isRepaid()
            && !empty($refinancingLoan->loan_id)
            // && $refinancingLoan->isActive()
        ) {
            return false;
        }

        // check if refinance process started
        if (
            $loan->isActive()
            && !empty($refinancingLoan->loan_id)
            && $refinancingLoan->isInStatusBeforeActivation()
        ) {
            return false;
        }

        return true;
    }

    public function canBeReverted(): bool
    {
        if ($this->isDeleted()) {
            return false;
        }
        if (!$this->isActive()) {
            return false;
        }
        if (!$this->isIncoming()) {
            return false;
        }
        if ($this->status != PaymentStatusEnum::DELIVERED) {
            return false;
        }

        return true;
    }

    public function isLast(): bool
    {
        $count = Payment::where('loan_id', $this->loan_id)
            ->where('created_at', '>', $this->created_at)
            ->count();

        return ($count == 0);

        // $nextPayments = Payment::where('loan_id', $this->loan_id)
        //     ->where('created_at', '>', $this->created_at)
        //     ->get();

        // if ($nextPayments->count() == 0) {
        //     return true;
        // }

        // $next = [];
        // $delNext = [];
        // foreach ($nextPayments as $payment) {
        //     if ($payment->amount > 0) {
        //         $next[$payment->payment_id] = $payment->payment_id;
        //     } else {
        //         $delNext[$payment->parent_payment_id] = $payment->parent_payment_id;
        //     }
        // }

        // $diffNext = $next;
        // if (!empty($delNext)) {
        //     $diffNext = array_diff_assoc($next, $delNext);
        // }

        // return (count($diffNext) == 0);
    }

    public function isCash(): bool
    {
        return $this->payment_method_id === PaymentMethod::PAYMENT_METHOD_CASH;
    }

    public function isManualLabel(): string
    {
        return ($this->source === PaymentSourceEnum::MANUAL_CREATION) ? __('table.manual') : __('table.automatic');
    }

    public function isDelivered(): bool
    {
        return ($this->status == PaymentStatusEnum::DELIVERED);
    }

    public function canBeAccounted(): bool
    {
        return (
            $this->status == PaymentStatusEnum::DELIVERED
            || $this->status == PaymentStatusEnum::EASY_PAY_SENT
            || $this->status == PaymentStatusEnum::REFUNDED
        );
    }

    public function getProcessLabel(): string
    {
        if ($this->status == PaymentStatusEnum::DELIVERED) {
            return $this->getProcessedAgentNames();
        }

        $pTask = PaymentTask::where([
            ['payment_id', '=', $this->payment_id],
            ['status', '=', TaskStatusEnum::PROCESSING],
            ['handled_by', '>', '0'],
        ])->first();

        if (!empty($pTask->payment_task_id)) {
            return __('head::clientCard.openTaskProcessing')
                . $this->getHandledAdminName($pTask->handled_by);
        }

        return '';
    }

    public function getStatusLabel(): string
    {
        if ($this->status == PaymentStatusEnum::UNKNOWN) {
            return __('table.unknown');
        }

        if ($this->status == PaymentStatusEnum::DELIVERED) {

            // ще показваме този лейбъл, да не се бъркат, защо се променил
            // технически ще го държим вече delivered, да не се бърка др логика
            if ($this->isEasypay()) {
                return __('table.SentViaEasypay');
            }

            return __('table.Delivered');
        }

        if ($this->status == PaymentStatusEnum::EASY_PAY_SENT) {
            return __('table.SentViaEasypay');
        }

        return 'Грешен';
    }

    public function getProcessedAgentNames(): string
    {
        if (empty($this->handled_by) || ($this->handled_by < 5 && $this->handled_by != 2)) {
            return __('table.automatic');
        }

        return $this->getHandledAdminName($this->handled_by);
    }

    public function getHandledAdminName(int $adminId): string
    {
        $admin = Administrator::where('administrator_id', $adminId)->first();
        if (empty($admin->administrator_id)) {
            return 'undefinded agent';
        }

        return $admin->getName();
    }

    public function canBeRefunded(): bool
    {
        return (
            $this->status == PaymentStatusEnum::EASY_PAY_SENT
            && $this->skip_easypay_sending == 0
            && self::EASYPAY_REFUND_DONE != $this->refund_state
        );
    }

    public function canSentRefundState(): bool
    {
        return (
            $this->canBeRefunded() && $this->isRefundInProgress()
        );
    }

    public function isRefundInProgress(): bool
    {
        return ($this->refund_state == self::EASYPAY_REFUND_PROCESSING);
    }

    public function canBeSent(): bool
    {
        return (
            $this->status == PaymentStatusEnum::NEW
            || $this->status == PaymentStatusEnum::EASY_PAY_SENDING_FAILED
        );
    }

    public function isEasypay(): bool
    {
        return $this->payment_method_id === PaymentMethod::PAYMENT_METHOD_EASYPAY;
    }

    public function isBank(): bool
    {
        return $this->payment_method_id === PaymentMethod::PAYMENT_METHOD_BANK;
    }

    public function method(): PaymentMethodEnum
    {
        return match ($this->payment_method_id) {
            1 => PaymentMethodEnum::BANK,
            2 => PaymentMethodEnum::EASY_PAY,
            3 => PaymentMethodEnum::CASH,
            4 => PaymentMethodEnum::OFFSET,
        };
    }

    public function shouldHaveNoTask(): bool
    {
        return $this->status->isFinal() || $this->status === PaymentStatusEnum::EASY_PAY_SENT;
    }

    public function paymentTask(): HasOne
    {
        return $this->hasOne(
            PaymentTask::class,
            'payment_id',
            'payment_id',
        )
            ->where([
                'payment_task.active' => '1',
                'payment_task.deleted' => '0',
            ]);
    }

    public function getRandomHexColor(): string
    {
//        return sprintf('#%06X', mt_rand(0, 0xFFFFFF));
        return 'rgba(' . rand(0, 255) . ',' . rand(0, 255) . ',' . rand(0, 255) . ', 0.2)';
    }

    public function scopeOfMyOffices(Builder $query)
    {
        // $officeIds = getAdmin()->offices->pluck('office_id', 'office_id')->toArray();
        $query->whereIn('office_id', getAdminOfficeIds());
    }

    // used for accounting row
    public function getCalcDistribution()
    {
        $result = (object)[
            'principal' => 0,
            'interest' => 0,
            'penalty' => 0,
            'late_interest' => 0,
            'late_penalty' => 0,
            'amount' => 0, // taxes
        ];

        $distibutionRows = $this->paymentDistribution;
        if ($distibutionRows->count() > 0) {
            foreach ($distibutionRows as $distibutionRow) {
                $prop = $distibutionRow->aim;
                $result->$prop += $distibutionRow->distributed_amount;
            }
        }

        return $result;
    }

    public function getEasyPayRequests(): array
    {
        return EasypayRequest::where('payment_id', $this->payment_id)
            ->orderBy('id', 'DESC')
            ->get()
            ->all();
    }

    public function sourceModel(): null|CashOperationalTransaction|ImportedPayment|EasyPayAttempt
    {
        return match (true) {
            (bool)$this->imported_payment_id => $this->importedPayment,
            //(bool)$this->dbModel->easy_pay_response_id => $this->dbModel->easyPayResponse,
            (bool)$this->cash_operational_transaction_id => $this->cashOperationalTransaction,
            (bool)$this->easy_pay_api_id => $this->easyPayAttempt,
            default => null,
        };
    }

    public function extractPin(): ?string
    {
        // extract pin from client if exists
        if (!empty($this->client_id)) {
            return $this->client->pin;
        }

        if (empty($this->description)) {
            return null;
        }

        // extract pin from description  if exists
        preg_match('/(ЕГН[\s]{0,}\:)[\s]{0,}([0-9]{9,10})/i', $this->description, $m);
        if (empty($m[2])) {
            return null;
        }

        return trim($m[2]);
    }

    public function getDownloadTransactionDocLink(): string
    {
        $downloadRoute = '';

        $cashTransaction = $this->cashOperationalTransaction;
        if (!empty($cashTransaction->cash_operational_transaction_id)) {
            $cashDoc = $cashTransaction->operationalDocument()?->first();
            if (!empty($cashDoc->cash_operational_documents_id)) {
                $downloadRoute = route(
                    'payment.cashDesk.getDocument',
                    $cashDoc->cash_operational_documents_id
                );
            }
        }

        return $downloadRoute;
    }

    public function getPaymentSnapshot()
    {
        if (!empty($this->del_expense_snapshot_id)) {
            return DelExpenseSnapshot::where('id', $this->del_expense_snapshot_id)->first();
        }

        return PaymentSnapshot::where('payment_id', $this->payment_id)->first();
    }

    public function unclaimedMoney(): HasOne
    {
        return $this->hasOne(UnclaimedMoney::class, 'payment_id', 'payment_id')
            ->where('direction', 'in');
    }

    public function getUnclaimedMoney(): ?UnclaimedMoney
    {
        return UnclaimedMoney::where('payment_id', $this->payment_id)
            ->where('direction', 'in')
            ->first();
    }

    public function getAccountingPayment(): ?AccountingPayment
    {
        return AccountingPayment::where('payment_id', $this->payment_id)
            ->where('isUnclaimedMoney', 0)
            ->first();
    }

    public function getChildRefinancePayments(string $status = PaymentStatusEnum::NEW->value): ?CustomEloquentCollection
    {
        return Payment::where('parent_payment_id', $this->payment_id)
            ->where('status', $status)
            ->get();
    }

    // could be refinancing payment OR payment from what we made a deleting copy
    public function getParentPayment(): ?Payment
    {
        if (empty($this->parent_payment_id)) {
            return null;
        }

        return Payment::where('payment_id', $this->parent_payment_id)->first();
    }

    public function getTotalPaidAmountBeforeThePayment(): int
    {
        $rows = PaymentDistribution::where('loan_id', $this->loan_id)
            ->where('payment_id', '<', $this->payment_id)
            ->get();

        if ($rows->count() < 1) {
            return 0;
        }

        $sum = 0;
        foreach ($rows as $row) {
            $sum += $row->distributed_amount;
        }

        return $sum;
    }

    public function shouldSkipAccountingPaymentCreation(): bool
    {
        $office = $this->office;

        if (empty($office->office_id)) {
            return false;
        }

        if ((bool) $office->skip_accounting === true) {
            return true;
        }

        return false;
    }

    public function isOnlineOffice(): bool
    {
        return (Office::OFFICE_ID_WEB === (int) $this->office_id);
    }
}
