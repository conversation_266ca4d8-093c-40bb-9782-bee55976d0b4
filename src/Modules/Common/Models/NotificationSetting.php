<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Communication\Interfaces\CommunicationInterface;
use Modules\Communication\Observers\NotificationSettingObserver;

/**
 * @mixin IdeHelperNotificationSetting
 */
class NotificationSetting extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'notification_setting';

    /**
     * @var string
     */
    protected $primaryKey = 'notification_setting_id';

    public const NOTIFICATION_SETTING_TYPE_SYSTEM = CommunicationInterface::TEMPLATE_TYPE_SYSTEM;
    public const NOTIFICATION_SETTING_TYPE_INFO = CommunicationInterface::TEMPLATE_TYPE_INFO;
    public const NOTIFICATION_SETTING_TYPE_MARKETING = CommunicationInterface::TEMPLATE_TYPE_MARKETING;
    public const NOTIFICATION_SETTING_TYPE_SALES = CommunicationInterface::TEMPLATE_TYPE_SALES;
    public const NOTIFICATION_SETTING_TYPE_APPROVE = CommunicationInterface::TEMPLATE_TYPE_APPROVE;
    public const NOTIFICATION_SETTING_TYPE_COLLECT = CommunicationInterface::TEMPLATE_TYPE_COLLECT;

    public const NOTIFICATION_SETTING_CHANNEL_SMS = 'sms';
    public const NOTIFICATION_SETTING_CHANNEL_EMAIL = 'email';
    public const NOTIFICATION_SETTING_CHANNEL_MAIL = 'mail'; // Letter
    public const NOTIFICATION_SETTING_CHANNEL_CALL = 'call';

    public const NOTIFICATION_SETTING_DEFAULT_VALUE = 1;

    /**
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'type',
        'channel',
        'value',
        'created_at',
        'created_by',
    ];

    /**
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        self::observe(NotificationSettingObserver::class);
    }

    /**
     * @return string[]
     */
    public static function getTypes(): array
    {
        return [
            self::NOTIFICATION_SETTING_TYPE_SYSTEM,
            self::NOTIFICATION_SETTING_TYPE_INFO,
            self::NOTIFICATION_SETTING_TYPE_MARKETING,
            self::NOTIFICATION_SETTING_TYPE_SALES,
            self::NOTIFICATION_SETTING_TYPE_APPROVE,
            self::NOTIFICATION_SETTING_TYPE_COLLECT,
        ];
    }

    public function getHiddenTypes(): array
    {
        return [
            self::NOTIFICATION_SETTING_TYPE_INFO,
            self::NOTIFICATION_SETTING_TYPE_SALES,
            self::NOTIFICATION_SETTING_TYPE_APPROVE,
            self::NOTIFICATION_SETTING_TYPE_COLLECT,
        ];
    }

    /**
     * @return string[]
     */
    public static function getChannels(): array
    {
        return [
            self::NOTIFICATION_SETTING_CHANNEL_SMS,
            self::NOTIFICATION_SETTING_CHANNEL_EMAIL,
            self::NOTIFICATION_SETTING_CHANNEL_MAIL,
            self::NOTIFICATION_SETTING_CHANNEL_CALL,

        ];
    }

    /**
     * @return array
     */
    public static function notificationDefaultValue(): array
    {
        $result = [];

        foreach (self::getTypes() as $type) {
            $result[$type] = [
                self::NOTIFICATION_SETTING_CHANNEL_CALL,
                self::NOTIFICATION_SETTING_CHANNEL_SMS,
                self::NOTIFICATION_SETTING_CHANNEL_EMAIL,
            ];
        }

        return $result;
    }

    /**
     * @return BelongsTo
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
