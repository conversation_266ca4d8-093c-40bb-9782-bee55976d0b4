<?php

namespace Modules\Common\Models;

use App\Casts\AmountCast;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use Modules\Admin\Entities\OfficeSetting;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Collect\Models\BucketTaskSkip;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Entities\AutoProcessSnapshot;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Common\Enums\BooleanCasesEnum;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\ProductTypeEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Enums\YesNoEnum;
use Modules\Common\Interfaces\HistoryInterface;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Discounts\Models\LoanDiscountLog;
use Modules\Payments\Repositories\PaymentRepository;
use Modules\Product\Repository\ProductSettingRepository;
use Modules\Product\Services\ProductService;
use StikCredit\Calculators\Calculator;
use StikCredit\Calculators\LoanCalculator;

/**
 * @method Builder ofOverdue(bool $isOnline)
 * @method Builder|SaleTask ofMyOffices()
 * @property int $loan_id
 * @property int $client_id
 * @property int $product_id
 * @property int $product_type_id
 * @property int|null $loan_type_id
 * @property string|null $discount_percent
 * @property int $amount_requested
 * @property int $amount_approved
 * @property int|null $installments_requested
 * @property int|null $installments_approved
 * @property int $currency_id
 * @property int|null $period_requested
 * @property int|null $period_approved
 * @property int|null $period_grace
 * @property int $loan_status_id
 * @property int $last_status_update_administrator_id
 * @property string $last_status_update_date
 * @property int $payment_method_id
 * @property int|null $channel_id
 * @property int $office_id
 * @property int|null $administrator_id
 * @property string $hash
 * @property string|null $comment
 * @property int $juridical
 * @property int $isClosedJuridicalCase
 * @property int $cession
 * @property int $fraud
 * @property string|null $repaid_at
 * @property string|null $activated_at
 * @property string|null $interest_updated_at
 * @property string|null $loan_changed_at
 * @property string|null $juridical_case_opened_at
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property string|null $grace_until
 * @property int|null $grace_updated
 * @property string|null $amount_rest
 * @property int $a4e_performance
 * @property string|null $a4e_performance_at
 * @property int|null $a4e_performance_flag
 * @property int $registered_in_ccr
 * @property int $need_ccr_sync
 * @property int $period_final
 * @property string|null $registered_in_ccr_at
 * @property string|null $set_need_ccr_sync_at
 * @property string|null $unset_need_ccr_sync_at
 * @property int $extended
 * @property int $ccr_finished
 * @property string|null $ccr_finished_at
 * @property int $interest_percent
 * @property int $penalty_percent
 * @property string $installment_modifier
 * @property LoanActualStats|null $loanActualStats
 * @property YesNoEnum $has_custom_payment_schedule
 * @property LoanActualStats|null $actualStats
 * @property-read CustomEloquentCollection<int, ClientAddress> $addresses
 * @property-read Administrator|null $administrator
 * @property-read CustomEloquentCollection<int, ApproveAttempt> $approveAttempts
 * @property-read CustomEloquentCollection<int, ClientBankAccount> $bankAccount
 * @property-read BucketTask|null $bucketTask
 * @property-read CustomEloquentCollection<int, CashOperationalTransaction> $cashOperationalTransactions
 * @property-read Client $client
 * @property-read CustomEloquentCollection<int, ClientName> $clientName
 * @property-read Administrator|null $creator
 * @property-read Currency|null $currency
 * @property-read Administrator|null $deleter
 * @property-read CustomEloquentCollection<int, Document> $documents
 * @property-read CustomEloquentCollection<int, ClientEmail> $email
 * @property-read CustomEloquentCollection<int, Email> $emails
 * @property-read CustomEloquentCollection<int, File> $file
 * @property-read Administrator|null $handler
 * @property-read CustomEloquentCollection<int, ClientIdCard> $idCard
 * @property-read CustomEloquentCollection<int, Installment> $installments
 * @property-read LoanIp|null $ip
 * @property-read Administrator|null $lastStatusUpdateAdministrator
 * @property-read LoanBucket|null $loanBucket
 * @property-read CustomEloquentCollection<int, ContactType> $loanContactType
 * @property-read CustomEloquentCollection<int, Contact> $loanContacts
 * @property-read CustomEloquentCollection<int, Contact> $loanContactsHistory
 * @property-read CustomEloquentCollection<int, GuarantType> $loanGuarantType
 * @property-read CustomEloquentCollection<int, Guarant> $loanGuarants
 * @property-read CustomEloquentCollection<int, Guarant> $loanGuarantsHistories
 * @property-read CustomEloquentCollection<int, LoanHistory> $loanHistory
 * @property-read CustomEloquentCollection<int, LoanParamsHistory> $loanParamsHistory
 * @property-read LoanStatus|null $loanStatus
 * @property-read CustomEloquentCollection<int, LoanStatusHistory> $loanStatusHistory
 * @property-read LoanType|null $loanType
 * @property-read CustomEloquentCollection<int, LoanMeta> $meta
 * @property-read CustomEloquentCollection<int, NoiReport> $noiReports
 * @property-read Office $office
 * @property-read CustomEloquentCollection<int, Installment> $orderedInstallments
 * @property-read PaymentMethod $paymentMethod
 * @property-read CustomEloquentCollection<int, Payment> $payments
 * @property-read CustomEloquentCollection<int, ClientPhone> $phones
 * @property-read Administrator|null $processedBy
 * @property-read Product|null $product
 * @property-read ProductType $productGroup
 * @property-read LoanProductSetting|null $loanProductSetting
 * @property-read LoanRefinance|null $refinanced
 * @property-read CustomEloquentCollection<int, Loan> $refinancing
 * @property-read CustomEloquentCollection<int, SaleTask> $saleTasks
 * @property-read CustomEloquentCollection<int, Sms> $smses
 * @property-read CustomEloquentCollection<int, Tax> $taxes
 * @property-read TmpRequestHistory|null $tmpRequest
 * @property-read Administrator|null $updater
 * @property string|null $grace_period_finished_at
 * @property string|null $grace_period_finished_source
 * @method static Builder|BaseModel filterBy(array $filters)
 * @mixin IdeHelperLoan
 */
final class Loan extends BaseModel implements HistoryInterface
{
    use Sortable;

    public const DEFAULT_LOAN_EXTENSION_PERIOD = 30;
    public const MAX_LOAN_COUNT_PER_DAY = 3;

    public const LOAN_OVERDUE_DAYS_SEND_SMS = [
        '3' => SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_3->value,
        '23' => SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_23->value,
        '60' => SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_60->value,
        '90' => SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_90->value,
    ];

    public const APPLICATION_SOURCE_WEB = 'web';
    public const APPLICATION_SOURCE_OFFICE = 'office';

    public const A4E_PRODUCT_PAYDAY = 'loan-to-salary';
    public const A4E_PRODUCT_INSTALLMENT = 'installment-credit';

    public const A4E_PERF_PRODUCT_PAYDAY = 'KDZ';
    public const A4E_PERF_PRODUCT_INSTALLMENT = 'KVN';

    public const APPROVE_TASK_INC = 'identification_new_client';
    public const APPROVE_TASK_IMNR = 'identification_mvr_no_response';
    public const APPROVE_TASK_IMWD = 'identification_mvr_wrong_data';
    public const APPROVE_TASK_IMVD = 'identification_mvr_valid_date';
    public const APPROVE_TASK_CHA = 'changed_amount';
    public const APPROVE_TASK_MNA = 'manual_approve';

    public const APPROVE_TASK_VERIFICATION = 'verification';

    /**
     * @var string
     */
    protected $historyClass = LoanHistory::class;

    /**
     * @var string
     */
    protected $table = 'loan';

    /**
     * @var string
     */
    protected $primaryKey = 'loan_id';

    protected $fillable = [
        'client_id',
        'product_id',
        'product_type_id',
        'loan_type_id',
        'administrator_id',
        'interest_percent',
        'penalty_percent',
        'installment_modifier',
        'discount_percent',
        'amount_requested',
        'amount_approved',
        'installments_requested',
        'installments_approved',
        'currency_id',
        'period_requested',
        'period_approved',
        'period_grace',
        'period_final',
        'loan_status_id',
        'last_status_update_administrator_id',
        'last_status_update_date',
        'payment_method_id',
        'source',
        'channel_id',
        'office_id',
        'hash',
        'juridical',
        'isClosedJuridicalCase',
        'juridical_case_opened_at',
        'cession',
        'fraud',
        'comment',
        'loan_changed_at',
        'grace_until',
        'grace_updated',
        'repaid_at',
        'amount_rest',
        'a4e_performance',
        'a4e_performance_at',
        'a4e_performance_flag',

        'registered_in_ccr', // CCR: CRED
        'registered_in_ccr_at',
        'need_ccr_sync',
        'set_need_ccr_sync_at',
        'unset_need_ccr_sync_at',
        'ccr_finished',
        'ccr_finished_at',
        'skip_ccr', // if loan is not register in CCR and this flag is ON - loan will never be registered in CCR
        'skip_ccr_at',
        'skip_ccr_by',
        'to_be_quit_ccr', // if loan is register in CCR and this flag is ON - loan will be quit from CCR reports(CRED/CUCR) as repaid
        'to_be_quit_ccr_at',
        'to_be_quit_ccr_by',

        'extended',
        'approve_tasks',
        'consultant_id',
        'created_at',
        'created_by',
        'activated_at',
        'has_custom_payment_schedule',
        'migration_db',
        'migration_provision_id',
        'migration_nefin_id',
        'contract_number', // nefin unique num
        'bank_account_id',
        'early_repaid',
        'last_attempt',
        'skip_till', // used for hiding the loan in approve listing till the certain time
        'outer_collector',
        'outer_collector_from_date',
        'marked_as_outer_collector_by',
        'wait_verification', // based on it we skip auto process, since we dont have client picture from MVR
        'skip_auto_process', // based on DTO param in domain/NewLoan by default is 0 - From New App page, checkbox - Без автоматично решение
        're_contracted_overdue',
    ];

    public array $importantProperties = [
        'client_id',
        'product_id',
        'product_type_id',
        'loan_type_id',
        'discount_percent',
        'amount_requested',
        'amount_approved',
        'installments_requested',
        'installments_approved',
        'currency_id',
        'period_requested',
        'period_approved',
        'period_grace',
        'loan_status_id',
        'client_discount_actual_id',
        'last_status_update_administrator_id',
        'last_status_update_date',
        'payment_method_id',
        'source',
        'channel_id',
        'office_id',
        'hash',
        'loan_changed_at',
        'grace_until',
        'grace_updated',
        'repaid_at',
        'amount_rest',
        'insurance',
        'insurance_amount',
    ];

    protected $casts = [
        'approve_tasks' => 'array',
        'interest_percent' => AmountCast::class,
        'penalty_percent' => AmountCast::class,
        'has_custom_payment_schedule' => YesNoEnum::class,
        'outer_collector' => BooleanCasesEnum::class,
        'wait_verification' => BooleanCasesEnum::class,
        'skip_auto_process' => BooleanCasesEnum::class,
        're_contracted_overdue' => BooleanCasesEnum::class,
        'insurance' => BooleanCasesEnum::class,
        'source' => LoanSourceEnum::class,
    ];

    public array $sortable = [
        'amount_approved'
    ];

    public function getAmountToPaid(): int
    {
        if (empty($this->insurance_amount)) {
            return $this->amount_approved;
        }

        return $this->amount_approved - $this->insurance_amount;
    }

    public function hasAnyApproveTask(array $tasks): bool
    {
        return !empty(array_intersect($tasks, $this->approve_tasks ?? []));
    }

    public function affiliateAttempt(): HasOne
    {
        return $this->hasOne(AffiliateAttempt::class, 'loan_id', 'loan_id');
    }

    public function moveApproveTasksToLoanHistory(): void
    {
        $this->loanHistory()->create([
            'administrator_id' => getAdminId(),
            'field' => 'approve_tasks',
            'value_from' => json_encode($this->approve_tasks),
            'value_to' => null
        ]);
    }

    public function getInterestAfterDiscount()
    {
        if (!intval($this->discount_percent) || !empty($this->grace_period_finished_at)) {
            return $this->interest_percent;
        }

        $interest_percent = $this->interest_percent;
        $discount_percent = $this->discount_percent / 100;

        return round($interest_percent * (1 - $discount_percent),4);
    }

    public function getPenaltyAfterDiscount()
    {
        if (!intval($this->discount_percent) || !empty($this->grace_period_finished_at)) {
            return $this->penalty_percent;
        }

        $penalty_percent = $this->penalty_percent;
        $discount_percent = $this->discount_percent / 100;

        return round($penalty_percent * (1 - $discount_percent));
    }

    public static function getTempLoanDataKey(int $loanId)
    {
        return sprintf('tmpLoanUpdateData.%s', $loanId);
    }

    public function markedAsOuterCollectorBy(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'marked_as_outer_collector_by', 'administrator_id');
    }

    public function loanRefinance(): HasMany
    {
        return $this->hasMany(LoanRefinance::class, 'refinancing_loan_id', 'loan_id');
    }

    public function creditLimit(): HasOne
    {
        return $this->hasOne(CreditLimit::class, 'loan_id', 'loan_id');
    }

    public function a4eReports(): HasMany
    {
        return $this->hasMany(A4EReport::class, 'loan_id', 'loan_id');
    }

    public function getA4eReport(): ?A4EReport
    {
        return A4EReport::where('loan_id', $this->loan_id)
                ->orderBy('created_at', 'DESC')
                ->first();
    }

    public function closedJuridicalCase(): HasOne
    {
        return $this->hasOne(ClosedJuridicalCase::class, 'loan_id', 'loan_id');
    }

    public function loanForApproveTimer(): string
    {
        if (!in_array($this->loan_status_id, [LoanStatus::SIGNED_STATUS_ID, LoanStatus::PROCESSING_STATUS_ID])) {
            return "0";
        }
        $statusDate = Carbon::parse($this->last_status_update_date);

        return $statusDate->diff(now())->format('%H:%I:%S');
    }

    public function wasImportantPropertyChanged(): bool
    {
        $changedProperties = array_keys($this->getDirty());

        return count(array_intersect($changedProperties, $this->importantProperties));
    }


    public function getCalculatorForInitialState(): LoanCalculator
    {
        return $this->product->getCalculator(
            $this->installments_approved,
            $this->amount_approved,
            $this->discount_percent,
            new CurrentDate($this->loanActualStats?->first_installment_date)
        );
    }

    public function createDynamicSettings(): LoanProductSetting
    {
        $currentProductSettings = app(ProductSettingRepository::class)->getCurrentProductSettings($this->product_id);
        $settings = new LoanProductSetting();
        $settings->loan_product_setting_id = 1;
        $settings->loan_id = $this->getKey();
        $settings->product_id = $this->product_id;
        $settings->period = $currentProductSettings[ProductSetting::PERIOD_KEY] ?? null;
        $settings->settings = $currentProductSettings;

        //$this->setRelation(LoanProductSetting::class, $settings);//doesn't fucking work!
        return $settings;
    }

    public function getLoanConfig(
        ?string $currentDate = null,
        array $data = []
    ): array {
        $loanProductSettings = $this->loanProductSetting;
        if (!$loanProductSettings) {
            $loanProductSettings = $this->createDynamicSettings();
        }

        if (!$loanProductSettings) {
            throw new \Exception('Invalid loan product settings.');
        }

        /// stupid fix because json_encoded twice
        if (is_string($loanProductSettings->settings)) {
            $loanProductSettings->settings = json_decode($loanProductSettings->settings, true);
        }

        /// loan config
        $utilisationDate = $this->getUtilisationDate();
        $startFromDate = $this->getStartFromDate(clone $utilisationDate);
        $currentDate = $this->getCurrentDate($currentDate);

        $loanConfig = [
            'air' => $this->interest_percent,
            'apr' => $this->penalty_percent,
            'graceDays' => $this->period_grace ?? 0,
            'late_interest' => $loanProductSettings->settings['late_interest'] ?? 10.01,
            'late_penalty' => $loanProductSettings->settings['late_penalty'] ?? 0,
            'late_penalty_days' => $loanProductSettings->settings['late_penalty_days'] ?? 0,
            'discountPercent' => $this->discount_percent ?? 0,
            'installmentModifier' => $this->installment_modifier,
            'utilisationDate' => $this->sqlDate($utilisationDate, 'Y-m-d'),
            'startFromDate' => $startFromDate->format('Y-m-d'),
            'currentDate' => $currentDate,
            'amount' => $this->amount_approved,
            'numberOfInstallments' => $this->isPaydayLoan()
                ? $this->period_approved
                : $this->installments_approved,
            'productType' => $this->productGroup->name,
            'isJuridical' => $this->isJuridical(),
            'juridicalCaseOpenedAt' => $this->juridical_case_opened_at,
            'hasCustomPaymentSchedule' => $this->hasCustomPaymentSchedule()
        ];
        $loanConfig['fees'] = $this->getLoanFees();

        $loanConfig['receivedPayments'] = $this->getReceivedPayments($currentDate);
        if (!empty($data['receivedPayments'])) {
            $loanConfig['receivedPayments'] = array_merge(
                $loanConfig['receivedPayments'] ?? [],
                $data['receivedPayments']
            );
        }

        return $loanConfig;
    }

    public function getCredit(
        string $currentDate = null,
        array $data = [],
        ?array $customConfig = null
    ): LoanCalculator {
        $loanConfig = $this->getLoanConfig($currentDate, $data);

        $loanCalculatorLoan = LoanCalculator::instance();
        $loanCalculatorLoan->build($customConfig ?? $loanConfig);

        return $loanCalculatorLoan;
    }

    public function hasCustomPaymentSchedule(): ?array
    {
        if (!$this->has_custom_payment_schedule?->check()) {
            return null;
        }

        return $this->installments()->orderBy('installment_id', 'ASC')
            ->get()
            ->map(function (Installment $installment) {
                $data = $installment->toArray();

                $data['due_date'] = Carbon::parse($installment->due_date)->format('Y-m-d');
                $data['accrued_interest'] = floatToInt($data['accrued_interest']);
                $data['accrued_penalty'] = floatToInt($data['accrued_penalty']);
                $data['interest'] = floatToInt($data['interest']);
                $data['penalty'] = floatToInt($data['penalty']);
                $data['rest_principal'] = floatToInt($data['rest_principal']);
                $data['principal'] = floatToInt($data['principal']);
                $data['total_amount'] = floatToInt($data['total_amount']);

                $data['paid_late_interest'] = floatToInt($data['paid_late_interest']);
                $data['late_interest'] = floatToInt($data['late_interest']);
                $data['late_penalty'] = floatToInt($data['late_penalty']);
                $data['paid_late_penalty'] = floatToInt($data['paid_late_penalty']);

                return $data;
            })
            ->keyBy('seq_num')
            ->toArray();
    }

    public function getStartFromDate(Carbon $utilisationDate): Carbon
    {
        if ($this->loanActualStats) {
            return Carbon::parse($this->loanActualStats->first_installment_date);
        }

        if ($this->isPaydayLoan()) {
            return $utilisationDate->modify("+{$this->period_approved} days");
        }

        return $utilisationDate->modify($this->installment_modifier);
    }

    protected function getCurrentDate(?string $currentDate): string
    {
        if ($this->saved_to_date) {
            return $this->sqlDate($this->saved_to_date, 'Y-m-d');
        }

        if ($this->isRepaid()) {
            return $this->sqlDate($this->repaid_at, 'Y-m-d');
        }

        return $this->sqlDate($currentDate, 'Y-m-d');
    }

    public function getUtilisationDate(): Carbon
    {
        return $this->activated_at ? Carbon::parse($this->activated_at) : Carbon::now();
    }

    public function getReceivedPayments(?string $toDate = null): array
    {
        if (!$toDate) {
            $toDate = Carbon::now()->format('Y-m-d');
        }

        $out = [];
        $payments = $this->payments
            ->where('direction', PaymentDirectionEnum::IN)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->where('active', 1)
            ->where('handled_at', '<=', $toDate . ' 23:59:59');

        if ($payments->count()) {
            /** @var Payment $payment * */
            foreach ($payments as $payment) {
                $out[] = [
                    'paymentId' => $payment->getKey(),
                    'createdAt' => $payment->handled_at ?? $payment->created_at,
                    'amount' => $payment->amount,
                    'paymentType' => $payment->getPaymentType(),
                    'hexColor' => $payment->getRandomHexColor()
                ];
            }
        }

        return $out;
    }

    protected function getLoanFees(): array
    {
        $loanFees = $this->taxes()->with('creator')->get();
        if (!$loanFees->count()) {
            return [];
        }

        $out = [];
        /** @var Tax $loanFee * */
        foreach ($loanFees as $loanFee) {
            $out[] = [
                'feeId' => $loanFee->getKey(),
                'dueDate' => $loanFee->created_at,
                'priority' => 1,
                'toInstallment' => $loanFee->toInstallment,
                'feeType' => $loanFee->type, // req
                'extensionDays' => $loanFee->extended_days, // req
                'fromDate' => $loanFee->created_at, // req
                'title' => $loanFee->comment, // todo check fee title
                'creator' => $loanFee->creator?->getFullNames(),
                'amount' => $loanFee->amount,
            ];
        }

        return $out;
    }


    public function getIsChangeable(): bool
    {
        if (
            $this->loan_status_id == $this->getOriginal('loan_status_id')
            && in_array($this->loan_status_id, LoanStatus::NOT_CHANGEABLE_STATUSES)
        ) {
            return false;
        }

        return true;
    }


    /**
     * @param int $id
     * @return string
     */
    public static function getLoanStatusMapping(int $id): string
    {
        return LoanStatus::STATUSES[$id];
    }

    /**
     * Get array of statuses which we can count, that client have loans
     *
     * @return array
     */
    public static function getCountableStatuses(): array
    {
        return [
            LoanStatus::ACTIVE_STATUS_ID,
            LoanStatus::REPAID_STATUS_ID,
        ];
    }

    public function loanHistory(): HasMany
    {
        return $this->hasMany(
            LoanHistory::class,
            'loan_id',
            'loan_id'
        )->orderBy('loan_history_id', 'DESC');
    }

    public function loanBucket(): HasOne
    {
        return $this->hasOne(
            LoanBucket::class,
            'loan_id',
            'loan_id'
        );
    }

    ////////////////////////// REFINANCE RELATED METHODS ///////////////////////

    public function refinancing(bool $checkStatus = true): BelongsToMany//getLoansRefinancedByThisLoan
    {
        $where = [
            'loan_refinance.active' => '1',
            'loan_refinance.deleted' => '0',
            'loan.loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
        ];
        if (!$checkStatus) {
            unset($where['loan.loan_status_id']);
        }

        return $this->belongsToMany(
            Loan::class,
            'loan_refinance',
            'refinancing_loan_id',
            'refinanced_loan_id',
        )
            ->where($where)
            ->using(LoanRefinance::class)
            ->withPivot(['refinanced_due_amount', 'refinancing_date']);
    }

    public function hasRefinancing(): BelongsToMany//getLoansRefinancedByThisLoan
    {
        return $this->belongsToMany(
            Loan::class,
            'loan_refinance',
            'refinancing_loan_id',
            'refinanced_loan_id',
        )
            ->where([
                'loan_refinance.active' => '1',
                'loan_refinance.deleted' => '0',
            ])
            ->using(LoanRefinance::class)
            ->withPivot(['refinanced_due_amount', 'refinancing_date']);
    }

    public function refinanced(): HasOne//getLoanRefinancingThisLoan
    {
        return $this->hasOne(
            LoanRefinance::class,
            'refinanced_loan_id',
            'loan_id'
        );
    }

    public function getRefinancingLoan(): ?Loan
    {
        $refLoanid = $this->getRefinancingId();
        if (empty($refLoanid)) {
            return null;
        }

        return Loan::find($refLoanid);
    }

    public static function getLoanRefinanceObj(
        int $refinancingLoanId,
        int $refinancedLoanId
    ): ?LoanRefinance {
        return LoanRefinance::where('refinancing_loan_id', $refinancingLoanId)
            ->where('refinanced_loan_id', $refinancedLoanId)
            ->where('active', '1')
            ->where('deleted', '0')
            ->orderBy('loan_refinance_id', 'DESC')
            ->first();
    }

    public function getRefinancedId(): ?int
    {
        $obj = LoanRefinance::where('refinancing_loan_id', $this->loan_id)
            ->where('active', '1')
            ->where('deleted', '0')
            ->orderBy('refinanced_loan_id', 'DESC')
            ->first();

        return $obj?->refinanced_loan_id ?? null;
    }

    public function getRefinancingId(): ?int
    {
        $obj = LoanRefinance::where('refinanced_loan_id', $this->loan_id)
            ->where('active', '1')
            ->where('deleted', '0')
            ->orderBy('loan_refinance_id', 'DESC')
            ->first();

        return $obj?->refinancing_loan_id ?? null;
    }

    public function getRefinancedLoansIds(): array
    {
        return LoanRefinance::where('refinancing_loan_id', $this->loan_id)
            ->where('active', '1')
            ->where('deleted', '0')
            ->orderByDesc('created_at')
            ->pluck('refinanced_loan_id')
            ->toArray();
    }

    public function getRefinancedLoansIdsFromLog(): array
    {
        return LoanRefinanceLog::where('refinancing_loan_id', $this->loan_id)
            ->where('active', '1')
            ->where('deleted', '0')
            ->orderByDesc('created_at')
            ->pluck('refinanced_loan_id')
            ->toArray();
    }

    public function getRefinancedLoans()
    {
        $refLoanIds = $this->getRefinancedLoansIds();

        return Loan::whereIn('loan_id', $refLoanIds)->get();
    }

    public function getRefinancingStartTime()
    {
        $obj = LoanRefinance::where('refinancing_loan_id', $this->loan_id)
            ->where('active', '1')
            ->where('deleted', '0')
            ->orderBy('loan_refinance_id', 'DESC')
            ->first();

        return $obj?->created_at ?? null;
    }

    public function hasStartedRefinanceProcess(): bool
    {
        $parentId = $this->getRefinancingId();
        if (empty($parentId)) {
            return false;
        }

        $loanRefianncing = Loan::where('loan_id', $parentId)->first();
        if (empty($loanRefianncing->loan_id)) {
            return false;
        }

        return $loanRefianncing->isInStatusBeforeActivation();
    }

    /**
        1. Aко кредит е от физ.офис - винаги взимаме сума - Предсрочно погасяване
        2. Ако кредит е онлайн:
        2а. ако е безлихвен - взимаме сума по предсрочно + сума на оскъпяване на 1ва вноска.
        2б. ако не е безлихвен:
            калкулираме сума за Предсрочно погасяване
            калкулираме сума на цялата главница + оскъпяване на 1ва вноска
            и използваме по-голямото от двете.
    **/
    public function getAmountForRefinance(
        bool $forOnline = true, // depends on parent loan type, we do certain logic
        bool $changeLoan = false // on approve, we not also take the amount, but save it in db + snapshot
    ): int
    {
        // for physical offices
        if (!$forOnline) {
            return $this->getEarlyRepaymentDebtDb();
        }

        // check if already changed installment and snapshot is done
        // so we can use early rep amount
        if (AutoProcessSnapshot::existsForLoan($this)) {
            return $this->getEarlyRepaymentDebtDb();
        }


        // for KDZ
        if ($this->inGracePeriod()) {
            // get recalculated data
            $loanConfig = $this->getLoanConfig();
            $loanConfig['graceDays'] = 0;
            // $loanConfig['discountPercent'] = 0;
            // $loanConfig['hasCustomPaymentSchedule'] = null;
            $calcInstallments = $this->getCredit(customConfig: $loanConfig)->installmentsCollection();

            // get current due
            $currentAmount = $this->getEarlyRepaymentDebtDb();

            $firstInstInterest = 0;
            $firstInstPenalty = 0;
            $firstInstInterest += $calcInstallments->first()?->interest;
            $firstInstPenalty += $calcInstallments->first()?->penaltyAmount;

            $firstInst = $this->getFirstInstallment();

            if ($changeLoan) {
                // before any change -> do a snapshot!
                $snapshot = AutoProcessSnapshot::makeSnapshot($this, 'grace');
                if (empty($snapshot->id)) {
                    throw new \Exception('Failed to create autoProcess snapshot');
                }

                if (!$firstInst) {
                    throw new \RuntimeException('Failed to get first installment');
                }

                if ($firstInst->paid == 1) {
                    $firstInst->paid = 0;
                }

                $firstInst->interest = (float) intToFloat($firstInstInterest);
                $firstInst->accrued_interest = (float) intToFloat($firstInstInterest);
                $firstInst->rest_interest = (float) intToFloat($firstInstInterest);

                $firstInst->penalty = (float) intToFloat($firstInstPenalty);
                $firstInst->accrued_penalty = (float) intToFloat($firstInstPenalty);
                $firstInst->rest_penalty = (float) intToFloat($firstInstPenalty);

                $firstInst->total_amount = $firstInst->principal + $firstInst->interest + $firstInst->penalty;
                $firstInst->save();
            } else {
                // stupid fix, if already set do nothing
                if (
                    $firstInst
                    && floatToInt($firstInst->interest) === $firstInstInterest
                    && floatToInt($firstInst->penalty) === $firstInstPenalty
                ) {
                    $firstInstInterest = 0;
                    $firstInstPenalty = 0;
                }
            }

            return ($currentAmount + $firstInstInterest + $firstInstPenalty);
        }


        // normal online loan
        // where we compare current early repayment amount and
        // whole principal + full 1st installment
        // and take the biggest amount
        $earlyRepAmount = $this->getEarlyRepaymentDebtDb();
        $newAmount = $this->getTotalUnpaidTaxAmount();
        $firstInst = null;
        foreach ($this->getAllInstallments() as $inst) {
            $newAmount += floatToInt($inst->principal);

            if (1 == $inst->seq_num) {
                $firstInst = $inst;

                $newAmount += floatToInt($inst->interest);
                $newAmount += floatToInt($inst->penalty);
            }
        }
        $newAmount = $newAmount - $this->getTotalPaidAmountWithoutExtension();

        if ($earlyRepAmount >= $newAmount) {
            return $earlyRepAmount;
        }


        // we need to update 1st installment
        if ($changeLoan && !empty($firstInst->installment_id)) {
            if (
                $firstInst->accrued_interest < $firstInst->interest
                || $firstInst->accrued_penalty < $firstInst->penalty
            ) {
                // before any change -> do a snapshot!
                $snapshot = AutoProcessSnapshot::makeSnapshot($this, 'normal');
                if (empty($snapshot->id)) {
                    throw new \Exception('Failed to create autoProcess snapshot');
                }

                if ($firstInst->paid == 1) {
                    $firstInst->paid = 0;
                }

                $firstInst->accrued_interest = $firstInst->interest;
                $firstInst->accrued_penalty = $firstInst->penalty;
                $firstInst->rest_interest = ($firstInst->interest - $firstInst->paid_interest);
                $firstInst->rest_penalty = ($firstInst->penalty - $firstInst->paid_penalty);
                $firstInst->save();
            }
        }


        return $newAmount;
    }

    public function getAmountsForRefinance(bool $forOnline = true): array
    {
        $refLoans = $this->getRefinancedLoans();
        if ($refLoans->count() < 1) {
            return [];
        }

        $totals = [];
        foreach ($refLoans as $refLoan) {
            $totals[$refLoan->loan_id] = $refLoan->getAmountForRefinance($forOnline);
        }

        return $totals;
    }

    public function getTotalAmountForRefinance(bool $forOnline = true): int
    {
        $totals = $this->getAmountsForRefinance($forOnline);
        if (empty($totals)) {
            return 0;
        }

        return array_sum($totals);
    }

    public function getTotalAmountForRefinanceForAllActiveLoans(bool $forOnline = true): int
    {
        $client = $this->client;
        $refLoans = $client->getAllActiveLoans();

        $totals = [];
        foreach ($refLoans as $refLoan) {
            $totals[$refLoan->loan_id] = $refLoan->getAmountForRefinance($forOnline);
        }

        return array_sum($totals);
    }

    ////////////////////////////////////////////////////////////////////////////

    public function bucketTaskSkip(): HasMany
    {
        return $this
            ->hasMany(BucketTaskSkip::class, 'loan_id', 'loan_id')
            ->orderBy('id', 'DESC');
    }

    public function activeBucketTaskSkipRule(): BelongsTo
    {
        return $this
            ->belongsTo(BucketTaskSkip::class, 'loan_id', 'loan_id')
            ->where('till_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'DESC');
    }

    public function isInStatusBeforeActivation(): bool
    {
        if (
            in_array($this->loan_status_id, [
                LoanStatus::NEW_STATUS_ID,
                LoanStatus::PROCESSING_STATUS_ID,
                LoanStatus::APPROVED_STATUS_ID
            ])
        ) {
            return true;
        }

        return false;
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(
            Product::class,
            'product_id',
            'product_id'
        );
    }

    public function meta(): HasMany
    {
        return $this->hasMany(
            LoanMeta::class,
            'loan_id',
            'loan_id'
        );
    }

    public function isFailedToRetryAutoProcess3Times(): bool
    {
        return LoanMeta::where('loan_id', $this->loan_id)
            ->where('key', 'RetryAutoProcessJob(3)')
            ->where('value', 'LIKE', 'Fail%')
            ->where('created_at', '>=', now()->subMinutes(2))
            ->exists();
    }

    public function addMeta(
        string $key,
        string $value = '',
        bool $skipExists = false // some actions is done several times, so we should always add them, not update when exists
    ): bool {

        if (empty($key)) {
            return false;
        }

        if ($skipExists === false) {
            $meta = $this->getMeta($key);
            if (!empty($meta->meta_id)) {
                $meta->value = $value;
                $meta->save();

                return true;
            }
        }

        $meta = new LoanMeta();
        $meta->key = $key;
        $meta->value = $value;
        $meta->loan_id = $this->getKey();
        $meta->save();

        return true;
    }

    public function getMeta(string $key = ''): ?LoanMeta
    {
        if (empty($key)) {
            return null;
        }

        return LoanMeta::where(
            [
                'loan_id' => $this->getKey(),
                'key' => $key,
                'active' => '1',
                'deleted' => '0',
            ]
        )->first();
    }

    public function loanActualStats(): HasOne
    {
        return $this->hasOne(
            LoanActualStats::class,
            'loan_id',
            'loan_id'
        );
    }

    /**
     * @return HasMany
     */
    public function file(): HasMany
    {
        return $this->hasMany(
            Document::class,
            'loan_id',
            'loan_id'
        );
    }

    /**
     * @return BelongsToMany
     */
    public function bankAccount()
    {
        return $this->belongsToMany(
            ClientBankAccount::class,
            'loan_bank_account',
            'loan_id',
            'client_bank_account_id'
        )->where('loan_bank_account.last', '=', 1);
    }

    public function paymentAccount(): ?BankAccount
    {
        if (empty($this->bank_account_id)) {
            return null;
        }

        return BankAccount::where('bank_account_id', $this->bank_account_id)->first();
    }

    /**
     * @return BelongsToMany
     */
    public function addresses()
    {
        return $this->belongsToMany(
            ClientAddress::class,
            'loan_address',
            'loan_id',
            'client_address_id'
        )->where('loan_address.last', '=', 1);
    }

    public function getAddressArrayForA4e(): array
    {
        $result = [
            'city' => 'None',
            'address' => 'None',
            'postcode' => 'None',
        ];

        $address = ClientAddress::whereRaw(
            'client_address_id = (
            SELECT la.client_address_id
            FROM loan_address la
            WHERE
                la.loan_id = ' . $this->loan_id . '
                AND la.last = 1
            ORDER BY la.created_at DESC
            LIMIT 1
        )'
        )->first();

        if (!empty($address->post_code)) {
            $result['postcode'] = $address->post_code;
        }
        if (!empty($address->address)) {
            $result['address'] = $address->address;
        }
        if (!empty($address->city_id)) {
            $result['city'] = $address->city?->name ?? 'None';
        }

        return $result;
    }

    public function getCcrArrayForA4e(string $sort = 'DESC'): array
    {
        $result = [
            'json' => null,
            'raw' => null,
            'ccr_report_id' => null,
        ];

        $obj = CcrReport::whereRaw('ccr_report_id IN (
                SELECT cpp.ccr_report_id
                FROM ccr_report_pivot cpp
                WHERE cpp.loan_id = ' . $this->loan_id . '
            )')
            ->orderBy('created_at', $sort)
            ->first();

        if (!empty($obj->ccr_report_id)) {
            $result['ccr_report_id'] = $obj->ccr_report_id;
        }
        if (!empty($obj->data)) {
            $result['json'] = $obj->data;
        }
        if (!empty($obj->raw_data) && $obj->raw_data != 'none') {
            $result['raw'] = $obj->raw_data;
        }

        return $result;
    }

    public function getNoiArrayForA4e(string $sort = 'DESC'): array
    {
        $result = [
            'noi2' => ['json' => null, 'raw' => null, 'noi_report_id' => null,],
            'noi7' => ['json' => null, 'raw' => null, 'noi_report_id' => null,],
            'noi51' => ['json' => null, 'raw' => null, 'noi_report_id' => null,],
        ];

        foreach ($result as $reportName => $repData) {
            $obj = NoiReport::whereRaw(
                "noi_report_id IN (
                SELECT nrp.noi_report_id
                FROM noi_report_pivot nrp
                WHERE
                    nrp.loan_id = " . $this->loan_id . "
                    AND name = '" . $reportName . "'
            )"
            )
                ->orderBy('created_at', $sort)
                ->first();

            if (!empty($obj->parsed_data)) {
                $result[$reportName]['json'] = $obj->parsed_data;
            }
            if (!empty($obj->raw_data)) {
                $result[$reportName]['raw'] = $obj->raw_data;
            }
            if (!empty($obj->noi_report_id)) {
                $result[$reportName]['noi_report_id'] = $obj->noi_report_id;
            }
        }

        return $result;
    }


    /**
     * @return BelongsToMany
     */
    public function idCard()
    {
        return $this->belongsToMany(
            ClientIdCard::class,
            'loan_idcard',
            'loan_id',
            'client_idcard_id'
        )->where('loan_idcard.last', '=', 1);
    }

    public function ip()
    {
        return $this->belongsTo(
            LoanIp::class,
            'loan_id',
            'loan_id'
        );
    }

    /**
     * @return BelongsToMany
     */
    public function email()
    {
        return $this->belongsToMany(
            ClientEmail::class,
            'loan_email',
            'loan_id',
            'client_email_id'
        )->where('loan_email.last', '=', 1);
    }

    public function phones(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                ClientPhone::class,
                'loan_phone',
                'loan_id',
                'client_phone_id'
            )
            ->where('loan_phone.last', '=', 1);
    }

    /**
     * @return BelongsToMany
     */
    public function clientName()
    {
        return $this->belongsToMany(
            ClientName::class,
            'loan_client_name',
            'loan_id',
            'client_name_id'
        )->where('loan_client_name.last', '=', 1);
    }

    public function installments(): HasMany
    {
        return $this->hasMany(
            Installment::class,
            'loan_id',
            'loan_id'
        );
    }

    public function orderedInstallments(): HasMany
    {
        return $this->hasMany(
            Installment::class,
            'loan_id',
            'loan_id'
        )->orderBy('seq_num');
    }

    public function taxes(): HasMany
    {
        return $this->hasMany(
            Tax::class,
            'loan_id',
            'loan_id'
        );
    }

    public function loanStatus(): BelongsTo
    {
        return $this->belongsTo(
            LoanStatus::class,
            'loan_status_id',
            'loan_status_id'
        );
    }

    public function loanStatusHistory(): HasMany
    {
        return $this
            ->hasMany(
                LoanStatusHistory::class,
                'loan_id',
                'loan_id'
            )
            ->orderBy('loan_status_history_id', 'DESC');
    }

    public function getLastHistoryForStatus(int $statusId): ?LoanStatusHistory
    {
        return LoanStatusHistory::where(['loan_id' => $this->getKey(), 'loan_status_id' => $statusId])
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    /**
     * Used in CcrReportService
     */
    public function getApproveDate(): string
    {
        $date = $this->getLastHistoryForStatus(LoanStatus::APPROVED_STATUS_ID)?->date;
        if (empty($date)) {
            return Carbon::parse($this->created_at)->format('Ymd');
        } else {
            $date = Carbon::parse($date);
        }

        return $date->format('Ymd');
    }

    public function loanContacts(): BelongsToMany
    {
        return $this->belongsToMany(
            Contact::class,
            'loan_contact_actual',
            'loan_id',
            'contact_id'
        );
    }

    /**
     * @return BelongsToMany
     */
    public function loanContactsHistory()
    {
        return $this->belongsToMany(
            Contact::class,
            'loan_contact_history',
            'loan_id',
            'contact_id'
        );
    }

    /**
     * [getLastContacts description]
     *
     * @param int|integer $limit
     *
     * @return array
     */
    public function getLastContacts(int $limit = 1): array
    {
        return $results = DB::select(
            DB::raw(
                "
            SELECT
                contact.contact_id,
                contact.name,
                contact.phone,
                lca.seq_num,
                contact_type.contact_type_id,
                contact_type.name as contact_type_name
            FROM contact
            JOIN loan_contact_actual as lca
                ON lca.contact_id = contact.contact_id
                AND lca.loan_id = " . $this->loan_id . "
            JOIN contact_type
                ON contact_type.contact_type_id =  lca.contact_type_id
            ORDER BY lca.loan_contact_actual_id DESC
            LIMIT " . $limit . ";
        "
            )
        );
    }

    public function loanGuarantType(): BelongsToMany
    {
        return $this->belongsToMany(
            GuarantType::class,
            'loan_guarant_actual',
            'loan_id',
            'guarant_type_id'
        );
    }

    public function loanContactType(): BelongsToMany
    {
        return $this->belongsToMany(
            ContactType::class,
            'loan_contact_actual',
            'loan_id',
            'contact_type_id'
        );
    }

    public function loanGuarantsHistories(): BelongsToMany
    {
        return $this->belongsToMany(
            Guarant::class,
            'loan_guarant_history',
            'loan_id',
            'guarant_id'
        );
    }

    public function smses(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Sms::class,
                CommunicationPivot::getTableName(),
                'loan_id',
                'communication_id'
            )
            ->where('communication_type', 'sms');
    }

    public function emails(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Email::class,
                CommunicationPivot::getTableName(),
                'loan_id',
                'communication_id'
            )
            ->where('communication_type', 'email');
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(
            PaymentMethod::class,
            'payment_method_id',
            'payment_method_id'
        );
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(
            Office::class,
            'office_id',
            'office_id'
        );
    }

    /**
     * @return BelongsTo
     */
    public function administrator(): BelongsTo
    {
        return $this->belongsTo(
            Administrator::class,
            'administrator_id',
            'administrator_id'
        );
    }

    /**
     * @return BelongsTo
     */
    public function lastStatusUpdateAdministrator()
    {
        return $this->belongsTo(
            Administrator::class,
            'last_status_update_administrator_id',
            'administrator_id'
        );
    }

    /**
     * @return HasMany
     */
    public function cashOperationalTransactions(): HasMany
    {
        return $this->hasMany(
            CashOperationalTransaction::class,
            'loan_id',
            'loan_id'
        );
    }

    public function payments(): HasMany
    {
        return $this
            ->hasMany(
                Payment::class,
                'loan_id',
                'loan_id'
            )
            ->orderBy('payment_id', 'ASC');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function loanType(): HasOne
    {
        return $this->hasOne(
            LoanType::class,
            'loan_type_id',
            'loan_type_id'
        );
    }

    public function productGroup(): BelongsTo
    {
        return $this->belongsTo(
            ProductType::class,
            'product_type_id',
            'product_type_id'
        );
    }

    /**
     * @return hasOne
     */
    public function tmpRequest()
    {
        return $this->hasOne(
            TmpRequestHistory::class,
            'loan_id',
            'loan_id',
        );
    }

    /**
     * @return HasMany
     */
    public function documents()
    {
        return $this->hasMany(
            Document::class,
            'loan_id',
            'loan_id'
        );
    }

    public function getChannel()
    {
        return Channel::getChannel($this->channel_id);
    }

    public function getSource(): string
    {
        if ($this->isOnlineLoan()) {
            return self::APPLICATION_SOURCE_WEB;
        }

        return self::APPLICATION_SOURCE_OFFICE;
    }

    public function getPaymentMethod(): ?string
    {
        $pm = $this->paymentMethod()->first();

        return $pm->name ?? null;
    }


    public function bucketTask(): HasOne
    {
        return $this->hasOne(
            BucketTask::class,
            'loan_id',
            'loan_id'
        );
    }

    public function saleTasks(): HasMany
    {
        return $this->hasMany(
            SaleTask::class,
            'loan_id',
            'loan_id'
        );
    }

    public function approveAttempts(): HasMany
    {
        return $this->hasMany(
            ApproveAttempt::class,
            'loan_id',
            'loan_id'
        );
    }

    public function getLastApproveDecisionLabel(): ?string
    {
        if ($this->loan_status_id == LoanStatus::PROCESSING_STATUS_ID) {
            $curAdmin = getAdmin();
            if ($this->last_status_update_administrator_id == $curAdmin->administrator_id) {
                $administrator = $curAdmin;
            } else {
                $administrator = Administrator::where(
                    'administrator_id',
                    $this->last_status_update_administrator_id
                )->first();
            }

            return __('table.InReviewFrom') . ':<br> ' . $administrator?->getName();
        }

        if (!empty($this->last_attempt)) {
            $lastApproveAttempt = $this->approveAttempts()->orderBy('approve_attempt_id', 'DESC')->first();

            return $this->last_attempt . ' ' . formatDate($lastApproveAttempt->end_at, 'd.m.Y H:i');
        }

        return '';
    }

    public function isOnlineLoan(): bool
    {
        return (Office::OFFICE_ID_WEB === (int) $this->office_id);
    }

    public function isPaymentMethodBank(): bool
    {
        return PaymentMethod::PAYMENT_METHOD_BANK === (int) $this->payment_method_id;
    }

    public function isPaymentMethodCash(): bool
    {
        return PaymentMethod::PAYMENT_METHOD_CASH === (int) $this->payment_method_id;
    }

    public function isPaymentMethodEasypay(): bool
    {
        return PaymentMethod::PAYMENT_METHOD_EASYPAY === (int) $this->payment_method_id;
    }

    public function currency()
    {
        return $this->belongsTo(
            Currency::class,
            'currency_id',
            'currency_id'
        );
    }

    public function getFirstUnpaidInstallment($afterNow = false): ?Installment
    {
        if ($afterNow) {
            return Installment::where('loan_id', '=', $this->loan_id)
                ->where('paid', '=', 0)
                ->where('due_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('seq_num', 'ASC')
                ->first();
        }

        return Installment::where('loan_id', '=', $this->loan_id)
            ->where('paid', '=', 0)
            ->orderBy('seq_num', 'ASC')
            ->first();
    }

    public function getFirstInstallmentPrimaryAmount(): float
    {
        $obj = Installment::where('loan_id', '=', $this->loan_id)
            ->orderBy('seq_num', 'ASC')
            ->first();

        if (empty($obj->installment_id)) {
            return 0;
        }

        return $obj->getPrimaryAmount();
    }

    public function getFirstInstallment(): ?Installment
    {
        return Installment::where('loan_id', '=', $this->loan_id)
            ->orderBy('seq_num', 'ASC')
            ->first();
    }

    public function getFirstInstallmentDate(): ?Carbon
    {
        $obj = Installment::where('loan_id', '=', $this->loan_id)
            ->orderBy('seq_num', 'ASC')
            ->first();

        if (empty($obj->due_date)) {
            return null;
        }

        return Carbon::parse($obj->due_date);
    }

    public function getUnpaidInstallments()
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->where('paid', '=', 0)
            ->orderBy('seq_num', 'ASC')
            ->get();
    }

    public function getUnpaidInstallmentsByIds(array $installmentIds = [])
    {
        if (empty($installmentIds)) {
            return null;
        }

        return Installment::where('loan_id', '=', $this->getKey())
            ->whereIn('installment_id', $installmentIds)
            ->where('paid', '=', 0)
            ->orderBy('seq_num', 'ASC')
            ->get();
    }

    public function getUnpaidInstallmentByOverdueDays(int $days)
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->where('paid', '=', 0)
            ->where('overdue_days', '=', $days)
            ->orderBy('seq_num', 'DESC')
            ->first();
    }

    public function getPaidInstallments()
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->where('paid', '=', 1)
            ->orderBy('seq_num', 'ASC')
            ->get();
    }

    public function getAllInstallments()
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->orderBy('seq_num', 'ASC')
            ->get();
    }

    public function getInstallmentsForRefresh()
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->where('paid', '=', 0)
            ->whereRaw('
                seq_num <= coalesce(
                    (
                    select i.seq_num
                    from installment i
                    where i.loan_id = ' . $this->loan_id . ' and i.paid = 0 and i.due_date >= now()
                    order by i.seq_num
                    limit 1
                    )
                    ,
                    (
                    select max(i.seq_num)
                    from installment i
                    where i.loan_id = ' . $this->loan_id . ' and i.paid = 0
                    )
                )
            ')
            ->orderBy('seq_num', 'ASC')
            ->get();
    }

    /**
     * @return Installment|null
     */
    public function getCurrentInstallment(): ?Installment
    {
        $installments = $this->getInstallmentsForRefresh();
        if ($installments->count() < 1) {
            return null;
        }

        return $installments->last();
    }

    public function getFirstInstallmentForExtend(): ?Installment
    {
        $installments = $this->getInstallmentsForRefresh();
        if ($installments->count() < 1) {
            return null;
        }

        return $installments->first();
    }

    public function getLastInstallment(): ?Installment
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->orderBy('seq_num', 'DESC')
            ->first();
    }

    public function getPrevInstallment(Installment $inst): ?Installment
    {
        return Installment::where('loan_id', '=', $this->getKey())
            ->where('due_date', '<', $inst->due_date)
            ->orderBy('seq_num', 'DESC')
            ->first();
    }

    public function getUnpaidTaxes(
        array $includeTypes = [],
        array $excludeTypes = [],
        string $order = 'ASC'
    ) {
        $builder = Tax::where('loan_id', '=', $this->getKey())
            ->where('paid', '=', 0)
            ->where('rest_amount', '>', 0);

        if (!empty($includeTypes)) {
            $builder->whereIn('type', $includeTypes);
        }

        if (!empty($excludeTypes)) {
            $builder->whereNotIn('type', $excludeTypes);
        }

        return $builder
            ->orderBy('created_at', $order)
            ->get();
    }

    public function getAllTaxes(
        array $includeTypes = [],
        array $excludeTypes = [],
        array $with = []
    ) {
        $builder = Tax::where('loan_id', '=', $this->getKey());

        if (!empty($includeTypes)) {
            $builder->whereIn('type', $includeTypes);
        }

        if (!empty($excludeTypes)) {
            $builder->whereNotIn('type', $excludeTypes);
        }

        if (!empty($with)) {
            $builder->with($with);
        }

        return $builder
            ->orderBy('created_at', 'ASC')
            ->get();
    }

    /**
     * @return bool
     */
    public function isFraud(): bool
    {
        return 1 == $this->fraud;
    }

    /**
     * @return bool
     */
    public function isCession(): bool
    {
        return 1 == $this->cession;
    }

    public function isJuridical(): bool
    {
        return 1 == $this->juridical;
    }

    public function isClosedJuridicalCase(): bool
    {
        return 1 == $this->isClosedJuridicalCase;
    }

    public function isSkipCcr(): bool
    {
        return 1 == $this->skip_ccr;
    }

    public function isToBeQuitFromCcr(): bool
    {
        return 1 == $this->to_be_quit_ccr;
    }

    public function loanParamsHistory()
    {
        return $this->hasMany(
            LoanParamsHistory::class,
            'loan_id',
            'loan_id'
        );
    }

    public function hasChangedAmount(): bool
    {
        $historyRow = LoanParamsHistory::where('loan_id', $this->loan_id)
            ->where('amount_requested', '!=', $this->amount_approved)
            ->orderBy('loan_params_history_id', 'DESC')
            ->first();

        if (!$historyRow) {
            return false;
        }

        return true;
    }

    // Count loan->installments with status paid ( 1 )
    public function paidInstallments()
    {
        return Installment::where(
            [
                'loan_id' => $this->getKey(),
                'paid' => 1,
            ]
        )->count();
    }

    public function isPaid(): bool
    {
        $unpaidInstallmentsCount = $this->getUnpaidInstallments()->count();
        $unpaidTaxesCount = $this->taxes()->where('paid', 0)->count();

        return ($unpaidInstallmentsCount + $unpaidTaxesCount) === 0;
    }

    ///////////////////////////////////////////////////////////////////////////

    public function isNew(): bool
    {
        return LoanStatus::NEW_STATUS_ID === $this->loan_status_id;
    }

    public function isSigned(): bool
    {
        return LoanStatus::SIGNED_STATUS_ID === $this->loan_status_id;
    }

    public function isProcessing(): bool
    {
        return $this->loan_status_id === LoanStatus::PROCESSING_STATUS_ID;
    }

    public function isApproved(): bool
    {
        return LoanStatus::APPROVED_STATUS_ID === $this->loan_status_id;
    }

    public function isActive(): bool
    {
        return $this->loan_status_id === LoanStatus::ACTIVE_STATUS_ID;
    }

    public function isRepaid(): bool
    {
        return LoanStatus::REPAID_STATUS_ID === $this->loan_status_id;
    }

    public function isCanceled(): bool
    {
        return $this->loan_status_id === LoanStatus::CANCELLED_STATUS_ID;
    }

    public function isWrittenOff(): bool
    {
        return LoanStatus::WRITTEN_OF_STATUS_ID === $this->loan_status_id;
    }

    public function wasActive(): bool
    {
        return in_array(
                $this->loan_status_id,
                LoanStatus::ACTIVE_STATUSES
            ) || $this->isDeactivated();
    }

    public function isFinished(): bool
    {
        return in_array($this->loan_status_id, LoanStatus::FINAL_STATUSES);
    }

    public function isCanceledByPayments(): bool
    {
        return LoanStatus::CANCELLED_STATUS_ID === $this->loan_status_id
            && $this->approveAttempts->last()?->approve_decision_reason_id
            === ApproveDecisionReason::APPROVE_DECISION_REASON_ID_PAYMENT_CANCELED;
    }

    // Check: loan_status_id and activated_at is not null
    public function isDeactivated(): bool
    {
        return LoanStatus::CANCELLED_STATUS_ID === $this->loan_status_id
            && $this->approveAttempts->last()?->approve_decision_reason_id
            === ApproveDecisionReason::APPROVE_DECISION_REASON_ID_EASYPAY_REFUND;
    }

    ///////////////////////////////////////////////////////////////////////////

    public function isRefinanceLoan(): bool
    {
        return LoanType::LOAN_TYPE_ID_REFINANCING === $this->loan_type_id;
    }

    public function isNormalLoan(): bool
    {
        return LoanType::LOAN_TYPE_ID_NORMAL === $this->loan_type_id;
    }

    public function isSaleTaskActive(): bool
    {
        return $this->saleTasks()
                ->whereRaw(
                    DB::raw(
                        "
                    (status = '" . SaleTask::SALE_TASK_STATUS_DONE . "' or show_after > '" . Carbon::now()->format(
                            'Y-m-d H:i:s'
                        ) . "')"
                    )
                )
                ->count() > 0;
    }

    /**
     * @return bool
     */
    public function isInstallmentLoan(): bool
    {
        return $this->product_type_id === ProductTypeEnum::INSTALLMENT->id();
    }

    /**
     * @return bool
     */
    public function isPaydayLoan(): bool
    {
        return $this->product_type_id === ProductTypeEnum::PAYDAY->id();
    }

    public function isOverdue(): bool
    {
        return $this->loanActualStats->current_overdue_days > 0;
    }

    public function getOverduedInstallments()
    {
        return $this->orderedInstallments()
            ->where(
                [
                    ['paid', '=', 0],
                    ['due_date', '<', now()],
                ]
            )->get();
    }

    public function paymentDistribution(): HasMany
    {
        return $this->hasMany(PaymentDistribution::class, 'loan_id', 'loan_id');
    }

    public function lastPayment(): ?Payment
    {
        return $this->payments()
            ->where('direction', Payment::PAYMENT_DIRECTION_IN)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    public function getTotalPaidAmount(): int
    {
        return $this->payments()
            ->where('direction', Payment::PAYMENT_DIRECTION_IN)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->sum('amount');
    }

    public function getTotalPaidAmountWithoutExtension(): int
    {
        return $this->payments()
            ->where('direction', Payment::PAYMENT_DIRECTION_IN)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->where('delivery_type', '!=', PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION)
            ->sum('amount');
    }

    public function getOutPayment(): ?Payment
    {
        return Payment::where([
            ['loan_id', '=', $this->loan_id],
            ['direction', '=', Payment::PAYMENT_DIRECTION_OUT],
            ['active', '=', '1'],
        ])
            ->orderBy('payment_id', 'DESC')
            ->first();
    }

    public function getUnclosedPaymentTask(): ?PaymentTask
    {
        return PaymentTask::where([
            ['loan_id', '=', $this->loan_id],
            ['active', '=', '1'],
        ])
            ->whereIn('status', [TaskStatusEnum::NEW, TaskStatusEnum::PROCESSING])
            ->orderBy('payment_id', 'DESC')
            ->first();
    }

    public function noiReports(): BelongsToMany
    {
        return $this->belongsToMany(
            NoiReport::class,
            'noi_report_pivot',
            'loan_id',
            'noi_report_id'
        )->using(NoiReportPivot::class)
            ->where('active', '=', '1')
            ->orderBy('created_at', 'DESC');
    }

    public function getLastNoiReport(string $type = 'noi2'): ?NoiReport
    {
        return NoiReport::join('noi_report_pivot', 'noi_report.noi_report_id', '=', 'noi_report_pivot.noi_report_id')
            ->where('noi_report_pivot.loan_id', $this->loan_id)
            ->where('noi_report.name', $type)
            ->where('noi_report.active', 1)
            ->orderBy('noi_report.created_at', 'DESC')
            ->first();
    }

    public function loanProductSetting(): HasOne
    {
        return $this->hasOne(
            LoanProductSetting::class,
            'loan_id',
            'loan_id'
        );
    }

    public function getSettingValue(string $key)
    {
        $productSetting = $this->loanProductSetting;
        if (!isset($productSetting->settings)) {
            return false;
        }
        $settings = is_string($productSetting->settings)
            ? json_decode($productSetting->settings, true)
            : $productSetting->settings;

        return $settings[$key] ?? false;
    }

    /**
     * @return false|mixed
     * @todo check where using and replace with real loan period
     */
    public function getPeriod()
    {
        return $this->getSettingValue(ProductSetting::PERIOD_KEY);
    }

    public function getLateInterestRateValue()
    {
        return $this->getSettingValue(ProductSetting::LATE_INTEREST_KEY);
    }

    public function inGracePeriod(): bool
    {
        //// if not set grace_until
        /// return false;
        if (!$this->grace_until) {
            return false;
        }

        /// if grace period is reset by extend loan
        if (!empty($this->grace_period_finished_at)) {
            return false;
        }

        //// now should be before or equal grace final date
        return Carbon::parse($this->grace_until)->gte(Carbon::today());
    }

    public function isExtendable()
    {
        return $this->getSettingValue(ProductSetting::EXTENSION_KEY);
    }

    public function canExtendLoan(): bool
    {
        if (!$this->isActive()) {
            return false;
        }

        return true;

        // $today = Carbon::now();

        // $extendFee = Tax::where('loan_id', $this->getKey())
        //     ->where('type', Tax::TAX_TYPE_LOAN_EXTENSION_FEE)
        //     ->whereBetween('created_at', [
        //         $today->startOfDay()->toDateTimeString(),
        //         $today->endOfDay()->toDateTimeString(),
        //     ])
        //     ->first();

        // if ($extendFee?->exists) {
        //     return false;
        // }

        // return true;
    }

    /**
     * Used in A4E service
     *
     * @param int $limit
     * @return array
     */
    public function getPreviousLoanIds(int $limit = 50): array
    {
        $rows = DB::select(
            DB::raw(
                "
            SELECT
                l.*,
                COALESCE(las.outstanding_amount_total, 0) as remaining_amount,
                COALESCE(las.current_overdue_days, 0) as overdue_days,
                COALESCE(las.current_overdue_amount, 0) as overdue_amount,
                COALESCE(las.max_overdue_amount, 0) as max_overdue_amount,
                COALESCE(las.max_overdue_days, 0) as max_overdue_days,
                (SELECT COUNT(p.payment_id) FROM payment p WHERE p.loan_id = l.loan_id and p.direction = 'in') as payments_count,
                COALESCE(las.paid_installments_count, 0) as installments_paid
            FROM loan l
            LEFT JOIN loan_actual_stats las ON las.loan_id = l.loan_id
            WHERE
                l.client_id = '" . $this->client_id . "'
                AND l.loan_status_id > '" . LoanStatus::PROCESSING_STATUS_ID . "'
                AND l.loan_id < '" . $this->getKey() . "'
                AND l.office_id = 1
                AND l.deleted = '0'
            LIMIT " . $limit . ";
        "
            )
        );

        $result = [];
        foreach ($rows as $key => $row) {
            $result[$row->loan_id] = [
                "id" => (string) $row->loan_id,
                "price" => (float) intToFloat($row->amount_approved),
                "days" => (int) $row->period_approved,
                "credit_type" => (string) $this->getMappedProductForA4EByProductTypeId($row->product_type_id),
                "datetime" => (string) Carbon::parse($row->created_at)->toDateTimeString(),
                "status" => (string) $this->getMappedLoanStatusForA4EByStatusId($row->loan_status_id),

                "remaining_amount" => (double) $row->remaining_amount,
                "overdue_days" => (int) $row->overdue_days,
                "overdue_amount" => (double) $row->overdue_amount,

                "max_overdue_amount" => (double) $row->max_overdue_amount,
                "max_overdue_days" => (int) $row->max_overdue_days,
                "payments_count" => (int) $row->payments_count,
                "installments_paid" => (int) $row->installments_paid,
            ];

            if ($result[$row->loan_id]['status'] != 'completed') { // TODO: to const
                $result[$row->loan_id]['overdue_days'] = (int) 0;
                $result[$row->loan_id]['overdue_amount'] = (double) 0;
                $result[$row->loan_id]['remaining_amount'] = (double) 0;
            }
        }

        return $result;
    }

    public function getMappedProductForA4E(bool $performance = false): ?string
    {
        return $this->getMappedProductForA4EByProductTypeId(
            $this->product_type_id,
            $performance
        );
    }

    private function getMappedProductForA4EByProductTypeId(
        int $productTypeId,
        bool $performance = false
    ): ?string {
        if ($productTypeId == ProductTypeEnum::PAYDAY->id()) {
            return self::A4E_PRODUCT_PAYDAY;
            // return ($performance ? self::A4E_PERF_PRODUCT_PAYDAY : self::A4E_PRODUCT_PAYDAY);
        }

        if ($productTypeId == ProductTypeEnum::INSTALLMENT->id()) {
            return self::A4E_PRODUCT_INSTALLMENT;
            // return ($performance ? self::A4E_PERF_PRODUCT_INSTALLMENT : self::A4E_PRODUCT_INSTALLMENT);
        }

        return null;
    }

    // TODO: creat constants

    /**
     * Стойности:
     * cancel        - отказан (автоматично/от инспектор)
     * cancel_client - отказан от клиента
     * completed     - завършен кредит
     * - за кредити наредени по банков път кредита се маркира като завършен ведната, след нареждане на кредита.
     * - за кредити наредени по изипей, кредита се маркира като „изплатен на клиента" и след получаване на е-мейл от изипей, че сумата е изтеглена, кредита се маркира като „завършен""
     * paid          - изплатен на клиента (сумата е наредена по банков път/изипей)
     * refund        - погасен кредит от клиента"
     */
    private function getMappedLoanStatusForA4EByStatusId(int $loanStatusId)
    {
        if (in_array($loanStatusId, [LoanStatus::WRITTEN_OF_STATUS_ID])) {
            return 'cancel';
        }

        if ($loanStatusId === LoanStatus::CANCELLED_STATUS_ID) {
            $val = 'cancel';

            if (!empty($this->a4e_performance_flag) && $this->a4e_performance_flag == '-6') { // TODO: const from A4E Monthly Performance
                $val = 'cancel_client';
            }

            return $val;
        }

        if (in_array($loanStatusId, [LoanStatus::REPAID_STATUS_ID])) {
            return 'refund';
        }

        return 'completed';
    }

    public function getCreditLimit(): ?CreditLimit
    {
        return CreditLimit::where([
            ['client_id', '=', $this->client_id],
            ['loan_id', '=', $this->loan_id],
            ['last', '=', '1'],
            ['active', '=', '1'],
            ['deleted', '=', '0'],
        ])
            ->orderBy('credit_limit_id', 'DESC')
            ->first();
    }

    public static function getApprovedLoans(int $beforeDays = 0)
    {
        $loans = DB::select(
            "
                 SELECT
                    l.office_id,
                    lsh.loan_id
                 FROM loan_status_history as lsh
                 JOIN loan l ON l.loan_id = lsh.loan_id
                 WHERE lsh.loan_status_id = " . LoanStatus::APPROVED_STATUS_ID . "
                    AND DATE(lsh.date) = DATE(NOW())" . ($beforeDays > 0 ? "- $beforeDays" : "") . "
            "
        );

        return Loan::hydrate($loans);
    }

    public function setStatus(string $status): bool
    {
        $id = array_search($status, LoanStatus::STATUSES, true);
        if (!$id) {
            return false;
        }
        $this->setAttribute('loan_status_id', $id);

        return true;
    }

    public function getStatusLabel(): string
    {
        return __('head::loanStatus.' . $this->loan_status_id);
    }

    public function getStatus(): string
    {
        return self::getLoanStatusMapping($this->getAttribute('loan_status_id'));
    }

    public function getProductTypeName()
    {
        return $this->getAttribute('product_type_id') === ProductTypeEnum::INSTALLMENT->id()
            ? ProductTypeEnum::INSTALLMENT->value
            : ProductTypeEnum::PAYDAY->value;
    }

    public function invalidGraceUntil(): bool
    {
        return empty($this->grace_until) || Carbon::today()->lte(Carbon::parse($this->grace_until));
    }

    public function getGuarantBySeqNum(int $seqNum = 1)
    {
        $row = LoanGuarantActual::where('loan_id', $this->loan_id)
            ->where('seq_num', $seqNum)
            ->where('active', '1')
            ->where('deleted', '0')
            ->whereNull('deleted_at')
            ->first();

        if (empty($row->guarant_id)) {
            return null;
        }

        return Guarant::where('guarant_id', $row->guarant_id)->first();
    }

    public function relLoanGuarantors(): HasMany
    {
        return $this->hasMany(LoanGuarantActual::class, 'loan_id', 'loan_id')
            ->where('active', '1')
            ->orderBy('seq_num');
    }

    public function loanGuarants()
    {
        $guarantsIds = LoanGuarantActual::where('loan_id', $this->loan_id)
            ->where('active', '1')
            ->where('deleted', '0')
            ->whereNull('deleted_at')
            ->orderBy('seq_num')
            ->pluck('guarant_id')
            ->toArray();

        if (empty($guarantsIds)) {
            return null;
        }

        return Guarant::whereIn('guarant_id', $guarantsIds)->get();
    }

    public function hasStatus(int $statusId): bool
    {
        return $this->loan_status_id === $statusId;
    }

    public function getRandomHexColor(int $index = 0): string
    {
        $colors = [
            '#A18594',
            '#E6D690',
            '#C1876B',
            '#7F7679',
            '#3E3B32',
            '#316650',
            '#26252D',
            '#5D9B9B',
            '#75151E',
        ];

        return $colors[$index] ?? '#A18594';
    }

    public function canCancelLoan(): bool
    {
        // can not cancel alredy activated loan
        if ($this->isActive()) {
            return false;
        }

        // for online office can not cancel already approved loan
        if ($this->isOnlineLoan() && $this->isApproved()) {
            return false;
        }

        return true;
    }

    public function addCreditLimitApproveTask(): bool
    {
        return $this->addApproveTask(self::APPROVE_TASK_CHA);
    }

    public function addApproveTask(string $taskName): bool
    {
        $tasks = (array) $this->approve_tasks;

        if (in_array($taskName, $tasks)) {
            return false;
        }

        $tasks[] = $taskName;

        $this->approve_tasks = $tasks;
        $this->save();

        return true;
    }

    public function removeManualProcessApproveTask(): bool
    {
        return $this->removeApproveTask(self::APPROVE_TASK_MNA);
    }

    public function removeApproveTask(string $taskName): bool
    {
        $tasks = (array) $this->approve_tasks;

        if (!in_array($taskName, $tasks)) {
            return false;
        }

        $this->approve_tasks = array_diff($tasks, [$taskName]);
        $this->save();

        return true;
    }

    public function scopeOfMyOffices(Builder $query)
    {
        // $officeIds = getAdmin()->offices->pluck('office_id');
        $query->whereIn('loan.office_id', getAdminOfficeIds());
    }

    public function getTimeBeforeSignAndProcessing(): ?int
    {
        // loan should be in processing state
        if (!$this->isProcessing()) {
            return null;
        }


        // time when agent processed
        $processedAt = Carbon::parse($this->last_status_update_date); // Carbon obj

        // time when loan become signed
        $signedAt = $this->getLastHistoryForStatus(LoanStatus::SIGNED_STATUS_ID)?->date; // Carbon obj
        //// todo ask Roman
        if (!$signedAt) {
            $signedAt = Carbon::now();
        } else {
            $signedAt = Carbon::parse($signedAt);
        }


        // get working time, for now it's just string in format: HH:MM - HH:MM
        $setting = OfficeSetting::where([
            ['office_id', '=', $this->office_id],
            ['setting_key', '=', SettingsEnum::work_time_office->value],
        ])->first();

        // if no office working hours setting, just return difference
        if (empty($setting->value)) {
            return $processedAt->diffInSeconds($signedAt);
        }


        // parse string, extract from/to
        preg_match(
            '/([0-9]{1,2}:[0-9]{1,2}) - ([0-9]{1,2}:[0-9]{1,2})/',
            $setting->value,
            $match
        );

        // if could not parse setting, just return difference
        if (empty($match[1]) || empty($match[2])) {
            return $processedAt->diffInSeconds($signedAt);
        }


        $fromPoint = Carbon::parse($signedAt->format('Y-m-d') . ' ' . $match[1]);
        $toPoint = Carbon::parse($signedAt->format('Y-m-d') . ' ' . $match[2]);

        // if loan is signed during working time
        if ($signedAt->between($fromPoint, $toPoint)) {
            return $processedAt->diffInSeconds($signedAt);
        }


        // part where we substract non-working hours from total diff
        $secondsToRemove = 0;
        $secondsToRemove2 = 0;

        // if loan is signed before working time
        if ($signedAt->lt($fromPoint)) {
            $secondsToRemove = $fromPoint->diffInSeconds($signedAt);
        }

        // if loan is signed after working time
        if ($signedAt->gt($toPoint)) {
            // time before end working time and sign time (Example: 21:50)
            $secondsToRemove = $signedAt->diffInSeconds($toPoint);

            // time after signin and next day start of working time
            $tomorrowFromPoint = $fromPoint->addDay();
            $secondsToRemove2 = $tomorrowFromPoint->diffInSeconds($signedAt);
        }

        // default difference
        $diff = $processedAt->diffInSeconds($signedAt);

        return ($diff - $secondsToRemove - $secondsToRemove2);
    }

    public function paymentMethodEnum(): PaymentMethodEnum
    {
        return PaymentMethodEnum::fromId($this->payment_method_id);
    }

    public function wasFullyRepaid(bool $earlyRepayment = false): bool
    {
        return $earlyRepayment
            ? $this->getEarlyRepaymentDebtDb() <= 0
            : $this->getRegularRepaymentDebtDb() <= 0;
    }

    public function getTriMurti(int $extendWithDays = 30): array
    {
        return [
            'early' => $this->getEarlyRepaymentDebtDb(),
            'regular' => $this->getRegularRepaymentDebtDb(),
            'extension' => $this->getExtendLoanFeeAmountDb($extendWithDays),
            'extension15' => $this->getExtendLoanFeeAmountDb(15),
        ];
    }

    public function getEarlyRepaymentDebtDb(): int
    {
        $result = DB::selectOne(
            "
            select
                (
                    select coalesce(sum(t.rest_amount),0) AS sum_tax
                    from tax t
                    where
                        t.loan_id = " . $this->loan_id . "
                        and t.paid = 0
                        and t.rest_amount > 0
                        and t.deleted = 0
                ) + (
                    select coalesce(
                        (round(
                            sum(i.rest_principal + i.rest_interest + i.rest_penalty + i.rest_late_interest + i.rest_late_penalty),2) * 100)::INTEGER,
                            0
                        ) AS sum_inst_late
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                        and due_date <= current_date
                ) + (
                    SELECT coalesce(
                            (round(
                                sum(i.rest_principal + i.rest_late_interest + i.rest_late_penalty), 2
                            ) * 100)::INTEGER + (
                                CASE
                                    WHEN sum(i.accrued_interest) > sum(i.paid_interest) THEN sum(i.accrued_interest) - sum(i.paid_interest)
                                    ELSE 0
                                END
                            )*100::integer + (
                                CASE
                                    WHEN sum(i.accrued_penalty) > sum(i.paid_penalty) THEN sum(i.accrued_penalty) - sum(i.paid_penalty)
                                    ELSE 0
                                END
                            )*100::integer,
                            0
                        ) AS sum_inst_accrued
                    FROM installment i
                    WHERE
                        i.loan_id = " . $this->loan_id . "
                        AND i.paid = 0
                        AND due_date > current_date
                ) as total_amount
        "
        );

        return $result->total_amount ?? 0;
    }

    public function getEarlyRepaymentDebtDbSeparated(): array
    {
        $result = DB::selectOne(
            "
            select
                (
                    select coalesce(sum(t.rest_amount),0) AS sum_tax
                    from tax t
                    where
                        t.loan_id = " . $this->loan_id . "
                        and t.paid = 0
                        and t.rest_amount > 0
                        and t.deleted = 0
                ) as rest_taxes,
                (
                    select coalesce((round(sum(i.rest_late_interest),2) * 100)::INTEGER, 0)
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_late_interest,
                (
                    select coalesce((round(sum(i.rest_late_penalty),2) * 100)::INTEGER, 0)
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_late_penalty,
                (
                    select coalesce((round(sum(i.rest_principal),2) * 100)::INTEGER, 0)
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_principal,
                (
                    select coalesce(
                        (round(
                            sum(i.rest_interest),2) * 100)::INTEGER,
                            0
                        )
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                        and due_date <= current_date
                ) as rest_interest,
                (
                    select coalesce(
                        (round(
                            sum(i.rest_penalty),2) * 100)::INTEGER,
                            0
                        )
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                        and due_date <= current_date
                ) as rest_penalty,
                (
                    SELECT coalesce(
                            (
                                CASE
                                    WHEN sum(i.accrued_interest) > sum(i.paid_interest) THEN sum(i.accrued_interest) - sum(i.paid_interest)
                                    ELSE 0
                                END
                            )*100::integer,
                            0
                        )
                    FROM installment i
                    WHERE
                        i.loan_id = " . $this->loan_id . "
                        AND i.paid = 0
                        AND due_date > current_date
                ) as rest_accrued_interest,
                (
                    SELECT coalesce(
                            (
                                CASE
                                    WHEN sum(i.accrued_penalty) > sum(i.paid_penalty) THEN sum(i.accrued_penalty) - sum(i.paid_penalty)
                                    ELSE 0
                                END
                            )*100::integer,
                            0
                        )
                    FROM installment i
                    WHERE
                        i.loan_id = " . $this->loan_id . "
                        AND i.paid = 0
                        AND due_date > current_date
                ) as rest_accrued_penalty
        "
        );

        return [
            'rest_taxes' => (int) $result->rest_taxes,
            'rest_principal' => (int) $result->rest_principal,
            'rest_interest' => (int) $result->rest_interest,
            'rest_penalty' => (int) $result->rest_penalty,
            'rest_accrued_interest' => (int) $result->rest_accrued_interest,
            'rest_accrued_penalty' => (int) $result->rest_accrued_penalty,
            'rest_late_interest' => (int) $result->rest_late_interest,
            'rest_late_penalty' => (int) $result->rest_late_penalty,
        ];
    }

    public function getRegularRepaymentDebtDb(): int
    {
        $result = DB::selectOne(
            "
            select
            (
                select coalesce(sum(t.rest_amount),0) AS sum_tax
                from tax t
                where
                    t.loan_id = " . $this->loan_id . "
                    and t.paid = 0
                    and t.rest_amount > 0
                    and t.deleted = 0
            ) + (
                select coalesce(
                        (round(sum(i.rest_principal + i.rest_interest + i.rest_penalty + i.rest_late_interest + i.rest_late_penalty),2) * 100)::INTEGER,
                        0
                    ) AS sum_inst
                from installment i
                where
                    i.loan_id = " . $this->loan_id . "
                    and i.paid = 0
            ) as total_amount
        "
        );

        return $result->total_amount ?? 0;
    }

    public function getRegularRepaymentDebtDbSeparated(): array
    {
        $result = DB::selectOne(
            "
            select
                (
                    select coalesce(sum(t.rest_amount),0) AS sum_tax
                    from tax t
                    where
                        t.loan_id = " . $this->loan_id . "
                        and t.paid = 0
                        and t.rest_amount > 0
                        and t.deleted = 0
                ) as rest_taxes,
                (
                    select coalesce((round(sum(i.rest_late_interest),2) * 100)::INTEGER, 0)
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_late_interest,
                (
                    select coalesce((round(sum(i.rest_late_penalty),2) * 100)::INTEGER, 0)
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_late_penalty,
                (
                    select coalesce((round(sum(i.rest_principal),2) * 100)::INTEGER, 0)
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_principal,
                (
                    select coalesce(
                        (round(
                            sum(i.rest_interest),2) * 100)::INTEGER,
                            0
                        )
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_interest,
                (
                    select coalesce(
                        (round(
                            sum(i.rest_penalty),2) * 100)::INTEGER,
                            0
                        )
                    from installment i
                    where
                        i.loan_id = " . $this->loan_id . "
                        and i.paid = 0
                ) as rest_penalty,
                '0' as rest_accrued_interest,
                '0' as rest_accrued_penalty
        "
        );

        return [
            'rest_taxes' => (int) $result->rest_taxes,
            'rest_principal' => (int) $result->rest_principal,
            'rest_interest' => (int) $result->rest_interest,
            'rest_penalty' => (int) $result->rest_penalty,
            'rest_accrued_interest' => (int) $result->rest_accrued_interest,
            'rest_accrued_penalty' => (int) $result->rest_accrued_penalty,
            'rest_late_interest' => (int) $result->rest_late_interest,
            'rest_late_penalty' => (int) $result->rest_late_penalty,
        ];
    }

    public function getTotalUnpaidTaxAmount(): int
    {
        $result = DB::selectOne(
            "
            select coalesce(sum(t.rest_amount),0) AS sum_tax
            from tax t
            where
                t.loan_id = " . $this->loan_id . "
                and t.paid = 0
                and t.amount > 0
                and t.rest_amount > 0
                and t.deleted = 0
        "
        );

        return $result->sum_tax ?? 0;
    }

    // used in LoanStats
    public function getDueAmounts(): array
    {
        $result = DB::selectOne(
            "
            select
                coalesce(sum(i.principal), 0) as prinicpal,
                coalesce(sum(i.interest), 0) as interest,
                coalesce(sum(i.penalty), 0) as penalty,
                coalesce(sum(i.late_interest), 0) as late_interest,
                coalesce(sum(i.late_penalty), 0) as late_penalty,
                (
                    select coalesce(sum(t.amount),0)
                    from tax t
                    where
                        t.loan_id = " . $this->loan_id . "
                        and t.amount > 0
                ) as taxes
            from installment i
            where i.loan_id = " . $this->loan_id . "
        "
        );

        $vars = [
            'due_amount_total_principal' => $result->prinicpal ?? 0,
            'due_amount_total_interest' => $result->interest ?? 0,
            'due_amount_total_penalty' => $result->penalty ?? 0,
            'due_amount_total_late_interest' => $result->late_interest ?? 0,
            'due_amount_total_late_penalty' => $result->late_penalty ?? 0,
            'due_amount_total_taxes' => (!empty($result->taxes) ? (float) intToFloat($result->taxes) : 0),
        ];
        $vars['due_amount_total'] = array_sum($vars);

        return $vars;
    }

    // used in CCR
    public function getOverdueAmounts()
    {
        $result = DB::selectOne(
            "
            select
                coalesce(sum(i.rest_principal), 0) as overdue_principal_amount,
                coalesce(sum(i.rest_interest), 0) as overdue_interest_amount,
                coalesce(sum(i.rest_penalty), 0) as overdue_penalty_amount,
                coalesce(sum(i.rest_late_interest), 0) as overdue_late_interest_amount,
                coalesce(sum(i.rest_late_penalty), 0) as overdue_late_penalty_amount,
                (
                    select coalesce(sum(t.rest_amount),0)::numeric/100
                    from tax t
                    where
                        t.loan_id = " . $this->loan_id . "
                        and t.rest_amount > 0
                ) as overdue_tax_amount
            from installment i
            where
                i.loan_id = " . $this->loan_id . "
                and i.due_date::DATE < current_date
        "
        );

        return $result;
    }

    public function getTotalRestPrincipal(): int
    {
        $result = DB::selectOne(
            "
            select coalesce((round(sum(i.rest_principal),2) * 100)::INTEGER, 0) as rest_principal
            from installment i
            where
                i.loan_id = " . $this->loan_id . "
                and i.paid = 0
        "
        );

        return (int) $result->rest_principal;
    }

    public static function getTotalOverdueAmount(int $loanId): float
    {
        $result = DB::selectOne(
            "
            select
                ROUND(
                    (
                        coalesce(sum(i.rest_principal), 0)
                        + coalesce(sum(i.rest_interest), 0)
                        + coalesce(sum(i.rest_penalty), 0)
                        + coalesce(sum(i.rest_late_interest), 0)
                        + coalesce(sum(i.rest_late_penalty), 0)
                        + (
                            select coalesce(sum(t.rest_amount),0)
                            from tax t
                            where
                                t.loan_id = " . $loanId . "
                                and t.rest_amount > 0
                        )
                    ), 2
                ) as current_overdue_amount
            from installment i
            where
                i.loan_id = " . $loanId . "
                and i.due_date::DATE < current_date
        "
        );

        return $result->current_overdue_amount ?? 0;
    }

    public static function getCombinedAmountsForLoanIdSql(int $loanId): array
    {
        $result = (array) DB::selectOne(
            "
            with
                installment_accrued AS (
                    select
                        i.loan_id,
                        coalesce(sum(i.rest_principal), 0) as accrued_amount_principal,
                        coalesce(sum(i.rest_interest), 0) as accrued_amount_interest,
                        coalesce(sum(i.rest_penalty), 0) as accrued_amount_penalty,
                        coalesce(sum(i.rest_late_interest), 0) as accrued_amount_late_interest,
                        coalesce(sum(i.rest_late_penalty), 0) as accrued_amount_late_penalty
                    from installment i
                    where
                        i.loan_id = " . $loanId . "
                        and i.due_date::DATE <= current_date
                    group by i.loan_id
                ), installment_overdue AS (
                    select
                        i.loan_id,
                        (
                            coalesce(sum(i.rest_principal), 0)
                            + coalesce(sum(i.rest_interest), 0)
                            + coalesce(sum(i.rest_penalty), 0)
                            + coalesce(sum(i.rest_late_interest), 0)
                            +coalesce(sum(i.rest_late_penalty), 0)
                        ) as overdue_amount
                    from installment i
                    where
                        i.loan_id = " . $loanId . "
                        and i.due_date::DATE < current_date
                    group by i.loan_id
                ), installment_overdue_days AS (
                    select
                        i.loan_id,
                        max(i.overdue_days) as overdue_days
                    from installment i
                    where
                        i.loan_id = " . $loanId . "
                    group by i.loan_id
                ), taxes_totals AS (
                    select
                        t.loan_id,
                        ROUND(coalesce(sum(t.amount), 0)::numeric / 100, 2) as due_amount_total_taxes,
                        ROUND(coalesce(sum(t.paid_amount), 0)::numeric / 100, 2) as repaid_amount_taxes,
                        ROUND(coalesce(sum(t.rest_amount), 0)::numeric / 100, 2) as outstanding_amount_taxes
                    from tax t
                    where t.loan_id = " . $loanId . "
                    group by t.loan_id
                )
                select
                    it.*,
                    ia.*,
                    tt.*,
                    (it.due_amount_total_principal + it.due_amount_total_interest + it.due_amount_total_penalty + it.due_amount_total_late_interest + it.due_amount_total_late_penalty + tt.due_amount_total_taxes) as due_amount_total,
                    (it.repaid_amount_principal + it.repaid_amount_interest + it.repaid_amount_penalty + it.repaid_amount_late_interest + it.repaid_amount_late_penalty + tt.repaid_amount_taxes) as repaid_amount_total,
                    (it.outstanding_amount_principal + it.outstanding_amount_interest + it.outstanding_amount_penalty + it.outstanding_amount_late_interest + it.outstanding_amount_late_penalty) as outstanding_amount_total_no_taxes,
                    (it.outstanding_amount_principal + it.outstanding_amount_interest + it.outstanding_amount_penalty + it.outstanding_amount_late_interest + it.outstanding_amount_late_penalty + tt.outstanding_amount_taxes) as outstanding_amount_total,
                    (ia.accrued_amount_principal + ia.accrued_amount_interest + ia.accrued_amount_penalty + ia.accrued_amount_late_interest + ia.accrued_amount_late_penalty + tt.outstanding_amount_taxes) as accrued_amount_total,
                    (io.overdue_amount + tt.outstanding_amount_taxes) as current_overdue_amount,
                    iod.overdue_days as current_overdue_days
                from (
                    select
                        i.loan_id,
                        coalesce(sum(i.principal), 0) due_amount_total_principal,
                        coalesce(sum(i.interest), 0) as due_amount_total_interest,
                        coalesce(sum(i.penalty), 0) as due_amount_total_penalty,
                        coalesce(sum(i.late_interest), 0) as due_amount_total_late_interest,
                        coalesce(sum(i.late_penalty), 0) as due_amount_total_late_penalty,
                        coalesce(sum(i.paid_principal), 0) as repaid_amount_principal,
                        coalesce(sum(i.paid_interest), 0) as repaid_amount_interest,
                        coalesce(sum(i.paid_penalty), 0) as repaid_amount_penalty,
                        coalesce(sum(i.paid_late_interest), 0) as repaid_amount_late_interest,
                        coalesce(sum(i.paid_late_penalty), 0) as repaid_amount_late_penalty,
                        coalesce(sum(i.rest_principal), 0) as outstanding_amount_principal,
                        coalesce(sum(i.rest_interest), 0) as outstanding_amount_interest,
                        coalesce(sum(i.rest_penalty), 0) as outstanding_amount_penalty,
                        coalesce(sum(i.rest_late_interest), 0) as outstanding_amount_late_interest,
                        coalesce(sum(i.rest_late_penalty), 0) as outstanding_amount_late_penalty
                    from installment i
                    where i.loan_id = " . $loanId . "
                    group by i.loan_id
                ) as it
                left join installment_accrued ia on ia.loan_id = it.loan_id
                left join installment_overdue io on io.loan_id = it.loan_id
                left join installment_overdue_days iod on iod.loan_id = it.loan_id
                left join taxes_totals tt on tt.loan_id = it.loan_id
        "
        );

        unset($result['loan_id']);

        return $result;
    }

    public static function getCombinedAmountsForLoanId(int $loanId, bool $returnInts = false): array
    {
        $rows = DB::select(DB::raw("
            select i.*
            from installment i
            where i.loan_id = " . $loanId . "
            order by i.seq_num
        "));

        $result = [
            'current_overdue_days' => 0,
            'current_overdue_amount' => 0,
            'due_amount_total_principal' => 0,
            'due_amount_total_interest' => 0,
            'due_amount_total_penalty' => 0,
            'due_amount_total_late_interest' => 0,
            'due_amount_total_late_penalty' => 0,
            'due_amount_total_taxes' => 0,
            'due_amount_total' => 0,
            'repaid_amount_principal' => 0,
            'repaid_amount_interest' => 0,
            'repaid_amount_penalty' => 0,
            'repaid_amount_late_interest' => 0,
            'repaid_amount_late_penalty' => 0,
            'repaid_amount_taxes' => 0,
            'repaid_amount_total' => 0,
            'outstanding_amount_principal' => 0,
            'outstanding_amount_interest' => 0,
            'outstanding_amount_penalty' => 0,
            'outstanding_amount_late_interest' => 0,
            'outstanding_amount_late_penalty' => 0,
            'outstanding_amount_taxes' => 0,
            'outstanding_amount_total' => 0,
            'outstanding_amount_total_no_taxes' => 0,
            'accrued_amount_principal' => 0,
            'accrued_amount_interest' => 0,
            'accrued_amount_penalty' => 0,
            'accrued_amount_late_interest' => 0,
            'accrued_amount_late_penalty' => 0,
            'accrued_amount_taxes' => 0,
            'accrued_amount_total' => 0,
        ];
        $overdue = [
            'accrued_amount_principal' => 0,
            'accrued_amount_interest' => 0,
            'accrued_amount_penalty' => 0,
            'accrued_amount_late_interest' => 0,
            'accrued_amount_late_penalty' => 0,
            'accrued_amount_taxes' => 0,
        ];

        $today = Carbon::today()->startOfDay();
        $currentOverdueDays = 0;

        foreach ($rows as $row) {
            $overDays = (int) $row->overdue_days;
            if ($overDays > $currentOverdueDays) {
                $currentOverdueDays = $overDays;
            }

            $principal = floatToInt($row->principal);
            $interest = floatToInt($row->interest);
            $penalty = floatToInt($row->penalty);
            $late_interest = floatToInt($row->late_interest);
            $late_penalty = floatToInt($row->late_penalty);
            $paid_principal = floatToInt($row->paid_principal);
            $paid_interest = floatToInt($row->paid_interest);
            $paid_penalty = floatToInt($row->paid_penalty);
            $paid_late_interest = floatToInt($row->paid_late_interest);
            $paid_late_penalty = floatToInt($row->paid_late_penalty);
            $rest_principal = floatToInt($row->rest_principal);
            $rest_interest = floatToInt($row->rest_interest);
            $rest_penalty = floatToInt($row->rest_penalty);
            $rest_late_interest = floatToInt($row->rest_late_interest);
            $rest_late_penalty = floatToInt($row->rest_late_penalty);

            $result['due_amount_total_principal'] += $principal;
            $result['due_amount_total_interest'] += $interest;
            $result['due_amount_total_penalty'] += $penalty;
            $result['due_amount_total_late_interest'] += $late_interest;
            $result['due_amount_total_late_penalty'] += $late_penalty;

            $result['repaid_amount_principal'] += $paid_principal;
            $result['repaid_amount_interest'] += $paid_interest;
            $result['repaid_amount_penalty'] += $paid_penalty;
            $result['repaid_amount_late_interest'] += $paid_late_interest;
            $result['repaid_amount_late_penalty'] += $paid_late_penalty;

            $result['outstanding_amount_principal'] += $rest_principal;
            $result['outstanding_amount_interest'] += $rest_interest;
            $result['outstanding_amount_penalty'] += $rest_penalty;
            $result['outstanding_amount_late_interest'] += $rest_late_interest;
            $result['outstanding_amount_late_penalty'] += $rest_late_penalty;

            $dueDate = Carbon::parse($row->due_date);
            if ($today->gt($dueDate)) {
                $overdue['accrued_amount_principal'] += $rest_principal;
                $overdue['accrued_amount_interest'] += $rest_interest;
                $overdue['accrued_amount_penalty'] += $rest_penalty;
                $overdue['accrued_amount_late_interest'] += $rest_late_interest;
                $overdue['accrued_amount_late_penalty'] += $rest_late_penalty;
            }
            if ($today->gte($dueDate)) {
                $result['accrued_amount_principal'] += $rest_principal;
                $result['accrued_amount_interest'] += $rest_interest;
                $result['accrued_amount_penalty'] += $rest_penalty;
                $result['accrued_amount_late_interest'] += $rest_late_interest;
                $result['accrued_amount_late_penalty'] += $rest_late_penalty;
            }
        }

        $rowsTaxes = DB::select(DB::raw("
            select i.*
            from tax i
            where i.loan_id = " . $loanId . " and deleted = 0
            order by i.tax_id
        "));

        foreach ($rowsTaxes as $rowTax) {
            $amount = (int) $rowTax->amount;
            $paid_amount = (int) $rowTax->paid_amount;
            $rest_amount = (int) $rowTax->rest_amount;

            $result['due_amount_total_taxes'] += $amount;
            $result['repaid_amount_taxes'] += $paid_amount;
            $result['outstanding_amount_taxes'] += $rest_amount;
        }

        $result['accrued_amount_taxes'] = $result['outstanding_amount_taxes'];
        $overdue['accrued_amount_taxes'] = $result['outstanding_amount_taxes'];


        // totals:
        $result['due_amount_total'] = (
            $result['due_amount_total_principal']
            + $result['due_amount_total_interest']
            + $result['due_amount_total_penalty']
            + $result['due_amount_total_late_interest']
            + $result['due_amount_total_late_penalty']
            + $result['due_amount_total_taxes']
        );
        $result['repaid_amount_total'] = (
            $result['repaid_amount_principal']
            + $result['repaid_amount_interest']
            + $result['repaid_amount_penalty']
            + $result['repaid_amount_late_interest']
            + $result['repaid_amount_late_penalty']
            + $result['repaid_amount_taxes']
        );
        $result['outstanding_amount_total_no_taxes'] = (
            $result['outstanding_amount_principal']
            + $result['outstanding_amount_interest']
            + $result['outstanding_amount_penalty']
            + $result['outstanding_amount_late_interest']
            + $result['outstanding_amount_late_penalty']
        );
        $result['outstanding_amount_total'] = (
            $result['outstanding_amount_total_no_taxes']
            + $result['outstanding_amount_taxes']
        );
        $result['accrued_amount_total'] = (
            $result['accrued_amount_principal']
            + $result['accrued_amount_interest']
            + $result['accrued_amount_penalty']
            + $result['accrued_amount_late_interest']
            + $result['accrued_amount_late_penalty']
            + $result['accrued_amount_taxes']
        );
        $result['current_overdue_amount'] = (
            $overdue['accrued_amount_principal']
            + $overdue['accrued_amount_interest']
            + $overdue['accrued_amount_penalty']
            + $overdue['accrued_amount_late_interest']
            + $overdue['accrued_amount_late_penalty']
            + $overdue['accrued_amount_taxes']
        );

        if ($returnInts) {
            $result['current_overdue_days'] = $currentOverdueDays;

            return $result;
        }


        $result = array_map('intToFloat', $result);
        $result['current_overdue_days'] = $currentOverdueDays;

        return $result;
    }

    public function getCartonDb(): array
    {
        return self::getCombinedAmountsForLoanId($this->loan_id, true);
    }

    public function getExtendLoanFeeAmountDb(int $extendWithDays = 30): int
    {
        $outstandingPrinciple = $this->getTotalOutstandingPrincipleDb();

        //// get from settings
        $extendLoanFeePercent = app(ProductService::class)->getProductSettingByKey(
            $this->product,
            ProductSetting::EXTEND_LOAN_FEE_PERCENT_KEY
        );

        /// set default
        $totalYearlyPercent = array_sum([$this->interest_percent, $this->penalty_percent]);

        /// if we have value in settings more than 0 use value form settings
        if (intval($extendLoanFeePercent) > 0) {
            $totalYearlyPercent = intval($extendLoanFeePercent);
        }

        return Calculator::round((($totalYearlyPercent / 360) / 100) * $extendWithDays * $outstandingPrinciple);
    }

    public function getTotalOutstandingPrincipleDb(): int
    {
        $result = DB::selectOne(
            "
            select coalesce(
                    (round(sum(i.rest_principal), 2) * 100)::INTEGER,
                    0
                ) AS total_amount
            from installment i
            where
                i.loan_id = " . $this->loan_id . "
                and i.paid = 0
        "
        );

        return $result->total_amount ?? 0;
    }

    public function getIncomingRefinancingPayment(): ?Payment
    {
        if (!$this->refinanced) {
            throw new \Exception('loan ' . $this->getKey() . ' was not refinanced');
        }

        return app(PaymentRepository::class)
            ->getIncomingRefinance(
                $this->refinanced->refinancing_loan_id,
                $this->getKey(),
            );
    }

    public function getOutgoingRefinancingPayment(): ?Payment
    {
        if (!$this->refinanced) {
            throw new \Exception('loan ' . $this->getKey() . ' was not refinanced');
        }

        return app(PaymentRepository::class)
            ->getOutgoingRefinance(
                $this->refinanced->refinancing_loan_id,
                $this->getKey(),
            );
    }

    public function consultant(): BelongsTo
    {
        return $this->belongsTo(Consultant::class, 'consultant_id', 'consultant_id');
    }

    public function getConsultantName(): string
    {
        if (empty($this->consultant_id)) {
            return '';
        }

        $obj = Consultant::where('consultant_id', $this->consultant_id)->first();
        if (empty($obj->name)) {
            return '';
        }

        return $obj->name;
    }

    public function isCash()
    {
        return $this->paymentMethodEnum() === PaymentMethodEnum::CASH;
    }

    public function showUpdateLoanParams(): bool
    {
        if (!$this->office->isWeb() && $this->loan_status_id === LoanStatus::APPROVED_STATUS_ID) {
            return false;
        }

        return true;
    }

    public function getAmountToPay(): int
    {
        $loanCartonDb = $this->getCartonDb();

        $totalAccruedAmount = (
        !empty($loanCartonDb['accrued_amount_total'])
            ? $loanCartonDb['accrued_amount_total']
            : 0
        );

        if (!empty($totalAccruedAmount)) {
            return $totalAccruedAmount;
        }

        $installments = $this->getUnpaidInstallments();
        $installment = $installments->first();

        return floatToInt($installment->getPrimaryTotalRestAmount());
    }

    public function repaymentDate(): Carbon
    {
        return Carbon::parse($this->installments->last()->due_date);
    }

    public function getPreviousLoans()
    {
        $date = $this->created_at;
        if (empty($date)) {
            $date = now(); // on creation we dont have such date
        }

        return Loan::where('created_at', '<', $date)
            ->where('client_id', $this->client_id)
            ->whereIn('loan_status_id', LoanStatus::ACTIVE_STATUSES)
            ->orderBy('created_at')
            ->get();
    }

    public function getPreviousLoansCount(): int
    {
        $date = $this->created_at;
        if (empty($date)) {
            $date = now(); // on creation we dont have such date
        }

        return Loan::where('created_at', '<', $date)
            ->where('client_id', $this->client_id)
            ->whereIn('loan_status_id', LoanStatus::ACTIVE_STATUSES)
            ->count();
    }

    public function getPreviousApplications()
    {
        return Loan::where('created_at', '<', $this->created_at)
            ->where('client_id', $this->client_id)
            ->get();
    }

    public function getFirstPayment()
    {
        return Payment::where('loan_id', $this->loan_id)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->where('direction', PaymentDirectionEnum::IN)
            ->where('active', 1)
            ->orderBy('created_at', 'ASC')
            ->first();
    }

    public function getLastPayment()
    {
        return Payment::where('loan_id', $this->loan_id)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->where('direction', PaymentDirectionEnum::IN)
            ->where('active', 1)
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    public function getAllPayments(array $with = [], string $direction = 'DESC')
    {
        $builder = Payment::where('loan_id', $this->loan_id);
        if (!empty($with)) {
            $builder->with($with);
        }

        return $builder
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->where('active', 1)
            ->orderBy('created_at', $direction)
            ->get();
    }

    public function getInPayments(string $direction = 'ASC')
    {
        return Payment::where('loan_id', $this->loan_id)
            ->where('direction', Payment::PAYMENT_DIRECTION_IN)
            ->where('status', PaymentStatusEnum::DELIVERED)
            ->where('active', 1)
            ->orderBy('created_at', $direction)
            ->get();
    }

    public function getPaymentsByPeriods()
    {
        return $results = DB::selectOne(
            DB::raw(
                "
            select
                l.loan_id,
                l.created_at,
                (
                    select coalesce(sum(p.amount), 0)::numeric/100
                    from payment p
                    where
                        p.loan_id = l.loan_id
                        and p.direction = 'in'
                        and p.status = 'delivered'
                        and p.deleted_at is NULL
                        and p.created_at::DATE >= (NOW()::DATE - INTERVAL '180 days')
                ) as paid_180,
                (
                    select coalesce(sum(p.amount), 0)::numeric/100
                    from payment p
                    where
                        p.loan_id = l.loan_id
                        and p.direction = 'in'
                        and p.status = 'delivered'
                        and p.deleted_at is NULL
                        and p.created_at::DATE >= (NOW()::DATE - INTERVAL '365 days')
                ) as paid_365,
                (
                    select coalesce(sum(p.amount), 0)::numeric/100
                    from payment p
                    where
                        p.loan_id = l.loan_id
                        and p.direction = 'in'
                        and p.status = 'delivered'
                        and p.deleted_at is NULL
                        and p.created_at::DATE >= (NOW()::DATE - INTERVAL '730 days')
                ) as paid_730,
                (
                    select coalesce(sum(p.amount), 0)::numeric/100
                    from payment p
                    where
                        p.loan_id = l.loan_id
                        and p.direction = 'in'
                        and p.status = 'delivered'
                        and p.deleted_at is NULL
                ) as total_paid
            from loan l
            where l.loan_id = " . $this->loan_id . "
        "
            )
        );
    }

    public function getLastCommunicationComment(): string
    {
        $r = CommunicationComment::where('loan_id', $this->loan_id)
            ->orderBy('communication_comment_id', 'desc')
            ->limit(1)
            ->first();

        if (!empty($r->text)) {
            return $r->text;
        }

        return '';
    }

    // Only for specific clients
    // Always go to manual approve
    // No credit limit
    public function isCustomProduct(): bool
    {
        $customProduct = ClientProduct::where('client_id', $this->client_id)
            ->where('product_id', $this->product_id)
            ->where('active', 1)
            ->first();

        if (!empty($customProduct->id)) {
            return true;
        }

        return false;
    }

    public function shouldSkipAccountingPaymentCreation(): bool
    {
        $office = $this->office;

        if (empty($office->office_id)) {
            return false;
        }

        if ((bool) $office->skip_accounting === true) {
            return true;
        }

        return false;
    }

    public function getLastAutoProcessRow(): ?AutoProcess
    {
        return AutoProcess::where('loan_id', $this->loan_id)
            ->where('active', 1)
            ->orderBy('auto_process_id', 'DESC')
            ->first();
    }

    public function updateSkipTill(
        $value = null,
        ?string $reason = null,
    ): void {

        $valueMeta = 's_t = ' . ($value ? $value : 'NULL');
        if (!empty($reason)) {
            $valueMeta .= ', r = ' . $reason;
        }

        $this->addMeta('skip_till_update', $valueMeta, true);

        $this->skip_till = $value;
        $this->saveQuietly();
    }

    public function discountLogs(): HasMany
    {
        return  $this->hasMany(
            LoanDiscountLog::class,
            'loan_id',
            'loan_id'
        );
    }

    public function latestDiscountLog(): HasOne
    {
        return $this->hasOne(LoanDiscountLog::class, 'loan_id', 'loan_id')
            ->latestOfMany('created_at');
    }

    public function getLoanMainData(): array
    {
        $result = [
            'client_id' => $this->client_id,
            'loan_id' => $this->loan_id,
            'loan_status_id' => $this->loan_status_id,
            'product_id' => $this->product_id,
            'product_type_id' => $this->product_type_id,
            'source' => $this->source,

            'period_approved' => $this->period_approved,
            'periodLabel' => $this->product->getPeriodLabel($this->period_approved),

            'amount_requested' => intToFloat($this->amount_requested),
            'amount_approved' => intToFloat($this->amount_approved),
            'insurance_amount' => intToFloat($this->insurance_amount ?? 0),
            'insurance_amount_eur' => amountEur(intToFloat($this->insurance_amount ?? 0),''),

            'payment_method_id' => $this->payment_method_id,
            'iban' => $this->client->getMainBankAccountIban(),

            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d'),
            'activated_at' => Carbon::parse($this->activated_at)->format('Y-m-d'),
        ];

        $result['amount_requested_eur'] = amountEur($result['amount_requested'] ?? 0, '');
        $result['amount_approved_eur'] = amountEur($result['amount_approved'] ?? 0, '');

        return $result;
    }
}
