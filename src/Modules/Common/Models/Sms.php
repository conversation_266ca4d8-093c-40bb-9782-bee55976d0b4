<?php

namespace Modules\Common\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Events\CommunicationWasCreatedEvent;
use Modules\Communication\Models\SmsTemplate;

/**
 * @property int $sms_id
 * @property int|null $sms_template_id
 * @property int $administrator_id
 * @property int $client_id
 * @property int $loan_id
 * @property string|null $module
 * @property string|null $identifier
 * @property string $text
 * @property string|null $sender
 * @property string $phone
 * @property string|null $response
 * @property string|null $queue
 * @property string|null $queued_at
 * @property int $tries
 * @property string|null $sent_at
 * @property string|null $comment
 * @property bool $active
 * @property bool $deleted
 * @property \Illuminate\Support\Carbon $created_at
 * @property int|null $created_by
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property \Illuminate\Support\Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property \Illuminate\Support\Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property string|null $type
 * @property int $manual
 * @property-read Administrator $administrator
 * @property-read CustomEloquentCollection|Client[] $client
 * @property-read int|null $client_count
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read CustomEloquentCollection|Loan[] $loan
 * @property-read int|null $loan_count
 * @property-read Administrator|null $processedBy
 * @property-read SmsTemplate|null $smsTemplate
 * @property-read Administrator|null $updater
 * @mixin IdeHelperSms
 */
class Sms extends BaseModel
{
    const RL_MAX_PER_HOUR = 10;
    const RL_MAX_PER_DAY = 15;

    public const POSITIVE_RESPONSE = 'OK';
    public const NEGATIVE_RESPONSE = 'KO';

    public const SMS_TEST_PHONES = [
        '0888 888 888',
        '0777 777 777',
    ];

    public const SMS_FIRM_NAME = 'Stikcredit';
    public const SMS_FIRM_PHONE = '0888 888 888';
    public const SMS_SENDER = 'System';

    public const SMS_TYPE_SYSTEM = 'system';
    public const SMS_TYPE_MARKETING = 'marketing';

    public const SMS_DEFAULT_MODULE = 'payments';

    public const SMS_DEFAULT_VARIABLES = [
        'firmName' => 'Stikcredit',
        'firmPhone' => '070010514',
        'firmEmail' => '<EMAIL>',
        'firmWebSite' => 'https://stikcredit.bg',
        'loginPage' => 'https://stikcredit.bg/login',
        'unsubscribeLink' => 'https://stikcredit.bg',
        'btnGet' => 'https://stikcredit.bg',
        'verification_link' => 'https://stikcredit.bg/login',
        'creditInterestPerYear' => '10',
        'GPR' => '11',
    ];

    protected $table = 'sms';

    protected $primaryKey = 'sms_id';

    protected $dispatchesEvents = [
        'created' => CommunicationWasCreatedEvent::class
    ];

    protected $fillable = [
        'sms_template_id',
        'administrator_id',
        'client_id',
        'loan_id',
        'type',
        'module',
        'identifier',
        'text',
        'sender',
        'phone',
        'response',
        'queue',
        'queued_at',
        'tries',
        'sent_at',
        'comment',
        'manual',
        'created_at',
        'created_by',
    ];

    public static function getSmsTypes()
    {
        return SmsTemplate::templateTypes();
    }

    public function isLoginCodeSms():bool
    {
        return $this->smsTemplate->key === SmsTemplateKeyEnum::SMS_TYPE_LOGIN_CODE->value;
    }

    public function client(): BelongsToMany
    {
        return $this->belongsToMany(
            Client::class,
            'communication_pivots',
            'sms_id',
            'client_id'
        )->withPivot('loan_id');
    }

    public function loan(): BelongsToMany
    {
        return $this->belongsToMany(
            Loan::class,
            'communication_pivots',
            'loan_id',
            'communication_id',
        )->withPivot('client_id');
    }

    public function smsTemplate(): BelongsTo
    {
        return $this->belongsTo(
            SmsTemplate::class,
            'sms_template_id'
        );
    }

    public function administrator(): BelongsTo
    {
        return $this->belongsTo(
            Administrator::class,
            'administrator_id'
        );
    }

    public function rateLimit(int $clientId = null, int $loanId = null): bool
    {
        if (empty($clientId)) {
            return true;
        }


        $start = Carbon::today()->startOfDay();
        $now = Carbon::now();
        $count1 = Sms::where([
            ['phone', '=', $this->phone],
            ['created_at', '>=', $start->toDateTimeString()],
            ['created_at', '<=', $now->toDateTimeString()],
        ])->count();

        if ($count1 >= self::RL_MAX_PER_DAY) {
            $this->saveSkipLog($clientId, $loanId, 'daily_limit: ' . $count1);
            return false;
        }


        $fromHour = Carbon::now()->subHours(1);
        $where = [
            ['communication_type', '=', 'sms'],
            ['client_id', '=', $clientId],
            ['created_at', '>=', $fromHour->toDateTimeString()],
            ['created_at', '<=', $now->toDateTimeString()],
        ];
        if (!empty($loanId)) {
            $where[] = ['loan_id', '=', $loanId];
        }
        $count2 = CommunicationPivot::where($where)->count();

        if ($count2 >= self::RL_MAX_PER_HOUR) {
            $this->saveSkipLog($clientId, $loanId, 'hourly_limit: ' . $count2);
            return false;
        }


        return true;
    }

    public function saveSkipLog(
        int    $clientId,
        int    $loanId = null,
        string $reason = ''
    )
    {
        $log = new SmsSkipLog();
        $log->client_id = $clientId;
        $log->loan_id = $loanId;
        $log->sms_id = $this->getKey();
        $log->reason = $reason;
        $log->save();
    }
}
