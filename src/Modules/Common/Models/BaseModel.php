<?php

namespace Modules\Common\Models;

use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\Common\Traits\BaseModelTrait;
use Modules\Common\Traits\DatesAwareTrait;
use Modules\Common\Traits\FilterModelTrait;

abstract class BaseModel extends Model
{
    use BaseModelTrait, FilterModelTrait, SoftDeletes, DatesAwareTrait, HasFactory;

    protected $historyClass = false;

    protected $casts = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->casts = array_replace($this->traitCasts, $this->casts);
    }

    public static function getCacheKey(...$args): string
    {
        $addonArgs = implode('.', $args);

        return static::class . '.' . $addonArgs;
    }

    public function getSelectedOptions(
        string $value,
        string $key,
        bool   $translateLabels = true,
        string $order = 'DESC'
    ): array {
        $rows = $this->orderBy($this->getKeyName(), $order)
            ->get()
            ->pluck($value, $key)
            ->toArray();

        /// translate rows
        if ($translateLabels) {
            foreach ($rows as $rowKey => $row) {
                $rows[$rowKey] = __($row);
            }
        }

        return $rows;
    }

    public static function selectOptions(string $value, string $key, $builder = false): static|array
    {
        if ($builder) {
            return app()->make(static::class);
        }

        $cacheKey = static::class . '.selectOptions';

        return Cache::get($cacheKey, function () use ($value, $key, $cacheKey) {
            $options = static::all()->pluck($value, $key)->toArray();

            Cache::put($cacheKey, $options, Carbon::now()->addHours(24));

            return $options;
        });
    }

    public function getHistoryClass()
    {
        return $this->historyClass;
    }

    public function setArchivedFields()
    {
        $this->archived_at = Carbon::now();
        $this->archived_by = Auth::user()->administrator_id;
    }

    public static function getTableName(): string
    {
        /** @var static $instance */
        $instance = app()->make(static::class);

        return $instance->getTable();
    }

    public function getImportantProperties(): array
    {
        $impFields = $this->fillable;
        if (empty($impFields)) {
            return [];
        }

        $allData = $this->toArray();

        $result = [];
        foreach ($impFields as $field) {
            if (isset($allData[$field])) {
                $result[$field] = $allData[$field];
            }
        }

        return $result;
    }

    public function getImportantChangedPropertiesWithNewValues(
        bool $withOldValues
    ): array {
        $impFields = $this->fillable;
        if (empty($impFields)) {
            return [];
        }

        $changes = $this->getDirty();

        $result = [];
        foreach ($changes as $field => $value) {
            if (in_array($field, $impFields)) {
                $original = $this->getOriginal($field);
                $newValue = is_array($value) ? json_encode($value) : $value;

                if ($original == $newValue) {
                    continue;
                }

                if ($withOldValues) {
                    $result[$field] = [
                        'old' => $original,
                        'new' => $newValue,
                    ];
                } else {
                    $result[$field] = $changes[$field];
                }
            }
        }

        return $result;
    }

    public function equals(string $attributeName, mixed $value): bool
    {
        return $this->getAttribute($attributeName) === $value;
    }

    public function equalsArray(array $array): bool
    {
        foreach ($array as $attributeName => $val) {
            if (!$this->equals($attributeName, $val)) {
                return false;
            }
        }

        return true;
    }

    public function getById(int $id): ?static
    {
        return $this->where([$this->getKeyName() => $id])->first();
    }


    public function getByIdOrFail(int $id): static
    {
        return $this->where([$this->getKeyName() => $id])->firstOrFail();
    }


    public function getByUuid(string $id): ?static
    {
        return $this->where([$this->getKeyName() => $id])->first();
    }


    public function getByUuidOrFail(string $id): static
    {
        return $this->where([$this->getKeyName() => $id])->firstOrFail();
    }

    public static function get(?int $id = null): ?static
    {
        /** @var static $model */
        $model = app()->make(static::class);

        return $id ? $model->getById($id) : $model;
    }

    public static function getNextAutoIncrement(): int
    {
        /** @var static $self */
        $self = app()->make(static::class);

        if (!$self->getIncrementing()) {
            throw new Exception(sprintf('Model (%s) is not auto-incremented', static::class));
        }

        $sequenceName = $self->getTable() . "_" . $self->getKeyName() . "_seq";

        return DB::selectOne("SELECT nextval('{$sequenceName}') AS val")->val + 1;
    }
}
