<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Communication\Events\CommunicationWasCreatedEvent;
use Modules\Communication\Models\EmailStatus;
use Modules\Communication\Models\EmailTemplate;

/**
 * @property int $email_id
 * @property int|null $email_template_id
 * @property int|null $administrator_id
 * @property int $client_id
 * @property int $loan_id
 * @property string|null $module
 * @property string|null $identifier
 * @property string $sender_from
 * @property string $sender_to
 * @property string|null $sender_reply
 * @property string $title
 * @property string|null $text
 * @property string|null $body
 * @property string|null $response
 * @property string|null $queue
 * @property string|null $queued_at
 * @property int|null $tries
 * @property string|null $send_at
 * @property string|null $received_at
 * @property string|null $opened_at
 * @property int|null $has_files
 * @property string|null $comment
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property string|null $type
 * @property int $manual
 * @property-read Administrator|null $administrator
 * @property-read CustomEloquentCollection<int, Client> $client
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read EmailTemplate|null $emailTemplate
 * @property-read CustomEloquentCollection<int, File> $files
 * @property-read Administrator|null $handler
 * @property-read CustomEloquentCollection<int, Loan> $loan
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperEmail
 */
final class Email extends BaseModel
{
    const RL_MAX_PER_HOUR = 10;
    const RL_MAX_PER_DAY = 15;

    public const POSITIVE_RESPONSE = 'OK';
    public const NEGATIVE_RESPONSE = 'KO';

    public const EMAIL_TYPE_SYSTEM = 'system';
    public const EMAIL_TYPE_MARKETING = 'marketing';
    public const EMAIL_DEFAULT_VARIABLES = [
        'layout_contacts_link' => 'https://stikcredit.bg/offices/gr-sofiya',
        'layout_contacts_link_title' => 'Контакти',
        'layout_about_us_link' => 'https://stikcredit.bg/about-us',
        'layout_about_us_link_title' => 'За нас',
        'firmFacebookLink' => 'https://www.facebook.com/stikcredit.bg/',
        'layout_facebook_link' => 'https://www.facebook.com/stikcredit.bg/',
        'layout_facebook_link_title' => 'Facebook',
        'layout_home_link_title' => 'Начало',
        'logo' => 'images/icons/logo.png',
        'firmName' => 'Stikcredit',
        'firmPhone' => '070010514',
        'firmEmail' => '<EMAIL>',
        'firmWebSite' => 'https://stikcredit.bg',
        'firmIBAN' => '**********************',
        'firmEasyPay' => 'xxx-xxx',
        'loginPage' => 'https://stikcredit.bg/login',
        'unsubscribeLink' => 'https://stikcredit.bg',
        'btnGet' => 'https://stikcredit.bg',
        'verification_link' => 'https://stikcredit.bg/login',
        'creditInterestPerYear' => '10',
        'GPR' => '11',
    ];

    const EMAIL_TEST_EMAILS = [
        '<EMAIL>',
    ];

    /**
     * @var string
     */
    protected $table = 'email';

    /**
     * @var string
     */
    protected $primaryKey = 'email_id';

    protected $dispatchesEvents = [
        'created' => CommunicationWasCreatedEvent::class
    ];

    /**
     * @var string[]
     */
    protected $fillable = [
        'email_template_id',
        'client_id',
        'loan_id',
        'administrator_id',
        'type',
        'identifier',
        'sender_from',
        'sender_to',
        'sender_reply',
        'title',
        'body',
        'text',
        'response',
        'queue',
        'queued_at',
        'tries',
        'send_at',
        'received_at',
        'opened_at',
        'has_files',
        'comment',
        'manual',
        'module',
        'status_id',
        'external_id',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public static function getEmailTypes()
    {
        return EmailTemplate::templateTypes();
    }

    public function sendToClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'client_id');
    }

    public function client(): BelongsToMany
    {
        return $this->belongsToMany(
            Client::class,
            'communication_pivots',
            'client_id',
            'communication_id',
        )->withPivot('loan_id');
    }

    public function loan(): BelongsToMany
    {
        return $this->belongsToMany(
            Loan::class,
            'communication_pivots',
            'loan_id',
            'communication_id',
        )->withPivot('client_id');
    }

    public function emailFiles(): BelongsToMany
    {
        return $this->belongsToMany(
            File::class,
            'email_file',
            'email_id',
            'file_id'
        );
    }

    public function emailTemplate(): BelongsTo
    {
        return $this->belongsTo(
            EmailTemplate::class,
            'email_template_id'
        );
    }

    public function administrator(): BelongsTo
    {
        return $this->belongsTo(
            Administrator::class,
            'administrator_id'
        );
    }

    public function rateLimit(int $clientId = null, int $loanId = null): bool
    {
        if (empty($clientId)) {
            return true;
        }


        $start = Carbon::today()->startOfDay();
        $now = Carbon::now();
        $count = Email::where([
            ['sender_to', '=', $this->sender_to],
            ['created_at', '>=', $start->toDateTimeString()],
            ['created_at', '<=', $now->toDateTimeString()],
        ])->count();

        if ($count > self::RL_MAX_PER_DAY) {
            $this->saveSkipLog($clientId, $loanId, 'daily_limit: ' . $count);
            return false;
        }


        $fromHour = Carbon::now()->subHours(1);
        $where = [
            ['communication_type', '=', 'email'],
            ['client_id', '=', $clientId],
            ['created_at', '>=', $fromHour->toDateTimeString()],
            ['created_at', '<=', $now->toDateTimeString()],
        ];
        if (!empty($loanId)) {
            $where[] = ['loan_id', '=', $loanId];
        }
        $count = CommunicationPivot::where($where)->count();

        if ($count > self::RL_MAX_PER_HOUR) {
            $this->saveSkipLog($clientId, $loanId, 'hourly_limit: ' . $count);
            return false;
        }


        return true;
    }

    public function saveSkipLog(
        int    $clientId,
        int    $loanId = null,
        string $reason = ''
    )
    {
        $log = new EmailSkipLog();
        $log->client_id = $clientId;
        $log->loan_id = $loanId;
        $log->email_id = $this->getKey();
        $log->reason = $reason;
        $log->save();
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(EmailStatus::class, 'status_id', 'id');
    }
}
