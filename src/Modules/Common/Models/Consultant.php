<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * Modules\Common\Models\Consultant
 *
 * @property int $consultant_id
 * @property string $name
 * @property string $phone
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @mixin IdeHelperConsultant
 */
class Consultant extends BaseAuthModel
{
    use HasFactory;

    /**
     * @var array
     */
    protected $traitCasts = [
        'active' => 'boolean',
        'deleted' => 'boolean',
        'created_at' => 'datetime:d-m-Y H:i',
        'updated_at' => 'datetime:d-m-Y H:i',
        'deleted_at' => 'datetime:d-m-Y H:i',
        'enabled_at' => 'datetime:d-m-Y H:i',
        'disabled_at' => 'datetime:d-m-Y H:i',
        'access_start_at' => 'datetime:d-m-Y H:i',
        'access_end_at' => 'datetime:d-m-Y H:i',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
        'enabled_by' => 'integer',
        'disabled_by' => 'integer',
    ];

    protected $table = 'consultant';

    protected $primaryKey = 'consultant_id';

    protected $fillable = [
        'name',
        'phone',
        'migration_db',
        'migration_id',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function loans(): HasMany
    {
        return $this->hasMany(Loan::class, 'consultant_id', 'consultant_id');
    }

    public function activeLoans(): HasMany
    {
        return $this
            ->hasMany(Loan::class, 'consultant_id', 'consultant_id')
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
    }

    public function restPrincipal(): string
    {
        return Installment::whereIn(
            'loan_id', $this->activeLoans->pluck('loan_id')
        )
            ->where('paid', 0)
            ->sum('rest_principal');
    }

    public function officesRelation(): BelongsToMany
    {
        return $this->belongsToMany(
            Office::class,
            ConsultantOffice::getTableName(),
            'consultant_id',
            'office_id'
        )->using(ConsultantOffice::class);
    }

    /**
     * @return BelongsToMany
     */
    public function offices(): BelongsToMany
    {
        return $this
            ->officesRelation()
            ->where('active', '=', 1)
            ->where('deleted', '=', 0)
            ->orderBy(
                DB::raw(ConsultantOffice::getTableName() . ".office_id = '" . Office::OFFICE_ID_WEB . "'"),
                'desc'
            )
            ->orderBy('name');
    }

    public function getOfficeIds(): array
    {
        $ids = [];

        foreach ($this->offices()->get() as $office) {
            $ids[] = $office->office_id;
        }

        return $ids;
    }

    public static function getAll(?int $adminId = null): array
    {
        if (!empty($adminId)) {
            $officeIds = getAdminOfficeIds();
            $res = DB::select(DB::raw("
                SELECT c.consultant_id, c.name
                FROM consultant c
                join consultant_office co on co.consultant_id = c.consultant_id and co.office_id in (" . implode(',', $officeIds) . ")
                ORDER BY c.name ASC
            "));

            $result = [];
            foreach ($res as $row) {
                $result[$row->consultant_id] = $row->name;
            }

            return $result;
        }

        return Consultant::where('deleted', '0')
            ->orderBy('name', 'ASC')
            ->pluck('name', 'consultant_id')
            ->toArray();
    }
}
