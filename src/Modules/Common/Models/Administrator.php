<?php

namespace Modules\Common\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Entities\ClientCardBoxAdminOrd;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Services\StorageService;
use Modules\Common\Traits\CustomPermissionTrait;

/**
 * Modules\Common\Models\Administrator
 *
 * @property int $administrator_id
 * @property Office[]|Collection $officesRelation
 * @property Office[]|Collection $offices
 * @property Client[]|Collection $administratorClients
 * @property ClientCardBoxAdminOrd[]|Collection $clientCardBoxAdminOrd
 * @property string $first_name
 * @property string $middle_name
 * @property string $last_name
 * @property string $phone
 * @property CustomEloquentCollection<int, Email> $email
 * @property string $username
 * @property string $password
 * @property string $avatar
 * @property string|null $remember_token
 * @property Carbon|null $access_start_at
 * @property Carbon|null $access_end_at
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read CustomEloquentCollection<int, AdministratorModule> $modules
 * @property-read CustomEloquentCollection<int, Permission> $permissions
 * @property-read Administrator|null $processedBy
 * @property-read CustomEloquentCollection<int, Role> $roles
 * @property-read CustomEloquentCollection<int, Setting> $settings
 * @property-read CustomEloquentCollection<int, Sms> $sms
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PersonalAccessToken> $tokens
 * @property-read Administrator|null $updater
 * @method static CustomEloquentCollection<int, static> all($columns = ['*'])
 * @method static \Modules\Common\Database\factories\AdministratorFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|BaseModel filterBy(array $filters)
 * @mixin IdeHelperAdministrator
 */
class Administrator extends BaseAuthModel
{
    use CustomPermissionTrait, HasFactory, HasApiTokens;

    const MAX_LOGIN_ATTEMPTS_PER_DAY = 5;

    private const MIN_PRIORITY_FOR_CHANGING_ROLES = 80; // unit test user

    public const SYSTEM_ADMINISTRATOR_ID = 1; // quiet daemon
    public const DEFAULT_ADMINISTRATOR_ID = 2; // super admin
    public const DEFAULT_UNIT_TEST_USER_ID = 3; // unit test user
    public const DEFAULT_EASYPAY_USER_ID = 5;

    public const DEFAULT_MAX_REPORTS_PER_MONTH_REGULAR_ADMIN = 3; // Per client
    public const DEFAULT_MAX_REPORTS_PER_MONTH_SUPER_ADMIN = 'unlimited'; // Per client

    /**
     * @var array
     */
    protected $traitCasts = [
        'active' => 'boolean',
        'deleted' => 'boolean',
        'created_at' => 'datetime:d-m-Y H:i',
        'updated_at' => 'datetime:d-m-Y H:i',
        'deleted_at' => 'datetime:d-m-Y H:i',
        'enabled_at' => 'datetime:d-m-Y H:i',
        'disabled_at' => 'datetime:d-m-Y H:i',
        'access_start_at' => 'datetime:d-m-Y H:i',
        'access_end_at' => 'datetime:d-m-Y H:i',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
        'enabled_by' => 'integer',
        'disabled_by' => 'integer',
    ];

    /**
     * @var string
     */
    protected $table = 'administrator';

    /**
     * @var string
     */
    protected $primaryKey = 'administrator_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'username',
        'password',
        'first_name',
        'middle_name',
        'last_name',
        'phone',
        'email',
        'avatar',
        'access_start_at',
        'access_end_at',
        'migration_db',
        'migration_id',
        'remember_token',
        'active',
        'disabled_at',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    protected $hidden = [
        'remember_token',
        'password',
    ];

    public function getFullNames(): string
    {
        $template = "%s %s %s";

        return sprintf(
            $template,
            $this->first_name,
            $this->middle_name,
            $this->last_name,
        );
    }

    public function getName(): string
    {
        $template = "%s %s";

        return sprintf(
            $template,
            $this->first_name,
            $this->last_name,
        );
    }

    public function getNameAttribute()
    {
        return $this->getName();
    }

    public function clientCardBoxAdminOrd(): HasMany
    {
        return $this->hasMany(ClientCardBoxAdminOrd::class, 'administrator_id', 'administrator_id');
    }

    public function officesRelation(): BelongsToMany
    {
        return $this->belongsToMany(
            Office::class,
            AdministratorOffice::getTableName(),
            'administrator_id',
            'office_id'
        )->using(AdministratorOffice::class);
    }

    /**
     * @return BelongsToMany
     */
    public function offices(): BelongsToMany
    {
        return $this
            ->officesRelation()
            ->where('active', '=', 1)
            ->where('deleted', '=', 0)
            ->orderBy(
                DB::raw(AdministratorOffice::getTableName() . ".office_id = '" . Office::OFFICE_ID_WEB . "'"),
                'desc'
            )
            ->orderBy('name');
    }

    /**
     * @return BelongsToMany
     */
    public function administratorClients(): BelongsToMany
    {
        return $this->belongsToMany(
            Client::class,
            'administrator_client',
            'administrator_id',
            'client_id'
        );
    }

    /**
     * @return string
     */
    public function getAvatarPath(): string
    {
        $path = StorageService::getAdminAvatarPath($this->getKey());
        if (!StorageService::hasFile($path)) {
            return StorageService::DEFAULT_AVATAR_PATH;
        }

        return $path;
    }

    /**
     * @return BelongsToMany
     */
    public function settings()
    {
        return $this->belongsToMany(
            Setting::class,
            'administrator_setting',
            'administrator_id',
            'setting_key'
        )->withPivot('value');
    }

    public function getAdminMaxDiscount(): int
    {
        $maxDiscount = $this->getSetting(SettingsEnum::max_discount_percent_administrator, true);
        if (!$maxDiscount) {
            return 0;
        }

        return $maxDiscount;
    }

    public function getAdminReportsCount(): int
    {
        $val = $this->getSetting(SettingsEnum::count_of_reports_per_month_for_a_given_client_administrator, true);
        if (!$val) {
            return 0;
        }

        return $val;
    }

    /**
     * @param string $settingKey
     * @param bool $valueOnly
     *
     * @return Model|Builder|mixed|object|null
     */
    public function getSetting(SettingsEnum $settingKey, bool $valueOnly = false)
    {
        $builder = AdministratorSetting::query()->where('administrator_id', $this->administrator_id);

        if ($settingKey) {
            $builder->where('setting_key', $settingKey);
        }

        $setting = $builder->first();
        if (!isset($setting->value)) {
            return null;
        }

        if ($valueOnly) {
            return $setting->value;
        }

        return $setting;
    }

    public function setSetting(SettingsEnum $enum, int|string $value): void
    {
        $adminSetting = $this->getSetting($enum);
        if ($adminSetting) {
            $adminSetting->value = $value;
            $adminSetting->save();
        } else {
            AdministratorSetting::create([
                'administrator_id' => $this->getKey(),
                'setting_key' => $enum,
                'value' => $value,
            ]);
        }
    }

    public function getAdminDiscount()
    {
        return $this->getSetting(SettingsEnum::max_discount_percent_administrator, true);
    }

    public function getAdminProducts(): array
    {
        $rows = DB::select(DB::raw("
            select p.product_id, p.name
            from product p
            join office_product op on op.product_id = p.product_id
            join administrator_office ao on ao.office_id = op.office_id
            where ao.administrator_id = " . getAdminId() . " and migrated = 0;
        "));

        $result = [];
        foreach ($rows as $row) {
            $result[$row->product_id] = $row->name;
        }

        return $result;
    }

    /**
     * @param string $settingKey
     *
     * @return Model|null
     */
    private function getDefaultSetting(SettingsEnum $settingKey)
    {
        $builder = DB::table('setting');

        if ($settingKey) {
            $builder->where('setting_key', $settingKey);
        }

        return $builder->first();
    }

    /**
     * @return HasMany
     */
    public function sms()
    {
        return $this->hasMany(Sms::class);
    }

    /**
     * @return HasMany
     */
    public function email()
    {
        return $this->hasMany(Email::class);
    }

    /**
     * [role description]
     *
     * @return Role
     */
    public function role()
    {
        return DB::table('role')
            ->join(
                'administrator_role',
                'administrator_role.role_id',
                '=',
                'role.id'
            )->where(
                [
                    'administrator_id' => $this->administrator_id,
                ]
            )->first();
    }

    /**
     * [getPriority description]
     *
     * @return int
     */
    public function getPriority(): int
    {
        $role = $this->role();
        if (empty($role->priority)) {
            return 0;
        }

        return (int) $role->priority;
    }

    /**
     * [canChangeRoles description]
     *
     * @return int
     */
    public function canChangeRoles()
    {
        return (self::MIN_PRIORITY_FOR_CHANGING_ROLES <= $this->getPriority());
    }

    /**
     * @param int $officeId
     *
     * @return bool
     */
    public function fromOffice(int $officeId): bool
    {
        return (
            AdministratorOffice::where(
                [
                    'administrator_id' => Auth::id(),
                    'office_id' => $officeId,
                ]
            )->count() > 0
        );
    }

    public function canApproveLoan(Loan $loan): bool
    {
        if (empty($loan->office_id) || empty($loan->amount_approved)) {
            return false;
        }

        if ($loan->isApproved()) {
            return false;
        }

        if (!$this->hasPermissionTo('approve.loan-decision.process')) {
            return false;
        }

        // $key = 'admin_from_online_' . $this->administrator_id;
        // $isAdminFromOnline =  \Cache::remember($key, 5 * 60, function () {
        //     return AdministratorOffice::where([
        //         'administrator_id' => Auth::id(),
        //         'office_id' => Office::OFFICE_ID_WEB,
        //     ])->count() > 0;
        // });

        $isAdminFromOnline = AdministratorOffice::where([
                'administrator_id' => Auth::id(),
                'office_id' => Office::OFFICE_ID_WEB,
            ])->count() > 0;

        $officeRel = $this->officesRelation;
        $key = 'office_self_approved_' . $loan->office_id;
        $isSelfApproveOffice = \Cache::remember($key, 5 * 60, function () use ($officeRel, $loan) {
            return $officeRel
                    ->where('office_id', $loan->office_id)
                    ->where('office_type_id', OfficeType::OFFICE_TYPE_SELF_APPROVE_ID)
                    ->count() > 0;
        });

        if (!$isAdminFromOnline && !$isSelfApproveOffice) {
            return false;
        }


        return true;
    }

    public function canApproveNotBusyLoan(Loan $loan): bool
    {
        if (!$this->canApproveLoan($loan)) {
            return false;
        }

        if (
            $loan->loan_status_id == LoanStatus::PROCESSING_STATUS_ID
            && $loan->last_status_update_administrator_id != Auth::id()
        ) {
            return false;
        }

        return true;
    }

    public function approveListingStatus(Loan $loan): string
    {
        $admin = Auth::user();
        $btnProcess = 'showProcessBtn';

        if ($loan->loan_status_id == LoanStatus::PROCESSING_STATUS_ID) {
            if ($loan->last_status_update_administrator_id == $admin->administrator_id) {
                return $btnProcess;
            }

            $lastAdmin = $loan->lastStatusUpdateAdministrator()->first();

            return __('head::clientCard.openTaskProcessing') . $lastAdmin?->getName();
        }

        if ($loan->loan_status_id == LoanStatus::SIGNED_STATUS_ID) {
            if ($admin->canApproveNotBusyLoan($loan)) {
                return $btnProcess;
            }

            return 'No approve permission';
        }

        return 'Wrong loan status';
    }

    public function isSuperAdmin(): bool
    {
        return DB::table('role')
                ->join(
                    'administrator_role',
                    'administrator_role.role_id',
                    '=',
                    'role.id'
                )->where(
                    [
                        'administrator_role.administrator_id' => $this->getKey(),
                        'administrator_role.role_id' => Role::ROLE_SUPER_ADMIN_ID,
                    ]
                )->count() > 0;
    }

    public function isAccountant(): bool
    {
        $role = $this->role();
        if (empty($role->name)) {
            return false;
        }

        return ($role->name == 'Счетоводител');
    }

    public function isSpecUser(): bool
    {
        if (empty($this->username)) {
            return false;
        }

        return in_array($this->username, config('common.spec_users'));
    }

    public function isSuperAdminOrSpecUser(): bool
    {
        if ($this?->hasRole(Role::ROLE_SUPER_ADMIN_ID) || $this?->isSpecUser()) {
            return true;
        }

        return false;
    }


    public function modules(): HasMany
    {
        return $this->hasMany(
            AdministratorModule::class,
            'administrator_id',
            'administrator_id'
        );
    }

    public function getPermission(Permission $permission)
    {
        return AdministratorPermission::where(
            [
                'administrator_id' => $this->getKey(),
                'permission_id' => $permission->getKey(),
            ]
        )->first();
    }

    public function getPermissionAdditionalInfo(?Permission $permission)
    {
        if (empty($permission)) {
            return false;
        }
        $administratorPermission = $this->getPermission($permission);

        if (empty($administratorPermission)) {
            return false;
        }

        if (empty($administratorPermission->additional_info)) {
            return json_decode($permission->additional_info, true);
        }

        return json_decode($administratorPermission->additional_info, true);
    }

    public function getPermissionAdditionalInfoByRouteName(string $routeName)
    {
        return $this->getPermissionAdditionalInfo(
            Permission::where('name', $routeName)->first()
        );
    }

    /**
     * @return bool
     */
    public function isOnlineAdmin(): bool
    {
        return DB::table('administrator_office')
                ->join(
                    'office',
                    'administrator_office.office_id',
                    '=',
                    'office.office_id'
                )->where(
                    [
                        'administrator_office.administrator_id' => $this->getKey(),
                        'administrator_office.office_id' => Office::OFFICE_ID_WEB,
                    ]
                )->count() > 0;
    }

    public function isSystem(): bool
    {
        return $this->administrator_id === self::SYSTEM_ADMINISTRATOR_ID;
    }

    public function duplicate()
    {
        // Start a database transaction
        DB::beginTransaction();

        try {
            // Duplicate the administrator record
            $newAdministrator = $this->replicate();
            $newAdministrator->last_name = $newAdministrator->last_name . '(COPY)';
            $newAdministrator->username = $newAdministrator->username . '_copy_' . time();
            $newAdministrator->save();

            // Duplicate related models

            $administratorId = $newAdministrator->administrator_id;

            // Duplicate administrator_office records
            $administratorOffices = DB::table('administrator_office')
                ->where('administrator_id', $this->administrator_id)
                ->get();

            foreach ($administratorOffices as $administratorOffice) {
                unset($administratorOffice->administrator_office_id);

                $administratorOffice->administrator_id = $administratorId;
                DB::table('administrator_office')->insert((array) $administratorOffice);
            }


            // Duplicate administrator_role records
            $administratorRoles = DB::table('administrator_role')
                ->where('administrator_id', $this->administrator_id)
                ->get();

            foreach ($administratorRoles as $administratorRole) {
                $administratorRole->administrator_id = $administratorId;
                DB::table('administrator_role')->insert((array) $administratorRole);
            }


            // Duplicate administrator_permission records
            $administratorPermissions = DB::table('administrator_permission')
                ->where('administrator_id', $this->administrator_id)
                ->get();

            foreach ($administratorPermissions as $administratorPermission) {
                unset($administratorPermission->administrator_permission_id);

                $administratorPermission->administrator_id = $administratorId;
                DB::table('administrator_permission')->insert((array) $administratorPermission);
            }


            // Duplicate administrator_setting records
            $administratorSettings = DB::table('administrator_setting')
                ->where('administrator_id', $this->administrator_id)
                ->get();

            foreach ($administratorSettings as $administratorSetting) {
                $administratorSetting->administrator_id = $administratorId;
                DB::table('administrator_setting')->insert((array) $administratorSetting);
            }

            // Commit the transaction
            DB::commit();

            return $newAdministrator;
        } catch (\Exception $e) {
            // An error occurred, rollback the transaction
            DB::rollback();
            throw $e;
        }
    }

    public function getProcessingTaskNotification(string $type = null, int $id = null): ?string
    {
        // payment_task: status = TaskStatusEnum::PROCESSING & handled_by = admin_id
        // bucket_task: status = BucketTask::STATUS_PROCESSING & processed_by = admin_id
        // loan: loan_status_id = LoanStatus::PROCESSING_STATUS_ID & administrator_id = admin_id
        // sale_task: status = SaleTask::SALE_TASK_STATUS_PROCESSING & processed_by = admin_id


        return null;


        $additionalS = '';
        $additionalA = '';
        $additionalC = '';
        $additionalP = '';
        if ($type == 'sale' && !empty($id)) {
            $additionalS = ' AND sale_task_id != ' . $id;
        }
        if ($type == 'approve' && !empty($id)) {
            $additionalA = ' AND loan_id != ' . $id;
        }
        if ($type == 'collect' && !empty($id)) {
            $additionalC = ' AND loan_id != ' . $id;
        }
        if ($type == 'payment' && !empty($id)) {
            $additionalP = ' AND payment_task_id != ' . $id;
        }


        $result = DB::select(
            DB::raw(
                "
            SELECT
            (
                SELECT STRING_AGG('#' || CAST(sale_task_id AS VARCHAR), ', ')
                FROM sale_task
                WHERE processed_by = " . $this->administrator_id . " AND status = '" . SaleTask::SALE_TASK_STATUS_PROCESSING . "' " . $additionalS . "
            ) AS sale_task_ids,
            (
                SELECT STRING_AGG('#' || CAST(loan_id AS VARCHAR), ', ')
                FROM loan
                WHERE administrator_id = " . $this->administrator_id . " AND loan_status_id = " . LoanStatus::PROCESSING_STATUS_ID . $additionalA . "
            ) AS approve_loan_ids,
            (
                SELECT STRING_AGG('#' || CAST(loan_id AS VARCHAR), ', ')
                FROM bucket_task
                WHERE processed_by = " . $this->administrator_id . " AND status = '" . BucketTask::STATUS_PROCESSING . "'  " . $additionalC . "
            ) AS collect_loan_ids,
            (
                SELECT STRING_AGG('#' || CAST(payment_task_id AS VARCHAR), ', ')
                FROM payment_task
                WHERE handled_by = " . $this->administrator_id . " AND status = '" . TaskStatusEnum::PROCESSING->value . "'  " . $additionalP . "
            ) AS payment_task_ids;
        "
            )
        );

        if (empty($result)) {
            return null;
        }

        $result = (array) $result[0];
        if (
            empty($result['sale_task_ids'])
            && empty($result['approve_loan_ids'])
            && empty($result['collect_loan_ids'])
            && empty($result['payment_task_ids'])
        ) {
            return null;
        }

        $str = 'Обаботката е забранена докато има други незатворени задачи. ';
        if (!empty($result['sale_task_ids'])) {
            $str .= 'Продажби, задачи: ' . $result['sale_task_ids'] . ". ";
        }
        if (!empty($result['approve_loan_ids'])) {
            $str .= 'Одобрение, заеми: ' . $result['approve_loan_ids'] . ". ";
        }
        if (!empty($result['collect_loan_ids'])) {
            $str .= 'Събиране, заеми: ' . $result['collect_loan_ids'] . ". ";
        }
        if (!empty($result['payment_task_ids'])) {
            $str .= 'Плащане, задачи: ' . $result['payment_task_ids'];
        }

        return $str;
    }
}
