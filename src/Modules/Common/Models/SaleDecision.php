<?php

namespace Modules\Common\Models;

use RuntimeException;

/**
 * @mixin IdeHelperSaleDecision
 */
class SaleDecision extends AbstractDecision
{
    public const SALE_DECISION_ID_NO_ANSWER = 1;
    public const SALE_DECISION_ID_BUSY = 2;
    public const SALE_DECISION_ID_RECALL = 3;
    public const SALE_DECISION_ID_WRONG_PHONE = 4;
    public const SALE_DECISION_ID_FAKE_REQUEST = 5;
    public const SALE_DECISION_ID_OTHER = 6;
    public const SALE_DECISION_ID_CLIENT_CONTINUE = 7;
    public const SALE_DECISION_ID_NO_INTEREST = 9;
    public const SALE_DECISION_ID_PROCESSED_CLIENT_APPLICATION_SUBMITTED = 8;
    public const SALE_DECISION_ID_CANCEL = 10;
    public const SALE_DECISION_ID_CHANGE_LOAN_PARAMS = 11;
    public const SALE_DECISION_ID_SELF_DESTROY = 12;
    public const SALE_DECISION_ID_TO_PRODUCT_APP_SUB_BY_AGENT = 13;

    public const SALE_DECISION_NO_ANSWER = 'the_call_is_unanswered';
    public const SALE_DECISION_BUSY = 'busy';
    public const SALE_DECISION_RECALL = 'call_later';
    public const SALE_DECISION_WRONG_PHONE = 'wrong_phone';
    public const SALE_DECISION_FAKE_REQUEST = 'doubt_that_the_request_is_genuine';
    public const SALE_DECISION_OTHER = 'something_else';
    public const SALE_DECISION_CLIENT_CONTINUE = 'the_client_will_submit_the_application_himself';
    public const SALE_DECISION_NO_INTEREST = 'no_interest_in_the_product';
    public const SALE_DECISION_PROCESSED_CLIENT_APPLICATION_SUBMITTED = 'processed_client_application_submitted';
    public const SALE_DECISION_CANCEL = 'cancel_loan';
    public const SALE_DECISION_CHANGE_LOAN_PARAMS = 'change_loan_params';
    public const SALE_DECISION_TO_PRODUCT_APP_SUB_BY_AGENT = 'to_product_app_submitted_by_agent';

    public const SALE_DECISION_MODAL_CALL_LATER = 'call_later';
    public const SALE_DECISION_TASK_MODAL_OTHER = 'something_else';

    public const SALE_DECISION_TYPE_FINAL = 'final';
    public const SALE_DECISION_TYPE_WAITING = 'waiting';
    public const SALE_DECISION_SELF_DESTROY = 'self_destroy';
    /**
     * @var string
     */
    protected $table = 'sale_decision';

    /**
     * @var string
     */
    protected $primaryKey = 'sale_decision_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'name',
        'type',
    ];

    public static function getDecisionIdsForCancelLoan(): array
    {
        return SaleDecision::whereIn('name', [
            SaleDecision::SALE_DECISION_CANCEL,
            SaleDecision::SALE_DECISION_WRONG_PHONE,
            SaleDecision::SALE_DECISION_NO_INTEREST,
            SaleDecision::SALE_DECISION_FAKE_REQUEST,
        ])->pluck('sale_decision_id')->toArray();
    }

    public static function getWaitingDecisions(): array
    {
        return [
            self::SALE_DECISION_ID_NO_ANSWER,
            self::SALE_DECISION_ID_BUSY,
            self::SALE_DECISION_ID_RECALL,
        ];
    }

    /**
     * @param int|null $id
     * @return mixed|string|string[]
     */
    public static function getSaleDecisionNames(?int $id = null)
    {
        $mapping = [
            self::SALE_DECISION_ID_NO_ANSWER => self::SALE_DECISION_NO_ANSWER,
            self::SALE_DECISION_ID_BUSY => self::SALE_DECISION_BUSY,
            self::SALE_DECISION_ID_RECALL => self::SALE_DECISION_RECALL,
            self::SALE_DECISION_ID_WRONG_PHONE => self::SALE_DECISION_WRONG_PHONE,
            self::SALE_DECISION_ID_FAKE_REQUEST => self::SALE_DECISION_FAKE_REQUEST,
            self::SALE_DECISION_ID_OTHER => self::SALE_DECISION_OTHER,
            self::SALE_DECISION_ID_CLIENT_CONTINUE => self::SALE_DECISION_CLIENT_CONTINUE,
            self::SALE_DECISION_ID_NO_INTEREST => self::SALE_DECISION_NO_INTEREST,
            self::SALE_DECISION_ID_PROCESSED_CLIENT_APPLICATION_SUBMITTED => self::SALE_DECISION_PROCESSED_CLIENT_APPLICATION_SUBMITTED,
            self::SALE_DECISION_ID_TO_PRODUCT_APP_SUB_BY_AGENT => self::SALE_DECISION_TO_PRODUCT_APP_SUB_BY_AGENT,
            self::SALE_DECISION_ID_CANCEL => self::SALE_DECISION_CANCEL,
            self::SALE_DECISION_ID_SELF_DESTROY => self::SALE_DECISION_SELF_DESTROY,
        ];

        if (empty($id)) {
            return $mapping;
        }

        return $mapping[$id];
    }

    public static function isWaitingDecision(int $saleDecisionId): bool
    {
        if (!in_array($saleDecisionId, array_keys(self::getSaleDecisionNames()))) {
            throw new RuntimeException('Invalid approve decision id!');
        }

        return in_array(
            $saleDecisionId,
            self::getWaitingDecisions()
        );
    }


    public function inSkipCounterCondition()
    {
        $decisionId = $this->getKey();
        if (empty($decisionId)) {
            return false;
        }

        return $this->isWaitingDecision($decisionId);
    }
}

