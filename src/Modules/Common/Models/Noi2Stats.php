<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin NoiStats
 * @mixin IdeHelperNoi2Stats
 */
final class Noi2Stats extends Model
{
    public $timestamps = false; // disable updated_at

    protected $table = 'noi2_stats';
    protected $primaryKey = 'id';

    protected $fillable = [
        'created_at',
        'created_by',

        'noi_report_id',
        'client_id',
        'loan_id',

        'call_status',
        'report_date',
        'last_salary',
        'last_input_date',
        // will add more later

    ];

    public function noiReport(): BelongsTo
    {
        return $this->belongsTo(NoiReport::class, 'noi_report_id', 'noi_report_id');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'client_id');
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id', 'loan_id');
    }
}
