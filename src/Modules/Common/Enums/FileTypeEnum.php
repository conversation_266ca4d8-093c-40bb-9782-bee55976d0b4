<?php

namespace Modules\Common\Enums;

enum FileTypeEnum: string
{
    case SEF = 'sef';
    case APPLICATION = 'application';
    case PERSONAL_DATA = 'personal_data_declaration';
    case GEN_TERMS = 'general_terms';
    case CONTRACT = 'contract';
    case PLAN = 'repayment_plan';
    case ORDER_RECORD = 'order_record';
    case CONTRACT_ORDER = 'contract_order';
    case RECEIPT_DEBIT_CARD = 'receipt_debit_card';
    case CERT_NO_OBL = 'certificate_no_obligation';
    case CERT_ACTIVE_LOAN = 'certificate_active_loan';
    case OTHER = 'other';
    case CLIENT_DOC = 'client_document'; // id card picture
    case DISCOUNT_IMPORT = 'discount_import';
    case INTEREST_TERM_IMPORT = 'interest_term_import';
    case PENALTY_TERM_IMPORT = 'penalty_term_import';
    case CASH_RECEIPT = 'cash_receipt';
    case CASH_INCOME_ORDER = 'cash_in_order';
    case CASH_OUTCOME_ORDER = 'cash_out_order';
    case DECLARATION_MARKETING = 'marketing_declaration';
    case DECLARATION_EL_SIGNATURE = 'declaration_el_signature';
    case LEGAL_INFO = 'legal_info';
    case DIR_SERVICE_EXP = 'direct_service_export';
    case A4E_PERFORMANCE_JSON = 'a4e_performance_json';
    case A4E_PERFORMANCE_ZIP = 'a4e_performance_zip';
    case CCR_REPORT_CSV = 'ccr_report_csv';
    case CCR_REPORT_ZIP = 'ccr_report_zip';
    case CCR_REPORT_TXT = 'ccr_report_txt';
    case LANDING_DOC = 'landing_doc';
    case PORTFOLIO_SNAPSHOT = 'portfolio_snapshot';
    case ACCOUNTING_STATS_REPORT = 'accounting_stats_report';
    case INSURANCE_CERTIFICATE = 'insurance_certificate';

    // scans from client
    case ID_CARD = 'id_card';
    case EMPLOYMENT_CONTRACT = 'employment_contract';
    case CIVIL_CONTRACT = 'civil_contract';
    case RENTAL_AGREEMENT = 'rental_agreement';
    case PROXY_CERTIFICATE = 'proxy_certificate';
    case DEED_CERTIFICATE = 'deed_certificate';
    case INHERITANCE_CERTIFICATE = 'inheritance_certificate';
    case DEATH_CERTIFICATE = 'death_certificate';
    case GUARANTEE_CONTRACT = 'guarantee_contract';

    public function label(): string
    {
        return __('product::product.' . $this->value);
    }

    public static function clientUploads(): array
    {
        return [
            self::ID_CARD,
            self::EMPLOYMENT_CONTRACT,
            self::CIVIL_CONTRACT,
            self::RENTAL_AGREEMENT,
            self::PROXY_CERTIFICATE,
            self::DEED_CERTIFICATE,
            self::INHERITANCE_CERTIFICATE,
            self::DEATH_CERTIFICATE,
            self::CONTRACT,
            self::GUARANTEE_CONTRACT,
            self::OTHER
        ];
    }

    public static function casesValue(array $array = null): array
    {
        return array_map(static function (self $item) {
            return $item->value;
        }, $array);
    }

    public static function clientUploadsValues(): array
    {
        return self::casesValue(self::clientUploads());
    }
}


