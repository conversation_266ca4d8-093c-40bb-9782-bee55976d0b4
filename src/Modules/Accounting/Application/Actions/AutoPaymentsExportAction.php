<?php

namespace Modules\Accounting\Application\Actions;

use Modules\Common\Models\Payment;
use Modules\Common\Repositories\PayConfirmRepository;
use Modules\Head\Services\SpreadsheetHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class AutoPaymentsExportAction
{
    public function __construct(
        public PayConfirmRepository $repository
    ) {
    }

    public function execute(array $filters = [])
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            __('table.PaymentId'),
            __('table.CreatedAt'),
            __('table.Amount'),
            __('table.Pin'),
            __('table.Transaction'),
            __('table.RawData'),
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;


        $builder = $this->repository->payConfirmBuilder($filters);

        $builder->chunkById(100, function ($rows) use (&$sheet, &$rowIndex) {
            /** @var Payment $payment ** */
            foreach ($rows as $payment) {
                $r[] = [
                    $payment->payment_id ?? 'Плащане не е намерено',
                    formatDate($payment->created_at, 'd.m.Y H:i'),
                    intToFloat($payment->total),
                    $payment->idn,
                    $payment->tid,
                    $payment->input_data,
                ];

                $rowIndex++;
                $sheet->fromArray($r, null, 'A' . $rowIndex);
                $r = [];
            }
        });

        /// set autosize columns
        app(SpreadsheetHelper::class)->setAutoSize($sheet);


        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Set the headers to force download the file
        $fileName = 'auto_payments_export_' . time() . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');

        // Write the spreadsheet to the output
        $writer->save('php://output');

        return;
    }
}
