<?php

namespace Modules\Sales\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskHistory;

class DailyArchiveSaleTasksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Collection $saleTasks
    ) {}

    public function handle(): void
    {
        $rowsToArchive = $this->saleTasks->map(function (SaleTask $saleTask) {
            $data = $saleTask->toArray();

            $this->castDate($data, 'created_at');
            $this->castDate($data, 'updated_at');
            $this->castDate($data, 'deleted_at');
            $this->castDate($data, 'enabled_at');
            $this->castDate($data, 'disabled_at');

            $data['archived_at'] = (Carbon::now())->format('Y-m-d H:i:s');
            $data['archived_by'] = Administrator::SYSTEM_ADMINISTRATOR_ID;
            $data['product_ids'] = json_encode($data['product_ids']);

            return $data;
        });
        $ids = $rowsToArchive->pluck('sale_task_id')->all();

        DB::beginTransaction();
        try {
            SaleTaskHistory::insert($rowsToArchive->toArray());
            SaleTask::query()->whereIn('sale_task_id', $ids)->forceDelete();
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function castDate(&$data, $key)
    {
        if (!empty($data[$key])) {
            $data[$key] = (Carbon::parse($data[$key]))->format('Y-m-d H:i:s');
        }
    }
}
