<?php

namespace Modules\Sales\Console;

use Illuminate\Console\Command;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\LoanStatus;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;

class RefreshNewClients extends CommonCommand
{
    protected $name = 'script:refresh-new-clients';
    protected $description = 'Find all clients which no have repaid loans.';

    public function handle(): void
    {
        $this->startLog();



        $rows = Client::query()
            ->where('new', 0) /// get all old clients
            /// which doesnt have loans with status repaid/active
            ->whereDoesntHave('loans', function ($query) {
                $query->whereIn('loan_status_id', [LoanStatus::ACTIVE_STATUS_ID, LoanStatus::REPAID_STATUS_ID]);
            })
            ->get();

        $this->info('(1) Clients to fix: ' . $rows->count());
        if ($rows->count() > 0) {
            // update clients
            $rows->each(function (Client $client) {
                $client->setAttribute('new', 1);
                $client->save();
            });
        }



        $rows = Client::query()
            ->where('new', 1) /// get all new clients
            /// which have loans with status repaid/active
            ->whereHas('loans', function ($query) {
                $query->whereIn('loan_status_id', [LoanStatus::ACTIVE_STATUS_ID, LoanStatus::REPAID_STATUS_ID]);
            })
            ->get();

        $this->info('(2) Clients to fix: ' . $rows->count());
        if ($rows->count() > 0) {
            // update clients
            $rows->each(function (Client $client) {
                $client->setAttribute('new', 0);
                $client->save();
            });
        }




        $this->finishLog([
            'Old client which no have repaid loans: ' . $rows->count(),
            'End execution',
        ]);
    }
}
