<?php

namespace Modules\Sales\Services;

use Illuminate\Support\Collection;
use Modules\Common\Services\BaseService;
use Modules\Sales\Repositories\SaleDecisionReasonRepository;
use RuntimeException;

class SaleDecisionReasonService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    public function __construct(
        private readonly SaleDecisionReasonRepository $saleDecisionReasonRepository
    ) {
        parent::__construct();
    }

    public function getSaleDecisions(): Collection
    {
        // \Cache::forget('sale_decisions');

        /** @var Collection $data */
        $data = \Cache::remember('sale_decisions', self::CACHE_TTL, function () {
            return $this->saleDecisionReasonRepository->getSaleDecisions()->collect();
        });

        if ($data->isEmpty()) {
            throw new RuntimeException(
                __('sales::saleDecisionReason.saleDecisionReasonDecisionsNotFound')
            );
        }

        return $data;
    }
}
