<?php

namespace Modules\Sales\Services;

use Cache;
use Carbon\Carbon;
use Exception;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Collection;
use Modules\Admin\Services\SettingService;
use Modules\Admin\Traits\AdminTrait;
use Modules\Approve\Application\Action\CancelLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\AbstractTask;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\SaleAttempt;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;
use Modules\Common\Services\BaseService;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Services\EmailService;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Repositories\SaleAttemptRepository;
use Modules\Sales\Repositories\SaleTaskRepository;
use RuntimeException;
use Throwable;

final class SaleService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    use AdminTrait;

    private const MAX_ATTEMPT_COUNT_SETTING_KEY = 'timer_for_sales_before_ending_the_loan_request_module';
    private const MULTIPLE_ATTEMPT_ADD_TIME_SETTING_KEY = 'multiple_skip_sale_attempts_minutes_interval_sales';

    private const FIRST_ADD_TIME_SETTING_KEYS = [
        SaleDecision::SALE_DECISION_ID_NO_ANSWER => 'no_answer_sales',
        SaleDecision::SALE_DECISION_ID_BUSY => 'busy_sales',
        SaleDecision::SALE_DECISION_ID_RECALL => 'busy_sales',
    ];

    public function __construct(
        private readonly SaleTaskRepository  $saleTaskRepository,
        private readonly ClientRepository    $clientRepository,
        private readonly SaleDecisionService $saleDecisionService,
        private readonly SaleAttemptService  $saleAttemptService,
        protected SettingService             $settingService,
    ) {
        // parent::__construct($settingService);
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return mixed
     * @throws Exception
     * @deprecated
     *
     */
    public function getByFilters(?int $limit, array $data)
    {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        if (array_key_exists('limit', $data) && $data['limit'] !== null) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        $whereConditionsAdditional = [];

        if (!empty($data['amountRequestedFrom'])) {
            $whereConditionsAdditional[] = [
                'loan.amount_requested',
                '>=',
                $data['amountRequestedFrom'],
            ];
            unset($data['amountRequestedFrom']);
        }

        if (!empty($data['amountRequestedTo'])) {
            $whereConditionsAdditional[] = [
                'loan.amount_requested',
                '<=',
                $data['amountRequestedTo'],
            ];
            unset($data['amountRequestedTo']);
        }

        $whereConditions = $this->getWhereConditions($data, ['name'], 'sale_task');

        array_walk(
            $whereConditions,
            function (&$element) {
                if ($element[0] === 'sale_task.phone') {
                    $element[0] = 'client.phone';
                }

                if ($element[0] === 'sale_task.loan_type_id') {
                    $element[0] = 'loan_type.loan_type_id';
                }

                if ($element[0] === 'sale_task.period_requested') {
                    $element[0] = 'loan.period_requested';
                }

                if ($element[0] === 'sale_task.amount_requested_from' ||
                    $element[0] === 'sale_task.amount_requested_to'
                ) {
                    $element[0] = 'loan.amount_requested';
                }
            }
        );

        $whereConditions = array_merge($whereConditions, $whereConditionsAdditional);

        // Reverse sorting if we have client_new order direction
        foreach ($order as $field => $direction) {
            if ($field === 'client_new') {
                $order[$field] = $this->reverseOrderDirection($direction);
                break;
            }

            if (strpos($field, 'sale_task.created_at_timer') !== false) {
                unset($order[$field]);
                $field = str_replace('sale_task.created_at_timer', 'time_difference', $field);
                $order[$field] = $direction;
            }
        }

        $data = $this->saleTaskRepository->getAll(
            $limit,
            $whereConditions,
            !empty($order) ? $order : ['sale_task_id' => 'DESC', 'active' => 'DESC']
        );

        array_map([$this, 'setDifference'], $data instanceof AbstractPaginator ? $data->items() : $data->all());

        $this->additionalSaleTaskPreparation($data);

        return $data;
    }

    private function reverseOrderDirection(string $direction): string
    {
        $reverse = [
            'ASC' => 'DESC',
            'DESC' => 'ASC',
        ];
        $direction = strtoupper($direction);

        if (!array_key_exists($direction, $reverse)) {
            return 'ASC';
        }

        return $reverse[$direction];
    }

    protected function getWhereConditions(
        array  $data,
        array  $names = ['name'],
        string $prefix = ''
    )
    {
        $where = [];

        $where[] = [
            'sale_task.show_after',
            '<=',
            Carbon::now()->format('Y-m-d H:i:s'),
        ];


        return array_merge($where, parent::getWhereConditions($data, $names, $prefix));
    }


    /**
     * @return array
     * @throws Exception
     */
    public function getStatsInfo(): array
    {
        return $this->saleTaskRepository->getStatsInfo();
    }

    /**
     * @param SaleTask $saleTask
     *
     * @return SaleTask
     */
    public function systemClose(SaleTask $saleTask): SaleTask
    {
        return $this->saleTaskRepository->systemClose($saleTask);
    }

    public function storeSaleAttempt(SaleTask $saleTask, array $data)
    {
        $saleDecision = $this->saleDecisionService->getById($data['sale_decision_id']);

        return $this->storeAttempt($saleTask, $data, $saleDecision);
    }

    public function storeAttempt(
        SaleTask              $saleTask,
        array                 $data,
        SaleDecision          $saleDecision,
        SaleAttemptRepository $saleAttemptRepository = new SaleAttemptRepository(),
        SaleTaskRepository    $taskRepository = new SaleTaskRepository()
    ): SaleAttempt {

        $now = Carbon::now();


        // prepare default attempt array
        $taskAttemptData = [
            'administrator_id' => getAdminId(),
            $saleTask->getKeyName() => $saleTask->getKey(),
            'office_id' => $saleTask->office_id,
            $saleDecision->getKeyName() => $saleDecision->getKey(),
            'comment' => !empty($data['comment']) ? $data['comment'] : null,
            'skip_time' => 0,
            'skip_till' => null,
            'skip_counter' => 0,
            'details' => null,
            'payment_task_id' => !empty($saleTask->payment_task_id) ? $saleTask->payment_task_id : null,
            'start_at' => $saleTask->last_status_update_date,
            'end_at' => $now,
            'waiting_time' => (Carbon::parse($saleTask->last_status_update_date))->diffInSeconds(Carbon::parse($saleTask->created_at)),
            'processing_time' => $now->diffInSeconds($saleTask->last_status_update_date),
        ];
        $taskAttemptData['total_time'] = ($taskAttemptData['waiting_time'] + $taskAttemptData['processing_time']);


        // Re-try logic: create sub task for waiting statuses: busy, call later, no answer
        if ($saleDecision->type === $saleDecision::TASK_DECISION_TYPE_WAITING) {

            // define how many times we can re-try
            $maxAttemptSetting = (int) $this
                ->settingService
                ->getSetting(SettingsEnum::max_allowed_sale_postponements_sales)
                ->default_value ?: 8;

            // define time step for re-try when we do re-try for the 1st time
            $skipTimeSettingForFirstTime = $this
                ->settingService
                ->getSetting($this->getFirstAddTimeSetting()[$saleDecision->getKey()])
                ->default_value ?: 5;


            $skipCounter = 1;
            $skipTime = $skipTimeSettingForFirstTime;

            // check if such task already been skipped
            $parentTask = $saleTask->parent;
            $prevTaskAttempt = null;
            $prevAttemptWasSkipped = false;
            $skipTimeSettingForMany = null;
            if (!empty($parentTask)) {
                $prevTaskAttempt = $parentTask->taskAttempt();
                $prevAttemptWasSkipped = $prevTaskAttempt->decision?->inSkipCounterCondition() ?? false;
            }

            // if prev.task handling attempt was skip, we need to increase counter and skip time
            if ($prevAttemptWasSkipped) {
                $skipTimeSettingForMany = $this
                    ->settingService
                    ->getSetting(SettingsEnum::multiple_skip_sale_attempts_minutes_interval_sales)
                    ->default_value ?: 60;

                $skipCounter = 1 + $prevTaskAttempt->skip_counter;
                $skipTime = $skipTimeSettingForMany;
            }


            $taskAttemptData['skip_counter'] = $skipCounter;


            // if we do Re-try too many times -> close loan and stop re-trying(do not create sub task)
            if ($skipCounter >= $maxAttemptSetting) {

                $loan = null;
                if (!empty($saleTask->loan_id)) {
                    $loan = Loan::where('loan_id', $saleTask->loan_id)->first();
                }

                if (!empty($loan) && $loan->isNew()) {

                    $decisionDto = DecisionDto::from([
                        'loan_id' => $saleTask->loan_id,
                        'admin_id' => getAdminId(),
                        'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                        'decision_reason' => 'other',
                        'description' => 'Too many postponed sale tasks'
                    ]);

                    app(CancelLoanAction::class)->execute($decisionDto);
                }

            }
            // or we create another sale task -> child of current
            else {

                $skipTill = (
                    !empty($data['show_after'])
                    ? Carbon::parse($data['show_after']) // specific date/time could be chosen by client
                    : Carbon::now()->addMinutes($skipTime) // or we use skip time from settings
                );


                // update current task handling attempt
                $taskAttemptData['skip_time'] = $skipTime;
                $taskAttemptData['skip_till'] = $skipTill;


                // we create sub-task only in case if loan is still not sign or no loan created
                $loan = null;
                if (!empty($saleTask->loan_id)) {
                    $loan = Loan::where('loan_id', $saleTask->loan_id)->first();
                }

                if (
                    empty($saleTask->loan_id)
                    || empty($loan->loan_id)
                    || $loan->isNew()
                ) {
                    // prepare new sale task
                    $newTaskData = $saleTask->toArray();
                    $newTaskData['show_after'] = $skipTill;
                    $newTaskData['parent_task_id'] = $saleTask->getKey();
                    $newTaskData['processed_at'] = null;
                    $newTaskData['processed_by'] = null;
                    $newTaskData['status'] = AbstractTask::TASK_STATUS_NEW;
                    // $newTaskData['type'] = SaleTaskType::SALE_TASK_TYPE_ID_REQUEST_BY_PHONE; // commented, because why change the task type?
                    unset($newTaskData['payment_task_id'], $newTaskData['created_at']);

                    $taskRepository->create($newTaskData);
                }
            }

        }

        if ($saleDecision->sale_decision_id == SaleDecision::SALE_DECISION_ID_WRONG_PHONE) {

            $loan = null;
            if (!empty($saleTask->loan_id)) {
                $loan = Loan::where('loan_id', $saleTask->loan_id)->first();
                if (!empty($loan->loan_id)) {
                    $loan->addMeta('skip_email', 'cancel_wrong_phone');
                }
            }

            $emailService = app(EmailService::class);
            $emailService->sendByTemplateKeyAndSaleTask(
                EmailTemplateKeyEnum::SALES_TASK_EXIT_NO_SUCH_PHONE->value,
                $saleTask
            );
        }


        // save attempt
        $taskAttempt = $saleAttemptRepository->create($taskAttemptData);


        // update sale task
        $taskRepository->update($saleTask,
            [
                'processed_at' => $now,
                'processed_by' => getAdminId(),
                'last_status_update_date' => $now,
                'status' => AbstractTask::TASK_STATUS_DONE,
            ]
        );


        return $taskAttempt;
    }

    /**
     * @param $value
     *
     * Show deference between carbon:now and created_at date
     * format minutes and seconds (02:15)
     *
     */
    private function setDifference($value)
    {
        $date = Carbon::parse($value->created_at);
        $now = Carbon::now();

        $value->diff_now_created_at = $date->diff($now)->format('%I:%S');
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    public function getSaleTaskTypes()
    {
        $data = Cache::remember('sale_task_types', self::CACHE_TTL, function () {
            return $this->saleTaskRepository->getSaleTaskTypes();
        });
        return $this->collect(Collection::class, $data);
    }

    /**
     * @return array
     */
    public function getStatuses()
    {
        return $this->saleTaskRepository->getStatuses();
    }

    public function getSaleTaskById($saleTaskId): SaleTask
    {
        $saleTask = $this->saleTaskRepository->getById($saleTaskId);

        if (!$saleTask) {
            throw new RuntimeException(__('sales::saleTask.saleTaskNotFound'));
        }
        return $saleTask;
    }

    /**
     * @param Loan $loan
     *
     * @return Collection
     */
    public function getUndoneTasksByLoan(Loan $loan): Collection
    {
        return $this->saleTaskRepository->getUndoneByLoan($loan);
    }

    /**
     * @param SaleTask $saleTask
     *
     * @return bool
     * @throws Throwable
     */
    public function processSaleTask(SaleTask $saleTask)
    {
        return $this->saleTaskRepository->processSaleTask($saleTask);
    }

    public function create(array $data): SaleTask
    {
        try {
            $saleTask = $this->saleTaskRepository->create($data);
        } catch (Throwable $t) {
            throw new RuntimeException(
                __('sales::saleTask.saleTaskCreationFailed'),
                $t
            );
        }

        return $saleTask;
    }

    /**
     * @param SaleTask $saleTask
     * @param array $data
     *
     * @return SaleTask|false
     */
    public function update(SaleTask $saleTask, array $data)
    {
        return $this->saleTaskRepository->update($saleTask, $data);
    }

    public function createSaleTaskWithDiscount(
        int    $clientId,
        int    $percent,
        array  $productIds,
        string $validFrom,
        string $validTo,
        string $discountFrom = 'export',
        string $discountDetails = 'Sale task created from an imported file with discounts'
    ): ?SaleTask
    {

        // deactivate previous sale tasks for discount
        $this->deactivateSaleTasks($clientId);

        $client = $this->clientRepository->getClientAndOffice($clientId);
        if (empty($client->client_id)) {
            return null;
        }

        $officeId = (!empty($client->office_id) ? $client->office_id : Office::OFFICE_ID_WEB);

        $newTaskData = [
            'sale_task_type_id' => SaleTaskType::SALE_TASK_TYPE_ID_DISCOUNT_PERCENT,
            'client_id' => $client->client_id,
            'office_id' => $officeId,
            'discount' => $percent,
            'discount_from' => $discountFrom,
            'pin' => $client->pin,
            'client_full_name' => "$client->first_name $client->middle_name $client->last_name",
            'phone' => $client->phone,
            'email' => $client->email ?? '',
            'details' => $discountDetails,
            'status' => SaleTask::SALE_TASK_STATUS_NEW,
            'show_after' => Carbon::now(),
            'product_ids' => $productIds,
            'valid_from' => $validFrom,
            'valid_till' => $validTo,
        ];

        return $this->create($newTaskData);
    }

    public function getDiscountSaleTasks(int $clientId)
    {
        return $this->getByCriteria([
            'client_id' => $clientId,
            'sale_task_type_id' => SaleTaskType::SALE_TASK_TYPE_ID_DISCOUNT_PERCENT,
            'active' => 1,
        ]);
    }

    private function deactivateSaleTasks(int $clientId): bool
    {
        return $this->saleTaskRepository->deactivateSaleTasks($clientId);
    }

    /**
     * @param SaleTask $saleTask
     *
     * @return SaleTask
     */
    public function deactivateSaleTask(SaleTask $saleTask): SaleTask
    {
        return $this->update(
            $saleTask,
            [
                'active' => 0,
                'deleted' => 1,
                'updated_at' => now(),
                'updated_by' => getAdminId(),
            ]
        );
    }

    /**
     * @param array $array
     *
     * @return Collection|SaleTask|null
     */
    public function getByCriteria(array $array)
    {
        return $this->saleTaskRepository->getByCriteria($array);
    }

    /**
     * @throws Exception
     */
    protected function additionalSaleTaskPreparation($saleTasks)
    {
        foreach ($saleTasks as $saleTask) {
            $this->addTimerParams(
                $saleTask,
                'show_after'
            );
        }
    }

    public function getFirstAddTimeSetting(): array
    {
        return [
            SaleDecision::SALE_DECISION_ID_NO_ANSWER => SettingsEnum::no_answer_sales,
            SaleDecision::SALE_DECISION_ID_BUSY => SettingsEnum::busy_sales,
            SaleDecision::SALE_DECISION_ID_RECALL => SettingsEnum::busy_sales,
        ];
    }
}
