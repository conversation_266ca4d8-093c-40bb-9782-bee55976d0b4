<?php

namespace Modules\Sales\Services;

use Exception;
use Modules\Common\Services\BaseService;
use Modules\Sales\Repositories\SaleDecisionRepository;
use RuntimeException;

class SaleDecisionService extends BaseService
{
    public function __construct(
        private readonly SaleDecisionRepository $saleDecisionRepository,
    ) {
        parent::__construct();
    }

    public function getById(int $id)
    {
        try {
            $saleDecision = $this->saleDecisionRepository->getById($id);

            if (!$saleDecision) {
                throw new RuntimeException(__('sales::saleDecision.saleDecisionNotFound'));
            }
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('sales::saleDecision.saleDecisionUpdateFailed'),
                $exception
            );
        }

        return $saleDecision;
    }
}
