<?php

namespace Modules\Sales\Services;

use Carbon\CarbonInterface;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Client;
use Modules\Common\Models\Office;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;
use Modules\Common\Repositories\SaleTaskTypeRepository;
use Modules\Common\Services\ClientDiscountActualService;
use RuntimeException;

final readonly class SaleTaskService
{
    public function __construct(
        private ClientDiscountActualService $clientDiscountActualService,
        private SaleTaskTypeRepository $saleTaskTypeRepo,
    ) {}

    public function createWithDiscountByProducts(
        Client $client,
        array $productIds,
        int $discount,
        string $discountFrom,
        CarbonInterface $validFrom,
        CarbonInterface $validTo,
    ): SaleTask {
        return DB::transaction(function () use ($client, $productIds, $discount, $validFrom, $validTo, $discountFrom) {
            $this->clientDiscountActualService->createByProducts(
                $client->getKey(), $productIds, $discount, $validFrom, $validTo
            );

            return $this->createByProducts(
                $client,
                $productIds,
                $discount !== 100 ? SaleTaskType::SALE_TASK_TYPE_DISCOUNT
                    : SaleTaskType::SALE_TASK_TYPE_INTEREST_FREE_LOAN,
                $validFrom,
                $validTo,
                null,
                $discount,
                $discountFrom
            );
        });
    }

    public function createByProducts(
        Client $client,
        array $productIds,
        string $typeName,
        CarbonInterface $validFrom,
        CarbonInterface $validTo,
        ?int $amount = null,
        ?int $discount = null,
        ?string $discountFrom = null,
    ): SaleTask {
        $model = new SaleTask();

        $model->fill([
            'client_id' => $client->getKey(),
            'sale_task_type_id' => $this->saleTaskTypeRepo->getByNameQuery($typeName)->value('sale_task_type_id'),
            'office_id' => Office::OFFICE_ID_WEB,
            'client_full_name' => $client->getFullName(),
            'pin' => $client->pin,
            'phone' => $client->phone,
            'email' => $client->email,
            'status' => SaleTask::SALE_TASK_STATUS_NEW,
            'show_after' => now(),
            'created_at' => now(),
            'active' => 1,
            'deleted' => 0,
            'product_ids' => $productIds,
            'valid_from' => $validFrom,
            'valid_till' => $validTo,
            'amount' => $amount ?? 0,
            'discount' => $discount,
            'discount_from' => $discountFrom,
        ]);

        if (!$model->save()) {
            throw new RuntimeException('Failed to create sale task');
        }

        return $model;
    }
}
