<x-card>
    <x-slot:title>{{__('Receive money method')}}</x-slot:title>

    {!! form_row($newLoanForm->bank_account_id) !!}
    {!! form_row($newLoanForm->payment_method) !!}

    <div id="receive-money" class="d-none">
        {!! form_row($newLoanForm->iban) !!}
    </div>


    <!-- End ./receive-money -->

    {!! form_row($newLoanForm->comment) !!}


    <div class="form-group mt-4">
        <label for="skip_auto_process" class="control-label cursor-pointer">
            <input type="hidden" name="loan[skip_auto_process]" value="0"/>
            <input type="checkbox" name="loan[skip_auto_process]" value="1" id="skip_auto_process"/>

            {{__('table.SkipAutoProcess')}}
        </label>
    </div>
    <!-- End ./form-group -->

    <div class="form-group mb-0">
        <label for="re_contracted_overdue" class="control-label cursor-pointer">
            <input type="hidden" name="loan[re_contracted_overdue]" value="0"/>
            <input type="checkbox" name="loan[re_contracted_overdue]" value="1" id="re_contracted_overdue"/>
            {{__('table.SkipRefAmountCheck')}}&nbsp;
            <a href="javascript:void(0);"
               data-toggle="tooltip"
               data-placement="top"
               title="Тази настройка ще прескочи проверката при рефинанс - сумата по новия кредит да е по-голяма със 100 лева от сумата за погасяване на стария кредит. Тази настройка ще одобри автоматично заявката за кредит след подпис от клиента."
            >(i)</a>
        </label>
    </div>
    <!-- End ./form-group -->

    <div class="form-group mt-4">
        <label for="insurance" class="control-label cursor-pointer">
            <input type="hidden" name="loan[insurance]" value="0"/>
            <input type="checkbox" name="loan[insurance]" value="1" id="insurance"/>

            {{__('table.WithInsurance')}}
        </label>
    </div>
    <!-- End ./form-group -->

</x-card>


@push('scripts')
    <script>
        let $officePaymentMethods = @json($officePaymentMethods);
        $(document).ready(function () {
            let selectedPaymentAccountId = $('select[name="loan[bank_account_id]"]').val();
            showIbanFiled(selectedPaymentAccountId);

            /// refresh on change
            $(document).on('change', 'select[name="loan[bank_account_id]"]', function () {
                selectedPaymentAccountId = $(this).val();

                showIbanFiled(selectedPaymentAccountId);
            });

            function showIbanFiled(selectedPaymentAccountId) {

                if (!selectedPaymentAccountId) {
                    return false;
                }

                if (!$officePaymentMethods.hasOwnProperty(parseInt(selectedPaymentAccountId))) {
                    alert('Problem with payment_account/payment_method');
                    return false;
                }

                $('input[name="loan[payment_method]"]').val(parseInt($officePaymentMethods[selectedPaymentAccountId]));

                if (parseInt($officePaymentMethods[selectedPaymentAccountId]) === 1) {
                    $('div#receive-money').removeClass('d-none');
                    //$('div#bank').removeClass('d-none');
                    $('input[name="loan[iban]"]').attr('required', 'required');
                } else {
                    $('div#receive-money').addClass('d-none');
                    //$('div#bank').addClass('d-none');
                    $('input[name="loan[iban]"]').removeAttr('required');
                }
            }
        });
    </script>
@endpush
