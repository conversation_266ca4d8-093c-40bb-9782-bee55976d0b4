<?php

namespace Modules\Sales\Domain\Entities\Loan;

use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Approve\Application\Action\CancelLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Application\Actions\PrepareCalculatorDataAction;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Domain\InstallmentsInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Entities\RefinanceSnapshot;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Client as ClientDbModel;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanType as DbLoanType;
use Modules\Common\Models\Office as DbOffice;
use Modules\Common\Models\PaymentMethod as DbPaymentMethod;
use Modules\Common\Models\Product as ProductDb;
use Modules\Common\Models\ProductSetting as ProductSettingDb;
use Modules\Common\Models\Setting as DbSetting;
use Modules\Discounts\Dtos\DiscountSourceDto;
use Modules\Discounts\Enums\DiscountSourceEnum;
use Modules\Discounts\Models\LoanDiscountLog;
use Modules\Discounts\Repositories\ClientDiscountActualRepository;
use Modules\Discounts\Repositories\DiscountByPhoneRepository;
use Modules\Discounts\Repositories\LoanDiscountLogRepository;
use Modules\Discounts\Services\LoanDiscountLogService;
use Modules\Head\Repositories\Loan\LoanParamsHistoryRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Product\Services\ProductService;
use Modules\Sales\Domain\Entities\Admin;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\Relations\LoanMeta;
use Modules\Sales\Domain\Entities\Loan\Relations\ProductSetting;
use Modules\Sales\Domain\Entities\Loan\Relations\Relations;
use Modules\Sales\Domain\Entities\Office\ClientOffice;
use Modules\Sales\Domain\Entities\Office\Office;
use Modules\Sales\Domain\Entities\Product\Product;
use Modules\Sales\Domain\Entities\Task;
use Modules\Sales\Domain\Events\LoanWasSigned;
use Modules\Sales\Domain\Events\NewLoanHasArrived;
use Modules\Sales\Domain\Events\NewLoanHasCreatedCommon;
use Modules\Sales\Domain\Exceptions\AmountForRefinanceIsLargerThanProductMaxAmount;
use Modules\Sales\Domain\Exceptions\ClientHasUnprocessedLoans;
use Modules\Sales\Domain\Exceptions\DiscountPercentAboveLimit;
use Modules\Sales\Domain\Exceptions\ExcessiveLoanAmount;
use Modules\Sales\Domain\Exceptions\IdCardExpired;
use Modules\Sales\Domain\Exceptions\IncorrectPaymentMethod;
use Modules\Sales\Domain\Exceptions\LoanNotSaved;
use Modules\Sales\Domain\Exceptions\NewLoanAmountTooSmall;
use Modules\Sales\Domain\Exceptions\NewLoanNotCoveringRefinance;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Services\ClientCheckerService;

class NewLoan extends DomainModel implements LoanInterface
{
    const MIN_REF_AMOUNT = 10000;

    private bool $isNew = false;
    private bool $fromApprove = false;
    private array $refinanceInsert = [];
    private array $refinancedLoans = [];

    protected DbModel $dbModel;
    protected ?LoanDto $loanDto = null;
    protected Client $client;

    private ?int $clientDiscountLimit = null;

    public function __construct(
        protected DbSetting                      $setting,
        protected Installments                   $installments,
        protected Relations                      $relations,
        protected LoanRepository                 $repo,
        protected Office                         $office,
        protected Admin                          $admin,
        protected ClientDiscountActualRepository $discountByClientRepo,
        protected CurrentDate                    $currentDate,
        protected Product                        $product,
        protected ProductSetting                 $productSetting,
        protected ClientOffice                   $clientOffice,
        protected LoanMeta                       $loanMeta,
        protected SettingRepository              $settingRepository,
        protected Task                           $task,
        protected LoanParamsHistoryRepository    $loanParamsHistoryRepo,
        protected CancelLoanAction $cancelLoanAction,
        private readonly DiscountByPhoneRepository $discountByPhoneRepo,
        protected readonly LoanDiscountLogRepository $loanDiscountLogRepo,
        protected readonly LoanDiscountLogService $loanDiscountLogService,
        protected readonly LoanDiscountLog $loanDiscountLog,
    ) {
    }

    public function build(LoanDto $loanDto, Client $client): self
    {
        $this->setLoanDto($loanDto)
            ->setDbModel($client)
            ->setClient($client)
            ->setSource()
            ->setOffice()
            ->setStatus()//setOffice
            ->setAdmin($loanDto->administrator_id)//setOffice, setStatus
            ->setProduct()//setOffice
            ->setProductType()//setOffice, setProduct
            ->setPaymentMethodId()//setOffice
            ->setConsultantId()
            ->setAmounts()//setOffice,setPaymentMethod, setActiveLoans

            ->setRefinanceData() // loan_type & amount_rest + prepare loan_refinance for insert

            ->setDiscountPercent($loanDto->discount_percent)//setClient, setProduct
            ->setBankAccountId($loanDto->bank_account_id)//setBankAccountId
            ->setPeriods()
            ->setInterestPercent()//setAmounts,setPeriods,setProduct
            ->setPenaltyPercent()//setAmounts,setPeriods,setProduct
            ->setInstallmentModifier()//setProduct
            ->setGraceDays()
            ->setInstallments()//setInterestPercent,setPenaltyPercent,setDiscountPercent,setInstallmentModifier,amount_approved,period_approved,setProductType,setGraceDays
            ->setApproveTasks()
            ->setComments()
            ->setInsuranceAmount()
            ->save()
            ->saveLoanParamsHistory()
            ->buildProductSetting()
            ->updateInstallments()
            ->saveRefLoans() // insert into loan_refinance

            ->buildRelations()
            ->buildClientOffice()
            ->buildLoanMeta()
            ->closeExistingTasks()
            ->dispatchNewLoanHasCreatedCommon($loanDto);

        if (
            $this->cancelLoanIfClientIsBlocked()
            || $this->cancelLoanIfClientHasBadAge()
            || $this->cancelLoanIfClientHasUnreceivedMoney()
            || $this->cancelLoanIfClientUseIbanOfOther()
        ) {
            return $this;
        }

        return $this
            ->dispatchNewLoanHasArrived($loanDto)
            ->dispatchLoanWasSigned();
    }

    public function buildStub(LoanDto $loanDto, Client $client): DbModel
    {
        $this->dbModel = new DbModel();
        $this->dbModel->loan_id = 1;
        $this->dbModel->created_at = now();

        return $this
            ->setLoanDto($loanDto)
            ->setDbModel($client)
            ->setClient($client)
            ->setOffice()
            ->setStatus()//setOffice
            ->setAdmin($loanDto->administrator_id)//setOffice, setStatus
            ->setProduct()//setOffice
            ->setProductType()//setOffice, setProduct
            ->setPaymentMethodId()//setOffice
            ->setAmounts()//setOffice,setPaymentMethod, setActiveLoans
            ->setLoanType()
            ->setPeriods()
            ->setInterestPercent()//setAmounts,setPeriods,setProduct
            ->setPenaltyPercent()//setAmounts,setPeriods,setProduct
            ->setInstallmentModifier()//setProduct
            ->setGraceDays()->dbModel;
    }

    protected function setLoanDto(LoanDto $loanDto): self
    {
        $this->validateViaProduct($loanDto);

        $this->loanDto = $loanDto;

        return $this;
    }

    protected function setInsuranceAmount(): self
    {
        if (0 === intval($this->loanDto->insurance)) {
            return $this;
        }

        $this->dbModel->insurance_amount = getInsuranceAmount($this->loanDto->loan_sum);

        return $this;
    }

    protected function setDbModel(Client $client): self
    {
        $dbClient = $client->dbModel();
        $clientId = $dbClient->getKey();

        if ($this->repo->clientHasUnprocessedLoans($clientId)) {
            throw new ClientHasUnprocessedLoans(
                $dbClient->getFullName(),
                $dbClient->getAttribute('pin')
            );
        }

        $this->dbModel = new DbModel();

        $this->dbModel->setAttribute('skip_auto_process', $this->loanDto->skip_auto_process);
        $this->dbModel->setAttribute('re_contracted_overdue', $this->loanDto->re_contracted_overdue);
        $this->dbModel->setAttribute('insurance', $this->loanDto->insurance);

        return $this;
    }

    protected function setClient(Client $client): self
    {
        $this->client = $client;
        $dbClient = $client->dbModel();
        $this->dbModel->setAttribute('client_id', $dbClient->getKey());

        /**************** Check to make sure client card hasn't expired **********/
        //not doing this check for loan updates or website requests
        if (!$this->loanDto || $this->loanDto->source === LoanSourceEnum::WEBSITE) {
            return $this;
        }
        $expirationDate = $dbClient->clientLastIdCard()?->valid_date;
        if (!$expirationDate) {
            $expirationDate = $client->mvrReport()?->getData()?->validDate;
        }
        if ($expirationDate) {
            $expirationDate = Carbon::parse($expirationDate);
            if (now()->startOfDay()->gt($expirationDate)) {
                throw new IdCardExpired($expirationDate);
            }
        }

        return $this;
    }

    protected function setSource(): self
    {
        $this->dbModel->source = $this->loanDto->source;

        return $this;
    }

    protected function setOffice(): self
    {
        $officeId = $this->loanDto->office_id;
        $this->office = $this->office->loadById($officeId);
        $this->dbModel->office_id = $this->office->dbModel()->getKey();

        return $this;
    }

    protected function setStatus(): self
    {
        $this->dbModel
            ->setAttribute(
                'loan_status_id',
                $this->dbModel->isOnlineLoan()
                    ? LoanStatus::NEW_STATUS_ID
                    : LoanStatus::SIGNED_STATUS_ID
            );

        return $this;
    }

    protected function setAdmin(?int $adminId): self
    {
        $this->admin->loadById($adminId);
//        $processedBy = $this->dbModel->hasStatus(LoanStatus::NEW_STATUS_ID) ? $adminId : null;
        $this->dbModel
            // ->setAttribute('administrator_id', $processedBy)
            ->setAttribute('last_status_update_administrator_id', $adminId)
            ->setAttribute('last_status_update_date', $this->currentDate->now());
//            ->setAttribute('loan_changed_at', $this->currentDate->now());

        return $this;
    }

    protected function setProduct(): self
    {
        $this->dbModel->product_id = $this->product
            ->loadById($this->loanDto->product_id)
            ->dbModel()
            ->getKey();
        $this->office->checkProduct(
            $this->dbModel->getAttribute('product_id')
        );
        //$this->dbModel->setRelation(DbProduct::class, $this->product->dbModel());

        return $this;
    }

    protected function setProductType(): self
    {
        $this->dbModel->product_type_id =
            $this->product->dbModel()->product_type_id;

        return $this;
    }

    protected function setConsultantId(): self
    {
        if (!empty($this->loanDto->consultant_id)) {
            $this->dbModel->consultant_id = $this->loanDto->consultant_id;
        }

        return $this;
    }

    protected function setPaymentMethodId(): self
    {
        $paymentMethodId = $this->loanDto->payment_method_id;
        if (!DbPaymentMethod::isValid($paymentMethodId)) {
            throw new IncorrectPaymentMethod($paymentMethodId);
        }
        $this->office->checkPaymentMethod($paymentMethodId);
        $this->dbModel->payment_method_id = $paymentMethodId;

        return $this;
    }

    protected function setAmounts(): self
    {
        if (
            $this->fromApprove
            && !empty($this->dbModel->amount_approved)
            && $this->loanDto->loan_sum !== $this->dbModel->amount_approved
        ) {
            $this->dbModel->addMeta('changed_amount_from_approve', 1);
        }

        $loanAmounts = app(PrepareCalculatorDataAction::class)->execute([
            'loanId' => 0,
            'productId' => $this->loanDto->product_id,
            'principle' => intToFloat($this->loanDto->loan_sum),
            'period' => $this->loanDto->loan_period_days,
            'discount' => $this->loanDto->discount_percent,
            'interest' => (!empty($this->loanDto->interest) && $this->loanDto->interest > 0 ? $this->loanDto->interest : null),
            'penalty' => (!empty($this->loanDto->penalty) && $this->loanDto->penalty > 0 ? $this->loanDto->penalty : null),
        ]);

        if ($this->dbModel->payment_method_id === DbPaymentMethod::PAYMENT_METHOD_CASH) {

            $loanSum = $this->loanDto->loan_sum;
            $loanReturnSum = !empty($loanAmounts['totalReturnAmount']) ? floatToInt($loanAmounts['totalReturnAmount']) : 0;

            /// when is calling from LoanForUpdate activeLoans is empty
            /// rebuild it.
            if ($this->client->activeLoans()->isEmpty()) {
                $this->client->buildActiveLoans();
            }

            $existingDebt = $this->client->activeLoans()->getProjectedDebtSum();
            $potentialDebt1 = $loanSum + $existingDebt;
            $potentialDebt2 = $loanReturnSum + $existingDebt;

            // Important, we keep settings as normal numer: 10000
            // But in app, it should be 1000000
            $maxVal = 100 * $this->settingRepository->getSetting(
                SettingsEnum::maximum_loan_sum_cash_common
            )->default_value;

            if ($potentialDebt1 > $maxVal || $potentialDebt2 > $maxVal) {
                throw new ExcessiveLoanAmount($potentialDebt2, $maxVal);
            }
        }

        $this->dbModel->amount_requested = $this->loanDto->loan_sum;
        $this->dbModel->amount_approved = $this->loanDto->loan_sum;

        return $this;
    }

    // amount_rest
    // loan_type_id
    protected function setRefinanceData(): self
    {
        $loansforRefinance = new Collection();
        $dbClient = $this->client->dbModel();
        $dto = $this->loanDto;


        // clear previous refinance state if loan_id exists (means we change loan params)
        if (!empty($this->dbModel->loan_id)) {
            DB::statement("DELETE FROM loan_refinance WHERE refinancing_loan_id = " . $this->dbModel->loan_id);
        }


        // in case of online loan, we take all active
        if ($dto->office_id === DbOffice::OFFICE_ID_WEB) {
            $loansforRefinance = $this->repo->getActiveLoans($dbClient, null);
        } else if (!empty($dto->refinanced_loan_ids)) {
            $loansforRefinance = $this->repo->getActiveLoansWithRelationsByIds(
                $dbClient,
                $dto->refinanced_loan_ids,
                []
            );
        }


        // if no refinance set fields and exit
        if ($loansforRefinance->count() == 0) {
            $this->dbModel->loan_type_id = DBLoanType::LOAN_TYPE_ID_NORMAL;
            $this->dbModel->amount_rest = $this->dbModel->getAmountToPaid();

            return $this;
        }


        $date = now();
        $adminId = getAdminId();
        $totalRefAmount = 0;

        $this->refinanceInsert = [];
        $this->refinancedLoans = [];

        /** @var \Modules\Common\Models\Loan $loanRef * */
        foreach ($loansforRefinance as $loanRef) {

            $loanRef->setRelations([]);
            $this->refinancedLoans[] = $loanRef;

            $earlyRepAmount = $loanRef->getAmountForRefinance(($dto->office_id === DbOffice::OFFICE_ID_WEB));

            $totalRefAmount += $earlyRepAmount;

            $this->refinanceInsert[] = [
                'refinancing_loan_id' => null,
                'refinanced_loan_id' => $loanRef->loan_id,
                'refinanced_due_amount' => $earlyRepAmount,
                'refinancing_date' => $date,
                'active' => 1,
                'created_at' => $date,
                'created_by' => $adminId,
            ];
        }


        // set refiance fields
        $this->dbModel->loan_type_id = DbLoanType::LOAN_TYPE_ID_REFINANCING;
        $this->dbModel->amount_rest = $this->dbModel->amount_approved - $totalRefAmount;

        $productMaxAmount = floatToInt($this->product->dbModel()->getSettingValueByKey(ProductSettingDb::MAX_AMOUNT_KEY));

        if ($productMaxAmount <= $totalRefAmount) {
            throw new AmountForRefinanceIsLargerThanProductMaxAmount(amount($totalRefAmount), amount($productMaxAmount));
        }

        if ($this->dbModel->amount_rest < 0) {
            throw new NewLoanNotCoveringRefinance(
                amount($this->dbModel->amount_approved),
                amount($totalRefAmount),
                amount(getMinRefinanceAmount($totalRefAmount))
            );
        }

        /// for re-contracted loan
        if ($this->dbModel->re_contracted_overdue->isTrue()) {
            return $this;
        }

        if ($this->dbModel->amount_rest < self::MIN_REF_AMOUNT) {
            throw new NewLoanAmountTooSmall(
                $this->dbModel->amount_rest,
                self::MIN_REF_AMOUNT
            );
        }


        return $this;
    }

    public function setLoanType(): self
    {
        $this->dbModel->loan_type_id = $this->refLoans->repaymentSum > 0
            ? DbLoanType::LOAN_TYPE_ID_REFINANCING
            : DBLoanType::LOAN_TYPE_ID_NORMAL;

        return $this;
    }

    protected function setPeriods(): self
    {
        $days = $this->loanDto->loan_period_days;
        if ($days) {
            $this->dbModel->period_requested = $days;
            $this->dbModel->period_approved = $days;
            $this->dbModel->period_grace = null;
            $this->dbModel->period_final = $days;
        }

        return $this;
    }

    protected function setBankAccountId(?int $bankAccountId = null): self
    {
        $this->dbModel->setAttribute('bank_account_id', $bankAccountId);

        return $this;
    }

    protected function setDiscountPercent(int $requestedDiscount): self
    {
        $sources = [];

        $clientDiscountActual = $this->discountByClientRepo->getClientDiscountWithProduct(
            $this->dbModel->client_id,
            $this->dbModel->product_id,
            $this->currentDate->now(),
        );

        $discountByClient = (int) $clientDiscountActual?->percent;

        if ($discountByClient) {
            $validFrom = $clientDiscountActual->valid_from;
            $validTill = $clientDiscountActual->valid_till;
            $sources[] = new DiscountSourceDto(
                DiscountSourceEnum::ClientDiscountActual,
                $clientDiscountActual->getKey(),
                $discountByClient,
                $clientDiscountActual->created_by !== Administrator::SYSTEM_ADMINISTRATOR_ID,
                $validFrom instanceof CarbonInterface ? $validFrom : CarbonImmutable::parse($validFrom),
                $validTill instanceof CarbonInterface ? $validTill : CarbonImmutable::parse($validTill),
            );
        }

        $agentDiscountLimit = $this->admin->getMaxDiscount();

        if ($agentDiscountLimit) {
            $sources[] = new DiscountSourceDto(
                DiscountSourceEnum::Administrator,
                $this->admin->getKey() ?? getAdminId(),
                $agentDiscountLimit,
            );
        }

        $discountByPhone = $this->discountByPhoneRepo->getByPhoneAndProduct(
            $this->client->dbModel()->phone,
            $this->dbModel->product_id,
            ['discount', 'id', 'created_by', 'valid_from', 'valid_until']
        );

        $discountByPhonePercent = (int) $discountByPhone?->discount;

        if ($discountByPhonePercent) {
            $sources[] = new DiscountSourceDto(
                DiscountSourceEnum::DiscountsByPhones,
                $discountByPhone->getKey(),
                $discountByPhonePercent,
                $discountByPhone->created_by !== Administrator::SYSTEM_ADMINISTRATOR_ID,
                $discountByPhone->valid_from,
                $discountByPhone->valid_until,
            );
        }

        // compare passed discount with max of (admin/client discount/phone discount)
        // it's forbidden to extend max
        if ($requestedDiscount > ($max = max($agentDiscountLimit, $discountByClient, $discountByPhonePercent))) {
            throw new DiscountPercentAboveLimit($requestedDiscount, $max);
        }

        // we set this flag, because of using for setting grace period if discount == 100%
        $this->clientDiscountLimit = $requestedDiscount;

        $this->dbModel->discount_percent = $requestedDiscount;

        $this->loanDiscountLogService->fillByRequestedDiscount(
            $this->loanDiscountLog,
            $this->admin->dbModel(),
            $requestedDiscount,
            $sources,
        );

        return $this;
    }

    protected function setInterestPercent(): self
    {
        $interestPercent = $this->loanDto->interest;

        // nullate for normal products
        if ($this->product->dbModel()->isFixed == 1) {
            $interestPercent = null;
        } else {
            // currently on loan params update we do not send interest
            // so we take old percent for now
            if (empty($interestPercent)) {
                $interestPercent = $this->dbModel->interest_percent;
            }
        }

        // since we set it as null for normal products, we recalc it based on amount/period
        if (!$interestPercent) {
            $interestPercent = $this->product->dbModel()->getInterestRate(
                $this->dbModel->period_approved,
                $this->dbModel->amount_approved
            );
        }

        $this->dbModel->setAttribute('interest_percent', $interestPercent);

        return $this;
    }

    protected function setPenaltyPercent(): self
    {
        $penaltyPercent = $this->loanDto->penalty;

        // nullate for normal products
        if ($this->product->dbModel()->isFixed == 1) {
            $penaltyPercent = null;
        } else {
            // currently on loan params update we do not send interest
            // so we take old percent for now
            if (empty($penaltyPercent)) {
                $penaltyPercent = $this->dbModel->penalty_percent;
            }
        }

        // since we set it as null for normal products, we recalc it based on amount/period
        if (!$penaltyPercent) {
            $penaltyPercent = $this->product->dbModel()->getPenaltyRate(
                $this->dbModel->period_approved,
                $this->dbModel->amount_approved
            );
        }

        $this->dbModel->setAttribute('penalty_percent', $penaltyPercent);

        return $this;
    }

    protected function setInstallmentModifier(): self
    {
        $this->dbModel->setAttribute(
            'installment_modifier',
            $this->product->dbModel()->getInstallmentModifier()
        );

        return $this;
    }

    protected function setGraceDays(): self
    {
        // by default, we set it = 0
        $this->dbModel->period_grace = 0;


        // if not 1st loan -> exit
        $prevLoansCount = $this->dbModel->getPreviousLoansCount();
        if ($prevLoansCount > 0) {
            return $this->setGraceBasedOn100PercentDiscount();
        }


        $product = $this->dbModel->product;
        $hasGracePeriod = $product->getSettingValueByKey(ProductSettingDb::GRACE_PERIOD_KEY);

        // if no grace product settings -> exit
        if (!$hasGracePeriod || (int) $hasGracePeriod === 0) {
            return $this->setGraceBasedOn100PercentDiscount();
        }


        $gracePeriodDays = (int)$product->getSettingValueByKey(ProductSettingDb::GRACE_PERIOD_DAYS_KEY);

        // if no grace days from product settings -> exit
        if ($gracePeriodDays < 1) {
            return $this->setGraceBasedOn100PercentDiscount();
        }


        return $this->setGracePeriod(++$gracePeriodDays);
    }

    private function setGraceBasedOn100PercentDiscount(): self
    {
        if ($this->clientDiscountLimit === 100) {
            return $this->setGracePeriod(31);
        }

        return $this;
    }

    private function setGracePeriod(int $days): self
    {
        $this->dbModel->period_grace = $days;
        $this->dbModel->grace_until = $this->currentDate->today()->addDays($days);

        return $this;
    }

    protected function setInstallments(): self
    {
        $this->installments->buildStart($this);
        $instCount = $this->installments->getCalcInstallments()->count();
        $this->dbModel->installments_requested = $instCount;
        $this->dbModel->installments_approved = $instCount;

        return $this;
    }

    /**
     * So by default we set all 2 possible approve task types for loan: indentification & manual approve
     *
     * Since we set flags by default,
     * we need to remove them in case of passed checkpoints.
     *
     * List of checkpoints:
     * 1) by MvrReport we will remove 1st task, or will change it to another if data is not valid
     * 2) if we have all reports and we have set credit limit settings
     *    AND we pass credit limit we will add another flag for it
     * 3) after credit limit we have auto process procedure, if such settings are actvated.
     *   so in case if we auto-processed loan we should remove third task type
     */
    protected function setApproveTasks(): self
    {
        $approve_tasks = $this->getIdentificationActions();

        // this tag will be removed in case of auto approve/disapprove
        $approve_tasks[] = DbModel::APPROVE_TASK_MNA;

        $this->dbModel->approve_tasks = $approve_tasks;
        return $this;
    }

    private function getIdentificationActions(): array
    {
        $actions = [];

        $client = $this->client->dbModel();
        if ($client->isNew()) {
            $actions[] = DbModel::APPROVE_TASK_INC;
        }

        $mvrAction = $this->getxMvrIndentificationAction($client);
        if ($mvrAction) {
            $actions[] = $mvrAction;
        }

        return $actions;
    }

    public function getxMvrIndentificationAction(ClientDbModel $client): ?string
    {
        // if no actual report, return default identification action
        $report = $this->client->mvrReport();
        if (
            empty($report->mvr_report_id)
            || empty($report->data)
            || !$report->isFresh()
        ) {
            return DbModel::APPROVE_TASK_IMNR; // no response
        }


        // sometimes mvr has no info about such person O_o, yep
        // so we have special flag for this case also
        if (empty($report->parsed_data)) {
            return DbModel::APPROVE_TASK_IMWD; // no data
        }


        // if we have parsed data, we could try to check validity of Id card
        $mvrData = json_decode($report->parsed_data);
        if (empty($mvrData->client_idcard->valid_date)) {
            return DbModel::APPROVE_TASK_IMWD; // no data
        }


        // if card is not valid we change action to not valid Id card
        $validDate = Carbon::parse($mvrData->client_idcard->valid_date);
        if ($validDate->isPast()) {
            return DbModel::APPROVE_TASK_IMVD; // invalid valid date
        }


        // everything is fine, we have data, we have valid date inside.
        return null;
    }

    protected function saveLoanParamsHistory(): self
    {
        $this->loanParamsHistoryRepo->createFromLoan($this->dbModel);

        return $this;
    }

    protected function dispatchLoanWasSigned(?SignContractDto $dto = null): static
    {
        LoanWasSigned::dispatchIf(
            $this->dbModel->hasStatus(LoanStatus::SIGNED_STATUS_ID),
            $this->dbModel,
            $dto
        );

        return $this;
    }

    protected function save(bool $changedLoanParams = false): static
    {
        DB::transaction(function () {
            $this->dbModel = $this->repo->save($this->dbModel);
            if (empty($this->dbModel->loan_id)) {
                throw new LoanNotSaved();
            }

            $this->saveLoanDiscountLog();
        });

        $this->actualizeRefinanceSnapshot();

        return $this;
    }

    protected function saveLoanDiscountLog(): void
    {
        if ($this->loanDiscountLog->discount) {
            $this->loanDiscountLog->loan_id = $this->dbModel->getKey();
            $this->loanDiscountLogRepo->save($this->loanDiscountLog);
        }
    }

    protected function dispatchNewLoanHasCreatedCommon(LoanDto $dto): self
    {
        // create loan/client stats
        NewLoanHasCreatedCommon::dispatch($this->dbModel, $dto);

        return $this;
    }

    protected function dispatchNewLoanHasArrived(LoanDto $dto): self
    {
        // close sale tasks
        // generate docs: DOC_PACK_NEW_LOAN -> sends: email, sms
        NewLoanHasArrived::dispatch($this->dbModel, $dto);

        return $this;
    }

    protected function cancelLoanIfClientIsBlocked(): bool
    {
        if ($this->dbModel->client->isBlocked()) {
            $decisionDto = DecisionDto::from([
                'loan_id' => $this->dbModel->getKey(),
                'admin_id' => getAdminId(),
                'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_CLIENT_IS_BLOCKED,
            ]);
            $this->cancelLoanAction->execute($decisionDto);

            return true;
        }

        return false;
    }

    protected function cancelLoanIfClientHasBadAge(): bool
    {
	if (!empty($this->dbModel->office_id) && $this->dbModel->office_id != DbOffice::OFFICE_ID_WEB) {
            return false;
	}

	$client = $this->dbModel->client;

        try {
            $age = (int) $client->getAgeAndSex($client->pin)['age'];
            if (empty($age)) {
                return false;
            }
        } catch (\Throwable $e) {
            return false;
        }

        $min = 18;
        $max = 70;
        if ($age < $min || $age >= $max) {
            $decisionDto = DecisionDto::from([
                'loan_id' => $this->dbModel->getKey(),
                'admin_id' => getAdminId(),
                'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_WRONG_AGE,
            ]);
            $this->cancelLoanAction->execute($decisionDto);

            return true;
        }

        return false;
    }

    private function cancelLoanIfClientHasUnreceivedMoney(): bool
    {
        if (
            ClientCheckerService::hasUnreceivedEasyPayMoney($this->dbModel->client->getKey(), $this->dbModel->office_id)
        ) {
            $decisionDto = DecisionDto::from([
                'loan_id' => $this->dbModel->getKey(),
                'admin_id' => getAdminId(),
                'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_CLIENT_HAS_UNRECEIVED_MONEY,
            ]);
            $this->cancelLoanAction->execute($decisionDto);

            return true;
        }

        return false;
    }

    private function cancelLoanIfClientUseIbanOfOther(): bool
    {
        if (
            empty($this->dbModel->office_id)
            || $this->dbModel->office_id != DbOffice::OFFICE_ID_WEB
        ) {
            return false;
        }

        if (
            empty($this->dbModel->payment_method_id)
            || $this->dbModel->payment_method_id != DbPaymentMethod::PAYMENT_METHOD_BANK
        ) {
            return false;
        }

        $lba = $this->relations()?->bankAccount()?->dbModel();
        if (empty($lba->client_bank_account_id)) {
            return false;
        }

        $ba = $lba->clientBankAccount;
        if (empty($ba->iban)) {
            return false;
        }

        if (
            ClientCheckerService::hasAnotherIbanUsers($this->dbModel->client->getKey(), $ba->iban)
        ) {
            $decisionDto = DecisionDto::from([
                'loan_id' => $this->dbModel->getKey(),
                'admin_id' => 1,
                'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_USE_IBAN_OF_OTHER,
            ]);
            $this->cancelLoanAction->execute($decisionDto);

            return true;
        }

        return false;
    }

    protected function buildRelations(): self
    {
        $this->relations->build($this, $this->client, $this->loanDto);

        return $this;
    }

    protected function saveRefLoans(): self
    {
        if (empty($this->refinanceInsert)) {
            return $this;
        }

        // add loan_id to elements
        foreach ($this->refinanceInsert as &$refinanceData) {
            $refinanceData['refinancing_loan_id'] = $this->dbModel->loan_id;
        }

        // save them in db
        LoanRefinance::insert($this->refinanceInsert);

        return $this;
    }

    protected function buildProductSetting(): self
    {
        $this->productSetting->build($this);
        //$this->dbModel->setRelation(LoanProductSetting::class, $this->productSetting->dbModel());
        $this->dbModel->refresh();
        return $this;
    }

    protected function updateInstallments(): self
    {
        $this->installments->buildFinish();

        return $this;
    }

    protected function buildClientOffice(): self
    {
        $this->clientOffice->build($this);

        return $this;
    }

    protected function buildLoanMeta(): self
    {
        $this->loanMeta->build($this);

        return $this;
    }

    protected function closeExistingTasks(): self
    {
        if (!$this->dbModel->isOnlineLoan()) {
            return $this;
        }

        foreach ($this->dbModel->phones as $phone) {
            $this->task->closeByPhone($phone->number);
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function installments(): InstallmentsInterface
    {
        return $this->installments;
    }

    public function relations(): Relations
    {
        return $this->relations;
    }

    public function validateViaProduct(LoanDto $loanDto)
    {
        if (empty($loanDto->product_id)) {
            throw new \Exception('No product_id in DTO');
        }
        if (empty($loanDto->loan_period_days)) {
            throw new \Exception('No period in DTO');
        }
        if (empty($loanDto->loan_sum)) {
            throw new \Exception('No amount in DTO');
        }

        $product = ProductDb::find($loanDto->product_id);
        if (empty($product->product_id)) {
            throw new \Exception('No product found for new loan request');
        }

        $prService = app(ProductService::class);

        $minAmount = 100 * $prService->getProductSettingByKey($product, ProductSettingDb::MIN_AMOUNT_KEY);
        $maxAmount = 100 * $prService->getProductSettingByKey($product, ProductSettingDb::MAX_AMOUNT_KEY);
        if ($loanDto->loan_sum < $minAmount || $loanDto->loan_sum > $maxAmount) {
            throw new \Exception('Amount is out of product range');
        }


        $minPeriod = $prService->getProductSettingByKey($product, ProductSettingDb::MIN_PERIOD_KEY);
        $maxPeriod = $prService->getProductSettingByKey($product, ProductSettingDb::MAX_PERIOD_KEY);
        if ($loanDto->loan_period_days < $minPeriod || $loanDto->loan_period_days > $maxPeriod) {
            throw new \Exception('Period is out of product range');
        }
    }

    public function actualizeRefinanceSnapshot(): self
    {
        $refinancingLoanId = $this->dbModel->loan_id;

        // clear old snapshots if exists
        DB::statement("DELETE FROM refinance_snapshot WHERE refinancing_loan_id = " . intval($refinancingLoanId));

        // exit if no refinanceed loans
        if (empty($this->refinancedLoans)) {
            return $this;
        }

        // add snapshot for refinanced loans
        foreach ($this->refinancedLoans as $loanRefinanced) {
            $this->saveRefinanceSnapshot($loanRefinanced, $refinancingLoanId);
        }

        return $this;
    }

    // WE NEED THIS EMPTY METHODS because of phpstan!
    protected function updateBankAccount(?string $iban = null): self
    {
        return $this;
    }

    protected function closeSaleTasks(): self
    {
        return $this;
    }

    private function saveRefinanceSnapshot(
        DbModel $loanRefinanced,
        int $refinancingLoanId
    ) {
        // snapshot
        $taxes = $loanRefinanced->getAllTaxes();
        $client = $loanRefinanced->client;
        $loanStats = $loanRefinanced->loanActualStats;
        $clientStats = $client->clientActualStats;
        $installments = $loanRefinanced->getAllInstallments();

        // set snapshot
        $snapshot = new RefinanceSnapshot();
        $snapshot->refinancing_loan_id = $refinancingLoanId;
        $snapshot->refinanced_loan_id = $loanRefinanced->loan_id;
        $snapshot->created_at = now();
        $snapshot->created_by = getAdminId();

        $snapshot->loan = $loanRefinanced->toArray();
        $snapshot->loan_stats = $loanStats->toArray();
        $snapshot->client_stats = $clientStats->toArray();

        $installmentsArray = [];
        foreach ($installments as $installment) {
            $installmentsArray[$installment->installment_id] = $installment->toArray();
        }
        $snapshot->installments = $installmentsArray;

        $taxesArray = [];
        if ($taxes->count() > 0) {
            foreach ($taxes as $tax) {
                $taxesArray[$tax->tax_id] = $tax->toArray();
            }
        }
        $snapshot->taxes = $taxesArray;

        // save and go on
        $snapshot->save();
    }

    private function setComments(): self
    {
        $this->dbModel->comment = $this->loanDto->comment;

        return $this;
    }

    public function setFromApprove(bool $fromApprove = false): self
    {
        $this->fromApprove = $fromApprove;

        return $this;
    }
}
