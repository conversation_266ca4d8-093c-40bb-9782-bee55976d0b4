<?php

namespace Modules\Sales\Repositories;

use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Admin\Traits\AdminTrait;
use Modules\Common\Enums\ClientCardRouteParamEnum;
use Modules\Common\Enums\ProductTypeEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;
use Modules\Common\Repositories\BaseRepository;
use Throwable;

class SaleTaskRepository extends BaseRepository
{
    use AdminTrait;

    protected const CLIENT_SIGNED_BY_IT_SELF = 'sales::saleTask.clientCloseByItself';

    public function getPaginatorByFilters(array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        return $this->getBuilderByFilters($filters)
            ->paginate($perPage)
            ->withQueryString();
    }

    public function getByFilters(array $filters = []): EloquentCollection
    {
        return $this->getBuilderByFilters($filters)->get();
    }

    public function getBuilderByFilters(array $filters = []): Builder
    {
        return SaleTask::filterBy($filters)
            ->ofMyOffices()
            ->with(['client', 'loan', 'saleTaskType', 'processedBy', 'lastAttempt'])
            ->orderByRaw('CASE WHEN sale_task_type_id IN (' . SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_PRODUCT_REQUEST . ', ' . SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_APPLICATION . ') THEN 0 ELSE 1 END')
            ->orderBy('sale_task_id', 'DESC');
    }


    /**
     * @param int $limit [description]
     * @param array $whereConditions [description]
     * @param  [type] $order           [description]
     * @return [type]                  [description]
     * @deprecated
     *
     */
    public function getAll(?int $limit, array $whereConditions, $order = null)
    {
        $whereConditions = $this->addStatusIfNotSelected($whereConditions);

        $order['sale_task_type_sorting_criteria'] = 'ASC';

        $prodGroupIdPayday = ProductTypeEnum::PAYDAY->id();
        $prodGroupIdInstallment = ProductTypeEnum::INSTALLMENT->id();

        $paydayPeriodType = Str::lower(__('head::loanCrud.LoanPayDayPeriodType'));
        $installmentPeriodType = Str::lower(__('head::loanCrud.LoanInstallmentPeriodType'));

        $saleTaskTypeIncompleteApplicationContact = SaleTaskType::SALE_TASK_TYPE_INCOMPLETE_APPLICATION;
        $saleTaskTypeDiscountPercent = SaleTaskType::SALE_TASK_TYPE_DISCOUNT;
        $saleTaskTypeNewInterestFreeLoan = SaleTaskType::SALE_TASK_TYPE_INTEREST_FREE_LOAN;
        $saleTaskTypePreApproved = SaleTaskType::SALE_TASK_TYPE_PRE_APPROVED;
        $saleTaskTypeUnearnedMoney = SaleTaskType::SALE_TASK_TYPE_UNRECEIVED_MONEY;
        $saleTaskTypeIncompleteProductRequest = SaleTaskType::SALE_TASK_TYPE_INCOMPLETE_PRODUCT_REQUEST;
        $saleTaskTypeRequestByPhone = SaleTaskType::SALE_TASK_TYPE_REQUEST_BY_PHONE;

        $builder = DB::table('sale_task');
        $builder->select(
            DB::raw(
                "
                        sale_task.*,
                        sale_task.status AS sale_task_status,
                        sale_task_type.name AS sale_task_type_name,
                        EXTRACT(EPOCH FROM (NOW()::timestamp - sale_task.show_after)) AS time_difference,
                        client.phone AS client_phone,
                        product.name as product_name,
                        ROUND(loan.amount_requested) AS loan_amount_requested,
                        loan.period_requested AS loan_period_requested,
                        loan_type.name AS loan_type_name,
                        loan.loan_status_id as loan_status_id,
                        client.new AS client_new,
                        CASE
                            WHEN product_type.product_type_id = $prodGroupIdPayday THEN '$paydayPeriodType'
                            WHEN product_type.product_type_id = $prodGroupIdInstallment THEN '$installmentPeriodType'
                        END AS days_or_months,
                        CASE
                            WHEN product_type.product_type_id = " . ProductTypeEnum::PAYDAY->id() . " THEN loan.period_requested*1
                            WHEN product_type.product_type_id = " . ProductTypeEnum::INSTALLMENT->id() . " THEN loan.period_requested*30
                        END AS sale_task_period,
                        concat_ws(' ', administrator.first_name, administrator.last_name) as process_by,
                        CASE
                            WHEN sale_task_type.name = '$saleTaskTypeIncompleteApplicationContact' THEN 1
                            WHEN sale_task_type.name = '$saleTaskTypeDiscountPercent' THEN 2
                            WHEN sale_task_type.name = '$saleTaskTypeNewInterestFreeLoan' THEN 3
                            WHEN sale_task_type.name = '$saleTaskTypePreApproved' THEN 4
                            WHEN sale_task_type.name = '$saleTaskTypeUnearnedMoney' THEN 5
                            WHEN sale_task_type.name = '$saleTaskTypeIncompleteProductRequest' THEN 6
                            WHEN sale_task_type.name = '$saleTaskTypeRequestByPhone' THEN 7
                            ELSE 8
                        END AS sale_task_type_sorting_criteria
                    "
            )
        );
        $builder->join('sale_task_type', 'sale_task_type.sale_task_type_id', '=', 'sale_task.sale_task_type_id');
        $builder->join('client', 'client.client_id', '=', 'sale_task.client_id');
        $builder->leftJoin('loan', 'loan.loan_id', '=', 'sale_task.loan_id');
        $builder->leftJoin('product', 'product.product_id', '=', 'loan.product_id');
        $builder->leftJoin('loan_type', 'loan_type.loan_type_id', '=', 'loan.loan_type_id');
        $builder->leftJoin('product_type', 'product_type.product_type_id', '=', 'loan.product_type_id');
        $builder->leftJoin('administrator', 'administrator.administrator_id', '=', 'sale_task.processed_by');
        $builder->leftJoin(
            'sale_attempt',
            function ($join) {
                $join->on('sale_attempt.sale_task_id', '=', 'sale_task.sale_task_id')
                    ->where('sale_attempt.last', '=', 1);
            }
        );
        $builder->where($whereConditions);
        $builder->where(
            function ($q) {
                $q->whereNull('sale_attempt.sale_attempt_id');
                $q->orWhere('sale_attempt.skip_till', '<=', Carbon::now());
                $q->orWhereNull('sale_attempt.skip_till');
            }
        );


        // add order if provided
        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }
        if (empty($limit)) {
            $records = $builder->get();
            $result = SaleTask::hydrate($records->toArray());
        } else {
            $result = $builder->paginate($limit);
            $records = SaleTask::hydrate($result->all());
            $result->setCollection($records);
        }

        return $result;
    }

    /**
     * @param array $whereConditions
     *
     * @return array
     */
    private function addStatusIfNotSelected(array $whereConditions): array
    {
        $hasStatusFilter = false;

        foreach ($whereConditions as $index => $conditionArr) {
            if ($conditionArr[0] === 'sale_task.status') {
                $hasStatusFilter = true;
                break;
            }
        }

        if (!$hasStatusFilter) {
            $whereConditions[] = [
                'sale_task.status',
                '!=',
                SaleTask::SALE_TASK_STATUS_DONE,
            ];
        }

        return $whereConditions;
    }

    /**
     * @return mixed
     */
    public function getSaleTaskTypes()
    {
        return SaleTaskType::where(
            [
                'deleted' => 0,
                'active' => 1,
            ]
        )->get();
    }

    /**
     * @return array
     */
    public function getStatuses(): array
    {
        return [
            SaleTask::SALE_TASK_STATUS_NEW,
            SaleTask::SALE_TASK_STATUS_PROCESSING,
            SaleTask::SALE_TASK_STATUS_DONE,
        ];
    }

    public function getById(int $saleTaskId): ?SaleTask
    {
        return SaleTask::where('sale_task_id', $saleTaskId)->first();
    }

    /**
     * @param int $clientId
     *
     * @return array
     */
    public function getOpenTasks(int $clientId, int $limit = 5, bool $showAll = false): array
    {
        $statusNew = SaleTask::SALE_TASK_STATUS_NEW;
        $statusProcessing = SaleTask::SALE_TASK_STATUS_PROCESSING;
        $comeFrom = ClientCardRouteParamEnum::SALES->value;

        return DB::select(
            DB::raw(
                "
                    SELECT
                        st.*,
                        '$comeFrom' AS come_from,
                        COALESCE(sa.sale_decision_id, sa2.sale_decision_id) AS last_decision_id
                    FROM sale_task AS st
                    LEFT JOIN sale_attempt AS sa ON st.sale_task_id = sa.sale_task_id
                    LEFT JOIN sale_attempt AS sa2 ON st.parent_task_id = sa2.sale_task_id
                    WHERE (st.status = '$statusNew' OR  st.status = '$statusProcessing')
                        AND client_id = '$clientId'
                    ORDER BY created_at
                    LIMIT '$limit'
                "
            )
        );
    }

    /**
     * @return array
     * @throws Exception
     */
    public function getStatsInfo(): array
    {
        $stats = (new \Modules\Common\Statistics\Dashboard\SaleTask())->getStatsByUser(getAdmin(), now());

        return $stats->toArray();
    }

    public function save(SaleTask $saleTask): ?SaleTask
    {
        return $saleTask->save() ? $saleTask : null;
    }

    public function delete(SaleTask $dbModel)
    {
        $dbModel->delete();
    }

    /**
     * @param Loan $loan
     *
     * @return Collection
     */
    public function getUndoneByLoan(Loan $loan): Collection
    {
        return SaleTask::where(
            [
                ['loan_id', '=', $loan->getKey()],
                ['status', '!=', SaleTask::SALE_TASK_STATUS_DONE],
            ]
        )->get();
    }

    /**
     * @param SaleTask $saleTask
     *
     * @return SaleTask
     */
    public function systemClose(SaleTask $saleTask): SaleTask
    {
        $saleTask->status = SaleTask::SALE_TASK_STATUS_DONE;
        $saleTask->processed_at = Carbon::now();
        $saleTask->processed_by = Administrator::SYSTEM_ADMINISTRATOR_ID;
        $saleTask->details = __(self::CLIENT_SIGNED_BY_IT_SELF);
        $saleTask->save();

        return $saleTask;
    }

    /**
     * @param SaleTask $saleTask
     *
     * @return bool
     * @throws Throwable
     */
    public function processSaleTask(SaleTask $saleTask): bool
    {
        /// refresh database data
        $saleTask->refresh();
        if ($saleTask->isStatusProcessing() && ($saleTask->processed_by == getAdminId())) {
            return true;
        }

        if ($saleTask->isStatusProcessing() && ($saleTask->processed_by != getAdminId())) {
            return false;
        }

        DB::beginTransaction();
//        $saleTask = $this->getLockedSaleTask($saleTask);
        $saleTask->status = SaleTask::SALE_TASK_STATUS_PROCESSING;
        $saleTask->last_status_update_date = Carbon::now();
        $saleTask->processed_at = Carbon::now();
        $saleTask->processed_by = getAdminId();
        $saleTask->updated_at = Carbon::now();
        $saleTask->updated_by = getAdminId();

        if ($saleTask->save()) {
            DB::commit();

            return true;
        } else {
            DB::rollBack();

            return false;
        }
    }

    /**
     * @param SaleTask $saleTask
     * @param array $data
     *
     * @return SaleTask|false
     */
    public function update(SaleTask $saleTask, array $data)
    {
        if (!$data) {
            return false;
        }

        $saleTask->fill($data);
        $saleTask->save();

        return $saleTask;
    }

    /**
     * @param array $data
     *
     * @return SaleTask
     */
    public function create(array $data): SaleTask
    {
        $saleTask = new SaleTask();
        $saleTask->fill($data);
        $saleTask->save();

        return $saleTask;
    }

    /**
     * @param array $criteria
     * @param int $productId
     * @return Collection|null
     */
    public function getByCriteriaWithProductId(array $criteria, int $productId)
    {
        $collection = SaleTask::where($criteria)
            ->whereJsonContains('product_ids', $productId)
            ->orderByDesc('sale_task_id')
            ->get();

        if ($collection->count() === 0) {
            return null;
        }

        return $collection;
    }

    public function getDiscountTaskByClientAndProduct(int $clientId, int $productId): ?SaleTask
    {
        return SaleTask::where([
            'client_id' => $clientId,
            'sale_task_type_id' => SaleTaskType::SALE_TASK_TYPE_ID_DISCOUNT_PERCENT,
            'active' => 1
        ])->whereJsonContains('product_ids', $productId)
            ->orderByDesc('sale_task_id')
            ->first();
    }

    /**
     * @param array $criteria
     *
     * @return Collection|null
     */
    public function getByCriteria(array $criteria)
    {
        $collection = SaleTask::where($criteria)
            ->orderByDesc('sale_task_id')
            ->get();

        if ($collection->count() === 0) {
            return null;
        }

        return $collection;
    }

    /**
     * @param array $multipleRecordsData
     *
     * @return int
     */
    public function bulkCreate(array $multipleRecordsData): int
    {
        $areCreated = SaleTask::insert($multipleRecordsData);

        return count($multipleRecordsData);
    }

    public function getLockedSaleTask(SaleTask $saleTask): SaleTask
    {
        return SaleTask::where('sale_task_id', $saleTask->getKey())->lockForUpdate()->first();
    }

    public function deactivateSaleTasks(int $clientId): bool
    {
        $updatedAt = now();
        $updatedBy = Administrator::SYSTEM_ADMINISTRATOR_ID;

        return DB::statement(
            "
            UPDATE sale_task st
            SET active = 0,
                deleted = 1,
                updated_at = '$updatedAt',
                updated_by = $updatedBy
            WHERE st.client_id = $clientId
                AND st.active != 0
                AND st.deleted != 1
        "
        );
    }

    public function closeNewTasksByPhone(string $phone)
    {
        $tasks = SaleTask::query()
            ->where('phone', $phone)
            ->where('status', SaleTask::SALE_TASK_STATUS_NEW)
            ->pluck('sale_task_id');

        SaleTask::query()->whereIn('sale_task_id', $tasks)->update([
            'status' => SaleTask::SALE_TASK_STATUS_DONE,
            'processed_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            'processed_at' => now(),
        ]);
    }

    public function closeTaskById(int $taskId)
    {
        SaleTask::query()->whereIn('sale_task_id', [$taskId])->update([
            'status' => SaleTask::SALE_TASK_STATUS_DONE,
            'processed_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            'processed_at' => now(),
        ]);
    }

    public function closeTaskByLoan(Loan $loan): void
    {
        SaleTask::query()
            ->where('client_id', $loan->client_id)
            ->where('loan_id', $loan->getKey())
            ->where('status', '!=', SaleTask::SALE_TASK_STATUS_DONE)
            ->update([
                'status' => SaleTask::SALE_TASK_STATUS_DONE,
                'processed_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'processed_at' => now(),
            ]);
    }

    public function closeTaskByPhone(string $phone): void
    {
        SaleTask::query()
            ->where('phone', $phone)
            ->where('status', '!=', SaleTask::SALE_TASK_STATUS_DONE)
            ->update([
                'status' => SaleTask::SALE_TASK_STATUS_DONE,
                'processed_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'processed_at' => now(),
            ]);
    }
}
