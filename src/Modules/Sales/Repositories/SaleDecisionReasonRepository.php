<?php

namespace Modules\Sales\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Repositories\BaseRepository;

class SaleDecisionReasonRepository extends BaseRepository
{
    public function getSaleDecisions(): Collection
    {
        return SaleDecision::where([
            'deleted' => 0,
            'active' => 1,
        ])->where(
            'sale_decision_id', '!=', SaleDecision::SALE_DECISION_ID_TO_PRODUCT_APP_SUB_BY_AGENT
        )->get();
    }
}
