<?php

namespace Modules\Sales\Repositories;

use Modules\Common\Models\SaleDecision;
use Modules\Common\Repositories\BaseRepository;

class SaleDecisionRepository extends BaseRepository
{
    public function getById(int $id)
    {
        $saleDecision = SaleDecision::where(
            'sale_decision_id',
            '=',
            $id
        )->get();

        return $saleDecision->first();
    }
}
