<?php

namespace Modules\Sales\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Models\SaleDecisionReason;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The module namespace to assume when generating URLs to actions.
     *
     * @var string
     */
    protected string $moduleNamespace = 'Modules\Sales\Http\Controllers';

    /**
     * Called before routes are registered.
     *
     * Register any model bindings or pattern based filters.
     *
     * @return void
     */
    public function boot(): void
    {
        parent::boot();

        Route::model('saleDecision', SaleDecision::class);
        Route::model('saleDecisionReason', SaleDecisionReason::class);
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map(): void
    {
        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes(): void
    {
        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Sales', '/Routes/web.php'));

        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Sales', '/Routes/saleTask.php'));

        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Sales', '/Routes/newApp.php'));

        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->name('sales.')
            ->group(module_path('Sales', '/Routes/refinanceSlider.php'));
    }
}
