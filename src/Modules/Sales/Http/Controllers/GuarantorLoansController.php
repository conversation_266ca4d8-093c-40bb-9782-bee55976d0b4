<?php

namespace Modules\Sales\Http\Controllers;

use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\GuarantRepository;
use Modules\Sales\Http\Requests\GuarantorLoansRequest;

class GuarantorLoansController extends BaseController
{

    public function loadGuarantorLoans(GuarantorLoansRequest $request): View
    {
        $pins = $request->validated('guarantor_pin');

        $guarantors = app(GuarantRepository::class)->getGuarantorByPins($pins);

        $loans = collect([]);

        /** @var \Modules\Common\Models\Guarant $guarantor * */
        foreach ($guarantors as $guarantor) {
            $loans[$guarantor->pin] = $guarantor
                ->loansActual()
                ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
                ->get();
        }

        $data['loans'] = $loans;

        return view(
            'sales::guarantor-loans.load-guarantor-loans',
            $data
        );
    }
}
