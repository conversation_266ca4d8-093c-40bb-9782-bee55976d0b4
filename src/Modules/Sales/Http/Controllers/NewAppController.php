<?php

namespace Modules\Sales\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Enums\LoanTypeEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Client;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Common\Models\SaleTask;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\GuarantRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\LoanService;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\PrepareNewLoanDataAction;
use Modules\Sales\Application\Actions\RefinanceSlider\RefinanceSliderDataAction;
use Modules\Sales\Domain\Exceptions\ExcessiveLoanAmount;
use Modules\Sales\Exceptions\AmountForRefinanceIsLargerThanLoanAmount;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Modules\Sales\Http\Requests\ApplicationSaveRequest;
use Modules\Sales\Http\Requests\CheckClientFromMvrRequest;
use Modules\Sales\Http\Requests\LegalEntityAppSaveRequest;
use Modules\Sales\Http\Requests\LoadGuarantorRequest;
use Modules\Sales\Http\Requests\NewAppLoadClientRequest;
use Modules\Sales\Services\RawRequestService;
use Modules\ThirdParty\Services\MvrService;
use StikCredit\Calculators\Calculator;
use Throwable;

final class NewAppController extends BaseController
{
    public function getGuarantorDataByPin(
        LoadGuarantorRequest $request,
        GuarantRepository $guarantRepository
    ): JsonResponse {
        $data = $request->validated();

        $guarantor = $guarantRepository->getGuarantByPin($data['pin']);

        // emulate guarant with client data
        if (empty($guarantor->guarant_id)) {
            $client = Client::where('pin', $data['pin'])->first();

            $idCard = $client->clientLastIdCard();
            $address = $client->clientLastAddressIdcard();

            $guarantor = new Guarant();
            $guarantor->pin = $client->pin;
            $guarantor->idcard_number = $client->idcard_number;
            $guarantor->first_name = $client->first_name;
            $guarantor->middle_name = $client->middle_name;
            $guarantor->last_name = $client->last_name;
            $guarantor->phone = $client->phone;
            $guarantor->idcard_issued_id = $idCard->idcard_issued_id;
            $guarantor->idcard_issue_date = $idCard->issue_date;
            $guarantor->idcard_valid_date = $idCard->valid_date;
            $guarantor->address = $address->address;
        }

        return response()->json([
            'guarantor' => $guarantor->mergeCasts([
                'idcard_issue_date' => 'date:d.m.Y',
                'idcard_valid_date' => 'date:d.m.Y',
            ])
        ]);
    }

    public function setOfficeId(Request $request): JsonResponse
    {
        $data = $request->all();
        $this->setTempSliderData($data['loan']);

        $office = Office::whereOfficeId($data['loan']['office_id'])->first();
        Session::put([
            'currentOfficeId' => $data['loan']['office_id'],
            'isWebOffice' => $office->isWeb()
        ]);
        Session::put('tempFormData', $data);
        Session::put('newLoanType', $data['newLoanType']);

        return response()->json($data);
    }

    public function newAppFromSaleTask(
        NewAppLoadClientRequest $loadClientRequest,
        SaleTask $saleTask,
        PrepareNewLoanDataAction $prepareFormAction
    ): View {
        if (!session('newLoanType', false)) {
            session()->put('newLoanType', LoanTypeEnum::Individual->value);
        }
        $type = session('newLoanType');

        //// load slider data from tmp-request
        $tmpRequest = $saleTask->tmpRequest;
        if ($tmpRequest) {
            $this->setTempSliderData([
                'product_id' => $tmpRequest->product_id,
                'loan_period' => $tmpRequest->period_requested,
                'loan_sum' => $tmpRequest->amount_requested,
                'discount_percent' => 0,
            ]);
        }

        $data = $prepareFormAction->getFormFromSaleTask(
            $saleTask,
            $loadClientRequest->validated()
        );

        return view('sales::new-application.create-' . $type, $data);
    }

    public function newApplicationReset(): RedirectResponse
    {
        $this->clearSessionTempData();

        return redirect()->route('sales.newApplication');
    }

    public function newApplication(
        NewAppLoadClientRequest $request,
        PrepareNewLoanDataAction $prepareFormAction
    ): View {
        $newLoanTypeSessionKey = 'newLoanType';

        $input = $request->all();
        $newLoanTypeFromReq = $request->validated($newLoanTypeSessionKey);
        $newLoanType = session($newLoanTypeSessionKey);

        /// if is not set loan type set it by default to: individual
        if (!$newLoanType) {
            $newLoanType = LoanTypeEnum::Individual->value;
            Session::put($newLoanTypeSessionKey, $newLoanType);
        }

        //// when we change loan type save form data to session
        if ($newLoanTypeFromReq && $newLoanType !== $newLoanTypeFromReq) {
            Session::put($newLoanTypeSessionKey, $newLoanTypeFromReq);
            $newLoanType = $newLoanTypeFromReq;
        }

        if (isset($input['loan'], $input['client'], $input['client_idcard'])) {
            Session::put('tempFormData', $input);
            $this->setTempSliderData($request->get('loan', []));
        }

        return view(
            'sales::new-application.create-' . $newLoanType,
            $prepareFormAction->getFormData($request->validated()),
        );
    }

    public function storeNewApplication(
        ApplicationSaveRequest $request,
        DtoSerializerNewClientLoan $dtoMaker,
        NewAppAction $newLoanAction,
        RawRequestService $rawRequestService,
    ): JsonResponse {
        $rawRequestService->createRequest(['requestString' => $request->asLoggableJson()]);

        $data = $request->validated();
        $data['loan']['loan_sum'] = floatToInt($data['loan']['loan_sum']);
        $this->setTempSliderData($data['loan']);

        try {
            /// checks for re-contracted loan amount
            if (
                1 === intval($request->validated('loan.re_contracted_overdue')) &&
                null === $request->validated('confirmed')
            ) {
                $refLoanIds = $request->validated('refinanced_loans');
                $refinanceAmount = app(RefinanceSliderDataAction::class)->getRefinanceAmount(
                    Loan::whereIn('loan_id', $refLoanIds)->get(),
                    true
                );

                $refinanceAmount = floatval(intToFloat($refinanceAmount));
                $requestedAmount = floatval($request->validated('loan.loan_sum'));


                /// тук има стандартна проверка и няма нужда да се прави нова
                // Ако е по-малка, няма как да продължи напред.

                /// тук ще мине по стандартния начин като е избран чекбокса няма проверка за сума.
                // Ако е равна, продължава без напомняне.

                // Ако е по-голяма, да има попъп с текст Бутони: Напред/Откажи.
                if ($requestedAmount > $refinanceAmount) {
                    throw new AmountForRefinanceIsLargerThanLoanAmount();
                }
            }

            /// check if client is deleted
            if (app(ClientRepository::class)->clientIsDeleted($request->validated('client_idcard.pin'))) {
                throw  new \RuntimeException('Kлиент е заличен от базата.');
            }


            DB::transaction(function () use ($dtoMaker, $data, $newLoanAction, $rawRequestService) {
                $mvrService = app(MvrService::class);

                //// validate client names
                $mvrService->validateClientNames($data);

                $dto = $dtoMaker->createClientDto($data);
                $clientDomain = $newLoanAction->execute($dto);

                $loan = $clientDomain->dbLoan();
                $client = $clientDomain->dbModel();


                $mvrService->updateRelations(
                    $client,
                    $loan->loan_id
                );

                $rawRequestService->updateResponseWithClientAndLoan($client, $loan);
            });
        } catch (Throwable $e) {
            $rawRequestService->saveResponse($e);
            Log::channel('new-app')->debug(json_encode($data));

            if ($e instanceof ExcessiveLoanAmount) {
                return response()->json([
                    'success' => false,
                    'modalSelector' => '#modalInflatedLoanAmount',
                    'data' => $data,
                    'msg' => $e->getMessage(),
                ]);
            }

            if ($e instanceof AmountForRefinanceIsLargerThanLoanAmount) {
                return response()->json([
                    'success' => false,
                    'modalSelector' => '#modalForContinueReContractedLoans',
                    'data' => $data,
                    'message' => $e->getMessage(),
                ]);
            }

            $message = method_exists($e, 'getFrontEndMessage') ? $e->getFrontEndMessage() : $e->getMessage();

            return response()->json([
                'success' => false,
                'message' => $message,
                'modalSelector' => '#errorModal',
                'data' => $data,
            ]);
        }

        $this->clearSessionTempData();

        $resp = $this->getNewApplicationResponse($newLoanAction->getDbLoan());

        return response()->json($resp);
    }

    public function storeNewCompanyApplication(
        LegalEntityAppSaveRequest $request,
        DtoSerializerNewClientLoan $dtoMaker,
        NewAppAction $newLoanAction,
        RawRequestService $rawRequestService
    ): JsonResponse {
        $rawRequestService->createRequest(['requestString' => $request->asLoggableJson()]);
        $data = $request->validated();
        $data['loan']['loan_sum'] = floatToInt($data['loan']['loan_sum']);
        $data['loan']['juridical'] = 1;
        $data['client']['legal_status'] = Client::LS_COMP;
        $data['client']['phone'] = $data['representor']['representor_phone'];
        $data['client']['email'] = $data['representor']['representor_email'];
        $this->setTempSliderData($data['loan']);

        try {
            $pin = $data['client_idcard']['pin'];
            $client = Client::where('legal_status', Client::LS_INDV)
                ->where('pin', $pin)
                ->first();

            /// if this client already registered like individual
            if ($client) {
                throw new \Exception(__('messages.ErrorCreatingCompanyLoanForIndividualClient', ['pin' => $pin]));
            }

            DB::transaction(function () use ($newLoanAction, $dtoMaker, $data, $rawRequestService) {
                $client = $newLoanAction->execute(
                    $dtoMaker->createClientDto($data)
                );

                $rawRequestService->updateResponseWithClientAndLoan($client->dbModel(), $client->dbLoan());
            });
        } catch (Throwable $e) {
            $rawRequestService->saveResponse($e);
            Log::channel('new-app')->debug(json_encode($data));

            if ($e instanceof ExcessiveLoanAmount) {
                return response()->json([
                    'success' => false,
                    'modalSelector' => '#modalInflatedLoanAmount',
                    'data' => $data,
                    'msg' => $e->getMessage(),
                ]);
            }

            $message = method_exists($e, 'getFrontEndMessage') ? $e->getFrontEndMessage() : $e->getMessage();

            return response()->json([
                'success' => false,
                'message' => $message,
                'modalSelector' => '#errorModal',
                'data' => $data,
            ]);
        }

        $this->clearSessionTempData();
        $resp = $this->getNewApplicationResponse($newLoanAction->getDbLoan());

        return response()->json($resp);
    }

    public function setTempSliderData(array $data): void
    {
        Session::put([
            'tempSliderData' => [
                'product_id' => $data['product_id'] ?? 0,
                'loan_period' => $data['loan_period'] ?? 0,
                'loan_sum' => floatToInt($data['loan_sum'] ?? 0),
                'discount_percent' => $data['discount_percent'] ?? 0,
                'interest' => $data['interest'] ?? 0,
                'penalty' => $data['penalty'] ?? 0,
            ]
        ]);
    }

    private function clearSessionTempData(): void
    {
        /// clear session temp data
        Session::remove('tempSliderData');
        Session::remove('tempFormData');
    }

    /**
     * MVR check by client PIN
     * @param CheckClientFromMvrRequest $request
     * @return string
     */
    public function getClientDataFromMvrAjax(
        CheckClientFromMvrRequest $request
    ): JsonResponse {
        $reqData = $request->validated();

        $service = app(MvrService::class);
        $data = $service->getClientData(
            $reqData['pin'],
            $reqData['idcard_number'],
            'new-application'
        );

        foreach (['issue_date', 'valid_date'] as $dateField) {
            if (!empty($data['client_idcard'][$dateField]) && strpos($data['client_idcard'][$dateField], '-')) {
                $data['client_idcard'][$dateField] = Carbon::parse($data['client_idcard'][$dateField])->format('d.m.Y');
            }
        }

        if (
            !empty($data['client_address']['city_id'])
            && !empty($data['client_address']['address'])
        ) {
            //// this case use only in client-card when we edit client data
            $client = app(ClientRepository::class)->getByPin($reqData['pin']);
            if (!empty($client->client_id)) {
                $currentAddresses = app(ClientAddressRepository::class)->getLatestByClientIdAndType(
                    $client->client_id,
                    AddressTypeEnum::Current->value
                );

                /// if is match set default same address
                $data['client_idcard']['address_is_match'] = 'yes';
                $data['client_address']['current_city_id'] = $data['client_address']['city_id'];
                $data['client_address']['current_address'] = $data['client_address']['address'];

                if ($currentAddresses->address_is_match === 'no') {
                    $data['client_idcard']['address_is_match'] = 'no';
                    $data['client_address']['current_city_id'] = $currentAddresses->city_id;
                    $data['client_address']['current_address'] = $currentAddresses->address;
                }
            }
        }

        return response()->json($data);
    }

    // used for refinance
    public function getClientActiveLoans(Client $client, $loanId = null): JsonResponse
    {
        if (!is_numeric($loanId)) {
            $loanId = null;
        }

        /** @var LoanRepository $loanRrepo */
        $loanRrepo = app(LoanRepository::class);
        $loans = $loanRrepo->getActiveLoansWithRelationsByClientIds([$client->client_id]);

        // check for checked options for refinance
        $refiancedLoans = [];
        if (!empty($loanId)) {
            $refiancedLoans = $loanRrepo->getRefinancedById($loanId);
        }

        /** @var LoanService $loanService */
        $loanService = app(LoanService::class);
        $loans = $loanService->prepareLoansForRefinance($loans, $refiancedLoans);

        $client = $client->toArray();

        return response()->json(compact('loans', 'client'));
    }

    // recalc amount after refinanced due substraction
    public function getRestAfterRefinance(Request $request): JsonResponse
    {
        $loanAmount = (float) $request['loanAmount'];
        $refiancedLoanIds = is_array($request['refiancedLoanIds']) ? $request['refiancedLoanIds'] : [];
        $repo = app()->make(LoanRepository::class);
        $amount = 0;
        /** @var Loan $reLoan */
        foreach ($repo->getByIds($refiancedLoanIds) as $reLoan) {
            $amount = Calculator::sum($amount, $reLoan->getEarlyRepaymentDebtDb());
        }

        return Response::json(
            ['amount' => Calculator::sub($loanAmount, $amount)],
            self::HTTP_OK
        );
    }

    private function getNewApplicationResponse(Loan $loan): array
    {
        // default url
        $response = [];
        $response['success'] = true;
        $response['modalSelector'] = '#newAppOnlineOffice';


        $loan->refresh();
        if ($loan->isCanceled()) {
            $response['success'] = false;
            $response['modalSelector'] = '#errorModal';
            $response['href'] = route('sales.saleTask.list');

            $response['message'] = 'Заем е отказан. Неизвестна причина.';

            $lastApproveAttempt = $loan->approveAttempts()->orderBy('approve_attempt_id', 'DESC')->first();
            $lastApproveDecisionReason = $lastApproveAttempt?->approveDecisionReason;
            if (!empty($lastApproveDecisionReason->description)) {
                $response['message'] = 'Заем е отказан. ' . $lastApproveDecisionReason->description;
            }

            return $response;
        }

        if ($loan->office_id == Office::OFFICE_ID_WEB) {
            $response['modalSelector'] = '#newAppOnlineOffice';
            $response['href'] = route('sales.saleTask.list');
            $response['officeId'] = Office::OFFICE_ID_WEB;

            return $response;
        }

        if ($loan->loan_status_id == LoanStatus::SIGNED_STATUS_ID) {
            $response['loanId'] = $loan->getKey();
            if ($loan->office?->isSelfApproved()) {
                $response['modalSelector'] = '';
                $response['printDocs'] = route('head.clientCard.printDocuments', [
                    'printFlag' => DocumentTemplate::DOC_PACK_NEW_APP,
                    'loan' => $loan->getKey()
                ]);
                $response['href'] = route('approve.loan-decision.process', $loan->getKey());
            } else {
                $response['modalSelector'] = '';
                $response['printDocs'] = route('head.clientCard.printDocuments', [
                    'printFlag' => DocumentTemplate::DOC_PACK_NEW_APP,
                    'loan' => $loan->getKey()
                ]);
                $response['href'] = route('head.client-with-loan.index', [
                    $loan->client_id,
                    $loan->loan_id
                ]);
            }
        }

        return $response;
    }
}
