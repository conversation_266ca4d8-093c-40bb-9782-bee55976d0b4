<?php

namespace Modules\Sales\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\View\View;
use Modules\Admin\Services\OfficeService;
use Modules\Api\Database\Entities\ClientSession;
use Modules\Common\CommandBus\CommandBus;
use Modules\Common\Exceptions\JsonException;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Client;
use Modules\Head\Application\Actions\ClientDataAction;
use Modules\Head\Application\Actions\ClientExportAction;
use Modules\Head\Forms\ClientCreateForm;
use Modules\Head\Forms\ClientEditForm;
use Modules\Head\Http\Middleware\AgentOfficeAuthorization;
use Modules\Head\Http\Middleware\ClientDtoSerializer;
use Modules\Head\Http\Requests\ClientAjaxOfficeConsultantRequest;
use Modules\Head\Http\Requests\ClientCrudRequest;
use Modules\Head\Http\Requests\ClientFilterRequest;
use Modules\Head\Http\Requests\GuarantAjaxPinRequest;
use Modules\Head\Http\Requests\PrefillOfficesConsultantRequest;
use Modules\Head\Repositories\ClientEmailRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\CityService;
use Modules\Head\Services\ClientIdCardService;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\ContactTypeService;
use Modules\Head\Services\GuarantTypeService;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\SaveEditedClientDataAction;
use Throwable;

final class ClientController extends BaseController
{
    public string $pageTitle = 'Client list';
    public string $indexRoute = 'head.clients.list';

    public function __construct(
        private ClientService $clientService,
        private CityService $cityService,
        private GuarantTypeService $guarantTypeService,
        private ContactTypeService $contactTypeService,
        private OfficeService $officeService,
        private ClientIdCardService $clientIdCardService,
        private AgentOfficeAuthorization $agentOfficeAuthorization,
        private CommandBus $bus
    ) {
        parent::__construct();
    }

    public function list(
        ClientFilterRequest $request,
        ClientDataAction $clientDataAction
    ) {
        $perPage = $this->getPaginationLimit();
        $filters = $request->validated();
        $data = $clientDataAction->execute($filters, $perPage);

        return view('head::client.list', $data);
    }

    public function export(ClientFilterRequest $request, ClientExportAction $action)
    {
        $export = $action->execute($request->validated());
        $fileName = 'clients_export_' . time() . '.csv';
        header('Content-type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename=' . $fileName);
        header("Pragma: no-cache"); // optional
        header("Expires: 0"); // optional

        $fp = fopen('php://output', 'w');
        // Add BOM for UTF-8
        fprintf($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));

        fputcsv($fp, $export->headings());
        $export->builder->chunk(100, function ($clients) use ($fp, $export) {
            foreach ($clients as $client) {
                fputcsv($fp, $export->formatRow($client));
            }
        });

        fclose($fp);
        die();
    }

    public function historyPaginate(int $id)
    {
        return $this->clientService->getClientById($id)->clientHistory()->paginate(10);
    }

    /**
     * It is not used in fact. The operation is not allowed
     */
    public function create(ClientCreateForm $clientCreateForm)
    {
        $this->setPageTitle('Create client');

        return view(
            'head::client.crud',
            $clientCreateForm->getData()
        );
    }

    public function store(
        ClientCrudRequest $request,
        ClientDtoSerializer $dtoMaker,
        NewAppAction $newLoanAction
    ): RedirectResponse {
        $newLoanAction->execute(
            $dtoMaker->createClientDto(
                $request->validated()
            )
        );

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('head::clientCrud.clientCreatedSuccessfully')
            );
    }

    public function refresh(ClientFilterRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'head::client.list-table',
            [
                'clients' => $this->getDataTable(),
            ]
        )->render();
    }

    public function getDataTable(int $limit = null)
    {
        return $this->clientService->getByFilters(
            $limit ?? parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * It is not used in fact. The operation is not allowed
     */
    public function edit(Client $client, ClientEditForm $form)
    {
        if ($this->agentOfficeAuthorization->check($client)) {
            return view(
                'head::client.crud',
                $form->getData($client)
            );
        }

        return redirect()
            ->route($this->indexRoute)
            ->with('fail', __('head::clientCrud.clientIsNotFromYourOffice'));
    }

    public function updateClientData(
        Client $client,
        ClientCrudRequest $request,
        SaveEditedClientDataAction $action,
    ): RedirectResponse {
        $message = __('head::clientCrud.clientIsNotFromYourOffice');
        $loanId = ((int) $request->route('loanId')) ?: null;
        $result = 'fail';
        if ($this->agentOfficeAuthorization->check($client)) {
            /// check if client has active ONLINE loan and email ($request->asDto()->email) not provided,
            /// if passed the agent will not be able to delete client email address
            // @phpstan-ignore-next-line
            $clientId = $request->route()->parameter('client')->getKey();
            if (app(LoanRepository::class)->hasActiveOnlineLoan($clientId) && !$request->asDto()->email) {
                return back()->with('fail', __('head::clientCrud.cantDeleteClientEmail'));
            }

            /// execute update client data
            $action->execute($request->asDto(), $loanId);

            /// if email field was deleted, run deletion for client_email
            if (!$request->asDto()->email) {
                app(ClientEmailRepository::class)->delete($client);
            }

            $message = __('head::clientCrud.clientUpdatedSuccessfully');
            $result = 'success';
        }

        return redirect()->back()->with($result, $message);
    }


    /**
     * It is not used in fact. The operation is not allowed
     */
    public function delete(Client $client): RedirectResponse
    {
        if ($client->activeLoans->count()) {
            $message = __("Can't delete the client. They have active loans.");

            return $this->backError($message);
        }

        $message = $this->clientService->delete($client, []);

        return $this->backSuccess($message);
    }

    public function search(Request $request): JsonResponse
    {
        $data = $request->validate([
            'term' => 'nullable|string|regex:/^[a-zA-Z0-9@\.\p{Cyrillic}\s]+$/u',
            'q' => 'nullable|string|regex:/^[a-zA-Z0-9@\.\p{Cyrillic}\s]+$/u',
            'query' => 'sometimes|string|regex:/^[a-zA-Z0-9@\.\p{Cyrillic}\s]+$/u',
            'withActiveLoans' => 'sometimes|boolean',
        ]);

        $value = $data['term'] ?? '';
        $checkActiveLoans = $data['withActiveLoans'] ?? true;
        if ($request->has('query')) {
            $checkActiveLoans = false;
            $value = $data['query'];
        }

        if ($request->has('q')) {
            $value = $data['q'];
        }
        if (empty($value) || strlen($value) < 1) {
            return response()->json([]);
        }

        $res = $this->clientService->getClientsBySearchString(
            $value,
            $checkActiveLoans
        );

        return response()->json($res);
    }

    public function searchForClientCard(Request $request): JsonResponse
    {
        $data = $request->validate([
            'query' => 'nullable|string',
        ]);

        $value = '';
        if ($request->has('query')) {
            $value = trim($data['query']);
        }

        if (empty($value) || strlen($value) < 1) {
            return response()->json([]);
        }

        /// detect if string has latin characters translitirate it to cyrillic
        if (preg_match('/[a-zA-Z]/u', $value) && !str_contains($value, '@')) {
            $value = replaceNonBulgarianChars($value);
        }

        if (is_numeric($value) && str_starts_with($value, '359')) {
            $value = normalizePhone($value);
        }


        $res = $this->clientService->getClientsWithLastActiveLoanBySearchString(
            $value
        );

        return response()->json($res);
    }

    public function multipleContactsAjax(Request $request): JsonResponse
    {
        $seqNum = intval($request['contactCount']);
        $contactTypes = $this->contactTypeService->getAllContactTypes();

        return response()->json(
            [
                view('components.new-application-contact-blade-only-forms')->with(
                    ['seqNum' => $seqNum, 'collection' => $contactTypes]
                )->render(),
            ]
        );
    }

    public function multipleGuarantsAjax(Request $request): JsonResponse
    {
        $seqNumGuarant = intval($request['guarantCount']);
        $guarantTypes = $this->guarantTypeService->getAllGuarantTypes();
        $cities = $this->cityService->getCities();
        $idCardIssuedTypes = $this->clientIdCardService->getAllIdCardIssuedTypes();

        return response()->json(
            [
                view('components.new-application-guarant-only-forms')->with(
                    [
                        'seqNumGuarant' => $seqNumGuarant,
                        'collection' => $guarantTypes,
                        'cities' => $cities,
                        'idCardIssuedTypes' => $idCardIssuedTypes,
                    ]
                )
                    ->render(),
            ]
        );
    }

    public function prefillOfficesConsultantsByLoanAjax(PrefillOfficesConsultantRequest $request): JsonResponse
    {
        $data = $request->validated();
        $consultants = $this->officeService->getOfficeById($data['office_id'])->administrators;
        $res = [];

        foreach ($consultants as $consultant) {
            $res[$consultant->getKey()] = $consultant->getFullNames();
        }

        return response()->json($res);
    }

    public function prefillOfficesByLoanAjax(Request $request): JsonResponse
    {
        $data = $this->officeService->getOfficeNames(intval($request['loan_id']));

        return response()->json(['data' => $data]);
    }

    public function updateOfficeConsultantByLoanAjax(ClientAjaxOfficeConsultantRequest $request): JsonResponse
    {
        $requestValidated = $request->validated();

        $this->officeService->updateOfficeConsultant(
            $requestValidated['office_id'],
            $requestValidated['consultant_id'],
            $requestValidated['loan_id'],
            $requestValidated['client_id']
        );

        return Response::json([
            'isSaved' => true,
            'successMsg' => __('head::clientCard.successfullyUpdated'),
        ], 200);
    }

    /*
     * Ajax call for search by pin in client/guarant
     */
    public function searchClientOrGuarantByPinAjax(GuarantAjaxPinRequest $request): JsonResponse
    {
        if (empty($request['guarantPin'])) {
            throw new Exception("PIN is not provided");
        }

        $data = $this->clientService->getClientGuarantData(($request['guarantPin']));

        return Response::json($data);
    }

    // ======================= CLIENT SESSIONS CONTROL =========================

    public function sessions()
    {
        return view(
            'head::sessions.list',
            [
                'rows' => ClientSession::getAllActive(),

            ]
        );
    }

    public function sessionDelete(int $id)
    {
        if (ClientSession::deactivateRow($id)) {
            return redirect()->back()->with([
                'success' => 'ok',
                'msg' => 'Successfully deactivated token',
            ]);
        }

        return redirect()->back()->with([
            'success' => 'ko',
            'msg' => 'Failed to deactivate token',
        ]);
    }
}
