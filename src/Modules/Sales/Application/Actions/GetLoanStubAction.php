<?php

namespace Modules\Sales\Application\Actions;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanActualStats;
use Modules\Head\Domain\LoanStats;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Common\Models\Client as DbClient;
use Modules\Sales\Http\Dto\LoanDto;

class GetLoanStubAction
{
    public function __construct(private Client $client){}

    public function execute(LoanDto $loanDto, DbClient $dbClient): DbLoan
    {
        $client = $this->client->buildFromExisting($dbClient);
        $dbLoan = $client->generateLoanStub($loanDto);
        $stats = $this->buildStub($dbLoan);
        $dbLoan->setRelation(LoanActualStats::class, $stats);
        $dbLoan->loanActualStats = $stats;
        return $dbLoan;
    }

    public function buildStub(DbLoan $loan): LoanActualStats
    {
        return app(LoanStats::class)
            ->buildFromLoan($loan)
            ->setCartonAmounts()
            ->setRateAnnualPercentage()
            ->dbModel();
    }
}
