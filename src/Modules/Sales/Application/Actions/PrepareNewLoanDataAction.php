<?php

namespace Modules\Sales\Application\Actions;

use Illuminate\Support\Facades\Session;
use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Office;
use Modules\Common\Models\SaleTask;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\ConsultantService;
use Modules\Sales\Forms\NewCompanyLoanForm;
use Modules\Sales\Forms\NewLoanForm;
use Modules\Sales\Services\SaleDecisionReasonService;

class PrepareNewLoanDataAction
{
    public function __construct(
        private readonly ClientRepository          $clientRepository,
        private readonly SaleDecisionReasonService $saleDecisionReasonService,
        private readonly FormBuilder               $formBuilder
    )
    {
    }

    public function getFormData(array $data = []): array
    {
        $client = null;
        if (isset($data['pin'])) {
            $client = $this->clientRepository->getByPin($data['pin']);
        }

        $tempFormData = Session::get('tempFormData', []);

        if (isset($data['phone'])) {
            $tempFormData['client']['phone'] = $data['phone'];

            if ($client) {
                $client->phone = $data['phone'];
            }
        }

        $data['newLoanForm'] = $this->getNewLoanForm($client, $tempFormData);
        $data['maxLoanSumCash'] = app(SettingRepository::class)
            ->getSetting(SettingsEnum::maximum_loan_sum_cash_common)->default_value;

        if ($client) {
            $data['loansForRefinance'] = $this->clientRepository->getLoansForRefinance($client);
            $data['guarantorForLoans'] = app(LoanRepository::class)->getGuarantorLoans($client);
        }

        //// wil be used fo js to detect payment method id
        /// and show or hide iban field
        $data['officePaymentMethods'] = getPaymentMethodsForPaymentAccounts();


        return $data;
    }

    public function getFormFromSaleTask(SaleTask $saleTask, ?array $requestData = null): array
    {
        $client = $saleTask?->client;

        $pin = $saleTask->pin;
        if (!$client && $pin) {
            $client = $this->clientRepository->getByPin($pin);
        }

        $phone = $saleTask->phone;
        if (!$client && $phone) {
            $client = $this->clientRepository->getClientsByPhone($phone)->first();
        }

        if (!$client && isset($requestData['pin'])) {
            $client = $this->clientRepository->getByPin($requestData['pin']);
        }

        //// wil be used fo js to detect payment method id
        /// and show or hide iban field
        $data['officePaymentMethods'] = getPaymentMethodsForPaymentAccounts();

        $data['saleTask'] = $saleTask;
        $data['saleDecisions'] = $this->saleDecisionReasonService->getSaleDecisions();

        $tempFormData = Session::get('tempFormData', []);
        $tempFormData['client'] = ['phone' => $saleTask->phone];

        $newLoanForm = $this->getNewLoanForm($client, $tempFormData);

        /// override client phone from sale task
        $newLoanForm->modify('phone', 'text', [
            'value' => $saleTask->phone,
            'attr' => [
                'name' => 'client[phone]',
                'readonly' => true,
            ]
        ]);

        if ($client) {
            $data['loansForRefinance'] = $this->clientRepository->getLoansForRefinance($client);
        }

        $data['newLoanForm'] = $newLoanForm;
        $data['maxLoanSumCash'] = app(SettingRepository::class)
            ->getSetting(SettingsEnum::maximum_loan_sum_cash_common)->default_value;

        return $data;
    }

    protected function getNewLoanForm(?Client $client = null, array|null $defaults = []): Form
    {
        $route = 'sales.saveNewApplication';
        $newLoanFormClass = NewLoanForm::class;
        if ((session('newLoanType') === 'company')) {
            $route = 'sales.storeNewCompanyApplication';
            $newLoanFormClass = NewCompanyLoanForm::class;
        }

        $clientContacts = $client?->clientContacts->unique('contact_id')->toArray();
        $formContacts = collect($defaults['contact'] ?? [])->filter(function ($row) {
            return $row['name'] || $row['phone'];
        })->toArray();
        if (!empty($formContacts)) {
            $clientContacts = $formContacts;
        }

        $data = [
            'client' => $client?->toArray() ?? $defaults['client'] ?? [],
            'client_idcard' => $client?->clientLastIdCard()?->toArray() ?? $defaults['client_idcard'] ?? [],
            'client_address' => $client?->clientLastAddressIdcard()?->toArray() ?? $defaults['client_idcard'] ?? [],
            'client_current_address' => $client?->clientLastAddressCurrent()?->toArray()
                ?? $defaults['client_current_address'] ?? [],
            'client_contacts' => $clientContacts,
            'guarant' => $defaults['guarant'] ?? null,
            'representor' => $defaults['representor'] ?? $client?->getRepesentor()
        ];

        $data['consultants'] = $this->getConsultants();

        return $this->formBuilder->create($newLoanFormClass, [
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'route' => $route,
            'data' => $data
        ]);
    }

    private function getConsultants(): array
    {
        $consultantService = \App::make(ConsultantService::class);

        $consultantIds = $consultantService->getConsultantIdsOfAdminOffices();
        if (count($consultantIds) === 0) {
            return [];
        }

        $consultants = $consultantService->getConsultantsIdNameByIds($consultantIds);
        if (count($consultants) === 0) {
            return [];
        }

        return $consultants;
    }
}
