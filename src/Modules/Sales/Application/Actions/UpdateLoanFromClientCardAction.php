<?php

namespace Modules\Sales\Application\Actions;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Modules\Common\Models\Loan;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Http\Dto\LoanDto;

readonly class UpdateLoanFromClientCardAction
{

    public function __construct(private LoanForUpdate $loanForUpdate)
    {
    }

    public function execute(LoanDto $loanDto, bool $fromApprove = false): array
    {
        /** @var LoanForUpdate $updatedLoan */
        $updatedLoan = $this->loanForUpdate
            ->loadById($loanDto->loan_id)
            ->setFromApprove($fromApprove)
            ->update($loanDto)
            ->nullateApproveTasks();

        $dbLoan = $updatedLoan->dbModel();

        return [
            $dbLoan,
            $this->getFreshLoanParam($dbLoan)
        ];
    }

    private function getFreshLoanParam(Loan $loan): array
    {
        $firstInstallment = $loan->orderedInstallments()->first();

        return [
            'productName' => $loan->product->trade_name,
            'amountApproved' => $loan->amount_approved,
            'loanPeriodParam' =>
                $loan->period_approved
                . ' '
                . trans_choice('product::product.keys.' . $loan->getPeriod(), $loan->period_approved),
            'installmentsCountParam' => $loan->installments_approved
                . ' '
                . trans_choice('head::clientCard.installmentSinglePlural', $loan->installments_approved),
            'firstInstallmentDate' => formatDate($firstInstallment->due_date, 'd.m.Y'),
            'firstInstallmentAmount' => amount(floatToInt($firstInstallment->total_amount)),
            'preliminaryPaymentPlanLink' => route('head.loans.showPreliminaryPaymentPlan', [
                'discount' => intval($loan->discount_percent),
                'period' => $loan->period_approved,
                'productId' => $loan->product_id,
                'sum' => $loan->amount_approved,
            ]),
            'paymentMethod' => __('payments::paymentMethods.' . $loan->payment_method_id),
            'paymentAccount' => $loan->paymentAccount()?->name,
            'officeName' => $loan->office?->name,
        ];
    }
}
