<?php

namespace Modules\Head\Facades;

use RuntimeException;

class CurrencyConverter
{
    /**
     * Convert amount from one currency to other currency
     *
     * @param int $currencyFromId
     * @param int $currencyToId
     * @param float $amount
     *
     * @return float
     */
    public function convert(
        int $currencyFromId,
        int $currencyToId,
        float $amount
    ): float {
        if ($currencyFromId == $currencyToId) {
            return $amount;
        }

        //TODO: implement currency 1 != currency 2
        return $amount;
    }

    /**
     * Convert multiple amounts from currency to other currency
     * Format: array of objects with key amount
     * Example: [0 => {"amount": "449.00", "currency_id": 2}]
     */
    public function massConvert(
        array $collection,
        int $currencyId
    ): array {
        $result = [];
        foreach ($collection as $key => $data) {
            if (
                empty($data->amount)
                || empty($data->currency_id)
            ) {
                throw new RuntimeException('Missing number/currency in the object.');
            }

            $result[$key] = $this->convert($data->currency_id, $currencyId, $data->amount);
        }

        return $result;
    }
}
