<?php

namespace Modules\Head\Application\Actions;

use Illuminate\Support\Facades\Session;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductSetting;
use Modules\Discounts\Repositories\DiscountByPhoneRepository;
use Modules\Head\Application\Dto\SliderCalcParamsDto;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Services\LoanService;
use Modules\Product\Services\ProductService;
use Modules\Common\Models\Office;

readonly class PrepareProductSettingsDataAction
{
    private const DEFAULT_AMOUNT_STEP = 50;

    public function __construct(
        private ProductService $productService,
        private LoanService $loanService,
        private ClientRepository $clientRepository,
        private DiscountByPhoneRepository $discountByPhoneRepository,
    ) {
    }

    public function execute(SliderCalcParamsDto $data): array
    {
        $product = $this->productService->getById($data->productId);

        extract($this->getProductSettings($product), EXTR_OVERWRITE);

        $loan = null;
        $refAmount = 0;

        if ($data->loanId) {
            $loan = $this->loanService->getLoanById($data->loanId);

            /// when we have refinancing loans set min amount + 100.00
            $refinancingLoans = $loan->refinancing()->get() ?? collect();

            if ($refinancingLoans->count()) {
                $refAmount = 0;

                /** @var \Modules\Common\Models\Loan $refLoan **/
                foreach ($refinancingLoans as $refLoan) {
                    $isForOnline = (Office::OFFICE_ID_WEB == $refLoan->office_id);
                    $refAmount += $refLoan->getAmountForRefinance($isForOnline);
                    // $refAmount += $refLoan->getEarlyRepaymentDebtDb();
                }

                $refAmount /= 100;
            }

            if ($loan->product_id === $product->getKey()) {
                $defAmount = $loan->amount_approved / 100;
                $defPeriod = (int) $loan->period_approved;

                if ((int) $maxAmount <= (int) $defAmount && !$product->isFixed) {
                    $maxAmount = $defAmount;
                }
            }
        }

        $client = $data->clientPin ? $this->clientRepository->getByPin($data->clientPin, ['client_id', 'phone']) : null;

        $discounts = $this->getDiscounts($client, $product->getKey(), $data->phone);
        $maxDiscount = $this->getMaxDiscount($discounts);

        extract($this->getDefaultSettings($discounts, $product, $loan, $defAmount, $defPeriod), EXTR_OVERWRITE);

        return [
            'refAmount' => $refAmount,
            'isJuridical' => $product->legal_status,
            'amount' => [
                'default' => $defaultAmountSetting,
                'start' => (int) $minAmount,
                'max' => (int) $maxAmount,
                'label' => 'лв',
                'step' => ($loan?->re_contracted_overdue?->isTrue()) ? 0.01 : (int)$amountStep,
            ],
            'period' => [
                'default' => (int) $defaultPeriodSetting,
                'start' => (int) $minPeriod,
                'max' => (int) $maxPeriod,
                'label' => ' ' . $product->getPeriodLabel($minPeriod),
            ],
            'discount' => [
                'default' => $defaultDiscountSetting,
                'start' => 0,
                'max' => $maxDiscount,
                'label' => ' %',
            ]
        ];
    }


    protected function getFromTempSliderData(int $productId, string $key, mixed $default, ?int $loanId): mixed
    {
        /// if open from client card and have valid loan
        /// return loan default
        if ($loanId) {
            return $default;
        }

        $sliderOldData = Session::get("tempSliderData");
        if (empty($sliderOldData['product_id'])) {
            return $default;
        }

        $sessionProductId = (int) $sliderOldData['product_id'];
        if ($sessionProductId !== $productId) {
            return $default;
        }

        if ($key === 'loan_sum' && Session::has("tempSliderData.{$key}")) {
            return Session::get("tempSliderData.{$key}") / 100;
        }

        return Session::get("tempSliderData.{$key}", $default);
    }

    /**
     * @param Client|null $client
     * @param int $productId
     * @param string|null $phone
     * @return array{discountByPhone: int, discountByClient: int, adminDiscountLimit: int}
     */
    private function getDiscounts(?Client $client, int $productId, ?string $phone = null): array
    {
        /**
         * @var Administrator $administrator
         */
        $administrator = auth()->user();

        $discountByPhone = 0;
        $discountByClient = 0;
        $adminDiscountLimit = $administrator->getAdminMaxDiscount();

        if ($client || $phone) {
            if (!$phone) {
                $phone = $client->phone;
            }

            $discountByPhone = (int) $this->discountByPhoneRepository->getPercentByPhoneAndProduct($phone, $productId);
            $discountByClient = (int) $client?->getDiscountForProduct($productId)?->percent;
        }

        return compact('discountByPhone', 'discountByClient', 'adminDiscountLimit');
    }

    private function getMaxDiscount(array $discounts): int
    {
        return max($discounts);
    }

    /**
     * @return array{defAmount: mixed, minAmount: mixed, maxAmount: mixed, amountStep: int|mixed, defPeriod: mixed, minPeriod: mixed, maxPeriod: mixed}
     */
    private function getProductSettings(Product $product): array
    {
        return [
            'defAmount' => $this->productService->getProductSettingByKey($product, ProductSetting::DEFAULT_AMOUNT_KEY),
            'minAmount' => $this->productService->getProductSettingByKey($product, ProductSetting::MIN_AMOUNT_KEY),
            'maxAmount' => $this->productService->getProductSettingByKey($product, ProductSetting::MAX_AMOUNT_KEY),
            'amountStep' => $this->productService->getProductSettingByKey(
                $product,
                ProductSetting::AMOUNT_STEP_KEY
            ) ?: self::DEFAULT_AMOUNT_STEP,
            'defPeriod' => $this->productService->getProductSettingByKey($product, ProductSetting::DEFAULT_PERIOD_KEY),
            'minPeriod' => $this->productService->getProductSettingByKey($product, ProductSetting::MIN_PERIOD_KEY),
            'maxPeriod' => $this->productService->getProductSettingByKey($product, ProductSetting::MAX_PERIOD_KEY),
        ];
    }

    private function getDefaultDiscountSetting(array $discounts, Product $product, ?Loan $loan): int
    {
        unset($discounts['adminDiscountLimit']);

        $defaultDiscountSetting = $this->getMaxDiscount($discounts) ?? 0;

        /// check for active client discounts
        if ($loan && $loan->product_id === $product->getKey()) {
            $defaultDiscountSetting = $this->getFromTempSliderData(
                $product->getKey(),
                'discount_percent',
                (int) $loan->discount_percent,
                $loan->getKey(),
            );
        }

        return $defaultDiscountSetting;
    }

    /**
     * @param array $discounts
     * @param Product $product
     * @param Loan|null $loan
     * @param int $defAmount
     * @param int $defPeriod
     * @return array{defaultDiscountSetting: int, defaultAmountSetting: int, defaultPeriodSetting: int}
     */
    private function getDefaultSettings(
        array $discounts,
        Product $product,
        ?Loan $loan,
        float $defAmount,
        int $defPeriod
    ): array {
        $defaultDiscountSetting = $this->getDefaultDiscountSetting($discounts, $product, $loan);
        $defaultAmountSetting = $this->getFromTempSliderData(
            $product->getKey(),
            'loan_sum',
            $defAmount,
            $loan?->getKey()
        );
        $defaultPeriodSetting = $this->getFromTempSliderData(
            $product->getKey(),
            'loan_period',
            $defPeriod,
            $loan?->getKey()
        );

        /// check if has saved temp data
        $sessionKey = $loan ? Loan::getTempLoanDataKey($loan->getKey()) : null;

        if ($sessionKey) {
            $tempData = Session::get($sessionKey, []);

            if ($tempData && $tempData['loan']['product_id'] == $product->getKey()) {
                $defaultAmountSetting = $tempData['loan']['loan_sum'];
                $defaultPeriodSetting = $tempData['loan']['loan_period'];
                $defaultDiscountSetting = $tempData['loan']['discount_percent'] ?? 0;
            }
        }

        return compact('defaultDiscountSetting', 'defaultAmountSetting', 'defaultPeriodSetting');
    }
}
