<?php

namespace Modules\Head\Application\Actions;

use Modules\Common\Models\ClientCreditLimit;
use Modules\Common\Repositories\ClientCreditLimitRepository;
use Modules\Head\Services\SpreadsheetHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ManualCreditLimitExportAction
{
    public function __construct(
        public ClientCreditLimitRepository $repository
    ) {
    }

    public function execute(array $filters = []): array
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            __('table.Pin'),
            __('table.Client'),
            __('table.CreditLimit'),
            __('table.CreatedAt'),
            __('table.CreatedBy'),
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;

        $builder = $this->repository
            ->dbBuilder($filters)
            ->orderBy('id', 'DESC');

        $builder->chunkById(100, function ($rows) use (&$sheet, &$rowIndex) {
            /** @var ClientCreditLimit $clientCreditLimit ** */
            foreach ($rows as $clientCreditLimit) {
                $r = [
                    $clientCreditLimit->client->pin,
                    $clientCreditLimit->client->getFullName(),
                    intToFloat($clientCreditLimit->amount),
                    $clientCreditLimit->created_at,
                    $clientCreditLimit->creator->getFullNames()
                ];

                // Populate data from the array
                $rowIndex++;
                foreach ($r as $columnIndex => $value) {
                    $sheet->setCellValue(
                        [$columnIndex + 1, $rowIndex],
                        $value
                    );
                }
            }
        });

        /// set autosize columns
        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'client_credit_limit_' . time() . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
    }
}
