<?php

namespace Modules\Head\Application\Actions;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Admin\Services\OfficeService;
use Modules\CashDesk\Application\Actions\PrepareCashDeskTopPanel;
use Modules\CashDesk\Http\Requests\DTO\CashDeskSearchRequest;
use Modules\Collect\Repositories\BucketTaskRepository;
use Modules\Collect\Services\CollectorDecisionService;
use Modules\Common\Enums\ClientCardBoxGroupEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BucketTaskHistory;
use Modules\Common\Models\Client;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Traits\DatesAwareTrait;
use Modules\Communication\Services\CommunicationService;
use Modules\Head\Enums\ClientCardTabsEnum;
use Modules\Head\Forms\ClientCard\CallMeLaterForm;
use Modules\Head\Forms\ClientCard\CancelDecisionForm;
use Modules\Head\Forms\ClientContactForm;
use Modules\Head\Forms\ClientGuarantorForm;
use Modules\Head\Forms\ClientPhoneForm;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\Loan\LoanContactRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\HeaderService;
use Modules\Head\Services\LoanService;
use Modules\Head\Services\OpenTasksService;
use Modules\Payments\Services\PaymentService;
use Modules\Sales\Services\SaleDecisionReasonService;
use Modules\Sales\Services\SaleService;
use StikCredit\Calculators\Calculator;

final class PrepareClientCardDataAction
{
    use DatesAwareTrait;

    public function __construct(
        private readonly PrepareBaseClientCardDataAction $prepareBaseClientCardDataAction,

        private readonly LoanService                     $loanService,
        private readonly LoanRepository                  $loanRepository,
        private readonly OfficeService                   $officeService,
        private readonly HeaderService                   $headerService,
        private readonly CommunicationService            $communicationService,
        private readonly OpenTasksService                $openTasksService,
        private readonly PaymentService                  $paymentService,
        private readonly SaleService                     $saleService,
        private readonly SaleDecisionReasonService       $saleDecisionReasonService,

        //// collectData action
        private readonly ClientCardCollectDataAction     $clientCardCollectDataAction,

        /// form builder
        private readonly FormBuilder                     $formBuilder,

        /// todo delete and move to form
        private readonly CollectorDecisionService        $collectorDecisionService,
        private readonly BucketTaskRepository            $bucketTaskRepository,
        private readonly LoanContactRepository           $loanContactRepository
    ) {}

    public function execute(array $routeParams): array
    {
        // надо переработать, т.к. массив собирает данные до проверки содержимого
        $data = $this->prepareBaseClientCardDataAction->execute($routeParams);

        /**
         * @var Client $client
         * @var Loan $loan
         * @var Administrator $administrator
         */
        $client = $data['client'];
        $loan = $data['loan'];
        $administrator = $data['administrator'];

        $data['refinancingLoanIds'] = $loan?->refinancing()->get()->keyBy('loan_id')->toArray() ?? []; // TODOR: refactor
        $data['refinancingLoans'] = app(ClientRepository::class)->getLoansForRefinance($client); // TODOR: refactor

        $data['canCreateNewLoan'] = $this->loanRepository->clientHasUnprocessedLoans($client->getKey());

        $data['offices'] = $this->officeService->getAllOffices();
        $data['headerData'] = $this->headerService->getHeaderData($client, $loan, $routeParams);
        $data['clientCardTabs'] = ClientCardTabsEnum::getAllTabs($routeParams);
        // if we are on client level, we dont have payment schedule!
        if (empty($routeParams['loanId'])) {
            unset($data['clientCardTabs']['PaymentSchedule']);
            unset($data['clientCardTabs']['JudicialTab']);
        } else if (!getAdmin()->hasPermissionTo('head.judicial-tab.index')) {
            unset($data['clientCardTabs']['JudicialTab']);
        }

        $data['installmentsDates'] = [];

        // TODOR: put in cache
        $data['manualTemplates'] = $this->communicationService->getTemplatesForManualSending(
//            $administrator->officesRelation
            collect([$loan?->office])
        );

        $data['smsTemplate'] = CommunicationService::COMMUNICATION_TYPE_SMS;
        $data['emailTemplate'] = CommunicationService::COMMUNICATION_TYPE_EMAIL;
        $data['mailTemplate'] = CommunicationService::COMMUNICATION_TYPE_MAIL;

        $data['loans'] = $this->loanRepository->getByClientId($client->getKey(), 50);
        $data['allOpenTasks'] = $this->openTasksService->getAllClientOpenTasks($client->getKey());
        $data['loanHistoryTotal'] = $this->loanService->getClientLoanStatistics($client);
        $data['lastFivePayments'] = $this->paymentService->getLoanLastPayments($loan, 5);

        $clientContacts = $client->getLastContactsCollection();
        $data['contactPersons'] = $clientContacts;

        $data['canPaidLoanCash'] = $this->canPaidLoanCash($loan);

        /// set task
        $taskId = 0;
        if (
            !empty($routeParams['taskId'])
            && !empty($routeParams['task'])
            && (
                $routeParams['task'] == 'sale'
                || $routeParams['task'] == 'sales'
            )
        ) {
            $data['saleTask'] = $this->saleService->getSaleTaskById($routeParams['taskId']);
            $taskId = $data['saleTask']->getKey();
        }

        /// create forms
        $data['clientGuarantorForm'] = $this->getCreateClientGuarantorForm($client, $loan);
        $data['clientPhoneForm'] = $this->getCreateClientPhoneForm($client, $loan);
        $data['callMeLaterForm'] = $this->getCallMeLaterForm($client, $loan);
        $data['cancelDecisionForm'] = $this->getCancelDecisionForm($client, $loan);
        $data['clientContactForms'] = $this->getClientContactForms($client, $clientContacts, $loan?->getKey());

        /// todo will be deleted and move to form
        $data['collectorDecisions'] = $this->collectorDecisionService->getByFilters(
            null,
            ['active' => 1],
            ['collector_decision_id' => 'ASC']
        );

        $data['lastBucketTasks'] = collect([]);
        $data['lastPromise'] = null;
        if ($loan) {
            $data['lastBucketTasks'] = $this->bucketTaskRepository->getLastBucketTasks($loan, 10);
            $data['lastPromise'] = BucketTaskHistory::where([
                'loan_id' => $loan->getKey(),
                'collector_decision_id' => CollectorDecision::PROMISE
            ])->orderBy('bucket_task_history_id', 'DESC')->first();
        }

        $data['saleDecisions'] = $this->saleDecisionReasonService->getSaleDecisions();

        if (($data['client_card_group'] ?? null) === ClientCardBoxGroupEnum::TASK_FOR_COLLECT->value) {
            $collectData = $this->clientCardCollectDataAction->execute($loan);
            $data = array_merge($data, $collectData);
        }

        $data['saleInformation'] = [
            'discounts' => $client->clientDiscountActuals()->where('valid_till', '>=', today())
                ->limit(5)
                ->get(),
            'unsigned_loans_msg' => $client->hasUnsingedLoans()
                ? __('head::clientCard.agentNotificationUnsignedLoan')
                : '',
            'interest_free_loan' => '', // TODO
            'pre_approved' => '', // TODO
            'unreceived_money' => $client->getUnreceivedMoneyDataFromEasyPay($loan?->getKey()),
        ];


        ///// set refinance data
        if ($loan) {
            $refinanced_due_amount = 0;
            $loan->refinancing(false)->get()->sum(function (Loan $loanRef) use (&$refinanced_due_amount) {
                $amountForRefinance = $loanRef?->refinanced?->refinanced_due_amount;
                $refinanced_due_amount += $amountForRefinance;
            });

            $data['refinanceParams'] = [
                'refinanceCheck' => '',
                'previousLoansDueAmount' => $refinanced_due_amount,
                'difference' => Calculator::sub($loan->getAmountToPaid(), $refinanced_due_amount),
            ];

            if ($loan->creditLimit) {
                $data['creditLimit'] = $loan->creditLimit->load(['creditLimitRule']);
            }


            // ------ PAYMENT ACCOUNT SECTION ---------
            $showIban = false;
            $mainIban = '';
            $bankAccount = $loan->bankAccount->first();
            if (!empty($bankAccount->iban)) {
                $mainIban = $bankAccount->iban;
            } else {
                $mainIban = $client->getMainBankAccountIban();
            }
            $paymentAccountsObjects = $loan->office->getOfficePaymentMethods(true);
            if (!empty($paymentAccountsObjects)) {
                foreach ($paymentAccountsObjects as $paObj) {
                    if (
                        $paObj->bank_account_id == $loan->bank_account_id
                        && $paObj->payment_method_id == PaymentMethod::PAYMENT_METHOD_BANK
                    ) {
                        $showIban = true;
                    }
                }
            }
            $data['paymentAccounts'] = $paymentAccountsObjects;
            $data['showIban'] = $showIban;
            $data['mainIban'] = $mainIban;


            $dataCalendar = app(CalendarDataAction::class)
                ->execute([
                    'loanId' => $loan->loan_id,
                    'currentDate' => Carbon::today()->format('Y-m-d')
                ]);

            $data = array_merge($data, $dataCalendar);


            $data['loanCartonDb'] = $loan->getCartonDb();

            $data['lastPayment'] = null;
            if (!empty($data['lastFivePayments']) && $data['lastFivePayments']->count() > 0) {
                $data['lastPayment'] = $data['lastFivePayments']->first();
            }
        }

        return $data;
    }

    protected function canPaidLoanCash(?Loan $loan = null): bool
    {
        if (!$loan || !$loan->isPaymentMethodCash()) {
            return false;
        }

        $dto = app(PrepareCashDeskTopPanel::class)
            ->execute($loan->office, app(CashDeskSearchRequest::class));

        if ($loan->amount_approved <= $dto->difference) {
            return true;
        }

        return false;
    }

    public function getCancelDecisionForm(Client $client, ?Loan $loan): Form
    {
        return $this->formBuilder->create(CancelDecisionForm::class, [
            'data-parsley-validate' => 'true',
            'route' => 'approve.loan-decision.cancel',
            'method' => 'POST',
            'data' => [
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey() ?? 0,
                'modalTitle' => __('head::clientCard.ReasonForRefusal'),
            ]
        ]);
    }

    protected function getCallMeLaterForm(Client $client, ?Loan $loan): Form
    {
        return $this->formBuilder->create(CallMeLaterForm::class, [
            'data-parsley-validate' => 'true',
            'route' => 'approve.loan-decision.delay',
            'method' => 'POST',
            'data' => [
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey() ?? 0,
                'modalTitle' => __('head::clientCard.ClientWantsLaterCall'),
            ]
        ]);
    }

    private function getClientContactForms(Client $client, Collection $contactPersons, ?int $loanId = null): array
    {
        /// array key will be used like modal id
        $clientContactForms['addClientContactModal'] = $this->formBuilder->create(ClientContactForm::class, [
            'data-parsley-validate' => 'true',
            'route' => 'head.client-contacts.create',
            'method' => 'POST',
            'data' => [
                'seq_num' => $this->loanContactRepository->getNextSeqNum($loanId ?? 0),
                'client_id' => $client->getKey(),
                'loan_id' => $loanId ?? 0,
                'modalTitle' => __('head::clientCard.addNewContact'),
            ]
        ]);

        /// edit forms
        if ($contactPersons->isNotEmpty()) {
            foreach ($contactPersons as $clientContact) {
                $updateModalId = 'updateClientContactModal-' . $clientContact->contact_id;
                $loanContactActual = $this->loanContactRepository->getByClientIdAndByContactId(
                    $client->getKey(),
                    $clientContact->contact_id
                );
                $clientContactForms[$updateModalId] = $this->formBuilder->create(ClientContactForm::class, [
                    'data-parsley-validate' => 'true',
                    'route' => ['head.client-contacts.edit', $clientContact->contact_id],
                    'method' => 'PUT',
                    'model' => $clientContact,
                    'data' => [
                        'seq_num' => $loanContactActual->seq_num,
                        'client_id' => $client->getKey(),
                        'loan_id' => $loanContactActual->loan_id,
                        'modalTitle' => __('head::clientCard.updateContact'),
                    ]
                ]);
            }
        }

        return $clientContactForms;
    }

    protected function getCreateClientPhoneForm(Client $client, ?Loan $loan): Form
    {
        return $this->formBuilder->create(ClientPhoneForm::class, [
            'data-parsley-validate' => 'true',
            'route' => 'head.client-phone.storePhone',
            'method' => 'POST',
            'data' => [
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey() ?? 0,
            ]
        ]);
    }

    protected function getCreateClientGuarantorForm(Client $client, ?Loan $loan): Form
    {
        return $this->formBuilder->create(ClientGuarantorForm::class, [
            'url' => route('head.client-guarantors.store'),
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'data' => [
                'client_id' => $client->getKey(),
                'loan_id' => $loan?->getKey() ?? 0,
            ]
        ]);
    }
}
