<?php

namespace Modules\Head\Application\Actions;

use Illuminate\Support\Carbon;
use Exception;
use Illuminate\Support\Facades\Session;
use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Admin\Repositories\OfficeRepository;
use Modules\Collect\Repositories\ClientCardBoxRepository;
use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientEmail;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Models\Loan;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Models\SmsTemplate;
use Modules\Head\Forms\ClientCard\BlockClientForm;
use Modules\Head\Forms\ClientCard\ClientEditForm;
use Modules\Head\Forms\ClientCard\CompanyEditForm;
use Modules\Head\Forms\ClientCard\PaidLoanCertificateForm;
use Modules\Head\Forms\ClientCard\RepresentorEditDataForm;
use Modules\Head\Forms\ClientCard\UnblockClientForm;
use Modules\Head\Forms\ClientCard\UploadClientDocumentForm;
use Modules\Head\Forms\EarlyRepaymentCommunicationForm;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;
use RuntimeException;

readonly class PrepareBaseClientCardDataAction
{
    public function __construct(
        private ClientService $clientService,
        private ClientRepository $clientRepository,
        private ClientAddressRepository $clientAddressRepository,
        private LoanService $loanService,
        private ClientCardBoxRepository $clientCardBoxRepository,
        private FormBuilder $formBuilder
    ) {
    }

    public function execute(array $args): array
    {
        /// clear data if exists from new-app page
        Session::remove('tempSliderData');
        Session::remove('tempFormData');

        $client = $data['client'] = $this->clientService->getClientById($args['clientId'], true);
        $clientId = $data['clientId'] = $args['clientId'];
        $data['guarantorLoans'] = $client->guarantorByPin?->loansActual;

        $data['loan'] = null;
        $data['office'] = null;
        if (isset($args['loanId'])) {
            $data['loan'] = $this->loanService->getLoanById($args['loanId']);
            $data['office'] = $data['loan']->office;
        }

        $loanId = $data['loanId'] = $data['loan']?->getKey() ?? 0;


        $data['administrator'] = auth()->user();
        $data['officeId'] = $data['loan']?->office_id ?? $data['administrator']->offices?->first()?->office_id;
        if (empty($data['officeId'])) {
            throw new RuntimeException('Admin has no attached office');
        }


        /// set office products
        $products = app(OfficeRepository::class)->getOfficeProductsByOfficeId(
            $data['officeId'],
            (bool) $data['loan']?->client?->isCompany(),
            ['product_id', 'trade_name'],
        );
        $data['products'] = $products->pluck('trade_name', 'product_id');

        /// if isset current date && !is_null loan set early repayment form
        $data['currentDate'] = $args['currentDate'] ?? date('Y-m-d');
        if (!is_null($data['loan'])) {
            $data['earlyRepaymentForm'] = $this->getEarlyRepaymentForm($data['loan'], $data['currentDate']);
        }

        /// forms
        $data['blockClientForm'] = $this->getBlockClientForm($clientId);
        $data['unblockClientForm'] = $this->getUnblockClientForm($clientId);
        $data['uploadClientDocumentForm'] = $this->getUploadClientDocumentForm($clientId, $loanId);
        $data['paidLoanCertificateForm'] = $this->getPaidLoanCertificateForm($clientId, $data['loan']);
        $data['clientEditForm'] = $this->getClientEditForm($data['client'], $loanId ?: null);

        /// client card boxes
        $skipCards = [];
        if (!empty($data['loan']->loan_id)) {
            if (!$data['loan']->isActive()) {
                $skipCards[] = 'boxClientEarlyRepayment';
                $skipCards[] = 'boxLast5Payments';
            } else {
                $skipCards[] = 'boxHistoryOfApplicationsAndLoans';
            }
        }
        $data['client_card_group'] = $this->getClientCardGroup($args);
        $data['cards'] = $this->clientCardBoxRepository->getSortedByGroup($data['client_card_group'], $skipCards);

        /// set last 5 communications
        if ($data['client']) {
            $data['last5Communications'] = $this->clientRepository->getCommunicationByFilters(['client_id' => $data['client']->getKey()], 5);
        }

        // TODOR: put in cache
        $data['smsOfficeInfoTemplateId'] = SmsTemplate::whereName(SmsTemplateKeyEnum::SMS_TYPE_OFFICE_INFO->value)->first()?->getKey(
        );
        $data['smsOfficePaymentInfoTemplateId'] = SmsTemplate::whereName(SmsTemplateKeyEnum::SMS_TYPE_PAYMENT_DATA->value)->first(
        )?->getKey();

        //// company
        $data['companyEditForm'] = null;
        $data['representorEditForm'] = null;
        if ($client->isCompany()) {
            $data['companyEditForm'] = CompanyEditForm::create([
                'url' => route('head.storeCompanyData', $client->getKey()),
                'method' => 'POST',
                'model' => $client
            ]);
            $data['representorEditForm'] = RepresentorEditDataForm::create([
                'url' => route('head.storeRepresentorData', $client->getRepesentor()?->getKey()),
                'method' => 'POST',
                'model' => $client->getRepesentor()
            ]);
        }


        return $data;
    }

    protected function getClientCardGroup(array $args): string
    {
        if (isset($args['task'])) {
            return 'task_for_' . $args['task'];
        }

        if (isset($args['loanId']) && (int) $args['loanId'] > 0) {
            return 'client_with_loan';
        } else {
            return 'client_without_loan';
        }
    }

    protected function getPaidLoanCertificateForm(int $clientId, ?Loan $loan = null): Form
    {
        return $this->formBuilder->create(PaidLoanCertificateForm::class, [
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'route' => 'payment.cashDesk.taxFeeTransaction',
            'data' => [
                'client_id' => $clientId,
                'loan_id' => $loan?->getKey(),
                'office_id' => $loan?->office_id,
            ]
        ]);
    }

    public function getClientEditForm(Client $client, ?int $loanId = null): Form
    {
        $lastIdCard = $client->clientLastIdCard();
        if (empty($lastIdCard->pin)) { // TMP FIX
            $lastIdCard = $client->clientIdCards->first();
        }

        if (empty($lastIdCard->pin)) {
            throw new RuntimeException('No id card for client #' . $client->client_id);
        }

        $currentAddress = $this->clientAddressRepository->getLastActive($client, AddressTypeEnum::Current);

        return $this->formBuilder->create(ClientEditForm::class, [
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'route' => ['head.clients.updateClientData', [$client->client_id, $loanId]],
            'data' => [
                'client_idcard' => [
                    'pin' => $lastIdCard->pin,
                    'idcard_number' => $lastIdCard->idcard_number,
                    'city_id' => $lastIdCard->city_id,
                    'idcard_issued_id' => $lastIdCard->idcard_issued_id,
                    'address' => $lastIdCard->address,
                    'issue_date' => (new Carbon($lastIdCard->issue_date))->format('d-m-Y'),
                    'valid_date' => (new Carbon($lastIdCard->valid_date))->format('d-m-Y')
                ],
                'client' => [
                    'first_name' => $client->first_name,
                    'middle_name' => $client->middle_name,
                    'last_name' => $client->last_name,
                    'phone' => $client->clientPhones()->get()->map(function (ClientPhone $phone) {
                        return $phone->number;
                    }),
                    'email' => $client->clientEmails()->get()->map(function (ClientEmail $clientEmail) {
                        return $clientEmail->email;
                    }),
                    'legal_status' => $client->legal_status,
                    'citizenship_type' => $client->citizenship_type,
                    'gender' => $client->gender,
                    'legal_status_code' => $client->legal_status_code,
                    'economy_sector_code' => $client->economy_sector_code,
                    'industry_code' => $client->industry_code,
                ],
                //Client should always have current address. Either manually entered or copied from address on id card.
                //Question marks are set to avoid issues with previous loans that were incorrectly created
                'client_address' => [
                    'post_code' => $currentAddress?->post_code,
                    'address' => $currentAddress?->address,
                    'city_id' => $currentAddress?->city_id,
                    'type' => $currentAddress?->type,
                ]
            ]
        ]);
    }

    /**
     * @param int $clientId
     * @param int $loanId
     * @return Form
     */
    protected function getUploadClientDocumentForm(int $clientId, int $loanId = 0): Form
    {
        return $this->formBuilder->create(UploadClientDocumentForm::class, [
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'enctype' => 'multipart/form-data',
            'route' => 'head.clientCard.uploadDocument',
            'data' => [
                'client_id' => $clientId,
                'loan_id' => $loanId,
            ]
        ]);
    }

    /**
     * @param int $clientId
     * @return Form
     */
    protected function getBlockClientForm(int $clientId): Form
    {
        return $this->formBuilder->create(BlockClientForm::class, [
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'route' => ["head.block-client.blockClient"],
            'data' => ['client_id' => $clientId]
        ]);
    }

    /**
     * @param int $clientId
     * @return Form
     */
    protected function getUnblockClientForm(int $clientId): Form
    {
        return $this->formBuilder->create(UnblockClientForm::class, [
            'data-parsley-validate' => 'true',
            'method' => 'POST',
            'route' => ["head.block-client.unBlockClient"],
            'data' => ['client_id' => $clientId]
        ]);
    }

    /**
     * @param Loan $loan
     * @param string $currentDate
     * @return Form
     */
    protected function getEarlyRepaymentForm(Loan $loan, string $currentDate): Form
    {
        return $this->formBuilder->create(EarlyRepaymentCommunicationForm::class, [
            'route' => 'communication.clientCardCommunication.sendEarlyCommunication',
            'model' => $loan,
            'data' => ['currentDate' => $currentDate]
        ]);
    }
}
