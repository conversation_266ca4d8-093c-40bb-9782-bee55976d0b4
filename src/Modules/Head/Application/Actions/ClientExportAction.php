<?php

namespace Modules\Head\Application\Actions;

use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Exports\ClientsExport;

class ClientExportAction
{
    public function __construct(
        private readonly ClientRepository       $clientRepository,
        private ClientsExport $export
    ) {}

    public function execute(array $filters = []): ClientsExport
    {
        $this->export->builder = $this->clientRepository->getBuilderByFilters($filters);
        return $this->export;
    }
}
