<?php

declare(strict_types=1);

namespace Modules\Head\Application\Actions\ClientsWithoutActiveLoan;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Modules\Head\Exports\ClientsWithoutActiveLoanExport;
use Mo<PERSON>les\Head\Repositories\ClientWithoutActiveLoanRepository;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

final class ExportAction
{
    public function __construct(private ClientWithoutActiveLoanRepository $repository)
    {
    }

    public function executeCSV(array $filters = [])
    {
        try {

            $fileName = 'clients_without_active_loan_' . time() . '.csv';
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename=' . $fileName);
            header("Pragma: no-cache"); // optional
            header("Expires: 0"); // optional

            $output = fopen('php://output', 'w');
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

            $headers = [
                __('table.ClientId'),
                __('table.Amount'),
                __('table.Product'),
                __('table.LastLoanRepaidAt'),
                __('table.DaysSinceLastLoan'),
                __('table.ActiveDiscount'),
                __('table.Phone'),
                __('table.Email'),
                __('table.NoRepaidLoans'),
                __('table.LastApplicationDate'),
                __('table.MaxOverdueLastLoan'),
            ];
            fputcsv($output, $headers);

            $builder = $this->repository->getBuilder($filters);
            $builder->getQuery()->chunkById(
                100,
                function ($rows) use ($output) {

                    foreach ($rows as $row) {

                        $r = [
                            $row->client_id,
                            intToFloat($row->amount_approved),
                            $row->product_id ? getProductName($row->product_id) : null,
                            Carbon::parse($row->repaid_at)->format('d/m/Y'),
                            $row->days_since_last_loan,
                            $row->active_discount,
                            $row->phone,
                            $row->email,
                            $row->no_repaid_loans,
                            Carbon::parse($row->created_at)->format('d/m/Y'),
                            (string) $row->max_overdue_days,
                        ];
                        fputcsv($output, $r);
                    }
                },
                'client_without_active_loan.client_id',
                'client_id'
            );

            fclose($output);
            return;

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error ExportAction(1): ' . $msg);

            return ;
        }
    }

    public function executeXLSX(array $filters = [])
    {
        // return new ClientsWithoutActiveLoanExport($this->repository->getBuilder($filters));

        try {

            // Create a new Spreadsheet object
            $spreadsheet = new Spreadsheet();

            // Get the active sheet
            $sheet = $spreadsheet->getActiveSheet();

            // Add column headers as the first row
            $headers = [
                __('table.ClientId'),
                __('table.Amount'),
                __('table.Product'),
                __('table.LastLoanRepaidAt'),
                __('table.DaysSinceLastLoan'),
                __('table.ActiveDiscount'),
                __('table.Phone'),
                __('table.Email'),
                __('table.NoRepaidLoans'),
                __('table.LastApplicationDate'),
                __('table.MaxOverdueLastLoan'),
            ];
            $sheet->fromArray([$headers], null, 'A1');
            $rowIndex = 1;

            $builder = $this->repository->getBuilder($filters);
            $builder->getQuery()->chunkById(
                100,
                function ($rows) use (&$sheet, &$rowIndex) {

                    foreach ($rows as $row) {

                        $r = [
                            $row->client_id,
                            intToFloat($row->amount_approved),
                            $row->product_id ? getProductName($row->product_id) : null,
                            Carbon::parse($row->repaid_at)->format('d/m/Y'),
                            $row->days_since_last_loan,
                            $row->active_discount,
                            $row->phone,
                            $row->email,
                            $row->no_repaid_loans,
                            Carbon::parse($row->created_at)->format('d/m/Y'),
                            (string) $row->max_overdue_days,
                        ];

                        // Populate data from the array
                        $rowIndex++;
                        foreach ($r as $columnIndex => $value) {
                            $sheet->setCellValue(
                                [$columnIndex + 1, $rowIndex],
                                $value
                            );
                        }
                    }
                },
                'client_id'
            );

            // Create a writer object
            $writer = new Xlsx($spreadsheet);

            // Set the headers to force download the file
            $fileName = 'report_clients_without_active_loan_export_' . time() . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $fileName . '"');
            header('Cache-Control: max-age=0');

            // Write the spreadsheet to the output
            $writer->save('php://output');
            return;

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error ExportAction(2): ' . $msg);
        }
    }
}
