<?php

namespace Modules\Head\Application\Actions;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\SaleAttempt;
use Modules\Head\Services\SpreadsheetHelper;
use Modules\Sales\Repositories\SaleAttemptRepository;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExportTelemarketingAction
{
    public function __construct(
        private SaleAttemptRepository $saleAttemptRepository
    ) {
    }

    public function execute(array $filters = []): void
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([$this->getHeaders()], null, 'A1');
        $rowIndex = 1;

        $builder = $this->saleAttemptRepository->telemarketingBuilder($filters);

        $builder->chunkById(100, function ($rows) use (&$sheet, &$rowIndex) {
            /** @var SaleAttempt $saleAttempt ** */
            foreach ($rows as $saleAttempt) {
                /// get sale task
                $saleTask = $saleAttempt->saleTask ?? $saleAttempt->saleTaskHistory;

                /// sometimes we delete sale tasks directly from database.
                /// in this case just continue
                if (!$saleTask) {
                    continue;
                }

                $loan = $saleTask?->loan;
                $sd = $saleAttempt?->saleDecision;

                $loanSignInDate = $loan?->getLastHistoryForStatus(LoanStatus::SIGNED_STATUS_ID)?->date ?? '';
                $row = [
                    $loan?->getKey(),
                    $saleTask->getKey(),
                    __('sales::saleTaskType.' . $saleTask->saleTaskType->name),
                    $saleTask->processedBy?->getFullNames(),
                    $saleTask->created_at,
                    $saleAttempt->start_at,
                    $saleAttempt->end_at,
                    $sd?->name ? __('sales::saleDecision.' . $sd?->name) : '',
                    $saleAttempt->comment,
                    $saleTask->discount,
                    $loanSignInDate,
                    $loan?->getStatusLabel(),
                    $loan?->product_id ? getProductName($loan?->product_id) : '',
                    $loan?->created_at,
                    $loan ? ($loan?->period_approved . ' ' . trans_choice(
                            'product::product.keys.' . $loan?->loanProductSetting?->period,
                            $loan?->period_approved
                        )) : '',
                ];

                // Populate data from the array
                $rowIndex++;
                $sheet->fromArray($row, null, 'A' . $rowIndex);
            }
        });

        /// set autosize columns
        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Set the headers to force download the file
        $fileName = 'export_telemarketing_' . now()->format('d_m_Y') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');

        // Write the spreadsheet to the output
        $writer->save('php://output');

        return ;
    }

    private function getHeaders(): array
    {
        return [
            __('table.LoanId'),
            __('table.SaleTaskId'),
            __('table.SaleTaskType'),
            __('table.SaleTaskAgent'),
            __('table.SaleTaskCreatedAt'),
            __('table.SaleTaskStartProcessing'),
            __('table.SaleTaskEndProcessing'),
            __('table.SaleTaskExit'),
            __('table.Comment'),
            __('table.SaleTaskDiscount'),
            __('table.SignLoanTime'),
            __('table.Status'),
            __('table.Product'),
            __('table.CreatedAt'),
            __('table.Period'),
        ];
    }
}
