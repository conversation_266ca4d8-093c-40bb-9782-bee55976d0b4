<?php

namespace Modules\Head\Application\Actions;

use Modules\Common\Models\Loan;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\SpreadsheetHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

readonly class ExportRequestsAction
{
    public function __construct(
        private LoanRepository $loanRepository
    ) {
    }

    public function execute(array $filters = []): array
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([$this->getHeaders()], null, 'A1');
        $rowIndex = 1;

        $builder = $this->loanRepository->requestsBuilder($filters);

        $builder->chunkById(100, function ($rows) use (&$sheet, &$rowIndex) {
            /** @var Loan $loan ** */
            foreach ($rows as $loan) {
                $row = [
                    $loan->getKey(),
                    $loan->created_at->format('d.m.Y'),
                    $loan->creator?->getFullNames(),
                    $loan->getStatusLabel(),
                    intToFloat($loan->amount_approved),
                    $loan->discount_percent
                ];

                // Populate data from the array
                $rowIndex++;
                foreach ($row as $columnIndex => $value) {
                    $sheet->setCellValue(
                        [$columnIndex + 1, $rowIndex],
                        $value
                    );
                }
            }
        });

        /// set autosize columns
        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'export_requests_' . now()->format('d_m_Y') . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
    }

    private function getHeaders(): array
    {
        return [
            __('table.LoanId'),
            __('table.CreatedAt'),
            __('table.CreatedBy'),
            __('table.Status'),
            __('table.AmountApproved'),
            __('table.Discount'),
        ];
    }
}