<?php

namespace Modules\Head\Application\Actions;

use <PERSON><PERSON><PERSON>\Head\Exports\DiscountedLoansExport;
use <PERSON><PERSON><PERSON>\Head\Repositories\Loan\DiscountedLoansRepository;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

final readonly class DiscountedLoansExportAction
{
    public function __construct(private DiscountedLoansRepository $repository)
    {
    }

    public function execute(array $filters): DiscountedLoansExport
    {
        return new DiscountedLoansExport($this->repository->getBuilder($filters));
    }

    public function exportXlsx2(array $filters = [])
    {
        try {

            // Set the headers to force download the file
            $fileName = 'loan_and_discounts_' . time() . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $fileName . '"');
            header('Cache-Control: max-age=0');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
            header('Cache-Control: cache, must-revalidate');
            header('Pragma: public');

            ob_start();

            // Create a new Spreadsheet object
            $spreadsheet = new Spreadsheet();

            // Get the active sheet
            $sheet = $spreadsheet->getActiveSheet();

            // Add column headers as the first row
            $headers = [
                __('table.CreatedAt'),
                __('table.LoanId'),
                __('table.LoanStatus'),
                __('table.LoanAmountApproved'),
                __('table.IsLoanFirst'),
                __('table.NumberOfLoansAtTimeOfApp'),
                __('table.DiscountPercent'),
            ];
            $sheet->fromArray([$headers], null, 'A1');
            $rowIndex = 1;

            $builder = $this->repository->getBuilder($filters);
            $builder->chunkById(
                100,
                function ($rows) use (&$sheet, &$rowIndex) {

                    foreach ($rows as $row) {

                        $r = [
                            $row->created_at,
                            $row->loan_id,
                            __('head::loanStatus.' . $row->name),
                            intToFloat($row->amount_approved),
                            $row->first_loan ? __('table.Yes') : __('table.No'),
                            (string) $row->prev_loans_count,
                            $row->discount_percent,
                        ];

                        // Populate data from the array
                        $rowIndex++;
                        $sheet->fromArray($r, null, 'A' . $rowIndex);
                    }
                },
                'loan.loan_id',
                'loan_id'
            );

            // Create a writer object
            $writer = new Xlsx($spreadsheet);

            // Write the spreadsheet to the output
            $writer->save('php://output');

            ob_end_flush();

            return;

        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error DiscountedLoansExportAction: ' . $msg);

            return;
        }
    }
}
