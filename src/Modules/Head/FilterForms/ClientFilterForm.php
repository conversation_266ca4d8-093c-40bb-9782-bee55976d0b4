<?php

namespace Modules\Head\FilterForms;

use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\Client;

class ClientFilterForm extends BaseFilterForm
{
    public function buildForm(): void
    {
        $this->addClientIdFilter();
        $this->addPinFilter();
        $this->addClientNameFilter();
        $this->addPhoneFilter();
        $this->addEmailFilter();

        $personTypeOptions = [
            Client::LS_INDV => __('table.individual'),
            Client::LS_COMP => __('table.company'),
        ];
        $this->addSimpleSelectFilter('legal_status', $personTypeOptions, __('table.LegalStatus'));

        $citizenshipTypeOptions = [
            Client::CT_LOCAL => __('table.local'),
            Client::CT_FOREIGNER => __('table.foreigner'),
        ];
        $this->addSimpleSelectFilter('citizenship_type', $citizenshipTypeOptions, __('table.CitizenshipType'));

        $this->addCreatedAtFilter();

        $personStatusOptions = [
            'active' => __('table.Active'),
            'blocked' => __('table.Blocked'),
            'deleted' => __('table.Deleted'),
        ];
        $this->addSimpleSelectFilter('client_status', $personStatusOptions, __('table.Status'));

        $reasonOptions = $this->getData('blockReasons', []);
        $this->addSimpleSelectFilter('block_reason_id', $reasonOptions, __('table.FilterReason'));

        $reasonOptions = $this->getData('deleteReasons', []);
        $this->addSimpleSelectFilter('delete_reason_id', $reasonOptions, __('table.FilterReason'));
    }
}
