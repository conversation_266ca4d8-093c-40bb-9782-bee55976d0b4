<?php

namespace Modules\Head\FilterForms;

use Mo<PERSON>les\Common\FilterForms\BaseFilterForm;
use Modules\Communication\Application\Enums\CommunicationClassificationEnum;
use Modules\Communication\Services\CommunicationService;

class ClientCommunicationFilterForm extends BaseFilterForm
{

    public function buildForm(): void
    {
        $this
            ->add('clientId', 'hidden', [
                'label' => __('head::clientCard.eventDate'),
                'value' => $this->request->get('clientId')
            ])
            ->add('createdAt', 'text', [
                'label' => __('head::clientCard.eventDate'),
                'attr' => [
                    'data-daterange-picker' => 'true'
                ]
            ])
            ->add('channel', 'select', [
                'label' => __('head::clientCard.channel'),
                'selected' => $this->request->get('channel') ?? '',
                'empty_value' => __('Select option'),
                'choices' => $this->getCommunicationChannels(),
            ]);
            // ->add('classification', 'select', [
            //     'label' => __('head::clientCard.classification'),
            //     'selected' => $this->request->get('classification') ?? '',
            //     'empty_value' => __('Select option'),
            //     'choices' => $this->getCommunicationClassifications(),
            // ]);
//            ->add('content', 'text', [
//                'label' => __('head::clientCard.content'),
//                'attr' => [
//
//                ]
//            ])
//            ->add('createdBy', 'text', [
//                'label' => __('head::clientCard.createdBy'),
//                'attr' => [
//
//                ]
//            ]);
    }

    private function getCommunicationClassifications(): array
    {
        $classifications = CommunicationClassificationEnum::toArray();

        $out = [];
        foreach ($classifications as $classification) {
            $out[$classification] = __(ucfirst($classification));
        }

        return $out;
    }

    /**
     * @return array
     */
    private function getCommunicationChannels(): array
    {
        $channels = CommunicationService::COMMUNICATION_CHANNELS;

        return array_map('__', $channels);
    }
}
