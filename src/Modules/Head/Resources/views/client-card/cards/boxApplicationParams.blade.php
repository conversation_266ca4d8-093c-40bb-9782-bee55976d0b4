@isset($loan)
    @php
        $firstInstallment = !empty($loan) ? $loan->installments->firstWhere('seq_num', 1) : null;
        $installmentsApproved = $loan->installments_approved;
        $loanGuarantsData = $loan->loanGuarants();
        $product = $loan->product;
    @endphp

    <x-card>
        <x-slot:title>
            @if($loan->wasActive())
                {!! __('head::clientCard.loanParameters') . " <span class='text-danger'>#" . $loan->getKey() . '</span>' !!}
                <span class="text-danger">&nbsp;[{{$loan->getStatusLabel()}}]</span>
            @else
                {!! __('head::clientCard.queryParameters') . ' <span class="text-danger">#' . $loan->getKey() . '</span>' !!}
                <span class="text-danger">&nbsp;[{{$loan->getStatusLabel()}}]</span>
            @endif

            @if(!empty($loan->migration_provision_id) || !empty($loan->migration_nefin_id))
                <br/>
            @endif
            @if(!empty($loan->migration_provision_id) || 'provision' == $loan->migration_db)
                <span
                    style="font-size: 10px; color: #2f3742">Provision id: {{ (!empty($loan->migration_provision_id) ? $loan->migration_provision_id : $loan->contract_number )}}</span>
            @endif
            @if(!empty($loan->migration_nefin_id))
                <span style="font-size: 10px; color: #2f3742">
                    Nefin id: {{$loan->migration_nefin_id . (preg_match('/^(nefin_)/', $loan->migration_db) ? '(' . preg_replace('/^(nefin_)/', '', $loan->migration_db) . ')': '') }}
                </span>
            @endif

        </x-slot:title>
        <x-slot:cardOptions>
        <span class="cursor-move" title="{{ __('click and move me') }}">
            <i class="fa fa-arrows-alt"></i>
        </span>
        </x-slot:cardOptions>

        <x-table>
            <tr>
                <th>{{ __('head::clientCard.product') }}</th>
                <td>{{ $product?->trade_name ?? '' }}</td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.wantedAmount') }}</th>
                <td>
                    {{ amount($loan->amount_approved ?? 0) }}
                </td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.period') }}</th>
                <td>
                    {{ $loan->period_approved ?? '' }}
                    {{ trans_choice('product::product.keys.' . $loan->getPeriod(), $loan->period_approved) }}
                </td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.installmentsCount') }}</th>
                <td>
                    {{
                        $loan->installments_approved
                        . ' '
                        . trans_choice('head::clientCard.installmentSinglePlural', $loan->installments_approved)
                    }}
                </td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.dateOfFirstInstallment')  }}</th>
                <td>{{ formatDate($firstInstallment->due_date, 'd.m.Y') }}</td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.firstInstallmentAmount') }}</th>
                <td>
                    <span id="firstInstallmentAmount">{{ $firstInstallment->total_amount }}</span>
                    @if(!$loan->wasActive())
                        <a
                            id="preliminaryPaymentPlanLink"
                            target="_blank"
                            href="{{ route('head.loans.showPreliminaryPaymentPlan', [
                                'discount' => intval($loan->discount_percent),
                                'period' => $loan->period_approved,
                                'productId' => $loan->product_id,
                                'sum' => $loan->amount_approved,
                                'interest' => $loan->interest_percent,
                                'penalty' => $loan->penalty_percent,
                            ]) }}"
                        >
                            <i class="fa fa-calendar ml-2" aria-hidden="true"></i>
                        </a>
                    @else
                        <i id="calendarIcon" class="fa fa-calendar ml-2" aria-hidden="true"></i>
                    @endif
                </td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.paymentMethod') }}</th>
                <td>{{ __('payments::paymentMethods.' . $loan->payment_method_id) }}</td>
            </tr>
            <tr>
                <th>{{ __('other.PaymentAccount') }}</th>
                <td>{{ $loan->paymentAccount()?->name }}</td>
            </tr>
            <tr>
                <th>{{ __('head::clientCard.office') }}</th>
                <td>{{ $loan->office?->name }}</td>
            </tr>
            @if($loan->payment_method_id == \Modules\Common\Models\PaymentMethod::PAYMENT_METHOD_BANK)
                <tr>
                    <th>{{ __('head::clientCard.bankAccount') }}</th>
                    <td>{{ $loan->bankAccount->first()->iban ?? ''}}</td>
                </tr>
            @endif
            <tr>
                <th>{{ __('head::clientCard.createdBy') }}</th>
                <td>
                    {{ !empty($loan->creator) ? $loan->creator->first_name . " " . $loan->creator->last_name : '' }}
                </td>
            </tr>

            @if($loan->insurance?->isTrue())
                <tr>
                    <th colspan="2" class="bg-light">{{ __('table.LoanInsurance') }}&nbsp;</th>
                </tr>
                <tr>
                    <th>{{ __('table.InsuranceAmount') }}</th>
                    <td>
                        {{intToFloat(getInsuranceAmount())}}
                    </td>
                </tr>
            @endif

            <tr>
                <th colspan="2" class="bg-light">
                    {{ __('table.Guarantors') }}&nbsp;
                    <a href="#addClientGuarantorModal"
                       data-target="#addClientGuarantorModal"
                       data-toggle="modal"
                       class="text-success"
                    >
                        <i class="fa fa-plus"></i>&nbsp;
                        {{__('table.AddingGuarant')}}
                    </a>
                </th>
            </tr>
            @if(!empty($loanGuarantsData))
                @foreach($loanGuarantsData as $loanGuarant)
                    <tr>
                        <th>{{ __('Guarantor') }}
                            :&nbsp;{{$loop->iteration}}</th>
                        <td class="cursor-pointer">
                            {{$loanGuarant->getFullName()}}
                            <div class="show-on-hover pull-right">
                                {{Form::open(['route' => ['head.client-guarantors.destroy', $loanGuarant->getKey(), $loan->getKey()], 'method' => 'DELETE'])}}

                                <button type="submit"
                                        data-confirm="{{__('You sure? This action is not recover.')}}"
                                        class="text-danger confirm border-0 p-0 bg-none"
                                        title="{{__('Delete contact')}}"
                                >
                                    <small><i class="fa fa-trash-o"></i>&nbsp;{{__('Delete guarantor')}}
                                    </small>
                                </button>
                                {{Form::close()}}
                            </div>
                            <!-- End ./show-on-hover -->
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('table.Phone')}}</th>
                        <td>{{$loanGuarant->phone}}</td>
                    </tr>
                @endforeach
            @endif
        </x-table>

    </x-card>
@endisset
