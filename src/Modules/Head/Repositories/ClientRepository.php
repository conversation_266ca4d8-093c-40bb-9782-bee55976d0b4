<?php

namespace Modules\Head\Repositories;

use App\Exceptions\FrontEndExceptions;
use Elastic\Elasticsearch\Client as EsClient;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Api\Domain\Exceptions\MvrReportNotFound;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Common\Enums\VeriffProviderStatusEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Models\CreditLimit;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Repositories\BaseRepository;
use Modules\ThirdParty\Services\MvrService;

final class ClientRepository extends BaseRepository
{
    public const LIMIT_FOR_SEARCH_CLIENTS = 5;
    public const ES_INDEX = 'clients';

    public const RELATIONSHIP_KEYS = [
        'block_reason_id' => 'clientBlockHistories',
        'delete_reason_id' => 'clientDeleteHistories',
    ];

    public function __construct(
        protected Client $client,
        private readonly EsClient $esClient,
    ) {
    }

    public function clientIsDeleted(string $pin): bool
    {
        return Client::withTrashed()->where([
            'pin' => $pin,
            'deleted' => 1,
        ])->whereNotNull('deleted_at')->exists();
    }

    public function veriffListingBuilder(array $filters): Builder
    {
        return Client::query()
            ->select(
                [
                    'client_id',
                    'pin',
                    'first_name',
                    'last_name',
                    'middle_name',
                    'phone',
                    'verif_offered_at',
                    'verif_provider_status',
                    'verif_processed_loan_id',
                    'verif_provider_data',
                ]
            )
            ->where('verif_offered', true) /// only which see page self-verification
            ->orderByRaw('verif_offered_at DESC NULLS LAST')
            ->filterBy($filters);
    }

    public function getVeriffStats(): array
    {
        return [
            'skip_verification' => Client::select(['client_id', 'verif_skipped'])
                ->where('verif_skipped', true)
                ->count(),

            'started_verification' => Client::select(['client_id', 'verif_skipped'])
                ->where('verif_skipped', false)
                ->count(),

            'finished_verification' => Client::select(['client_id', 'verif_skipped', 'verif_provider_status'])
                ->where('verif_skipped', false)
                ->whereIn('verif_provider_status', [
                    VeriffProviderStatusEnum::VERIF_STATUS_APPROVED->value,
                    VeriffProviderStatusEnum::VERIF_STATUS_DECLINED->value,
                ])
                ->count(),
        ];
    }

    public function blackListClientsBuilder(array $filters = []): Builder
    {
        return $this->client
            ->with(['clientBlockHistories', 'clientUnblockBlockHistories'])
            ->leftJoin('client_block_history', 'client.client_id', '=', 'client_block_history.client_id')
            ->where('blocked', 1)
            ->filterBy($filters)
            ->select('client.*', DB::raw('MAX(client_block_history.created_at) as latest_block_date'))
            ->groupBy('client.client_id')
            ->orderBy('latest_block_date', 'DESC')
            ->distinct();
    }

    public function blackListClientsHistoryBuilder(array $filters = []): Builder
    {
        return $this->client
            ->with(['clientBlockHistories', 'clientUnblockBlockHistories'])
            ->leftJoin('client_block_history', 'client.client_id', '=', 'client_block_history.client_id')
            ->filterBy($filters)
            ->whereHas('clientBlockHistories')
            ->select('client.*', DB::raw('MAX(client_block_history.created_at) as latest_block_date'))
            ->groupBy('client.client_id')
            ->orderBy('latest_block_date', 'DESC')
            ->distinct();
    }

    public function createClientCard(Client $client, string $idCardNumber): void
    {
        $mvrReportData = app(MvrService::class)->addMvrReport($client->pin, $idCardNumber)?->getData();
        if ($mvrReportData) {
            $client->setAttribute('idcard_number', $mvrReportData->idCardNumber);
            $client->saveQuietly();

            app(ClientIdCardRepository::class)->createFromMvrData($client, $mvrReportData);
        } else {
            throw new MvrReportNotFound($client->pin, $idCardNumber);
        }
    }

    public function updateClientIdCardNumber(Client $client, string $idCardNumber): void
    {
        /// first check if we have this id-card number
        $clientIdCard = ClientIdCard::where([
            'client_id' => $client->getKey(),
            'idcard_number' => $idCardNumber,
        ])->first();

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        /// if we have in database this id-card
        /// update id-card number in clients.
        if ($clientIdCard) {
            $client->setAttribute('idcard_number', $clientIdCard->idcard_number);
            $client->saveQuietly();

            return;
        }

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        /// if we don't have this id-card in database
        /// run MVR-check
        $mvrReportData = app(MvrService::class)->addMvrReport($client->pin, $idCardNumber)?->getData();
        if ($mvrReportData && !Carbon::parse($mvrReportData->validDate)->isPast()) {
            //// sometimes mvr not provide id_card number
            /// in this task: https://trello.com/c/SDkOxbD0 have additional details
            if ($mvrReportData->idCardNumber) {
                $client->setAttribute('idcard_number', $mvrReportData->idCardNumber);
                $client->saveQuietly();
            }

            app(ClientIdCardRepository::class)->createFromMvrData($client, $mvrReportData);
        }

        //// if we dont receive mvr report throw exception
        throw new MvrReportNotFound($client->pin, $idCardNumber);
    }

    public function getLoansForRefinance(?Client $client = null): Collection
    {
        if (!$client) {
            return collect([]);
        }

        return $client->loans()
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->with(['client', 'loanStatus'])
            ->get();
    }

    public function getClientsByFilters(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        return $this->getBuilderByFilters($filters)
            ->paginate($perPage);
    }

    public function getBuilderByFilters(array $filters): Builder
    {
        return $this->client
            ->filterBy($filters)
            ->with(['loans', 'activeAndRepaidLoans', 'clientDeleteHistories', 'clientBlockHistories'])
            ->orderBy('created_at', 'DESC');
    }

    public function getCommunicationByFilters(array $filters = [], ?int $limit = null)
    {
        if (empty($filters['client_id'])) {
            throw new FrontEndExceptions('No client provided for communication log');
        }

        $clientId = $filters['client_id'];
        $channel = !empty($filters['channel']) ? $filters['channel'] : null;
        $createdAt = !empty($filters['createdAt']) ? $filters['createdAt'] : null;

        $builder = CommunicationPivot::select(
            DB::raw(
                "
            communication_pivots.id,
            communication_pivots.communication_id,
            TO_CHAR(communication_pivots.created_at, 'DD.MM.YYYY HH24:MI') as created_at,
            communication_pivots.communication_type,
            communication_pivots.communication_key,
            CASE
                WHEN (communication_pivots.created_by IS NULL OR communication_pivots.created_by = 0 OR communication_pivots.created_by = 1) THEN 'Автоматично'
                ELSE CONCAT_WS(' ', administrator.first_name, administrator.last_name)
            END as sender
        "
            )
        );

        $builder->where('communication_pivots.client_id', $clientId);
        if ($channel) {
            $builder->where('communication_pivots.communication_type', $channel);
        }
        if ($createdAt) {
            $to = null;
            $from = null;
            $dateRange = explode(' - ', $createdAt);
            if (count($dateRange) === 1) {
                // Date format "dd-mm-yyyy"
                $from = date("Y-m-d 00:00:00", strtotime($createdAt));
                $to = date("Y-m-d 23:59:59", strtotime($createdAt));
            } elseif (count($dateRange) === 2) {
                // Date format "dd-mm-yyyy - dd-mm-yyyy"
                $from = date("Y-m-d 00:00:00", strtotime(trim($dateRange[0])));
                $to = date("Y-m-d 23:59:59", strtotime(trim($dateRange[1])));
            }

            if ($to && $from) {
                $builder->where('communication_pivots.created_at', '>=', $from);
                $builder->where('communication_pivots.created_at', '<=', $to);
            }
        }

        $builder
            ->leftJoin('administrator', 'administrator.administrator_id', '=', 'communication_pivots.created_by')
            ->orderBy('communication_pivots.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);

            return $builder->get();
        }

        return $builder->paginate(10);
    }

    /**
     * @param int|null $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     *
     * @return mixed
     */
    public function getAll(
        ?int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['active' => 'DESC', 'client_id' => 'DESC']
    ) {
        // first we check if any office attached to logged-in administrator
        // by getting his office IDs
        $adminOfficeIds = $this->getAdminOfficeIds();
        if (empty($adminOfficeIds)) {
            return null;
        }

        // mandatory join to client_office, since administrator could see only clients
        // from the offices he attached
        $joins['join'][] = [
            'client_office',
            'client_office.client_id',
            '=',
            'client.client_id',
        ];

        $builder = DB::table('client');

        // select the fields we need
        $builder->select(
            DB::raw(
                'client.*, ('
                . "SELECT COUNT(loan.loan_id)
                FROM loan
                WHERE
                    loan.client_id = client.client_id
                    AND loan.loan_status_id IN ("
                . implode(', ', Loan::getCountableStatuses())
                . ")
            ) as loans_total_count"
            )
        );
        $builder->groupBy('client.client_id');
        // join tables, depends on filters, etc.
        $this->setJoins($joins, $builder);

        // if we have relational filters, such as block/delete history
        // we need always to check versus last record, so we add this flag to where
        if ($this->hasRelationalKeys($where)) {
            $where[] = [
                'last',
                '=',
                1,
            ];
        }

        // add where conditions
        if (!empty($where)) {
            $builder->where($where);
        }
        $builder->whereIn('client_office.office_id', $adminOfficeIds);

        // add order if provided
        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }

        // get paginated result, but since our data is made from several table,
        // we are hydrating the data to return it to Collection with Clients
        // instead of arrays, then we reset it to paginator
        $result = $builder->paginate($limit);
        $records = (new Client())->hydrateCustom($result->all());
        $result->setCollection($records);

        return $result;
    }


    public function getById(int $clientId, bool $withTrashed = false): ?Client
    {
        $query = Client::query();

        if ($withTrashed) {
            $query->withTrashed();
        }

        return $query->where('client_id', $clientId)->first();
    }

    public function getByPin(string $pin, array $columns = ['*']): ?Client
    {
        if (empty($pin)) {
            return null;
        }

        return Client::where('pin', '=', $pin)->first($columns);
    }

    public function getIdByPin(string $pin): ?int
    {
        return Client::where('pin', '=', $pin)->value('client_id');
    }

    public function getClientDataWithIdCardByPin(string $pin): ?Client
    {
        return Client::where('client.pin', $pin)
            ->leftJoin('client_idcard', 'client_idcard.client_id', '=', 'client.client_id')
            ->leftJoin('idcard_issued', 'idcard_issued.idcard_issued_id', '=', 'client_idcard.idcard_issued_id')
            ->where('client_idcard.last', 1)
            ->first();
    }

    /**
     * @param Client $client
     */
    public function delete(Client $client)
    {
        $client->delete();
    }

    /**
     * @param array $data
     *
     * @return Client
     */
    public function create(array $data)
    {
        $client = new Client();
        $client->fill($data);
        $client->save();

        return $client;
    }

    public function save(Client $client): ?Client
    {
        return $client->save() ? $client : null;
    }

    /**
     * @param Client $client
     * @param array $data
     */
    public function update(Client $client, array $data)
    {
        $client->fill($data);
        $client->save();
    }

    /**
     * @return mixed
     */
    public function getAllClients()
    {
        return Client::where(['deleted' => 0, 'active' => 1])->get();
    }

    public function searchClientsByString(string $searchString, bool $checkActiveLoans): array
    {
        $searchString = str_replace(' ', "%", trim($searchString));

        if (ctype_digit($searchString)) {
            return Client::query()
                ->select(DB::raw("CONCAT_WS(' ',first_name,middle_name,last_name) as name,phone,email,pin,client_id"))
                ->where(function ($query) use ($searchString) {
                    $query
                        ->orWhere('client_id', $searchString)
                        ->orWhere('pin', 'ILIKE', "%{$searchString}%")
                        ->orWhere('phone', 'ILIKE', "%{$searchString}%");
                })
                ->whereHas('loans', function (Builder $builder) use ($checkActiveLoans) {
                    if ($checkActiveLoans) {
                        $builder->select('loan.loan_status_id')->where(
                            'loan.loan_status_id',
                            LoanStatus::ACTIVE_STATUS_ID
                        );
                    }
                })
                ->limit(10)
                ->get()
                ->toArray();
        }

        return Client::query()
            ->select(DB::raw("CONCAT_WS(' ',first_name,middle_name,last_name) as name,phone,email,pin,client_id"))
            ->where(function ($query) use ($searchString) {
                $query
                    ->orWhereRaw("CONCAT_WS(' ',first_name,middle_name,last_name) ILIKE '%{$searchString}%'")
                    ->orWhere('email', 'ILIKE', "%{$searchString}%");
            })
            ->whereHas('loans', function (Builder $builder) use ($checkActiveLoans) {
                if ($checkActiveLoans) {
                    $builder->select('loan.loan_status_id')->where('loan.loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
                }
            })
            ->limit(10)
            ->get()
            ->toArray();
    }

    public function searchByClientNameInElastic(string $searchString): array
    {
        $keywords = preg_split('/\s+/', trim($searchString)); // ['Easus', 'Payus', 'Testus']
        $mustClauses = [];

        foreach ($keywords as $word) {
            $mustClauses[] = [
                'match' => [
                    'name' => [
                        'query' => $word,
                        'operator' => 'and' // ensure stricter match
                    ]
                ]
            ];
        }

        $query = [
            'index' => 'clients',
            'body' => [
                'size' => 15,
                '_source' => ['_id'],
                'query' => [
                    'bool' => [
                        'must' => $mustClauses
                    ]
                ]
            ]
        ];

        $results = $this->esClient->search($query)->asArray();
        $hits = $results['hits']['hits'];

        return array_map(fn($hit) => $hit['_id'], $hits);
    }

    public function getClientsWithLastActiveLoanBySearchString(string $searchString): array
    {
        if (str_contains($searchString, '@')) {
            $query = [
                'multi_match' => [
                    'query' => $searchString,
                    'type' => 'phrase', // Enables the "prefix" search-as-you-type functionality
                    'fields' => ['email', 'email._2gram']
                ]
            ];
        } else {
            if (ctype_digit($searchString)) {
                $query = [
                    'multi_match' => [
                        'query' => $searchString,
                        'type' => 'bool_prefix', // Enables the "prefix" search-as-you-type functionality
                        'fields' => ['pin', 'pin._2gram', 'phone', 'phone._2gram']
                    ]
                ];
            } else {
                $query = [
                    'multi_match' => [
                        'query' => $searchString,
                        'type' => 'bool_prefix', // Enables the "prefix" search-as-you-type functionality
                        'fields' => ['name', 'name._2gram', 'email', 'email._2gram']
                    ]
                ];
            }
        }

        $results = $this->esClient->search([
            'index' => self::ES_INDEX,
            'body' => ['size' => 20, 'query' => $query]
        ])->asArray();

        $clients = [];
        foreach ($results['hits']['hits'] as $hit) {
            $clients[] = [
                'name' => $hit['_source']['name'],
                'phone' => $hit['_source']['phone'],
                'email' => $hit['_source']['email'],
                'pin' => $hit['_source']['pin'],
                'client_id' => $hit['_id'],
            ];
        }

        $loansByClients = DB::table(
            DB::table('loan', 'l')
                ->leftjoin('office as of', 'of.office_id', '=', 'l.office_id')
                ->whereIn('client_id', array_column($clients, 'client_id'))
                ->selectRaw(
                    'client_id, loan_id, loan_status_id, of.name as office_name, row_number() over (partition by client_id order by loan_status_id = '
                    . LoanStatus::ACTIVE_STATUS_ID . ' desc, loan_status_id = ' . LoanStatus::REPAID_STATUS_ID
                    . ' desc, l.created_at desc) as row_num'
                ),
            't'
        )->where('row_num', 1)->get();

        foreach ($clients as &$client) {
            $loan = $loansByClients->where('client_id', $client['client_id']);
            if (!$loan) {
                continue;
            }

            // we add loan id only for active loans.
            if ($loan->value('loan_status_id') === LoanStatus::ACTIVE_STATUS_ID) {
                $client['loan_id'] = $loan->value('loan_id');
            }

            $client['office_name'] = $loan->value('office_name');
        }

        return $clients;
    }

    /**
     * @param string $searchString
     *
     * @return array
     */
    public function searchClientsByStringLite(string $searchString): array
    {
        return DB::select(
            "
                SELECT
                    c.client_id,
                    c.pin,
                    CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) AS name,
                    (CASE
                        WHEN CAST(c.client_id AS TEXT) ILIKE CONCAT('', :search_string::text, '%') THEN 'id'
                        WHEN c.pin ILIKE CONCAT('%', :search_string::text, '%') THEN 'pin'
                        ELSE 'name'
                    END) AS flag
                FROM client AS c
                WHERE
                    CAST(c.client_id AS TEXT) ILIKE CONCAT('', :search_string::text, '%')
                    OR c.pin ILIKE CONCAT('%', :search_string::text, '%')
                    OR CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) ILIKE CONCAT('%', :search_string::text, '%')
                LIMIT :limit;
            ",
            [
                'search_string' => $searchString,
                'limit' => self::LIMIT_FOR_SEARCH_CLIENTS,
            ]
        );
    }

    public function searchClientsByKey(string $key, string $searchString): array
    {
        $where = "CAST($key AS TEXT) ILIKE CONCAT('', :search_string::text, '%')";
        if ($key == 'client_name') {
            $where = "CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) ILIKE CONCAT('%', :search_string::text, '%')";
        }

        return DB::select(
            "
                SELECT
                    c.client_id,
                    c.pin,
                    CONCAT_WS(' ', c.first_name, c.middle_name, c.last_name) AS client_name
                FROM
                     client AS c
                WHERE
                    $where
                LIMIT :limit;
            ",
            [
                'search_string' => $searchString,
                'limit' => self::LIMIT_FOR_SEARCH_CLIENTS,
            ]
        );
    }

    public function block(Client $client): Client
    {
        $client->disable();
        $client->blocked = 1;
        $client->save();

        return $client;
    }

    public function unblock(Client $client): Client
    {
        $client->enable();
        $client->blocked = 0;
        $client->blocked_to_date = null;
        $client->save();

        return $client;
    }

    /**
     * [hasRelationalKeys description]
     *
     * @param array $where
     *
     * @return boolean
     */
    private function hasRelationalKeys(array $where = []): bool
    {
        if (empty($where)) {
            return false;
        }

        return count(
            array_intersect(
                array_keys(self::RELATIONSHIP_KEYS),
                array_column($where, 0)
            )
        );
    }

    /**
     * @param array $whereConditions
     * @param int $limit
     * @param array|string[] $order
     * @param false $showDeleted
     */
    public function getClientsWithDiscount(
        array $whereConditions,
        int $limit,
        array $order = [],
        $showDeleted = false
    ) {
        if (empty($order)) {
            $order = ['client_discount_actual.client_discount_actual_id' => 'DESC'];
        }

        $whereConditions = $this->checkForDeleted(
            $whereConditions,
            $showDeleted,
            'client'
        );

        $builder = DB::table('client');
        $builder->select(
            DB::raw(
                "
            client.*,
            TRIM(CONCAT_WS(' ', client.first_name, client.middle_name, client.last_name)) AS client_full_name,
            client_discount_actual.client_discount_actual_id,
            client_discount_actual.product_id AS discount_product_id,
            product.name as discount_product_name,
            client_discount_actual.percent AS discount_percent,
            client_discount_actual.valid_from AS discount_valid_from,
            client_discount_actual.valid_till AS discount_valid_till,
            client_discount_actual.created_at AS discount_created_at,
            TRIM(CONCAT_WS(' ', administrator.first_name, administrator.last_name)) AS discount_creator
        "
            )
        );
        $builder->join('client_discount_actual', 'client_discount_actual.client_id', '=', 'client.client_id');
        $builder->join('product', 'product.product_id', '=', 'client_discount_actual.product_id');
        $builder->join('administrator', 'administrator.administrator_id', '=', 'client_discount_actual.created_by');
        $builder->where($whereConditions);

        foreach ($order as $key => $direction) {
            $builder->orderBy($key, $direction);
        }

        return $builder->paginate($limit);
    }

    /**
     * @param int $clientId
     *
     * @return array
     */
    public function getMaxSeqNum(int $clientId)
    {
        return DB::select(
            "SELECT MAX(seq_num)FROM loan_contact_actual where client_id = '$clientId'"
        );
    }

    public function getMaxSeqNumGuarant(int $clientId)
    {
        return DB::select(
            "SELECT MAX(seq_num)FROM loan_guarant_actual where client_id = '$clientId'"
        );
    }

    public function getClientGuarantData(string $pin)
    {
//        $row = DB::select(DB::raw(
//            "
//SELECT *
//FROM     (SELECT 'guarant' as data_from, guarant.* FROM guarant where ) guarant
//            NATURAL FULL JOIN
//         (SELECT 'client' as data_from, client,ci.* FROM client join client_idcard ci on client.client_id = ci.client_id where pin) client
//WHERE pin = '$pin'
//            "
//        ));


        $q1 = DB::select(
            DB::raw(
                "
            SELECT 'client' as data_from,
                   client.*,
                   ci.*
            FROM client
                     join client_idcard ci
                          on client.client_id = ci.client_id
            where client.pin = '$pin';

        "
            )
        );

        $q2 = DB::select(
            DB::raw(
                "
            SELECT 'guarant' as data_from,
                   guarant.*
            FROM guarant
            where guarant.pin = '$pin';

        "
            )
        );

        return [
            'client' => $q1,
            'guarant' => $q2,
        ];
    }

    public function getActiveOldClientsBuilder(?int $clientId = null)
    {
        $builder = Client::query()
            ->where('active', '=', 1)
            ->where('new', 0);

        if (!empty($clientId)) {
            $builder->where('client_id', $clientId);
        }

        return $builder->orderBy('client_id');
    }

    public function getMigratedClientsBuilder()
    {
        return Client::query()->where('active', '=', 1)->where('new', 0)->whereNotNull('migration_db')->orderBy(
            'client_id'
        );
    }

    public function getClientAndOffice(int $clientId): ?object
    {
        $statusApproved = LoanStatus::APPROVED_STATUS_ID;

        return DB::selectOne(
            "
            (SELECT
                c.client_id,
                c.pin,
                c.phone,
                c.email,
                c.first_name,
                c.middle_name,
                c.last_name,
                ll.office_id
            FROM client c
                INNER JOIN (
                SELECT l.office_id, l.loan_id
                FROM loan l
                WHERE l.client_id = $clientId
                ORDER BY
                    l.loan_status_id = $statusApproved DESC,
                    l.created_at DESC
                LIMIT 1
            ) as ll
            ON c.client_id = $clientId)
        "
        );
    }

    public function getClientsByPhone(string $phone): CustomEloquentCollection
    {
        $clientIds = ClientPhone::where(['number' => $phone])->get()->pluck('clientId');

        return Client::where(['phone' => $phone])->orWhereIn('client_id', $clientIds)->get();
    }

    /**
     * @param int $clientId
     * @return mixed
     */
    public function getLastCreditLimit(int $clientId)
    {
        return CreditLimit::where([
            'client_id' => $clientId
        ])->latest()->first();
    }

    public function simpleUpdate(
        Client $client,
        array $data
    ) {
        $client->fill($data);
        $client->save();

        return $client;
    }

    public function undelete(Client $client)
    {
        $client->deleted = 0;
        $client->updated_by = getAdminId();
        $client->updated_at = Carbon::now();
        $client->enabled_at = Carbon::now();
        $client->enabled_by = getAdminId();
        $client->active = 1;
        $client->save();
    }
}
