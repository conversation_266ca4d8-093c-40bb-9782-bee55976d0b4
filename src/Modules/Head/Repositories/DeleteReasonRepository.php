<?php

namespace Modules\Head\Repositories;

use Modules\Common\Models\DeleteReason;
use Modules\Common\Repositories\BaseRepository;

class DeleteReasonRepository extends BaseRepository
{
    public function getSelectOptions(): array
    {
        return DeleteReason::where('active', 1)
            ->where('deleted', 0)
            ->pluck('name', app(DeleteReason::class)->getKeyName())
            ->toArray();
    }
}
