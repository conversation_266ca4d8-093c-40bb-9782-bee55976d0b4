<?php

namespace Modules\Head\Repositories\Loan;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Builder;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Common\Repositories\BaseRepository;

class LoanActualStatsRepository extends BaseRepository
{
    public function getOverdueLoansForCreateCollectorFee(?int $loanId = null): Collection
    {
        $loanActualStats = LoanActualStats::query();

        $loanActualStats
            ->where('current_overdue_days', '>', 0)
            ->where('active', 1)
            ->where('deleted', 0);

        if ($loanId) {
            $loanActualStats->where('loan_id', $loanId);
        }

        /// filter only active loans
        $loanActualStats->whereHas('loan', function (Builder $builder) {
            $builder->where('loan.loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
        });

        return $loanActualStats->get();
    }

    /**
     * @param int $id
     *
     * @return mixed
     */
    public function getById(int $id)
    {
        return LoanActualStats::where(
            [
                'loan_stats_id' => $id,
                'deleted' => 0,
            ]
        )->first();
    }

    public function getActiveOverdueLoanStats(?int $limit = null, int $offset = 0): Collection
    {
        return LoanActualStats::select('loan_actual_stats.*')
            ->join('loan', 'loan.loan_id', '=', 'loan_actual_stats.loan_id')
            ->where([
                'loan.deleted' => 0,
                'loan.active' => 1,
                'loan_actual_stats.deleted' => 0
            ])
            ->where('loan.office_id', '!=', Office::OFFICE_ID_WEB)
            ->whereIn('loan_actual_stats.current_overdue_days', array_keys(Loan::LOAN_OVERDUE_DAYS_SEND_SMS))
            ->orderBy('loan_actual_stats.loan_stats_id')
            ->offset($offset)
            ->limit($limit)
            ->get();
    }

    public function save(LoanActualStats $dbModel): ?LoanActualStats
    {
        return $dbModel->save() ? $dbModel : null;
    }
}
