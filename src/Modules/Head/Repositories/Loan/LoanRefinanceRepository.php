<?php

namespace Modules\Head\Repositories\Loan;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\LoanRefinanceLog;

class LoanRefinanceRepository
{
    public function deleteByIds(int $refinancingId, int $refinancedId): void
    {
        /** @var LoanRefinance $existing */
        $existing = $this->getByIds($refinancingId, $refinancedId);
        if($existing){
            $this->delete($existing);
        }
    }

    public function getByIds(int $refinancingId, int $refinancedId): ?LoanRefinance
    {
        return LoanRefinance::where([
            'refinancing_loan_id'=>$refinancingId,
            'refinanced_loan_id'=>$refinancedId,
            'active'=>1
        ])->first();
    }

    public function getByRefinancingId(int $refinancingId): Collection
    {
        return LoanRefinance::where('refinancing_loan_id', $refinancingId)->get();
    }

    public function getDeletedByRefinancingId(int $refinancingId): Collection
    {
        return LoanRefinance::where('refinancing_loan_id', $refinancingId)
            ->where('deleted', 1)
            ->get();
    }

    public function save(LoanRefinance $loanRefinance): ?LoanRefinance
    {
        return $loanRefinance->save() ? $loanRefinance : null;
    }

    public function delete(LoanRefinance $existing): bool
    {
        $log = new LoanRefinanceLog();
        $log->fill($existing->getAttributes());
        $log->save();

        DB::statement(
            "DELETE FROM loan_refinance WHERE loan_refinance_id = " . intval($existing->loan_refinance_id) . ";"
        );

        return true;
    }

    public function restore(LoanRefinance $deleted): bool
    {
        $log = LoanRefinanceLog::where([
            'refinancing_loan_id'=>$deleted->refinancing_loan_id,
            'refinanced_loan_id'=>$deleted->refinanced_loan_id])
            ->orderBy('created_at', 'DESC')
            ->first();
        $log?->delete();

        // Custom restore implementation since LoanRefinance doesn't use SoftDeletes trait
        $deleted->deleted = 0;
        $deleted->active = 1;
        $deleted->deleted_at = null;
        $deleted->deleted_by = null;
        $deleted->updated_by = getAdminId();
        $deleted->updated_at = now();

        return $deleted->save();
    }
}
