<?php

namespace Modules\Head\Repositories;

use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Collect\Domain\Events\MarkedAsClosedJuridicalCase;
use Modules\Collect\Models\OuterCollectorReport;
use Modules\Common\Enums\BooleanCasesEnum;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Channel;
use Modules\Common\Models\Client;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanHistory;
use Modules\Common\Models\LoanIp;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanStatusHistory;
use Modules\Common\Models\LoanType;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductType;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\Tax;
use Modules\Common\Repositories\BaseRepository;

final class LoanRepository extends BaseRepository
{
    public function getDbModel(): Model
    {
        return new Loan();
    }

    public function getGuarantorLoans(Client $client): Collection
    {
        if (!$client->guarantorByPin) {
            return collect([]);
        }

        return $client->guarantorByPin?->loansActual()
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->get();
    }

    public function requestsBuilder(array $filters = []): Builder
    {
        /** @phpstan-ignore-next-line */
        return $this->getDbModel()
            ->with(['client', 'creator'])
            ->where('office_id', Office::OFFICE_ID_WEB)
            ->where('created_by', '!=', Administrator::SYSTEM_ADMINISTRATOR_ID) /// not system admin
            ->filterBy($filters)
            ->orderBy('loan_id', 'DESC');
    }

    public function outerCollectorLoansBuilder(array $filters = []): Builder
    {
        /** @phpstan-ignore-next-line */
        return $this->getDbModel()
            ->with(['client', 'product', 'markedAsOuterCollectorBy'])
            ->filterBy($filters)
            ->where('outer_collector', BooleanCasesEnum::YES)
            ->orderBy('outer_collector_from_date', 'DESC');
    }

    public function getApsExportRows(int $consultantId): ?array
    {
        $sql = <<<SQL
SELECT
    x.outer_collector_from_date::DATE AS "Дата на предаване за работа",
    x.loan_id AS "Номер на договор",
    CONCAT(c.first_name, ' ', c.middle_name, ' ', c.last_name) AS "Имена кредитополучател",
    c.pin AS "ЕГН",
    x.activated_at::DATE AS "Начало на договора",
    (SELECT i.due_date
         FROM installment i
         WHERE i.loan_id = x.loan_id
         ORDER BY i.due_date DESC
         LIMIT 1
    ) AS "Край на договора",
    las.due_amount_total  as "Пълен размер на кредита",
    las.accrued_amount_total as "Актуален дълг",
    (las.outstanding_amount_total - coalesce(las.accrued_amount_total,0)) as "Непадежирала сума",
    las.repaid_amount_total as "Общо платена сума",
    las.total_installments_count as "Общ брой вноски",
    (SELECT (i.principal + i.interest + i.penalty)
         FROM installment i
         WHERE i.loan_id = x.loan_id and paid = 0
         ORDER BY i.due_date ASC
         LIMIT 1
    ) AS "Размер на вноската",
    las.unpaid_installments_count as "Оставащ брой вноски",
    las.outstanding_amount_principal as "Главница(дълг)",
    las.outstanding_amount_interest as "Лихви(дълг)",
    '0' as "Разноски(дълг)",
    (las.outstanding_amount_penalty +  las.outstanding_amount_late_interest + las.outstanding_amount_late_penalty + las.outstanding_amount_taxes) as "Други(дълг)",
    c.phone as "Телефони за връзка",
    c.email as "E-Mai-и за връзка",
    las.current_overdue_days as "Дни просрочие",
    pc.name as "Лице за контакт",
    pc.phone as "Тел. лице за контакт",
    sc.name as "Лице за контакт 2",
    sc.phone as "Тел. лице за контакт 2",
    (
        select
            ((
                select coalesce(sum(t.rest_amount),0) AS sum_tax
                from tax t
                where
                    t.loan_id = x.loan_id
                    and t.paid = 0
                    and t.rest_amount > 0
                    and t.deleted = 0
            ) + (
                select coalesce(
                    (round(
                        sum(i.rest_principal + i.rest_interest + i.rest_penalty + i.rest_late_interest + i.rest_late_penalty),2) * 100)::INTEGER,
                        0
                    ) AS sum_inst_late
                from installment i
                where
                    i.loan_id = x.loan_id
                    and i.paid = 0
                    and due_date <= current_date
            ) + (
                SELECT coalesce(
                        (round(
                            sum(i.rest_principal + i.rest_late_interest + i.rest_late_penalty), 2
                        ) * 100)::INTEGER + (
                            CASE
                                WHEN sum(i.accrued_interest) > sum(i.paid_interest) THEN sum(i.accrued_interest) - sum(i.paid_interest)
                                ELSE 0
                            END
                        )*100::integer + (
                            CASE
                                WHEN sum(i.accrued_penalty) > sum(i.paid_penalty) THEN sum(i.accrued_penalty) - sum(i.paid_penalty)
                                ELSE 0
                            END
                        )*100::integer,
                        0
                    ) AS sum_inst_accrued
                FROM installment i
                WHERE
                    i.loan_id = x.loan_id
                    AND i.paid = 0
                    AND due_date > current_date
            ))::numeric/100
    )as "Сума предср. пог.",
    las.accrued_amount_principal as "Падеж. главница",
    las.accrued_amount_interest as "Падеж. лихви",
    (las.accrued_amount_penalty + las.accrued_amount_late_interest + las.accrued_amount_late_penalty + las.accrued_amount_taxes) as "Падеж. други",
    (select address from client_address where client_id = x.client_id and type = 'current' and last=1 order by client_address_id DESC limit 1) as "Адрес"
FROM loan x
join client c on x.client_id = c.client_id
join loan_actual_stats las on las.loan_id = x.loan_id
left join contact pc on (
    pc.contact_id = (
        select contact_id
        from loan_contact_actual
        where
            loan_contact_actual.client_id = x.client_id
            and deleted_at is null
            and active = 1 and
            seq_num = 1
        order by contact_id DESC
        limit 1
    )
)
left join contact sc on (
    sc.contact_id = (
        select contact_id
        from loan_contact_actual
        where
            loan_contact_actual.client_id = x.client_id
            and deleted_at is null
            and active = 1
            and seq_num = 2
        order by contact_id DESC
        limit 1
    )
)
where
    x.outer_collector = 1
    and x.consultant_id = $consultantId
order by x.loan_id;
SQL;

        return DB::select($sql);
    }

    public function getLmfExportRows()
    {
        $consultantId = OuterCollectorReport::LMF_CONSULTANT_ID;
        if (env('PROJECT') == 'lendivo') {
            $consultantId = OuterCollectorReport::LND_LMF_CONSULTANT_ID;
        }

        return Loan::where('office_id', Office::OFFICE_ID_WEB)
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->where('outer_collector', BooleanCasesEnum::YES)
            ->where('consultant_id', $consultantId)
            ->with([
                'client',
                'loanActualStats',
            ])
            ->orderBy('loan_id');
    }

    public function getLmfExportRows1(): ?array
    {
        $consultantId = OuterCollectorReport::LMF_CONSULTANT_ID;
        if (env('PROJECT') == 'lendivo') {
            $consultantId = OuterCollectorReport::LND_LMF_CONSULTANT_ID;
        }


        $builder = Loan::where('office_id', Office::OFFICE_ID_WEB)
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->where('outer_collector', BooleanCasesEnum::YES)
            ->where('consultant_id', $consultantId)
            ->with(['client']);

        $result = [];
        $builder->chunkById(200, function ($loans) use (&$result) {
            foreach ($loans as $loan) {
                $overdueAmounts = $loan->getOverdueAmounts();
                $lastPayment = $loan->getLastPayment();
                $periodPayments = $loan->getPaymentsByPeriods();
                $client = $loan->client;
                $clientPhones = $client->allClientPhonesExceptMain(2);
                $loanStats = $loan->loanActualStats;

                $phone2 = '';
                $phone3 = '';
                if ($clientPhones->count() > 0) {
                    foreach ($clientPhones as $clientPhoneObj) {
                        if (empty($phone2)) {
                            $phone2 = $clientPhoneObj->number;
                            continue;
                        }

                        $phone3 = $clientPhoneObj->number;
                    }
                }

                $addrCurrent = $client->clientLastAddressCurrent();
                $addrIdCard = $client->clientLastAddressIdcard();

                $contacts = $client->getLastContactsCollection(2);
                $contact1_phone = '';
                $contact1_name = '';
                $contact2_phone = '';
                $contact2_name = '';
                if ($contacts->count() > 0) {
                    foreach ($contacts as $contact) {
                        if (empty($contact1_phone)) {
                            $contact1_phone = $contact->phone;
                            $contact1_name = $contact->name;
                            continue;
                        }

                        $contact2_phone = $contact->phone;
                        $contact2_name = $contact->name;
                    }
                }

                $gender = '';
                if (!empty($client->gender)) {
                    $gender = ($client->gender == 'female' ? 'жена' : 'мъж');
                } else {
                    $idCard = $client->clientLastIdCard();
                    if (!empty($idCard->sex)) {
                        $gender = ($idCard->sex == 'female' ? 'жена' : 'мъж');
                    }
                }

                $r = [
                    'account_number' => $loan->loan_id,
                    'client_pin' => $client->pin,
                    'product_name' => ($loan->product_type_id == 1 ? 'до заплата' : 'на вноски'),
                    'client_gender' => $gender,
                    'client_name' => $client->getFullName(),
                    'due_date' => $loanStats->last_installment_date,
                    'total_overdue_amount' => (
                        $overdueAmounts->overdue_principal_amount
                        + $overdueAmounts->overdue_interest_amount
                        + $overdueAmounts->overdue_penalty_amount
                        + $overdueAmounts->overdue_late_interest_amount
                        + $overdueAmounts->overdue_late_penalty_amount
                        + $overdueAmounts->overdue_tax_amount
                    ),
                    'overdue_principal_amount' => $overdueAmounts->overdue_principal_amount,
                    'overdue_interest_amount' => $overdueAmounts->overdue_interest_amount,
                    'insurance' => '0.00',
                    'overdue_charges' => (
                        $overdueAmounts->overdue_late_penalty_amount
                        + $overdueAmounts->overdue_late_interest_amount
                        + $overdueAmounts->overdue_tax_amount
                    ),
                    'overdue_penalty_amount' => $overdueAmounts->overdue_penalty_amount,
                    'client_email' => $client->email,
                    'client_phone1' => $client->phone,
                    'client_phone2' => $phone2,
                    'client_phone3' => $phone3,
                    'addr_idcard_city' => $addrIdCard?->city?->name,
                    'addr_idcard_address' => $addrIdCard?->getAddress(),
                    'addr_current_postcode' => $addrCurrent?->post_code,
                    'addr_current_city' => $addrCurrent?->city?->name,
                    'addr_current_address' => $addrCurrent?->getAddress(),
                    'contract_date' => Carbon::parse($loan->created_at)->format('d.m.Y'),
                    'contact1_name' => $contact1_name,
                    'contact1_phone' => $contact1_phone,
                    'contact2_name' => $contact2_name,
                    'contact2_phone' => $contact2_phone,
                    'currency' => 'BGN',
                    'overdue_days' => $loanStats->current_overdue_days,
                    'loan_amount' => intToFloat($loan->amount_approved),
                    'installments_count' => $loanStats->total_installments_count,
                    'last_payment_date' => !empty($lastPayment->created_at) ? Carbon::parse(
                        $lastPayment->created_at
                    )->format('d.m.Y') : '',
                    'last_payment_amount' => !empty($lastPayment->amount) ? intToFloat($lastPayment->amount) : '',
                    'unpaid_installments_count' => $loanStats->unpaid_installments_count,
                    'paid_last_6m' => $periodPayments?->paid_180 ?? 0,
                    'paid_last_365' => $periodPayments?->paid_365 ?? 0,
                    'paid_last_730' => $periodPayments?->paid_730 ?? 0,
                    'total_paid' => $periodPayments?->total_paid ?? 0,
                ];

                $result[] = $r;
            }
        });

        return $result;
    }

    public function countLoansForToday(int $clientId): int
    {
        return Loan::query()
            ->where('client_id', $clientId)
            ->where('office_id', Office::OFFICE_ID_WEB)
            ->whereBetween('created_at', [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()])
            ->count();
    }

    public function hasActiveOnlineLoan(int $clientId): bool
    {
        $query = Loan::query()
            ->where('office_id', Office::OFFICE_ID_WEB)
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->where('client_id', $clientId);

        return $query->count() > 0;
    }

    public function hasActiveLoanByPin(string $pin): bool
    {
        $query = Loan::query()
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->whereHas('client', function ($query) use ($pin) {
                $query->where('pin', $pin);
            });

        return $query->count() > 0;
    }

    /**
     * @throws Exception
     */
    public function markAsClosedJuridicalCase(Loan $loan): void
    {
        $loan->setAttribute('isClosedJuridicalCase', 1);
        if (!$loan->save()) {
            throw new Exception('Error saving markAsClosedJuridicalCase();');
        }

        event(new MarkedAsClosedJuridicalCase($loan));
    }

    public function getByLoanIds(array $loanIds): Collection
    {
        return Loan::whereIn('loan_id', $loanIds)->get();
    }

    public function getByClientId(int $clientId, int $limit = 10): Collection
    {
        return Loan::where('client_id', $clientId)
            ->with(['product', 'loanStatus', 'office'])
            ->orderBy('created_at', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function getClientFirstLoan(int $clientId): ?Loan
    {
        return Loan::where('client_id', $clientId)
            ->whereIn('loan_status_id', LoanStatus::ACTIVE_STATUSES)
            ->orderBy('created_at', 'ASC')
            ->limit(1)
            ->first();
    }

    public function getClientFirstActivatedLoan(int $clientId): ?Loan
    {
        return Loan::where('client_id', $clientId)
            ->whereIn('loan_status_id', LoanStatus::ACTIVE_STATUSES)
            ->orderBy('activated_at', 'ASC')
            ->limit(1)
            ->first();
    }

    public function getClientLastActivatedLoan(int $clientId): ?Loan
    {
        return Loan::where('client_id', $clientId)
            ->whereIn('loan_status_id', [LoanStatus::ACTIVE_STATUS_ID, LoanStatus::REPAID_STATUS_ID])
            ->orderBy('activated_at', 'DESC')
            ->limit(1)
            ->first();
    }

    public function getClientFirstRepaidLoan(int $clientId): ?Loan
    {
        return Loan::where('client_id', $clientId)
            ->where('loan_status_id', LoanStatus::REPAID_STATUS_ID)
            ->orderBy('created_at', 'ASC')
            ->limit(1)
            ->first();
    }

    public function getClientLoansCount(int $clientId): int
    {
        return (int) Loan::where('client_id', $clientId)->count();
    }

    public function getClientApprovedLoansCount(int $clientId): int
    {
        return (int) Loan::where('client_id', $clientId)
            ->whereIn('loan_status_id', array_merge(LoanStatus::ACTIVE_STATUSES, [LoanStatus::APPROVED_STATUS_ID]))
            ->count();
    }

    public function getClientActivatedLoansCount(int $clientId): int
    {
        return (int) Loan::where('client_id', $clientId)
            ->whereIn('loan_status_id', LoanStatus::ACTIVE_STATUSES)
            ->count();
    }

    public function getClientCancelLoansCount(int $clientId): int
    {
        return (int) Loan::where('client_id', $clientId)
            ->where('loan_status_id', LoanStatus::CANCELLED_STATUS_ID)
            ->count();
    }

    public function getClientRepaidLoansCount(int $clientId): int
    {
        return (int) Loan::where('client_id', $clientId)
            ->where('loan_status_id', LoanStatus::REPAID_STATUS_ID)
            ->count();
    }

    public function getLifetimeValues(int $clientId): array
    {
        $result = DB::selectOne(
            "
            select
                sum(i.principal) as total_principal,
                sum(i.paid_principal) as total_paid_principal,
                (sum(i.paid_interest) + sum(i.paid_penalty) + sum(i.paid_late_interest) + sum(i.paid_late_penalty)) as total_paid_income
            from installment i
            where
                i.loan_id in (
                    select l.loan_id
                    from loan l
                    where
                        l.client_id = " . $clientId . "
                        and l.loan_status_id in (" . implode(',', LoanStatus::ACTIVE_STATUSES) . ")
                )
        "
        );

        $lfPrincipalWd = !empty($result->total_principal) ? $result->total_principal : 0;
        $lfPrincipalRepaid = !empty($result->total_paid_principal) ? $result->total_paid_principal : 0;
        $lfPrincipalIncome = !empty($result->total_paid_income) ? $result->total_paid_income : 0;

        $result = DB::selectOne(
            "
            select
                sum(t.paid_amount) as tax_amount
            from tax t
            where
                t.loan_id in (
                    select l.loan_id
                    from loan l
                    where
                        l.client_id = " . $clientId . "
                        and l.loan_status_id in (" . implode(',', LoanStatus::ACTIVE_STATUSES) . ")
                )
        "
        );

        $lfPrincipalIncomeTax = !empty($result->tax_amount) ? (float) intToFloat($result->tax_amount) : 0;

        return [
            'lifetime_principal_withdrawn' => $lfPrincipalWd,
            'lifetime_principal_repaid' => $lfPrincipalRepaid,
            'lifetime_income_repaid' => ($lfPrincipalIncome + $lfPrincipalIncomeTax),
            'lifetime_value_total' => ($lfPrincipalRepaid + $lfPrincipalIncome + $lfPrincipalIncomeTax - $lfPrincipalRepaid),
        ];
    }

    public function getClientMaxFromActiveLoansCurrentOverdueDays(int $clientId): int
    {
        $result = DB::selectOne(
            "
            select MAX(las.current_overdue_days) as current_overdue_days
            from loan_actual_stats las
            where
                las.loan_id in (
                    select l.loan_id
                    from loan l
                    where
                        l.client_id = " . $clientId . "
                        and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                )
        "
        );

        if (empty($result->current_overdue_days)) {
            return 0;
        }

        return $result->current_overdue_days;
    }

    public function getClientTotalOverdueAmountOfActiveLoans(int $clientId): float
    {
        $result = DB::selectOne(
            "
            select sum(las.current_overdue_amount) as current_overdue_amount
            from loan_actual_stats las
            where
                las.loan_id in (
                    select l.loan_id
                    from loan l
                    where
                        l.client_id = " . $clientId . "
                        and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                )
        "
        );

        if (empty($result->current_overdue_amount)) {
            return 0;
        }

        return $result->current_overdue_amount;
    }

    public function getLoansWithClients(array $loanIds = []): Collection
    {
        $filters['loan_id'] = $loanIds;
        $filters['loan_status_id'] = LoanStatus::ACTIVE_STATUS_ID;

        return Loan::filterBy($filters)
            ->with(['client'])
            ->get();
    }

    public function getActiveLoansWithRelationsByClientIds(array $clientIds, $with = [])
    {
        $loanWith = ['client', 'taxes'];
        if (!empty($with)) {
            $loanWith = array_merge($loanWith, $with);
        }

        $filters['client_id'] = $clientIds;
        $filters['loan_status_id'] = LoanStatus::ACTIVE_STATUS_ID;

        return Loan::filterBy($filters)
            ->with($loanWith)
            ->orderBy('loan_id', 'DESC')
            ->get();
    }

    public function getActiveLoansByClientId(int $clientId): EloquentCollection
    {
        return Loan::where([
            'client_id' => $clientId,
            'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
        ])->orderBy('created_at', 'DESC')->get();
    }

    public function getPaginatorByFilters(array $filters = []): LengthAwarePaginator
    {
        return $this->getBuilderByFilters($filters)->paginate($this->getPaginationLimit());
    }

    public function getBuilderByFilters(array $filters = []): Builder
    {
        return Loan::query()->select([
            'loan_id',
            'created_at',
            'loan_status_id',
            'amount_approved',
            'client_id',
            'product_id',
            'office_id',
            'product_type_id',
            'period_approved',
            'juridical',
            'cession',
            'fraud',
        ])->filterBy($filters)->ofMyOffices()->with([
            'client' => function ($q) {
                $q->withTrashed()->select('client_id', 'pin', 'first_name', 'middle_name', 'last_name');
            },
            'loanActualStats:loan_id,loan_stats_id,repaid_amount_total,outstanding_amount_principal',
            'loanProductSetting:loan_id,loan_product_setting_id,period',
            'product:product_id,name',
            'office:office_id,name',
        ])->orderBy('created_at', 'DESC');
    }

    public function getLoansForApprovalBuilder(array $filters = []): Builder
    {
        return Loan::query()
            ->filterBy($filters)
            ->with(['client', 'loanProductSetting'])
            ->whereIn('loan.loan_status_id', [
                LoanStatus::SIGNED_STATUS_ID,
                LoanStatus::PROCESSING_STATUS_ID,
            ])
            ->where(function ($query) {
                $query->whereNull('loan.skip_till')
                      ->orWhere('loan.skip_till', '<=', now());
            })
            ->ofMyOffices()
            ->orderBy('loan.loan_id', 'DESC');
    }

    public function getLoansForApproval(array $filters = []): EloquentCollection
    {
        return $this->getLoansForApprovalBuilder($filters)->get();
    }

    public function getLoansApproveFilterBy(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        $builder = $this->getLoansForApprovalBuilder($filters);
        $loans = $builder->paginate($perPage);

        return $loans;
    }

    public function getAll(
        ?int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['active' => 'DESC', 'loan_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $adminOfficeIds = $this->getAdminOfficeIds();
        if (empty($adminOfficeIds)) {
            return null;
        }

        $builder = DB::table('loan');
        $builder->select(
            DB::raw(
                "
            loan.*,
            TRIM(CONCAT_WS(' ', client.first_name, client.middle_name, client.last_name)) AS client_full_name,
            product.name AS product_name
        "
            )
        );
        $builder->join('client', 'client.client_id', '=', 'loan.client_id');
        $builder->join('product', 'product.product_id', '=', 'loan.product_id');

        if (!empty($joins)) {
            foreach ($joins as $joinType => $joinVars) {
                foreach ($joinVars as $key => $joinArgs) {
                    $builder->{$joinType}(
                        $joinArgs[0], // reference table
                        $joinArgs[1], // reference column
                        $joinArgs[2], // sign
                        $joinArgs[3]  // reference condition
                    );
                }
            }
        }

        if (!empty($where)) {
            foreach ($where as $seqNum => $row) {
                if ($row[1] == 'in') {
                    $builder->whereIn($row[0], $row[2]);
                    unset($where[$seqNum]);
                }
            }
            $builder->where($where);
        }

        $builder->whereIn('loan.office_id', $adminOfficeIds);

        // add order if provided
        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }

        if ($limit != null) {
            /** @var LengthAwarePaginator|EloquentCollection $result */
            $result = $builder->paginate($limit);
            $records = Loan::hydrate($result->all());
            $result->setCollection($records);
        } else {
            $result = Loan::hydrate($builder->get()->toArray());
        }

        return $result;
    }

    public function getPaginatedLoansForClientCard(
        int $clientId,
        int $limit
    ) {
        $builder = Loan::selectRaw('*');
        $builder->where('client_id', $clientId);
        $builder->orderBy('created_at', 'DESC');
        $builder->with(
            'product',
            'loanStatus',
            'office',
            'administrator',
            'loanActualStats'
        );

        return $builder->paginate($limit);
    }

    public function getLogsForLoansClientsInstallments(
        int $clientId,
        int $pageNum,
        array $requestQuery,
        int $limit = 10
    ): LengthAwarePaginator {
        $offset = ($limit * $pageNum) - $limit;
        $sql = "
                            SELECT
                    ch.client_history_id AS id,
                    ch.field,
                    ch.value_to,
                    ch.value_from,
                    ch.administrator_id,
                    ch.ip,
                    ch.headers,
                    ch.created_at
                FROM client_history AS ch
                WHERE ch.client_id = :client_id
                UNION ALL
                    SELECT
                        ih.installment_history_id AS id,
                        ih.field,
                        ih.value_to,
                        ih.value_from,
                        ih.administrator_id,
                        ih.ip,
                        ih.headers,
                        ih.created_at
                    FROM
                        installment_history AS ih
                    INNER JOIN
                        installment AS i ON i.installment_id = ih.installment_id
                    WHERE
                        i.client_id = :client_id
                UNION ALL
                    SELECT
                        lh.loan_history_id AS id,
                        lh.field,
                        lh.value_to,
                        lh.value_from,
                        lh.administrator_id,
                        lh.ip,
                        lh.headers,
                        lh.created_at
                    FROM
                        loan_history AS lh
                    INNER JOIN
                        loan AS l ON l.loan_id = lh.loan_id
                    WHERE
                        l.client_id = :client_id
        ";

        $countAll = DB::selectOne(
            "
                SELECT
                    COUNT(id) as count
                FROM (
                    $sql
                ) as result
            ",
            [
                'client_id' => $clientId
            ]
        )->count;

        $result = DB::select(
            "
                SELECT
                    result.*,
                    CONCAT_WS( ' ' , a.first_name, a.middle_name, a.last_name) as admin_fullname
                FROM
                     (
                        $sql
                     ) as result
                JOIN
                    administrator a ON a.administrator_id = result.administrator_id
                ORDER BY
                    result.created_at DESC
                LIMIT " . $limit . "
                OFFSET " . $offset . ";
                ",
            ['client_id' => $clientId]
        );

        return new Paginator(
            $result,
            $countAll,
            $limit,
            $pageNum,
            [
                'path' => Paginator::resolveCurrentPath(),
                'query' => $requestQuery,
                'fragment' => null,
                'pageName' => 'page',
            ]
        );
    }

    public function getById(int $loanId): ?Loan
    {
        return Loan::where(['loan_id' => $loanId])->first();
    }

    public function getByHash(string $loanHash): ?Loan
    {
        return Loan::where('hash', $loanHash)->first();
    }

    /**
     * @param Loan $loan
     *
     * @throws Exception
     */
    public function delete(Loan $loan)
    {
        $loan->delete();
    }

    /**
     * @param Loan $loan
     */
    public function disable(Loan $loan)
    {
        $loan->disable();
    }

    /**
     * @param Loan $loan
     */
    public function enable(Loan $loan)
    {
        $loan->enable();
    }


    public function create(array $data): ?Loan
    {
        $data['currency_id'] = Currency::BGN_CURRENCY_ID;
        $data['source'] = LoanSourceEnum::CRM;
        $data['channel_id'] = Channel::PHONE_ID;
        $data['loan_status_id'] = LoanStatus::NEW_STATUS_ID;
        $data['last_status_update_administrator_id'] = Auth::user() ? Auth::user(
        )->administrator_id : Administrator::SYSTEM_ADMINISTRATOR_ID;
        $data['last_status_update_date'] = Carbon::now();
        $data['hash'] = md5(time() + rand(0, 9999));

        $loan = new Loan();
        $loan->fill($data);

        return $this->save($loan);
    }

    /**
     * @param Loan $loan
     * @param array $data
     *
     * @return Loan
     */
    public function update(Loan $loan, array $data): ?Loan
    {
        $loan->fill($data);

        return $this->save($loan);
    }

    // returns Resource for chunking
    public function getResourceForA4ePerformance()
    {
        $fromDate = ((Carbon::now())->subMonths(12))->format('Y-m-d 00:00:00');

        $builder = Loan::where('loan.a4e_performance', '=', 1);
        $builder->select(DB::raw("loan.*, loan_refinance.loan_refinance_id"));
        $builder->leftJoin(
            'loan_refinance',
            'loan.loan_id',
            '=',
            'loan_refinance.refinanced_loan_id'
        );
        $builder->where('loan.active', '=', '1');
        $builder->where('loan.deleted', '=', '0');
        $builder->where('loan.created_at', '>=', $fromDate);
        $builder->whereIn('loan.loan_status_id', [
            LoanStatus::ACTIVE_STATUS_ID,
            LoanStatus::REPAID_STATUS_ID,
            LoanStatus::WRITTEN_OF_STATUS_ID,
            LoanStatus::CANCELLED_STATUS_ID,
        ]);

        return $builder->orderBy('loan.loan_id', 'asc');
    }

    /**
     * @param int $clientId
     *
     * @return null|Loan
     */
    public function getActiveLoan(int $clientId): ?Loan
    {
        return Loan::where(
            [
                ['client_id', '=', $clientId],
                ['loan_status_id', '=', LoanStatus::ACTIVE_STATUS_ID],
            ]
        )
            ->first();
    }

    public function getActiveLoans(Client $client, ?Payment $payment = null): EloquentCollection
    {
        $builder = $client
            ->loans()
            ->where(
                [
                    ['loan_status_id', '=', LoanStatus::ACTIVE_STATUS_ID],
                ]
            );

        if (!empty($payment) && !empty($payment->loan_id)) {
            $builder->orWhere(
                [
                    ['loan_id', '=', $payment->loan_id],
                ]
            );
        }

        return $builder->with('office')
            ->orderBy('loan_id')
            ->get();
    }

    public function getActiveLoansWithRelationsByIds(
        Client $client,
        array $loanIds,
        $with = ['office', 'loanActualStats', 'refinanced', 'product']
    ): EloquentCollection {
        $builder = $client
            ->loans()
            ->whereIn('loan_id', $loanIds)
            ->where(
                [
                    ['loan_status_id', '=', LoanStatus::ACTIVE_STATUS_ID],
                ]
            );

        if (!empty($with)) {
            $builder->with($with);
        }

        return $builder->orderBy('loan_id')->get();
    }

    /**
     * @return Collection
     */
    public function getLoanStatuses()
    {
        return LoanStatus::where(['deleted' => 0, 'active' => 1])->get();
    }

    /**
     * @return Collection
     */
    public function getLoanProductTypes(): Collection
    {
        return ProductType::where(BaseRepository::NOT_DEL_ACTIVE)->get();
    }


    public function getLoanTypes(): Collection
    {
        return LoanType::where(['deleted' => 0, 'active' => 1])->get();
    }

    /**
     * [getLastLoanProductIdFromProducts description]
     *
     * @param int $clientId [description]
     * @param array $productIds [description]
     *
     * @return int|null
     */
    public function getLastLoanProductIdFromProducts(
        int $clientId,
        array $productIds
    ): ?int {
        $loan = Loan::where('client_id', '=', $clientId)
            ->whereIn('product_id', $productIds)
            ->orderBy('created_at', 'DESC')
            ->first();

        if (empty($loan)) {
            return null;
        }

        return $loan->product_id;
    }

    /**
     * @param Loan $loan
     *
     * @return bool
     */
    public function addLoanStatusHistory(Loan $loan): bool
    {
        $obj = new LoanStatusHistory();
        $obj->loan_id = $loan->loan_id;
        $obj->loan_status_id = $loan->loan_status_id;
        $obj->administrator_id = $loan->getOriginal('last_status_update_administrator_id');
        $obj->date = $loan->getOriginal('last_status_update_date');

        return $obj->save();
    }

    /**
     * @return Collection
     */
    public function getLoanPaymentMethods()
    {
        return PaymentMethod::where(['deleted' => 0, 'active' => 1])
            ->get();
    }

    /**
     * @return Collection
     */
    public function getLoanOffices()
    {
        return Office::where(['deleted' => 0, 'active' => 1])
            ->orderBy('name', 'ASC')
            ->get();
    }

    public function getClientLoans(Client $client, int $limit): Collection
    {
        $loans = DB::table('loan AS l')
            ->join('loan_status AS ls', 'l.loan_status_id', '=', 'ls.loan_status_id')
            ->join('product AS p', 'l.product_id', '=', 'p.product_id')
            ->join('office AS of', 'of.office_id', '=', 'l.office_id')
            ->select('l.*', 'ls.name AS loan_status_name', 'p.name AS product_name', 'of.name AS office_name')
            ->where('l.client_id', '=', $client->getKey())
            ->where('l.active', '=', 1)
            ->where('l.deleted', '=', 0)
            ->orderBy('l.loan_id', 'DESC')
            ->limit($limit);

        return Loan::hydrate($loans->get()->toArray());
    }

    public function getRawClientLoanIds(Client $client): array
    {
        return DB::select(
            '
                SELECT
                    l.loan_id,
                    l.loan_status_id,
                    l.client_id
                FROM
                    loan AS l
                WHERE
                    l.client_id = :client_id
                    AND l.active = 1
                    AND l.deleted = 0
                ORDER BY
                    l.loan_id DESC
            ',
            [
                'client_id' => $client->getKey(),
            ]
        );
    }

    public function getLoanFirstStatusHistoryEntry(int $loanId, int $loanStatusId): ?LoanHistory
    {
        $query = LoanHistory::where([
            'loan_id' => $loanId,
            'loan.loan_status_id' => $loanStatusId
        ])->orderBy('loan_history_id', 'ASC');

        return $query->first();
    }

    public function changeStatus(Loan $loan, int $newLoanStatusId): ?Loan
    {
        $loan = $this->getLockedLoan($loan);
        $adminId = getAdminId();

        $loan->administrator_id = null;
        if ($newLoanStatusId === LoanStatus::PROCESSING_STATUS_ID) {
            $loan->administrator_id = $adminId;
        }

        $loan->last_status_update_administrator_id = $adminId;
        $loan->last_status_update_date = now();
        $loan->loan_status_id = $newLoanStatusId;

        return $this->save($loan);
    }

    /**
     * @param array $criteria
     *
     * @return Loan|null
     */
    public function getByCriteria(array $criteria): ?Loan
    {
        $loans = Loan::where($criteria)->get();

        return $loans->first();
    }

    public function getLockedLoan(Loan $loan): Loan
    {
        return Loan::where('loan_id', $loan->getKey())->lockForUpdate()->first();
    }

    /**
     * @param int $secondsBack
     * @return Loan[]|EloquentCollection
     */
    public function getUnsignedLoans(int $minutes): array|EloquentCollection
    {
        return Loan::where('loan_status_id', LoanStatus::NEW_STATUS_ID)
            ->whereRaw("last_status_update_date + ($minutes * INTERVAL '1 minute') <= NOW()")
            ->whereDoesntHave('saleTasks', function (Builder $query) {
                $query->whereIn('status', [SaleTask::SALE_TASK_STATUS_NEW, SaleTask::SALE_TASK_STATUS_PROCESSING]);
            })
            ->get();
    }

    public function getProductById(int $productId): ?Product
    {
        return Product::where('product_id', $productId)->first();
    }


    public function clientHasUnprocessedLoans(int $clientId): bool
    {
        return Loan::where(['client_id' => $clientId])
                ->whereIn('loan_status_id', [
                    LoanStatus::NEW_STATUS_ID,
                    LoanStatus::SIGNED_STATUS_ID,
                    LoanStatus::PROCESSING_STATUS_ID,
                    LoanStatus::APPROVED_STATUS_ID
                ])
                ->count() > 0;
    }

    public function save(Loan $loan): ?Loan
    {
        if (!App::runningInConsole() && $loan->wasImportantPropertyChanged() && !$loan->getIsChangeable()) {
            throw new \RuntimeException(__('head::loanCrud.NotChangeable'));
        }
        if (!$loan->currency_id) {
            $loan->currency_id = Currency::BGN_CURRENCY_ID;
        }
        if (!$loan->source) {
            $loan->source = LoanSourceEnum::CRM;
        }
        if (!$loan->channel_id) {
            $loan->channel_id = Channel::PHONE_ID;
        }
        if (!$loan->last_status_update_administrator_id) {
            $loan->last_status_update_administrator_id = Auth::user()
                ? Auth::user()->administrator_id
                : Administrator::SYSTEM_ADMINISTRATOR_ID;
        }
        if (!$loan->last_status_update_date) {
            $loan->last_status_update_date = Carbon::now();
        }
        if (!$loan->hash) {
            $loan->hash = md5(time() + rand(0, 9999));
        }
        $statusChanged = $loan->isDirty('loan_status_id');
        if (!$loan->save()) {
            return null;
        }
        if ($statusChanged) {
            $this->addLoanStatusHistory($loan);
        }

        return $loan;
    }

    public function getAllActiveByClientId(int $clientId): EloquentCollection
    {
        return Loan::where([
            'client_id' => $clientId,
            'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID
        ])->orderBy('created_at')->get();
    }

    public function getLastActiveLoanByClientId(int $clientId): ?Loan
    {
        return Loan::where([
            'client_id' => $clientId,
            'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID
        ])->orderBy('created_at', 'DESC')->first();
    }

    public function getClientLoansWithActualStats(int $clientId, ?int $limit = null): EloquentCollection
    {
        $builder = Loan::where(['client_id' => $clientId, 'deleted' => 0])
            ->with('loanActualStats')
            ->orderBy('loan_id', 'DESC');

        if (!empty($limit)) {
            $builder->limit($limit);
        }

        return $builder->get();
    }

    public function getByIds(array $loanIds): EloquentCollection
    {
        return Loan::whereIn('loan_id', $loanIds)->get();
    }

    /**
     * Get loans that were refinanced by a specific loan ID
     *
     * @param int $refinancingLoanId The loan ID that refinanced other loans
     * @return EloquentCollection Collection of loans that were refinanced
     */
    public function getRefinancedById(int $refinancingLoanId): EloquentCollection
    {
        return Loan::whereHas('refinanceAsRefinanced', function ($query) use ($refinancingLoanId) {
            $query->where('refinancing_loan_id', $refinancingLoanId)
                  ->where('active', 1)
                  ->where('deleted', 0);
        })->get();
    }

    public function getExtensionCount(int $loanId): int
    {
        return Tax::where(['loan_id' => $loanId, 'type' => Tax::TAX_TYPE_LOAN_EXTENSION_FEE])->count();
    }

    // ip and browser
    public function getCreationLoanIpData(int $loanId): ?LoanIp
    {
        return LoanIp::where(['loan_id' => $loanId, 'loan_status_id' => LoanStatus::NEW_STATUS_ID])->first();
    }

    public function withoutNoiReportForOnline(Carbon $from, Carbon $to): Builder
    {
        return $this->getFromToForOnline($from, $to)
            ->whereDoesntHave('noiReports');
    }

    public function getFromToForOnline(Carbon $from, Carbon $to): Builder
    {
        return Loan::query()
            ->whereBetween('loan.created_at', [
                $from->toDateTimeString(),
                $to->toDateTimeString()
            ])
            ->where('office_id', Office::OFFICE_ID_WEB);
    }
}
