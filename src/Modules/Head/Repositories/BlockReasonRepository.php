<?php

namespace Modules\Head\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\BlockReason;
use Modules\Common\Repositories\BaseRepository;

final class BlockReasonRepository extends BaseRepository
{
    public function getSelectOptions(): array
    {
        return $this->builder()
            ->pluck('name', app(BlockReason::class)->getKeyName())
            ->toArray();
    }

    private function builder(): Builder
    {
        return BlockReason::query()->where([
            'active' => '1',
            'deleted' => '0',
        ])->orderBy('name');
    }

    public function getAll(): Collection
    {
        return $this->builder()->get();
    }

    public function getIdNameArray(): array
    {
        return $this->getAll()->pluck(
            'name',
            'block_reason_id'
        )->toArray();
    }
}
