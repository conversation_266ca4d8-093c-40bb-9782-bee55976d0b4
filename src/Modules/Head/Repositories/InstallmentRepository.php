<?php

namespace Modules\Head\Repositories;

use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Traits\AdminTrait;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Models\Installment;
use Modules\Common\Models\InstallmentActionLog;
use Modules\Common\Models\InstallmentHistory;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment;
use Modules\Common\Models\Tax;
use Modules\Common\Repositories\BaseRepository;
use Modules\Common\Repositories\ExtendLoanSnapshotRepository;
use Modules\Common\Traits\DateBuilderTrait;

class InstallmentRepository extends BaseRepository
{
    use AdminTrait, DateBuilderTrait;

    public function __construct(
        private readonly Installment $installment
    ) {
    }

    public function getNextInstallment(int $loanId): Installment
    {
        return $this->installment->getNextInstallment($loanId);
    }

    public function create(array $data): Installment
    {
        $installment = new Installment();
        $installment->fill($data);
        $this->save($installment);

        return $installment;
    }

    public function bulkCreate(array $installments)
    {
        return Installment::insert($installments);
    }

    public function update(
        Installment $installment,
        array $data
    ): Installment {
        $installment->fill($data);
        $installment->save();

        return $installment;
    }

    public function getById(int $installmentId): ?Installment
    {
        return Installment::where([
                'installment_id' => $installmentId,
                'deleted' => 0,
                'active' => 1,
            ])
            ->first();
    }

    public function postponeInstallmentsBySnapshot(Loan $loan, ?Tax $tax = null): bool
    {
        /** @var ExtendLoanSnapshotRepository $repo */
        $repo = app(ExtendLoanSnapshotRepository::class);

        $snapshot = $repo->getLastExtendLoanSnapshot($loan->loan_id, $tax?->tax_id);
        if (empty($snapshot?->id)) {
            throw new \RuntimeException('No available loan snapshot.');
        }
        if (empty($snapshot->installments) || !is_array($snapshot->installments)) {
            throw new \RuntimeException('Snapshot is missing installments.');
        }

        if (!empty($tax?->tax_id)) {
            $hasExtendFeeAfterCurrentFee = Tax::where([
                'client_id' => $tax->client_id,
                'loan_id' => $tax->loan_id,
                'type' => Tax::TAX_TYPE_LOAN_EXTENSION_FEE,
                'active' => 1,
            ])->where('created_at', '>', $tax->created_at)->exists();

            if ($hasExtendFeeAfterCurrentFee) {
                throw new \RuntimeException('After this extend we has other extend. First delete last extend.');
            }
        }

        // Prepare rows for upsert
        $rows = [];
        foreach ($snapshot->installments as $installmentId => $attrs) {
            if (!is_array($attrs)) {
                throw new \RuntimeException("Invalid installment payload for #{$installmentId}.");
            }

            $rows[] = $attrs;
        }

        return (bool) DB::transaction(function () use ($loan, $snapshot, $rows) {
            // Lock the loan row to keep state consistent while we check + revert
            Loan::query()
                ->where('loan_id', $loan->loan_id)
                ->lockForUpdate()
                ->first();

            // Re-check for payments AFTER the snapshot, inside the tx
            $hasPayment = Payment::query()
                ->where('client_id', $loan->client_id)
                ->where('loan_id', $loan->loan_id)
                ->where('direction', PaymentDirectionEnum::IN->value)
                ->where('created_at', '>=', $snapshot->created_at)
                ->exists();

            if ($hasPayment) {
                throw new \RuntimeException('A payment was registered after the snapshot was created.');
            }

            $ids = array_column($rows, 'installment_id');

            $lockedIds = Installment::query()
                ->where('loan_id', $loan->loan_id)
                ->whereIn('installment_id', $ids)
                ->select('installment_id')
                ->lockForUpdate()
                ->pluck('installment_id')
                ->all();

            if (count($lockedIds) !== count($ids)) {
                throw new \RuntimeException('Snapshot references installments that do not exist for this loan.');
            }

            $updateColumns = array_keys($rows[0]);
            $updateColumns = array_values(array_diff($updateColumns, ['installment_id']));

            return Model::withoutEvents(function () use ($rows, $updateColumns) {
                DB::table((new Installment)->getTable())
                    ->upsert($rows, ['installment_id'], $updateColumns);

                return true;
            });
        });
    }

    public function postponeInstallments(
        SupportCollection $installments,
        int $periodDays,
        bool $reverse = false
    ): bool {
        $counter = collect([]);

        $isMonthlyInstallment = null;

        $installments->each(
            function (Installment $item) use (
                $periodDays,
                $counter,
                $reverse,
                &$isMonthlyInstallment
            ) {
                if (is_null($isMonthlyInstallment)) {
                    $isMonthlyInstallment = $item->loan->installment_modifier === '+1 month';
                }

                if ($reverse) {
                    $installmentsCollection = null;
                    if ("+1 month" === $item->loan->installment_modifier) {
                        $installmentsCollection = Cache::remember(__METHOD__, 5, function () use ($periodDays, $item) {
                            $loanConfig = $item->loan->getLoanConfig();
                            $loanConfig['utilisationDate'] = $item->due_date->subDays($periodDays)->subMonth();
                            $loanConfig['hasCustomPaymentSchedule'] = [];


                            $installments = $item->loan->getCredit(customConfig: $loanConfig)
                                ->installmentsCollection()
                                ->toArray();

                            $currentIndex = $item->seq_num;
                            $reindexedInstallments = [];
                            foreach ($installments as $installment) {
                                $reindexedInstallments[$currentIndex] = $installment;
                                $currentIndex++;
                            }

                            return $reindexedInstallments;
                        });
                    }

                    if ($installmentsCollection) {
                        if (!$installmentsCollection[$item->seq_num]) {
                            throw new Exception('Error installment with index' . $item->seq_num . ' not found');
                        }

                        $item->due_date = $installmentsCollection[$item->seq_num]->dueDate;
                    } else {
                        $item->due_date = $item->due_date->subDays($periodDays);
                    }
                } else {
                    $item->due_date = $item->due_date->addDays($periodDays);
                }

                $counter->push($item->save());
            }
        );

        return $installments->count() === $counter->count();
    }

    public function saveActionLog(array $data)
    {
        $installmentLog = new InstallmentActionLog();
        $installmentLog->fill($data);
        $installmentLog->save();

        return $installmentLog;
    }

    public function delete(Installment $installment): Installment
    {
        $installment->delete();

        return $installment;
    }

    public function getUnpaidInstallmentsByLoanId(int $loanId)
    {
        return Installment::where('loan_id', '=', $loanId)
            ->where('paid', '=', 0)
            ->orderBy('seq_num', 'ASC')
            ->get();
    }

    public function getByLoanIdAndSeq(int $loanId, int $seqNum): ?Installment
    {
        return Installment::where(['loan_id' => $loanId, 'seq_num' => $seqNum])->first();
    }

    public function createHistoryEntry(
        Installment $installment,
        array $options
    ): bool {

        $obj = new InstallmentHistory();
        $obj->installment_id = $installment->getKey();
        $obj->field = $options['field'];
        $obj->value_from = $options['oldVal'];
        $obj->value_to = $options['newVal'];
        $obj->administrator_id = getAdminId();

        return $obj->save();
    }

    public function createHistory(Installment $installment): bool
    {
        $saves = [];
        $changedFields = $installment->getDirty();
        foreach ($changedFields as $field => $newVal) {
            if (!in_array($field, InstallmentHistory::HISTORY_FIELDS, true)) {
                continue;
            }

            $oldVal = $installment->getOriginal($field);

            if ($newVal === $oldVal) {
                continue;
            }

            $options = [
                'field' => $field,
                'newVal' => $newVal,
                'oldVal' => $oldVal,
            ];
            $saves[] = $this->createHistoryEntry($installment, $options);
        }

        if (count(array_unique($saves)) === 1) {
            return current($saves);
        }

        return false;
    }

    public function save(Installment $installment): ?Installment
    {
        if ($installment->exists) {
            $this->createHistory($installment);
        }

        return $installment->save() ? $installment : null;
    }

    public function getInstallmentsForDate(
        string $date,
        bool $chunk = false,
        ?int $officeId = null,
        ?int $loanId = null
    ) {
        $builder = Installment::select(
            DB::raw(
                "
                installment.*,
                loan.office_id
            "
            )
        )
            ->join('loan', 'loan.loan_id', '=', 'installment.loan_id')
            ->where('loan.loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->where('installment.paid', 0)
            ->where('due_date', $date);

        if (!empty($officeId)) {
            $builder->where('loan.office_id', $officeId);
        }

        if (!empty($loanId)) {
            $builder->where('loan.loan_id', $loanId);
        }

        $builder->orderBy('installment.installment_id');

        if ($chunk) {
            return $builder;
        }

        return $builder->get();
    }

    public function removeRedundantByLoanIdAndMaxSec(int $loanId, int $maxSeqNum = 0)
    {
        /// first remove it from history
        InstallmentHistory::whereIn(
            'installment_id',

            /// find all installments to delete
            Installment::where('loan_id', $loanId)
                ->where('seq_num', '>', $maxSeqNum)
                ->pluck('installment_id', 'installment_id')
                ->toArray(),

        )->forceDelete();

        /// second delete installments
        Installment::where('loan_id', $loanId)
            ->where('seq_num', '>', $maxSeqNum)
            ->forceDelete();
    }
}
