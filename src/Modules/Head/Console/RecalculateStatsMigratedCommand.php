<?php

namespace Modules\Head\Console;

use Illuminate\Support\Facades\Log;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Entities\MigrationStats;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment;
use Modules\Head\Application\Actions\RecalculateClientStatsAction;
use Modules\Head\Application\Actions\RecalculateLoanStatsAction;
use Modules\Head\Application\Actions\RecalculateLoanStatsForA4EAction;
use Modules\Payments\Application\Actions\RecalculatePaymentStatsAction;

class RecalculateStatsMigratedCommand extends CommonCommand
{
    protected $name = 'script:stats:recalculate-migrated';
    protected $description = 'Recalculate stats of migrated loans';

    // aim: clients, loans, payments
    // db: nefin_office1, nefin_office2, nefin_online, provision
    // id: client_id OR loan_id OR payment_id
    // protected $signature = 'script:stats:recalculate-migrated {aim?} {statusId?} {id?} {type?}';
    protected $signature = 'script:stats:recalculate-migrated {client_id?}';

    protected string $logChannel = 'statsRecalculationChanges';

    // if lite update => run RecalculateLoanStatsForA4EAction else => RecalculatePaymentStatsAction
    protected bool $liteUpdate = false;

    public function __construct(
        private RecalculateClientStatsAction  $clientAction,
        private RecalculateLoanStatsAction  $loanAction,
        private RecalculateLoanStatsForA4EAction  $loanLiteAction,
        private RecalculatePaymentStatsAction $paymentAction
    ) {
        parent::__construct();
    }

    public function handle()
    {
        return ; // tmp stopped!

        $clientId = (int) $this->argument('client_id');
        $this->fixFirstLoansForAll($clientId);
        $this->info('----');


        $this->startLog();

        $aim = $this->argument('aim');
        if (!in_array($aim, ['clients', 'loans', 'payments'])) {
            $this->info('Wrong aim');
            return;
        }

        $statusId = $this->argument('statusId');
        if (empty($statusId)) {
            $this->info('Wrong statusId');
            return;
        }

        $id = (int) $this->argument('id');

        $type = $this->argument('type');
        if ($type != 'full') {
            $this->liteUpdate = true;
        }


        if ($aim == 'loans') {
            if (!empty($id)) {
                $builder = Loan::where('loan_id', $id);
                // $ids = [];
                // $builder = Loan::whereIn('loan_id', $ids);
            } else {
                $builder = Loan::whereIn('loan_status_id', [6,7,9]); // active/repaid/written-off
                $builder->whereNotNull('migration_db');
            }
            $builder->where('office_id', 1); // optional
            $builder->chunkById(200, function ($loans) {

                // $loanIds = $loans->map(function($loan) {
                //     return $loan->loan_id;
                // });

                // $existingLoans = MigrationStats::whereIn('loan_id', $loanIds)->pluck('loan_id')->toArray();

                foreach ($loans as $loan) {
                    // if (in_array($loan->loan_id, $existingLoans)) {
                    //     continue;
                    // }

                    $this->recalculateLoan($loan);
                    // MigrationStats::create(['loan_id' => $loan->loan_id]);
                }
            });
        }

        if ($aim == 'clients') {
            $builder = Client::where('active', 1);
            // $builder = Client::where('migration_db', $db);
            if (!empty($id)) {
                $builder->where('client_id', $id);
            }
            $builder->whereRaw("client.client_id IN (select distinct l.client_id from loan l where l.office_id = 1 and l.created_at >= '2024-05-11 14:00:00')");

            $builder->chunkById(100, function ($clients) {
                foreach ($clients as $client) {
                    $this->recalculateClient($client);
                    // $existsInMigrationStats = MigrationStats::where('client_id', $client->client_id)->exists();
                    // if (!$existsInMigrationStats) {
                    //     $this->recalculateClient($client);
                    //     MigrationStats::create(['client_id' => $client->client_id]);
                    // }
                }
            });
        }

        if ($aim == 'payments') {
            if (!empty($id)) {
                $existsInMigrationStats = MigrationStats::where('payment_id', $id)->exists();
                if (!$existsInMigrationStats) {
                    $payment = Payment::find($id);
                    $this->recalculatePayment($payment);
                    MigrationStats::create(['payment_id' => $id]);
                }
            } else {
                Loan::where('migration_db', $db)->chunkById(100, function ($loans) {
                    foreach ($loans as $loan) {
                        foreach ($loan->payments as $payment) {
                            $existsInMigrationStats = MigrationStats::where('payment_id', $payment->payment_id)->exists();
                            if (!$existsInMigrationStats) {
                                $this->recalculatePayment($payment);
                                MigrationStats::create(['payment_id' => $id]);
                            }
                        }
                    }
                });
            }
        }


        $this->finishLog();

        return static::SUCCESS;
    }

    private function fixFirstLoansForAll($clientId)
    {
        $builder = Client::whereNotNull('created_at');
        if (!empty($clientId)) {
            $builder->where('client_id', $clientId);
        }

        $builder->chunk(
            200,
            function ($clients) {
                $firstLoans = [];
                $notFirstLoans = [];

                foreach ($clients as $client) {
                    $this->info('-- processing: ' . $client->client_id);

                    $loans = Loan::where('client_id', $client->client_id)->get(['loan_id', 'created_at', 'loan_status_id']);
                    if ($loans->isEmpty()) {
                        continue;
                    }

                    $loanIds = $loans->pluck('loan_id')->toArray();
                    $notFirstLoans = array_merge($notFirstLoans, $loanIds);

                    $firstLoan = $loans
                        ->whereIn('loan_status_id', LoanStatus::ACTIVE_STATUSES)
                        ->sortBy('created_at')
                        ->first();

                    if ($firstLoan) {
                        $firstLoans[] = $firstLoan->loan_id;
                    }
                }

                if (!empty($notFirstLoans)) {
                    LoanActualStats::whereIn('loan_id', $notFirstLoans)->update(['first_loan' => 0]);
                }

                if (!empty($firstLoans)) {
                    LoanActualStats::whereIn('loan_id', $firstLoans)->update(['first_loan' => 1]);
                }
            }
        );
    }

    public function recalculateLoan(?Loan $loan): bool
    {
        try {
            if (!$loan) {
                return false;
            }

            if ($this->liteUpdate) {
                $changes = $this->loanLiteAction->execute($loan);
            } else {
                $changes = $this->loanAction->execute($loan);
            }

            $this->info('-- handled loan #' . $loan->loan_id);

            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }

    public function recalculateClient(?Client $client): bool
    {
        if (!$client) {
            return false;
        }

        $changes = $this->clientAction->execute($client);
        $this->info('-- handled client #' . $client->client_id);

        return true;
    }

    public function recalculatePayment(?Payment $payment): bool
    {
        if (!$payment || $payment->direction !== PaymentDirectionEnum::IN) {
            return false;
        }

        $changes = $this->paymentAction->execute($payment);
        $this->info('-- handled payment #' . $payment->payment_id);

        return true;
    }
}
