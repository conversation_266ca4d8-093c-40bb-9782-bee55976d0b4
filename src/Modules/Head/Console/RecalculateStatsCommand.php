<?php

namespace Modules\Head\Console;

use Illuminate\Support\Facades\Log;
use Modules\Collect\Domain\Entities\Installments;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Head\Application\Actions\RecalculateClientStatsAction;
use Modules\Head\Application\Actions\RecalculateLoanStatsAction;
use Modules\Head\Jobs\RecalculateStatsJob;
use Modules\Head\Repositories\ClientRepository;
use Modules\Payments\Application\Actions\RecalculatePaymentStatsAction;

class RecalculateStatsCommand extends CommonCommand
{
    protected $name = 'script:stats:recalculate';
    protected $signature = 'script:stats:recalculate {clientId?} {loanId?} {paymentId?} {type?}'; // type: active, migrated
    protected $description = 'Recalculate stats';

    protected string $logChannel = 'statsRecalculationChanges';

    public function __construct(
        private RecalculateClientStatsAction  $clientAction,
        private RecalculateLoanStatsAction  $loanAction,
        private RecalculatePaymentStatsAction $paymentAction,
        private Installments $installments,
        private ClientRepository $clientRepository
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->startLog();

        $clientId = (int) $this->argument('clientId');
        $loanId = (int) $this->argument('loanId');
        $paymentId = (int) $this->argument('paymentId');
        $type = $this->argument('type');

        if ($paymentId) {
            $this->recalculatePayment(Payment::find($paymentId));
        } else if ($loanId) {
            $this->recalculateLoan(Loan::find($loanId));
        } else if ($clientId) {
            $this->recalculateClient(Client::find($clientId));
        } else {
            if ($type == 'active') {

//                $this->clientRepository
//                    ->getActiveOldClientsBuilder()
//                    ->chunkById(50, function ($clients) {
//                        foreach ($clients as $client) {
//                            $this->recalculateClient($client);
//                        }
//                    });

                $this->clientRepository
                    ->getActiveOldClientsBuilder()
                    ->chunkById(2, function ($clients) {
                        RecalculateStatsJob::dispatch($clients)->onQueue('commands');
                    });

            } else if ($type == 'migrated') {

//                $this->clientRepository
//                    ->getMigratedClientsBuilder()
//                    ->chunkById(50, function ($clients) {
//                        foreach ($clients as $client) {
//                            dump('-- handle #' . $client->client_id);
//                            $this->recalculateClient($client);
//                        }
//                    });

                $this->clientRepository
                    ->getMigratedClientsBuilder()
                    ->chunkById(2, function ($clients) {
                        RecalculateStatsJob::dispatch($clients)->onQueue('commands');
                    });
            }
            else {
                $this->info('no type/params provided, nothing todo');
            }
        }

        $this->finishLog();

        return static::SUCCESS;
    }

    public function recalculatePayment(?Payment $payment): void
    {
        if (!$payment || $payment->direction !== PaymentDirectionEnum::IN) {
            return;
        }

        $changes = $this->paymentAction->execute($payment);
        Log::channel($this->logChannel)->info($changes);
    }

    public function recalculateLoan(?Loan $loan): void
    {
        if (!$loan) {
            return;
        }

        $changes = $this->loanAction->execute($loan);
        Log::channel($this->logChannel)->info($changes);

        // INFO: commented because installment do not have stats!
        // $this->installments->buildFromExisting(
        //     $loan->installments,
        //     $loan->getCredit()->freshInstallmentCollection()
        // )->recalculate();

        foreach ($loan->payments as $payment){
            $this->recalculatePayment($payment);
        }

    }

    public function recalculateClient(? Client $client): void
    {
        if (!$client) {
            return;
        }

        $changes = $this->clientAction->execute($client);
        Log::channel($this->logChannel)->info($changes);

        foreach ($client->loans as $loan){
            $this->recalculateLoan($loan);
        }
    }
}
