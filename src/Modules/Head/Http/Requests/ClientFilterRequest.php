<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;

class ClientFilterRequest extends BaseRequest implements ListSearchInterface
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'client_id' => 'nullable|integer|exists:client,client_id',
            'pin' => 'nullable|numeric',
            'client_status' => 'nullable|in:active,blocked,deleted',
            'clientNames' => $this->getConfiguration('requestRules.nameNullable'),
            'clientPhone' => $this->getConfiguration('requestRules.phoneSearch'),
            'clientEmail' => 'nullable|string',
            'block_reason_id' => 'nullable|exists:block_reason,block_reason_id',
            'delete_reason_id' => 'nullable|exists:delete_reason,delete_reason_id',
            'citizenship_type' => 'nullable|in:local,foreigner',
            'legal_status' => 'nullable|in:individual,company',
            'created_at' => [
                'nullable',
                'string',
                'regex:' . $this->getDateValidationRegex()
            ],
        ];
    }

    public function getFilters(): array
    {
        $filters = $this->validated();
        if (
            !empty($filters['created_at'])
            && !preg_match("/(\d{2}-\d{2}-\d{4}) - (\d{2}-\d{2}-\d{4})/", $filters['created_at'])
        ) {
            $filters['created_at'] = $filters['created_at'] . ' - ' . $filters['created_at'];
        }
        return $filters;
    }
}
