<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Modules\Admin\Services\OfficeService;
use Modules\Approve\Application\Action\CancelLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Approve\Services\ApproveAttemptService;
use Modules\Common\Enums\ClientCardRouteParamEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;
use Modules\Common\Traits\DatesAwareTrait;
use Modules\Docs\Services\DocumentService;
use Modules\Head\Enums\ClientCardTabsEnum;
use Modules\Head\Http\Requests\CancelLoanRequest;
use Modules\Head\Http\Requests\LoanCalculateAjaxRequest;
use Modules\Head\Http\Requests\SaleAttemptRequest;
use Modules\Head\Repositories\BlockReasonRepository;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\HeaderService;
use Modules\Head\Services\LoanService;
use Modules\Head\Services\OpenTasksService;
use Modules\Payments\Application\Actions\CancelPaymentAction;
use Modules\Payments\Services\PaymentService;
use Modules\Sales\Services\SaleService;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

final class ClientCardController extends BaseController
{
    use DatesAwareTrait;

    protected const CLIENT_CARD_TABS = [
        'GeneralView' => 'overview',
        'LoanHistory' => 'ckr_reports',
        'Income' => 'noi_reports',
        'Mvr' => 'mvr_reports',
        'Scoring' => 'a4e_reports',
        'ClientCommunication' => 'communication',
        'Payments' => 'payment',
        'PaymentSchedule' => 'payment_schedule',
        'Log' => 'systemLog',
        'PreviousRequests' => 'previous_requests',
    ];

    private ?ApproveAttemptService $approveAttemptService = null;
    private ?ClientService $clientService = null;
    private ?DocumentService $documentService = null;
    private ?HeaderService $headerService = null;
    private ?LoanService $loanService = null;
    private ?OfficeService $officeService = null;
    private ?OpenTasksService $openTasksService = null;
    private ?PaymentService $paymentService = null;
    private ?SaleService $saleService = null;

    public string $indexRoute = 'head.approveLoans.list';
    public string $pageTitle = 'Client card';

    public function __construct(private readonly BlockReasonRepository $blockReasonRepo)
    {
        parent::__construct();
    }

    public function index()
    {
        $routeParams = $this->getRouteParams();

        $clientId = !empty($routeParams['clientId']) ? (int) $routeParams['clientId'] : null;
        if (empty($clientId)) {
            return to_route('home')->with('fail', 'No client ID');
        }

        $client = $this->getClientService()->getClientById($clientId);
        if (empty($client->client_id)) {
            return to_route('home')->with('fail', 'Failed to get client #' . $clientId);
        }

        $loan = null;
        $loanId = !empty($routeParams['loanId']) ? (int) $routeParams['loanId'] : null;
        if (!empty($loanId)) {
            $loan = $this->getLoanService()->getLoanById($loanId);

            if (empty($loan->loan_id) || empty($loan->client_id)) {
                return to_route('home')->with('fail', 'Failed to get loan #' . $loanId);
            }

            if ($loan->client_id != $client->client_id) {
                return to_route('home')->with('fail', __('head::clientCard.LoanNotOnThisClient'));
            }
        }


        // TODO: refactor this
        $vars = $this->getCommonVars($client, $loan, $routeParams);
        $vars = $this->getTabs($vars);
        $vars['moduleName'] = !empty($routeParams['do']) ? $routeParams['do'] : '';
        $vars['loanId'] = !empty($routeParams['loanId']) ? (int) $routeParams['loanId'] : null;
        $vars['taskId'] = !empty($routeParams['taskId']) ? (int) $routeParams['taskId'] : null;
        $vars['loan'] = $loan ?? null;
        $vars['paymentMethods'] = [
            'bankId' => PaymentMethod::PAYMENT_METHOD_BANK,
            'easypayId' => PaymentMethod::PAYMENT_METHOD_EASYPAY,
            'cashId' => PaymentMethod::PAYMENT_METHOD_CASH,
        ];
//        $vars['currentDate'] = \request('currentDate', $this->sqlDate()->format('Y-m-d'));
//        if (request()->has('currentDate')) {
//            $routeParams['currentDate'] = request()->get('currentDate');
//        }
        $vars['clientCardTabs'] = ClientCardTabsEnum::getAllTabs($routeParams);

//        dd($routeParams);

//        return view('head::card.view', $vars);
        return view('head::card.index', $vars);
    }

    ///////////////////////////////// CALCULATIONS ///////////////////////////////////////////////

    /**
     * @param LoanCalculateAjaxRequest $request
     * @return Loan
     * @throws Exception
     */
    public function recalculateCurrentLoan(LoanCalculateAjaxRequest $request): Loan
    {
        throw new Exception('TODO: most development');
    }

    private function getPaymentTableData(array $data = [], int $limit = null)
    {
        if (!isset($data['limit'])) {
            $data['limit'] = parent::getTableLength();
        }

        return $this->getPaymentService()->getAllPaymentsByFilters(
            $limit ?? parent::getTableLength(),
            $data
        );
    }

    public function cancelLoan(CancelLoanRequest $request, CancelPaymentAction $action)
    {
        DB::beginTransaction();
        try {
            $tmpDto = $request->asDto();

            $loan = Loan::where('loan_id', $tmpDto->loan_id)->first();
            if (empty($loan->loan_id)) {
                throw new \Exception('Loan not found');
            }

            $data = $action->executeByLoan(
                $loan,
                $tmpDto?->description ?? '',
                $tmpDto?->decision_reason ?? ''
            );

            DB::commit();

            $routeName = $loan->isOnlineLoan() ? 'head.approveLoans.list' : 'head.loans.list';

            return to_route($routeName, [])->with('success', __('head::loanCrud.loanSuccessfullyCancelled'));
        } catch (\Throwable $e) {
            DB::rollBack();

            return back()->with('fail', __('head::loanCrud.loanCancellationFailed') . '. ' . $e->getMessage());
        }
    }

    //////////////////////////////////////// OTHER ////////////////////////////////////////////

    // main tab - div with documents prints & new app popup for doc printing
    public function printDocuments(string $printFlag, Loan $loan)
    {
        return redirect($this->getDocumentService()->getLoanDocs($loan, $printFlag));
    }

    // used for tests only!
    // URL: http://**********/head/clientCard/generate-documents/doc_pack_new_loan/180690
    public function generateDocuments(string $printFlag, Loan $loan)
    {
        return $this->getDocumentService()->generatePdfDocumentsPackForLoan($loan, $printFlag);
    }

    public function getOpenTasks(
        Client $client
    ): string {
        return view(
            'head::card.items.boxOpenTasksGenerateDocuments.all-open-tasks-table',
            [
                'allOpenTasks' => $this->getOpenTaskService()->getAllClientOpenTasks($client->getKey())
            ],
        )->render();
    }

    public function storeAttempt(SaleTask $saleTask, SaleAttemptRequest $request): RedirectResponse|JsonResponse
    {
        try {
            if (
                $saleTask->saleTaskType->isType(SaleTaskType::SALE_TASK_TYPE_INCOMPLETE_APPLICATION) &&
                $saleTask->loan?->isActive()
            ) {
                $data['comment'] = 'Не може да откажете кредит който е активен.';
                $data['sale_decision_reason_id'] = 'other';

                /// close sale task
                $this->getSaleService()->storeSaleAttempt($saleTask, $data);

                throw new Exception(
                    __('head::loanCrud.ClientAlreadySignTheLoan')
                );
            }

            DB::transaction(function () use ($saleTask, $request) {
                if (
                    $saleTask->saleTaskType->isType(SaleTaskType::SALE_TASK_TYPE_INCOMPLETE_APPLICATION) &&
                    in_array(intval($request->get('sale_decision_id')), SaleDecision::getDecisionIdsForCancelLoan())
                ) {
                    $data = $request->validated();
                    $data['comment'] = 'Отказана от агент';
                    $data['sale_decision_reason_id'] = 'other';
                    $this->getSaleService()->storeSaleAttempt($saleTask, $data);

                    $decisionDto = DecisionDto::from([
                        'loan_id' => $saleTask->loan_id,
                        'admin_id' => getAdminId(),
                        'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                        'decision_reason' => 'other',
                        'description' => 'Отказана от агент'
                    ]);

                    app(CancelLoanAction::class)->execute($decisionDto);
                } else {
                    $this->getSaleService()->storeSaleAttempt($saleTask, $request->validated());
                }
            });
        } catch (Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage()
                ]);
            }

            return back()->with('fail', $e->getMessage());
        }

        if ($request->ajax()) {
            $request->session()->put('successfullyProcessed', __('sales::saleTask.successfullyProcessedSaleTask'));
            $request->session()->put('success', __('sales::saleTask.successfullyProcessedSaleTask'));

            return response()->json([
                'redirectTo' => route('sales.saleTask.list')
            ]);
        }

        return redirect()
            ->route('sales.saleTask.list')
            ->with('successfullyProcessed', __('sales::saleTask.successfullyProcessedSaleTask'))
            ->with('success', __('sales::saleTask.successfullyProcessedSaleTask'));
    }

    /**
     * Get parsed URL params
     *
     * Possible variants:
     * - clientCard/{client_id}
     * - clientCard/{client_id}/{loan_id}
     * - clientCard/{client_id}/{sales|approve|collect}
     * - clientCard/{client_id}/{sales|approve|collect}/{task_id}
     * - clientCard/{client_id}/{loan_id}/{sales|approve|collect}
     * - clientCard/{client_id}/{loan_id}/{sales|approve|collect}/{task_id}
     */
    private function getRouteParams(): array
    {
        $clientId = Route::current()->parameter('clientId');
        $secondParam = Route::current()->parameter('second');
        $thirdParam = Route::current()->parameter('third');
        $fourthParam = Route::current()->parameter('fourth');

        // Check if client id is not numeric
        if (!is_numeric($clientId)) {
            throw new RuntimeException(
                __('head::clientCard.clientIdRouteParamMustBeInt')
            );
        }

        $routeParams = [
            'clientId' => (int) $clientId,
        ];

        // clientCard/{client_id}
        if (
            is_null($secondParam)
            && is_null($thirdParam)
            && is_null($fourthParam)
        ) {
            return $routeParams;
        }

        // clientCard/{client_id}/{loan_id}
        if (
            is_numeric($secondParam)
            && is_null($thirdParam)
            && is_null($fourthParam)
        ) {
            $routeParams['loanId'] = (int) $secondParam;

            return $routeParams;
        }

        // clientCard/{client_id}/{sales|approve|collect}
        if (
            !is_numeric($secondParam)
            && is_null($thirdParam)
            && is_null($fourthParam)
        ) {
            if (!$this->isValidStringRouteParam($secondParam)) {
                throw new RuntimeException(
                    __('head::clientCard.invalidSecondRoutingParam')
                );
            }
            $routeParams['do'] = $secondParam;

            return $routeParams;
        }

        // clientCard/{client_id}/{loan_id}/{sales|approve|collect}
        if (
            is_numeric($secondParam)
            && !is_numeric($thirdParam)
            && is_null($fourthParam)
        ) {
            if (!$this->isValidStringRouteParam($thirdParam)) {
                throw new RuntimeException(
                    __('head::clientCard.invalidThirdRoutingParam')
                );
            }
            $routeParams['loanId'] = (int) $secondParam;
            $routeParams['do'] = $thirdParam;

            return $routeParams;
        }

        // clientCard/{client_id}/{sales|approve|collect}/{task_id}
        if (
            !is_numeric($secondParam)
            && is_numeric($thirdParam)
            && is_null($fourthParam)
        ) {
            if (!$this->isValidStringRouteParam($secondParam)) {
                throw new RuntimeException(
                    __('head::clientCard.invalidSecondRoutingParam')
                );
            }
            $routeParams['do'] = $secondParam;
            $routeParams['taskId'] = (int) $thirdParam;

            return $routeParams;
        }

        // clientCard/{client_id}/{loan_id}/{sales|approve|collect}/{task_id}
        if (
            is_numeric($secondParam)
            && !is_numeric($thirdParam)
            && is_numeric($fourthParam)
        ) {
            if (!$this->isValidStringRouteParam($thirdParam)) {
                throw new RuntimeException(
                    __('head::clientCard.invalidThirdRoutingParam')
                );
            }
            $routeParams['loanId'] = (int) $secondParam;
            $routeParams['do'] = $thirdParam;
            $routeParams['taskId'] = (int) $fourthParam;

            return $routeParams;
        }

        throw new RuntimeException(__('head::clientCard.noneRoutesMatchCriteria'));
    }

    private function getCommonVars(
        Client $client,
        Loan $loan = null,
        array $routeParams = []
    ): array {
        $loans = $this->getLoanService()->getClientLoanIds($client);

        return [
            'administrator' => $this->getAdministrator(),
            'client' => $client,
            'clientLoans' => $this->getLoanService()->getClientLoanIds($client),
            'blockReasons' => $this->blockReasonRepo->getAll(),
            'offices' => $this->getOfficeService()->getAllOffices(),
            'headerData' => $this->getHeaderService()->getHeaderData($client, $loan, $routeParams),
        ];
    }

    public function refreshPreviousRequests(Client $client)
    {
        return view(
            'head::card.previous-requests-list',
            [
                'prevLoans' => $this->getLoanService()->getPaginatedLoansForClientCard($client->getKey()),
            ]
        )->render();
    }

    public function refreshSystemLogs(Client $client)
    {
        return view(
            'head::card.systemLog-list',
            [
                'loanClientInstallmentsHistory' => $this->getSystemLogsTableData($client->client_id),
                'client' => $client,
            ]
        )->render();
    }

    private function getSystemLogsTableData(int $clientId, int $limit = null)
    {
        $currentPage = LengthAwarePaginator::resolveCurrentPage();
        parse_str(request()->getQueryString(), $query);

        return $this->getLoanService()->getLogsForLoansClientsInstallments(
            $clientId,
            $currentPage,
            $query,
            $limit ?? parent::getTableLength(),
        );
    }

    private function getTabs(array $vars): array
    {
        $vars['tabs'] = self::CLIENT_CARD_TABS;

        return $vars;
    }

    private function isValidStringRouteParam(string $param): bool
    {
        $possibleStringParams = [
            ClientCardRouteParamEnum::SALES->value,
            ClientCardRouteParamEnum::APPROVE->value,
            ClientCardRouteParamEnum::COLLECT->value,
        ];

        if (!in_array($param, $possibleStringParams)) {
            return false;
        }

        return true;
    }

    public function checkIfLoanIsProcessed(Request $request): JsonResponse
    {
        $loanId = (int) $request['loanId'];
        $hide = $this->getApproveAttemptService()->checkIfLoanIsProcessed($loanId);

        return Response::json(
            [
                'hide' => $hide,
            ],
            SymfonyResponse::HTTP_OK
        );
    }

    public function refreshClientLoans(Client $client)
    {
        return view(
            'head::card.items.boxHistoryOfApplicationsAndLoans.ccr-report-history-list',
            [
                'loans' => $this->getLoanService()->getPaginatedLoansForClientCard($client->getKey(), 5),
            ]
        )->render();
    }

    private function getClientService(): ClientService
    {
        if ($this->clientService === null) {
            $this->clientService = app(ClientService::class);
        }

        return $this->clientService;
    }

    private function getLoanService(): LoanService
    {
        if ($this->loanService === null) {
            $this->loanService = app(LoanService::class);
        }

        return $this->loanService;
    }

    private function getOfficeService(): OfficeService
    {
        if ($this->officeService === null) {
            $this->officeService = app(OfficeService::class);
        }

        return $this->officeService;
    }

    private function getDocumentService(): DocumentService
    {
        if ($this->documentService === null) {
            $this->documentService = app(DocumentService::class);
        }

        return $this->documentService;
    }


    private function getSaleService()
    {
        if ($this->saleService === null) {
            $this->saleService = app(SaleService::class);
        }

        return $this->saleService;
    }

    private function getOpenTaskService(): OpenTasksService
    {
        if ($this->openTasksService === null) {
            $this->openTasksService = app(OpenTasksService::class);
        }

        return $this->openTasksService;
    }

    private function getApproveAttemptService(): ApproveAttemptService
    {
        if ($this->approveAttemptService === null) {
            $this->approveAttemptService = app(ApproveAttemptService::class);
        }

        return $this->approveAttemptService;
    }

    private function getPaymentService(): PaymentService
    {
        if ($this->paymentService === null) {
            $this->paymentService = app(PaymentService::class);
        }

        return $this->paymentService;
    }

    private function getHeaderService(): HeaderService
    {
        if ($this->headerService === null) {
            $this->headerService = app(HeaderService::class);
        }

        return $this->headerService;
    }
}
