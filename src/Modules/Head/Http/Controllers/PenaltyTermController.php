<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Http\Requests\PenaltyTermSearchRequest;
use Modules\Head\Services\PenaltyTermService;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;
use RuntimeException;
use Symfony\Component\HttpFoundation\Request;

class PenaltyTermController extends BaseController
{
    /**
     * @var string
     */
    protected string $pageTitle = 'Penalty term list';

    /**
     * @var string
     */
    private string $indexRoute = 'head.penaltyTerm.list';

    /**
     * @var string
     */
    private string $detailRoute = 'head.penaltyTerm.detail';

    /**
     * @var PenaltyTermService
     */
    private PenaltyTermService $penaltyTermService;

    /**
     * @var ProductService
     */
    private ProductService $productService;

    /**
     * PenaltyTermController constructor.
     *
     * @param PenaltyTermService $penaltyTermService
     * @param ProductService $productService
     *
     * @throws \ReflectionException
     */
    public function __construct(
        PenaltyTermService $penaltyTermService,
        ProductService $productService,
        private readonly ProductTypeService $productTypeService = new ProductTypeService,
    ) {
        $this->penaltyTermService = $penaltyTermService;
        $this->productService = $productService;

        parent::__construct();
    }

    public function list()
    {
        return view(
            'head::penaltyTerm.list',
            [
                'products' => $this->getTableData(),
                'productGroups' => $this->productTypeService->all(),
                'cacheKey' => $this->cacheKey,
            ]
        );
    }

    /**
     * @return mixed
     *
     * @throws Exception
     */
    private function getTableData()
    {
        return $this->penaltyTermService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param PenaltyTermSearchRequest $request
     *
     * @return array|string
     * @throws \Throwable
     */
    public function refresh(PenaltyTermSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'head::penaltyTerm.list-table',
            [
                'products' => $this->getTableData(),
            ]
        )->render();
    }

    /**
     * @param int $productId
     *
     * @return array|string
     * @throws NotFoundException
     * @throws \Throwable
     */
    public function detail(int $productId)
    {
        $product = $this->productService->getById($productId);
        $lastPenaltyTerm = $product->penaltyTerms()->first();
        $lastImportAt = $lastPenaltyTerm ? $lastPenaltyTerm->created_at->toDateTimeString() : null;
        $lastImportBy = $lastPenaltyTerm ? $lastPenaltyTerm->creator->getFullNames() : null;
        $penaltyTerms = $product
            ->penaltyTerms()
            ->paginate(parent::getTableLength());

        return view(
            'head::penaltyTerm.detail',
            [
                'product' => $product,
                'penaltyTerms' => $penaltyTerms,
                'importHistory' => [
                    'lastImportAt' => $lastImportAt,
                    'lastImportBy' => $lastImportBy,
                ],
            ]
        )->render();
    }

    public function import(Request $request): RedirectResponse
    {
        $file = $request->import_file;
        $productId = $request->product_id;

        if (empty($file)) {
            throw new RuntimeException(
                __('head::penaltyTerm.pleaseSelectImportFile')
            );
        }

        if (empty($productId) || !is_numeric($productId)) {
            throw new RuntimeException(
                __('head::productCrud.productInvalidId')
            );
        }

        $successfulImport = $this->penaltyTermService->import(
            $file,
            $this->productService->getById($productId)
        );

        if (!$successfulImport) {
            throw new RuntimeException(
                __('head::penaltyTerm.unsuccessfullyImportedPenaltyTerms')
            );
        }

        return redirect()
            ->route($this->detailRoute, $productId)
            ->with(
                'successfullyImported',
                __('head::penaltyTerm.successfullyImportedPenaltyTerms')
            );
    }
}
