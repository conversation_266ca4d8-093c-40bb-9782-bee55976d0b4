<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Application\Actions\CustomNoiReports\CustomNoiReportsListAction;
use Modules\Head\Http\Requests\CustomNoiReportsRequest;
use Modules\Head\Models\CustomNoiReports;
use Modules\Head\Services\CustomNoiReportsService;
use RuntimeException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class CustomNoiReportsController extends BaseController
{
    public function list(CustomNoiReportsListAction $action): View
    {
        return view('head::custom-noi-reports.list', $action->execute());
    }

    public function import(CustomNoiReportsRequest $request, CustomNoiReportsService $service): RedirectResponse
    {
        try {
            $service->import($request->validated('custom_noi_report'));

            return to_route('head.custom-noi-reports.list')
                ->with('success', __('messages.importFileSuccess'));

        } catch (\Throwable $e) {
            $msg = 'Error ' . $e->getMessage() . ', file: ' . $e->getFile() . ', line: ' . $e->getLine();

            return to_route('head.custom-noi-reports.list')
                ->with('fail', $e->getMessage());
        }
    }

    public function processFile(CustomNoiReports $customNoiReports, CustomNoiReportsService $service): RedirectResponse
    {
        try {
            if ($customNoiReports->processed) {
                throw new RuntimeException('This file already processed.');
            }

            $customNoiReports->setAttribute('processed_at', now()); // we need it in view
            $customNoiReports->saveQuietly();

            $service->runNoiReports($customNoiReports, getAdminId());

            return to_route('head.custom-noi-reports.list')
                ->with('success', __('messages.GettingNewReportsForImprtedIds'));

        } catch (\Throwable $e) {
            $msg = 'Error ' . $e->getMessage() . ', file: ' . $e->getFile() . ', line: ' . $e->getLine();

            return to_route('head.custom-noi-reports.list')
                ->with('fail', $e->getMessage());
        }
    }

    public function downloadProcessedReport(CustomNoiReports $customNoiReports): BinaryFileResponse
    {
        return response()->download(
            $customNoiReports->exportedFile->filepath()
        );
    }
}
