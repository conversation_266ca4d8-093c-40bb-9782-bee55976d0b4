<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Maatwebsite\Excel\Excel;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\Head\Application\Actions\ExportLoansAction;
use Modules\Head\Application\Actions\LoansDataAction;
use Modules\Head\Http\Requests\LoanSearchRequest;
use Modules\Head\Services\LoanRefundService;
use Modules\Head\Services\LoanService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

final class LoanController extends BaseController
{
    public string $pageTitle = 'Loan list';

    public function list(
        LoanSearchRequest $request,
        LoansDataAction $loansDataAction
    ): View {
        return view('head::loan.list', $loansDataAction->execute($request->validated()));
    }

    public function exportCsv(
        LoanSearchRequest $request,
        ExportLoansAction $action
    ): Response|BinaryFileResponse {
        return $action->execute($request->validated())->download(
            'loans_export_' . time() . '.csv',
            Excel::CSV
        );
    }

    public function exportXlsx(
        LoanSearchRequest $request,
        ExportLoansAction $action
    ): Response|BinaryFileResponse {
        return $action->execute($request->validated())->download(
            'loans_export_' . time() . '.xlsx',
            Excel::XLSX
        );
    }

    public function cancelActiveLoan(int $loanId, LoanRefundService $service): Response
    {
        $message = '';
        $status =  Response::HTTP_NO_CONTENT;

        try {
            $service->cancelById($loanId, [PaymentMethodEnum::BANK]);
        } catch (\RuntimeException $e) {
            $status = Response::HTTP_BAD_REQUEST;
            $message = $e->getMessage();
        } catch (Throwable $e) {
            $status = Response::HTTP_INTERNAL_SERVER_ERROR;
            $message = $e->getMessage();

            Log::error($e->getMessage(), compact('loanId'));
        }

        return response($status !== Response::HTTP_NO_CONTENT ? ['message' => $message] : null, $status);
    }

    public function removeFromOuterCollector(int $loanId, LoanService $service): Response
    {
        $message = '';
        $status =  Response::HTTP_NO_CONTENT;

        try {
            $loan = Loan::findOrFail($loanId);
            $service->removeFromOuterCollector($loan);
        } catch (ModelNotFoundException $e) {
            $status = Response::HTTP_NOT_FOUND;
            Log::warning($e->getMessage(), compact('loanId'));
            $message = __('Loan not found');
        } catch (Throwable $e) {
            $status = Response::HTTP_INTERNAL_SERVER_ERROR;
            Log::error($e->getMessage(), compact('loanId'));
            $message = __('Operation failed');
        }

        return response($status !== Response::HTTP_NO_CONTENT ? compact('message') : null, $status);
    }
}
