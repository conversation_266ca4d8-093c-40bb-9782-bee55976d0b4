<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Application\Actions\ExportRequestsAction;
use Mo<PERSON>les\Head\Application\Actions\ExportTelemarketingAction;
use Modules\Head\Application\Actions\TelemarketingDataAction;
use Mo<PERSON>les\Head\Http\Requests\FilterRequests\TelemarketingFilterRequest;

class TelemarketingController extends BaseController
{
    public function list(
        TelemarketingFilterRequest $request,
        TelemarketingDataAction $action
    ) {
        return view(
            'head::telemarketing.list',
            $action->execute(
                $request->validated(),
                $this->getPaginationLimit()
            )
        );
    }

    public function exportRequests(
        TelemarketingFilterRequest $request,
        ExportRequestsAction $action
    ): Response|RedirectResponse {
        try {
            $exportData = $action->execute($request->validated());

            // Check if export data is available
            if (empty($exportData) || !isset($exportData['file_path'])) {
                return back()->with('fail', 'Няма данни за експорт');
            }

            // Create a response with the file content
            $fileContent = file_get_contents($exportData['file_path']);

            // Clean up the temporary file
            unlink($exportData['file_path']);

            return response($fileContent)
                ->header('Content-Type', $exportData['content_type'])
                ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
                ->header('Cache-Control', 'max-age=0');

        } catch (\Exception $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return back()->with('fail', __('messages.error'));
        }
    }

    public function exportTelemarketing(
        TelemarketingFilterRequest $request,
        ExportTelemarketingAction $action
    ) {
        try {
            $action->execute($request->validated());
        } catch (\Exception $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return back()->with('fail', __('messages.error') . ' ' . $e->getMessage());
        }

        return back()->with('fail', __('messages.success'));
    }
}