<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Http\Requests\InterestTermSearchRequest;
use Modules\Head\Services\InterestTermService;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;
use RuntimeException;
use Symfony\Component\HttpFoundation\Request;
use Throwable;

class InterestTermController extends BaseController
{
    /**
     * @var string
     */
    protected string $pageTitle = 'Interest term list';

    /**
     * @var string
     */
    private string $indexRoute = 'head.interestTerm.list';

    /**
     * @var string
     */
    private string $detailRoute = 'head.interestTerm.detail';

    /**
     * @var InterestTermService
     */
    private InterestTermService $interestTermService;

    /**
     * @var ProductService
     */
    private ProductService $productService;

    /**
     * InterestTermController constructor.
     *
     * @param InterestTermService $penaltyTermService
     *
     * @param ProductService $productService
     *
     * @throws \ReflectionException
     */
    public function __construct(
        InterestTermService $penaltyTermService,
        ProductService $productService,
        private readonly ProductTypeService $productTypeService = new ProductTypeService,
    ) {
        $this->interestTermService = $penaltyTermService;
        $this->productService = $productService;

        parent::__construct();
    }

    public function list()
    {
        return view(
            'head::interestTerm.list',
            [
                'products' => $this->getTableData(),
                'productGroups' => $this->productTypeService->all(),
                'cacheKey' => $this->cacheKey,
            ]
        );
    }

    /**
     * @return mixed
     *
     * @throws Exception
     */
    private function getTableData()
    {
        return $this->interestTermService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param InterestTermSearchRequest $request
     *
     * @return array|string
     *
     * @throws Throwable
     */
    public function refresh(InterestTermSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'head::interestTerm.list-table',
            [
                'products' => $this->getTableData(),
            ]
        )->render();
    }

    /**
     * @param int $productId
     *
     * @return array|string
     *
     * @throws NotFoundException
     * @throws Throwable
     */
    public function detail(int $productId)
    {
        $product = $this->productService->getById($productId);
        $lastInterestTerm = $product->interestTerms()->first();
        $lastImportAt = $lastInterestTerm ? $lastInterestTerm->created_at->toDateTimeString() : null;
        $lastImportBy = $lastInterestTerm ? $lastInterestTerm->creator->getFullNames() : null;
        $interestTerms = $product
            ->interestTerms()
            ->paginate(parent::getTableLength());

        return view(
            'head::interestTerm.detail',
            [
                'product' => $product,
                'interestTerms' => $interestTerms,
                'importHistory' => [
                    'lastImportAt' => $lastImportAt,
                    'lastImportBy' => $lastImportBy,
                ]
            ]
        )
            ->render();
    }

    public function import(Request $request): RedirectResponse
    {
        $file = $request->import_file;
        $productId = $request->product_id;

        if (empty($file)) {
            throw new RuntimeException(
                __('head::interestTerm.pleaseSelectImportFile')
            );
        }

        if (empty($productId) || !is_numeric($productId)) {
            throw new RuntimeException(
                __('head::productCrud.productInvalidId')
            );
        }

        $successfulImport = $this->interestTermService->import(
            $file,
            $this->productService->getById($productId)
        );

        if (!$successfulImport) {
            throw new RuntimeException(
                __('head::interestTerm.unsuccessfullyImportedTerms')
            );
        }

        return redirect()
            ->route($this->detailRoute, $productId)
            ->with(
                'successfullyImported',
                __('head::interestTerm.successfullyImportedTerms')
            );
    }
}
