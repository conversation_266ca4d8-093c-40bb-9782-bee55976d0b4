<?php

namespace Modules\Head\Http\Controllers;

use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\ThirdParty\Services\AutegryService;

final class AutegryController extends BaseController
{
    public function getAutegryJson($loan_id)
    {
        $loan = Loan::findOrFail($loan_id);

        $res = app(AutegryService::class)->getRequestData2(
            $loan->client,
            $loan,
            true
        );

        // Recursively decode JSON-encoded strings (e.g. "ccr": "{\"@code\":...}")
        $decode = function ($value) use (&$decode) {
            if (is_array($value)) {
                foreach ($value as $k => $v) {
                    $value[$k] = $decode($v);
                }
                return $value;
            }
            if (is_string($value)) {
                $t = trim($value);
                // Looks like JSON object/array?
                if ($t !== '' && in_array($t[0], ['{', '['], true) && in_array(substr($t, -1), ['}', ']'], true)) {
                    $d = json_decode($t, true);
                    if (json_last_error() === JSON_ERROR_NONE && (is_array($d) || is_object($d))) {
                        return $decode((array) $d);
                    }
                    // Try un-escaping if double-escaped
                    $d = json_decode(stripslashes($t), true);
                    if (json_last_error() === JSON_ERROR_NONE && (is_array($d) || is_object($d))) {
                        return $decode((array) $d);
                    }
                }
            }
            return $value;
        };

        $payload = [$loan->loan_id => $decode($res)];

        return response()->json(
            $payload,
            200,
            [],
            JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
        );
    }
}
