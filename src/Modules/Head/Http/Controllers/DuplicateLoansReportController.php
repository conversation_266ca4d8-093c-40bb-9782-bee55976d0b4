<?php

namespace Modules\Head\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\LoanEmail;
use Modules\Common\Models\LoanPhone;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Services\SpreadsheetHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

class DuplicateLoansReportController extends BaseController
{
    public function duplicateLoans(): View
    {
        return view('head::duplicate-loans.list');
    }

    public function duplicateEmailLoansReport()
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        /// get all loans where has duplicate emails
        $duplicateEmailLoans = LoanEmail::query()
            ->with([
                'clientEmail:client_email_id,email,client_id',
                'loan:loan_id,client_id,created_at',
                'loan.loanActualStats:loan_id,first_loan',
            ])
            ->whereIn('client_email_id', function ($builder) {
                $builder->select('client_email_id')
                    ->from('client_email')
                    ->whereIn('email', function ($subQuery) {
                        $subQuery->select('email')
                            ->from('client_email')
                            ->groupBy('email')
                            ->havingRaw('COUNT(*) > 1');
                    });
            })
            ->whereHas('clientEmail')
            ->whereHas('loan', function (Builder $builder) {
                $builder->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
            })
            ->distinct('loan_id');

        $sheet->fromArray([
            __('table.LoanId'),
            __('table.ClientId'),
            __('table.CreatedAt'),
            __('table.IsLoanFirst'),
            __('table.Email'),
        ], null, 'A1');
        $rowIndex = 1;

        $duplicateEmailLoans->chunkById(3000, function (Collection $collection) use (&$sheet, &$rowIndex) {
            /** @var LoanEmail $row ** */
            foreach ($collection as $row) {
                $batchData[] = [
                    $row->loan_id,
                    $row->loan->client_id,
                    $row->loan->created_at->format('Y-m-d H:i'),
                    intval($row->loan->loanActualStats->first_loan) ? __('table.Yes') : __('table.No'),
                    $row->clientEmail->email,
                ];
                $rowIndex++;
                $sheet->fromArray($batchData, null, 'A' . $rowIndex);

                /// clear row
                $batchData = [];
            }
        });

        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Csv($spreadsheet);

        // Set the headers to force download the file
        $fileName = 'email_duplicates_' . Carbon::now()->toDateTimeString() . '.csv';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');

        // Write the spreadsheet to the output
        $writer->save('php://output');
        return ;
    }

    public function duplicatePhoneLoansReport()
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        /// get all loans where has duplicate phones
        $duplicatePoneLoans = LoanPhone::query()
            ->with([
                'clientPhone:client_phone_id,number,client_id',
                'loan:loan_id,client_id,created_at',
                'loan.loanActualStats:loan_id,first_loan',
            ])
            ->whereIn('client_phone_id', function ($query) {
                $query->select('client_phone_id')
                    ->from('client_phone')
                    ->whereIn('number', function ($subQuery) {
                        $subQuery->select('number')
                            ->from('client_phone')
                            ->groupBy('number')
                            ->havingRaw('COUNT(*) > 1');
                    });
            })
            ->whereHas('clientPhone')
            ->whereHas('loan', function (Builder $builder) {
                $builder->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
            })
            ->distinct('loan_id');


        $sheet->fromArray([
            __('table.LoanId'),
            __('table.ClientId'),
            __('table.CreatedAt'),
            __('table.IsLoanFirst'),
            __('table.Phone'),
        ], null, 'A1');
        $rowIndex = 1;

        $duplicatePoneLoans->chunkById(3000, function (Collection $collection) use (&$sheet, &$rowIndex) {
            /** @var LoanPhone $row ** */
            foreach ($collection as $row) {
                $batchData[] = [
                    $row->loan_id,
                    $row->loan->client_id,
                    $row->loan->created_at->format('Y-m-d H:i'),
                    intval($row->loan->loanActualStats->first_loan) ? __('table.Yes') : __('table.No'),
                    $row->clientPhone->number,
                ];
                $rowIndex++;
                $sheet->fromArray($batchData, null, 'A' . $rowIndex);

                /// clear row
                $batchData = [];
            }
        });

        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Csv($spreadsheet);

        // Set the headers to force download the file
        $fileName = 'phone_duplicates_' . Carbon::now()->toDateTimeString() . '.csv';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');

        // Write the spreadsheet to the output
        $writer->save('php://output');
        return  ;
    }
}
