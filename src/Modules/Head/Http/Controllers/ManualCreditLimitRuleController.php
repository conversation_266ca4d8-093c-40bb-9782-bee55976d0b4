<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\ClientCreditLimit;
use Modules\Common\Repositories\ClientCreditLimitRepository;
use Modules\Head\Application\Actions\ManualCreditLimitExportAction;
use Modules\Head\Application\Actions\ManualCreditLimitRuleListAction;
use Modules\Head\Http\Requests\FilterRequests\ManualCreditLimitRuleFilterRequest;
use Modules\Head\Http\Requests\StoreCreditLimitAmountRequest;

class ManualCreditLimitRuleController extends BaseController
{
    public function list(
        ManualCreditLimitRuleFilterRequest $request,
        ManualCreditLimitRuleListAction $action
    ): View {
        return view(
            'head::manual-credit-limit-rules.list',
            $action->execute(
                $request->validated(),
                $this->getPaginationLimit()
            )
        );
    }

    public function disableCreditLimit(ClientCreditLimit $clientCreditLimit): RedirectResponse
    {
        try {
            if (!$clientCreditLimit->exists) {
                throw new \Exception('Error invalid client credit limit id');
            }

            $clientCreditLimit->setAttribute('active', 0);
            $clientCreditLimit->setAttribute('deleted', 1);
            $clientCreditLimit->setAttribute('deleted_at', now());
            $clientCreditLimit->setAttribute('deleted_by', getAdminId());
            $clientCreditLimit->setAttribute('disabled_by', getAdminId());
            if ($clientCreditLimit->saveQuietly()) {
                return back()->with('success', __('messages.generalSuccessMessage'));
            }
        } catch (\Exception $exception) {
            return back()->with('fail', $exception->getMessage());
        }

        return back()->with('fail', __('messages.generalErrorSomethingWrong'));
    }

    public function export(
        ManualCreditLimitRuleFilterRequest $request,
        ManualCreditLimitExportAction $action
    ): Response|RedirectResponse {
        try {
            $exportData = $action->execute(
                $request->validated()
            );

            // Check if export data is available
            if (empty($exportData) || !isset($exportData['file_path'])) {
                return back()->with('fail', 'Няма данни за експорт');
            }

            // Create a response with the file content
            $fileContent = file_get_contents($exportData['file_path']);

            // Clean up the temporary file
            unlink($exportData['file_path']);

            return response($fileContent)
                ->header('Content-Type', $exportData['content_type'])
                ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
                ->header('Cache-Control', 'max-age=0');

        } catch (\Exception $e) {
            Log::debug($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return back()->with(
                'fail',
                __('messages.generalErrorSomethingWrong')
            );
        }
    }


    public function storeCreditLimitAmount(
        StoreCreditLimitAmountRequest $request,
        ClientCreditLimitRepository $clientCreditLimitRepository
    ) {
        try {
            //// check if already created
            if ($clientCreditLimitRepository->getByClientId($request->validated('client_id'))) {
                throw new \Exception(__('messages.errorClientCreditLimitAlreadyExists'));
            }

            $row = $clientCreditLimitRepository->create($request->validated());
            if ($row->exists) {
                return back()->with('success', __('messages.generalSuccessMessage'));
            }
        } catch (\Exception $exception) {
            \Log::debug($exception->getMessage());

            return back()->with('fail', $exception->getMessage());
        }

        return back()->with('fail', __('messages.generalErrorSomethingWrong'));
    }
}