<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Admin\Services\AdministratorService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\AdministratorClient;
use Modules\Common\Models\NoiReport;
use Modules\Head\Http\Requests\CreateNoiRequest;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;
use Modules\ThirdParty\Services\NoiService;
use Modules\ThirdParty\Services\ReportService;

final class NoiReportsController extends BaseController
{
    public function __construct(
        private readonly LoanService $loanService,
        private readonly ClientService $clientService,
        private readonly AdministratorService $administratorService,
        private readonly ReportService $reportService,
        private readonly NoiService $noiService
    ) {
        parent::__construct();
    }

    public function index(TabSwitchRequest $request): View
    {
        $loan = null;
        if ($request->has('loanId')) {
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }
        $client = $this->clientService->getClientById($request->get('clientId'));

        if (!empty($loan->loan_id)) {
            $reports = $this->noiService->getAllForLoan(
                $loan->loan_id,
                $client->client_id
            );
        } else {
            $reports = $this->noiService->getAllByPin($client->pin);
        }

        $hasNoiReportPermission = $this->administratorService->hasAdminMoreAttempts(
            $client->getKey(),
            AdministratorClient::ADMINISTRATOR_CLIENT_TYPE_NOI_REPORT
        );

        /// prepare view data
        $data = [
            'client' => $client,
            'loan' => $loan,
            'noiReports' => $reports,
            'hasNoiReportPermission' => $hasNoiReportPermission,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::noi-reports.index', $data);
    }

    /**
     * @param CreateNoiRequest $request
     * @return View
     * @throws \Modules\Common\Exceptions\NotFoundException
     */
    public function createNewNoiReport(CreateNoiRequest $request): View
    {
        $data = $request->validated();
        $reportTypes = $data['reportType'];

        $loan = null;
        if ($request->has('loanId')) {
            $loan = $this->loanService->getLoanById($data['loanId']);
        }
        $client = $this->clientService->getClientById($data['clientId']);


        list($attemptResult, $attemptRestCount) = $this->administratorService->reportAttempt(
            $client->getKey(),
            NoiReport::getTableName()
        );

        if ($attemptResult) {
            foreach ($reportTypes as $reportCode) {
                $this->reportService->addNoiReport(
                    $client,
                    $reportCode,
                    'manual-report',
                    $loan
                );
            }
        }

        if (!empty($loan->loan_id)) {
            $noiReports = $this->noiService->getAllForLoan(
                $loan->loan_id,
                $client->client_id
            );
        } else {
            $noiReports = $this->noiService->getAllByPin(
                $client->pin,
                [
                    'name' => 'ASC',
                    'created_at' => 'DESC',
                ]
            );
        }

        /// prepare view data
        $data = [
            'client' => $client,
            'loan' => $loan,
            'noiReports' => $noiReports,
            'hasNoiReportPermission' => $attemptRestCount > 0,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::noi-reports.index', $data);
    }
}
