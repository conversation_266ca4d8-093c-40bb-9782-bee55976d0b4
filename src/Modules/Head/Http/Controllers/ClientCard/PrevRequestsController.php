<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\ClientService;

class PrevRequestsController extends BaseController
{

    public function __construct(
        private readonly ClientService  $clientService,
        private readonly LoanRepository $loanRepository
    ) {
        parent::__construct();
    }

    public function index(TabSwitchRequest $request): View
    {
        $client = $this->clientService->getClientById($request->integer('clientId'));

        $loanId = $request->integer('loanId');
        $loan = $loanId ? $this->loanRepository->getById($loanId) : null;

        $data = [
            'clientId' => $client->getKey(),
            'client' => $client,
            'loan' => $loan,
            'loans' => $this->loanRepository->getByClientId($client->getKey(), 99999),
        ];

        return view('head::prev-requests.index', $data);
    }
}
