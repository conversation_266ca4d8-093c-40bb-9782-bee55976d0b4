<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;

class SystemLogController extends BaseController
{
    public function __construct(
        private readonly ClientService $clientService,
        private readonly LoanService   $loanService
    ) {
        parent::__construct();
    }

    public function index(TabSwitchRequest $request): View
    {
        $client = $this->clientService->getClientById($request->get('clientId'));
        $loan = null;
        if ($request->get('loanId')) {
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }

        /// todo refactor this
        $currentPage = LengthAwarePaginator::resolveCurrentPage();
        parse_str($request->getQueryString(), $query);

        $data = [
            'client' => $client,
            'loan' => $loan,
            /// todo refactor this
            'loanClientInstallmentsHistory' => $this->loanService->getLogsForLoansClientsInstallments(
                $client->getKey(),
                $currentPage,
                $query,
                $this->getPaginationLimit()
            ),
        ];

        return view('head::system-log.index', $data);
    }
}
