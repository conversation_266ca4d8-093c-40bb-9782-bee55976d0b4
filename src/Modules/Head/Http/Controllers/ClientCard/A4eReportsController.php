<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Admin\Services\AdministratorService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\AdministratorClient;
use Modules\Head\Http\Requests\A4EReportRequest;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;
use Modules\ThirdParty\Services\A4EService;

final class A4eReportsController extends BaseController
{
    public function __construct(
        private readonly ClientService $clientService,
        private readonly LoanService $loanService,
        private readonly A4EService $a4EService,
        private readonly AdministratorService $administratorService
    ) {
        parent::__construct();
    }

    public function index(TabSwitchRequest $request): View
    {
        $client = $this->clientService->getClientById($request->get('clientId'));
        $loan = null;
        if ($request->has('loanId')) {
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }

        $where = ['client_id' => $client->getKey()];
        if (!empty($loan->loan_id)) {
            $where['loan_id'] = $loan->loan_id;
        }

        $reports = $this->a4EService->getReportsByCriteria(
            $where,
            ['created_at' => 'DESC']
        );

        $hasA4EReportPermission = $this->administratorService->hasAdminMoreAttempts(
            $client->getKey(),
            AdministratorClient::ADMINISTRATOR_CLIENT_TYPE_A4E_REPORT
        );
        $creditLimit = $request->has('loanId') ? $loan->getCreditLimit() : null;

        $data = [
            'client' => $client,
            'loan' => $loan,
            'creditLimit' => $creditLimit,
            'clientCreditLimit' => $client->clientCreditLimit,
            'a4eReports' => $reports,
            'hasA4EReportPermission' => $hasA4EReportPermission,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::a4e-reports.index', $data);
    }

    public function createNewA4EReport(A4EReportRequest $request): View
    {
        $loan = null;
        $reportType = $request->get('reportType');
        $client = $this->clientService->getClientById($request->get('clientId'));

        list(
            $attemptResult,
            $attemptRestCount
        ) = $this->administratorService->reportAttempt(
            $client->getKey(),
            $reportType
        );

        $creditLimit = null;
        if ($attemptResult && $request->has('loanId')) {
            $loan = $latestLoan = $this->loanService->getLoanById($request->get('loanId'));
            $this->a4EService->getScoringForLoan($latestLoan, [], 'manual-report');
        }

        $where = ['client_id' => $client->getKey()];
        if (!empty($loan->loan_id)) {
            $where['loan_id'] = $loan->loan_id;
        }
        $a4eReports = $this->a4EService->getReportsByCriteria(
            $where,
            ['created_at' => 'DESC']
        );

        $data = [
            'client' => $client,
            'loan' => $loan,
            'creditLimit' => $creditLimit,
            'a4eReports' => $a4eReports,
            'hasA4EReportPermission' => $attemptRestCount > 0,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::a4e-reports.index', $data);
    }
}
