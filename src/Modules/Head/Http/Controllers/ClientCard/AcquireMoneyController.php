<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Modules\CashDesk\Application\Actions\GiveOutCashAction;
use Modules\Payments\Application\Actions\Task\ConfirmTransferToCashAction;
use Modules\Common\Models\Loan;
use Modules\Payments\Repositories\PaymentRepository;
use Modules\ThirdParty\Libraries\Tremol\TremolException;

class AcquireMoneyController extends Controller
{
    public function __invoke(
        Loan              $loan,
        Request           $request,
        GiveOutCashAction $giveOutCashAction,
        ConfirmTransferToCashAction $paymentDeliveryAction
    ): RedirectResponse {

        $payment = app()->make(PaymentRepository::class)->getMainOutgoingPaymentForLoan($loan->getKey());
        if (empty($payment->payment_id)) {
            return back()->with('fail', __('head::loanCrud.paymentNotFound'));
        }

        DB::beginTransaction();
        try {

            // make payment delivered, activate loan, stats, etc
            // also make delivered IN payments for refinance
            $payment = $paymentDeliveryAction->execute($loan, $payment);

            // create cashdesk OUT transaction, docs, print bon
            $result = $giveOutCashAction->execute($payment);

            if (Session::has(Loan::getTempLoanDataKey($loan->getKey()))) {
                Session::forget(Loan::getTempLoanDataKey($loan->getKey()));
            }

            DB::commit();

        } catch (TremolException $te) {

            DB::rollBack();

            Log::channel('fiscalDevice')->error(
                $te->getMessage(),
                [
                    'route' => $request->route()->getName(),
                    'loan' => $payment->loan_id,
                ]
            );

            session()->put('tremol', [
                'route' => $request->getUri(),
                'message' => $te->getMessage(),
            ]);

            return back()->with('fail', __('head::loanCrud.acquireError'));
        }

        try {
            session()->remove('tremol'); // TODO, а ако няма такъв ключ в сессия, само за loan->office != web
        } catch (TremolException $e) {
            $message = $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine();
            Log::debug($message);
        }


        //$successMsg = !empty($result['paymentTask']) ? 'Успешно създадена задача за нареждане.' : '';
        $successMsg = $result ? ' ' . __('head::loanCrud.loanActivated') : '';


        $downloadRoute = '';
        $payment->refresh();
        if (!empty($payment->cash_operational_transaction_id)) {
            $downloadRoute = $payment->getDownloadTransactionDocLink();
        }


        return to_route('head.clients.cardProfile', [$loan->client_id, $loan->loan_id])
            ->with([
                'successfullyPaidCash' => true,
                'successfullyProcessed' => $successMsg,
                'downloadTransactionDocRoute' => $downloadRoute,
            ]);
    }
}
