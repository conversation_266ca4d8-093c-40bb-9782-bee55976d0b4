<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Carbon\Carbon;
use Illuminate\View\View;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;

class JudicialTabController extends BaseClientCardController
{
    public function index(): View
    {
        if (!getAdmin()->hasPermissionTo('head.judicial-tab.index')) {
            throw new \Exception('Нямаш права за тази страница');
        }

        $routeParams = $this->getRouteParams();

        $data = [
            'client_id' => $routeParams['clientId'],
            'loan_id' => $routeParams['loanId'],
        ];

        return view('head::judicial-tab.index', $data);
    }

    public function process(): View
    {
        // permission check
        if (!getAdmin()->hasPermissionTo('head.judicial-tab.index')) {
            throw new \Exception('Нямаш права за тази страница');
        }


        // get params from url
        $routeParams = $this->getRouteParams();
        $clientId = $routeParams['clientId'] ?? null;
        $loanId = $routeParams['loanId'] ?? null;


        // get main objects
        $loan = Loan::find($loanId);
        if (empty($loan->loan_id)) {
            return back()
                ->with('fail', __('messages.WrongInputData') . '(1)')
                ->withFragment('#judicialtab');
        }

        $client = $loan->client;
        if (empty($client->client_id)) {
            return back()
                ->with('fail', __('messages.WrongInputData') . '(2)')
                ->withFragment('#judicialtab');
        }

        $installments = $loan->getAllInstallments();
        if (empty($installments) || $installments->count() < 1) {
            return back()
                ->with('fail', __('messages.WrongInputData') . '(3)')
                ->withFragment('#judicialtab');
        }


        // prepare data for excel generation
        $data = [
            'common' => $this->getCommonTableData($client, $loanId),
            'installments' => $this->getInstallmentsTableData($installments),
            'payments' => $this->getPaymentsTableData($loan),
        ];


        // download generated excel file
       return $this->downloadExcel($data);
    }

    ////////////////////// DATA ////////////////////////////////////////////////

    private function getCommonTableData(Client $client, int $loanId): array
    {
        return [
            'application_id' => $loanId,
            'client_first_name' => $client->first_name,
            'client_middle_name' => $client->middle_name,
            'client_surname' => $client->last_name,
            'client_pin' => $client->pin,
            'date' => now()->format('d.m.Y'),
            'company_name' => config('company.name_bg'),
        ];
    }

    private function getInstallmentsTableData($installments): array
    {
        $tableData = [
            'header' => [
                // 1
                'Вноска',
                'Дата падеж',
                'Главница',
                'Лихва',
                'Неустойка',
                'Други приходи',
                'Общо начислено',
                // 2
                'Плащане по главница',
                'Плащане по лихва',
                'Плащане по неустойка',
                'Плащане по други приходи',
                'Общо платено по вноска',
                'Текущо просрочие по вноска (дни)',
                'Макс. просрочие по вноска (дни)',
                'Дата на последно плащане по вноска',
                // 3
                'Дължима главница',
                'Дължима лихва',
                'Дължима неустойка',
                'Дължими други приходи',
                'Общо дължима сума',
            ],
            'rows' => [],
            'active_seq_num' => null,
            'totals' => [
                // 1
                'seq_num' => '',
                'due_date' => 'Общо',
                'principal' => 0,
                'interest' => 0,
                'penalty' => 0,
                'other' => 0,
                'total' => 0,
                // 2
                'paid_principal' => 0,
                'paid_interest' => 0,
                'paid_penalty' => 0,
                'paid_other' => 0,
                'paid_total' => 0,
                'cur_overdue_days' => '',
                'max_overdue_days' => '',
                'last_paid_date' => '',
                // 3
                'rest_principal' => 0,
                'rest_interest' => 0,
                'rest_penalty' => 0,
                'rest_other' => 0,
                'rest_total' => 0,
            ],
            'due_row' => [
                // 1
                'seq_num' => 'Дълг към:',
                'due_date' => now()->format('d.m.Y'),
                'principal' => '',
                'interest' => '',
                'penalty' => '',
                'other' => '',
                'total' => '',
                // 2
                'paid_principal' => '',
                'paid_interest' => '',
                'paid_penalty' => '',
                'paid_other' => '',
                'paid_total' => '',
                'cur_overdue_days' => '',
                'max_overdue_days' => '',
                'last_paid_date' => '',
                // 3
                'rest_principal' => 0,
                'rest_interest' => 0,
                'rest_penalty' => 0,
                'rest_other' => 0,
                'rest_total' => 0,
            ],
        ];


        $now = Carbon::today();
        $rows = [];
        $activeSeqNum = null;

        foreach ($installments as $installment) {

            // make all fields to be float
            $installment->principal = (float) $installment->principal;
            $installment->paid_principal = (float) $installment->paid_principal;
            $installment->rest_principal = (float) $installment->rest_principal;
            $installment->accrued_interest = (float) $installment->accrued_interest;
            $installment->interest = (float) $installment->interest;
            $installment->late_interest = (float) $installment->late_interest;
            $installment->paid_accrued_interest = (float) $installment->paid_accrued_interest;
            $installment->paid_interest = (float) $installment->paid_interest;
            $installment->rest_interest = (float) $installment->rest_interest;
            $installment->paid_late_interest = (float) $installment->paid_late_interest;
            $installment->accrued_penalty = (float) $installment->accrued_penalty;
            $installment->penalty = (float) $installment->penalty;
            $installment->late_penalty = (float) $installment->late_penalty;
            $installment->paid_accrued_penalty = (float) $installment->paid_accrued_penalty;
            $installment->paid_penalty = (float) $installment->paid_penalty;
            $installment->rest_penalty = (float) $installment->rest_penalty;
            $installment->paid_late_penalty = (float) $installment->paid_late_penalty;
            $installment->rest_late_penalty = (float) $installment->rest_late_penalty;
            $installment->rest_late_interest = (float) $installment->rest_late_interest;

            $dueDate = Carbon::parse($installment->due_date);

            if (
                is_null($activeSeqNum)
                && $installment->paid == 0
                && $now->lte($dueDate)
            ) {
                $activeSeqNum = $installment->seq_num;
            }

            $interest = $installment->interest;
            $penalty = $installment->penalty;
            if ($dueDate->lt($now)) {
                $interest = $installment->accrued_interest;
                $penalty = $installment->accrued_penalty;
            }
            if ($interest < $installment->rest_interest) {
                $interest = $installment->rest_interest;
            }
            if ($penalty < $installment->rest_penalty) {
                $penalty = $installment->rest_penalty;
            }

            $maxOverdueDays = $installment->max_overdue_days;
            $currOverdueDays = $installment->overdue_days;

            $instTaxesSums = $installment->getSumsOfTaxes();
            $taxesAmount = (float) intToFloat($instTaxesSums?->total_amount ?? 0);
            $taxesPaidAmount = (float) intToFloat($instTaxesSums?->total_paid_amount ?? 0);
            $taxesRestAmount = (float) intToFloat($instTaxesSums?->total_rest_amount ?? 0);

            $rows[] = [
                // Кредитен план и начисления
                'seq_num' => $installment->seq_num,
                'due_date' => $dueDate->format('d.m.Y'),
                'principal' => $installment->principal,
                'interest' => $interest,
                'penalty' => $penalty,
                'other' => ($installment->late_interest + $installment->late_penalty + $taxesAmount),
                'total' => (
                    + $installment->principal
                    + $interest
                    + $penalty
                    + $installment->late_interest
                    + $installment->late_penalty
                    + $taxesAmount
                ),

                // Платежни операции
                'paid_principal' => $installment->paid_principal,
                'paid_interest' => $installment->paid_interest,
                'paid_penalty' => $installment->paid_penalty,
                'paid_other' => ($installment->paid_late_interest + $installment->paid_late_penalty + $taxesPaidAmount),
                'paid_total' => (
                    + $installment->paid_principal
                    + $installment->paid_interest
                    + $installment->paid_penalty
                    + $installment->paid_late_interest
                    + $installment->paid_late_penalty
                    + $taxesPaidAmount
                ),
                'cur_overdue_days' => $currOverdueDays,
                'max_overdue_days' => $maxOverdueDays,
                'last_paid_date' => !empty($installment->paid_at) ? Carbon::parse($installment->paid_at)->format('d.m.Y') : '',

                // Салдо вземания
                'rest_principal' => $installment->rest_principal,
                'rest_interest' => $installment->rest_interest,
                'rest_penalty' => $installment->rest_penalty,
                'rest_other' => ($installment->rest_late_interest + $installment->rest_late_penalty + $taxesRestAmount),
                'rest_total' => (
                    + $installment->rest_principal
                    + $installment->rest_interest
                    + $installment->rest_penalty
                    + $installment->rest_late_interest
                    + $installment->rest_late_penalty
                    + $taxesRestAmount
                ),
            ];
        }


        $tableData['rows'] = $rows;
        // 1
        $tableData['totals']['principal'] = array_sum(array_column($rows, 'principal'));
        $tableData['totals']['interest'] = array_sum(array_column($rows, 'interest'));
        $tableData['totals']['penalty'] = array_sum(array_column($rows, 'penalty'));
        $tableData['totals']['other'] = array_sum(array_column($rows, 'other'));
        $tableData['totals']['total'] = array_sum(array_column($rows, 'total'));
        // 2
        $tableData['totals']['paid_principal'] = array_sum(array_column($rows, 'paid_principal'));
        $tableData['totals']['paid_interest'] = array_sum(array_column($rows, 'paid_interest'));
        $tableData['totals']['paid_penalty'] = array_sum(array_column($rows, 'paid_penalty'));
        $tableData['totals']['paid_other'] = array_sum(array_column($rows, 'paid_other'));
        $tableData['totals']['paid_total'] = array_sum(array_column($rows, 'paid_total'));
        // 3
        $tableData['totals']['rest_principal'] = array_sum(array_column($rows, 'rest_principal'));
        $tableData['totals']['rest_interest'] = array_sum(array_column($rows, 'rest_interest'));
        $tableData['totals']['rest_penalty'] = array_sum(array_column($rows, 'rest_penalty'));
        $tableData['totals']['rest_other'] = array_sum(array_column($rows, 'rest_other'));
        $tableData['totals']['rest_total'] = array_sum(array_column($rows, 'rest_total'));

        $tableData['due_row']['rest_principal'] = $tableData['totals']['rest_principal'];
        $tableData['due_row']['rest_interest'] = $tableData['totals']['rest_interest'];
        $tableData['due_row']['rest_penalty'] = $tableData['totals']['rest_penalty'];
        $tableData['due_row']['rest_other'] = $tableData['totals']['rest_other'];
        $tableData['due_row']['rest_total'] =  $tableData['totals']['rest_total'];

        $tableData['active_seq_num'] =  $activeSeqNum;


        return $tableData;
    }

    private function getPaymentsTableData(Loan $loan): array
    {
        $tableData = [
            'header' => [
                'Плащане No.',
                'Дата',
                'Плащане по главница',
                'Плащане по лихва',
                'Плащане по неустойка',
                'Плащане по други приходи',
                'Общо платена сума',
            ],
            'rows' => [],
            'totals' => [
                'payment_num' => 'Общо платено:',
                'date' => '',
                'principal' => 0,
                'interest' => 0,
                'penalty' => 0,
                'other' => 0,
                'total' => 0,
            ],
        ];

        $payments = $loan->getInPayments();
        if ($payments->count() < 1) {
            return $tableData;
        }


        $rows = [];
        foreach ($payments as $payment) {
            if (!empty($payment->deleted_at) || $payment->active != 1) {
                continue;
            }

            $distribution = $payment->getCalcDistribution();
            $rows[] = [
                'payment_num' => $payment->payment_id,
                'date' => Carbon::parse($payment->created_at)->format('d.m.Y'),
                'principal' => intToFloat($distribution->principal),
                'interest' => intToFloat($distribution->interest),
                'penalty' => intToFloat($distribution->penalty),
                'other' => intToFloat((
                    $distribution->late_interest
                    + $distribution->late_penalty
                    + $distribution->amount
                )),
                'total' => intToFloat((
                    $distribution->principal
                    + $distribution->interest
                    + $distribution->penalty
                    + $distribution->late_interest
                    + $distribution->late_penalty
                    + $distribution->amount
                )),
            ];
        }


        $tableData['rows'] = $rows;
        $tableData['totals']['principal'] = array_sum(array_column($rows, 'principal'));
        $tableData['totals']['interest'] = array_sum(array_column($rows, 'interest'));
        $tableData['totals']['penalty'] = array_sum(array_column($rows, 'penalty'));
        $tableData['totals']['other'] = array_sum(array_column($rows, 'other'));
        $tableData['totals']['total'] = array_sum(array_column($rows, 'total'));

        return $tableData;
    }

    ////////////////////// EXCEL ///////////////////////////////////////////////

    private function downloadExcel(array $data)
    {
        // Create spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // common:
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial')->setSize(10);
        foreach (range('A', 'T') as $col) {
            $sheet->getColumnDimension($col)->setWidth(12.10);
        }

        // Row 1
        $sheet->setCellValue('A1', "Кредит No. " . $data['common']['application_id']);
        $sheet->setCellValue('C1', "Клиент:" . $data['common']['client_first_name'] .  (!empty($data['common']['client_middle_name']) ? ' ' . $data['common']['client_middle_name'] : '') . ' ' . $data['common']['client_surname']);
        $sheet->setCellValue('I1', "ЕГН: " . $data['common']['client_pin']);
        $sheet->getStyle('A1')->getFont()->setBold(true);
        $sheet->getStyle('C1')->getFont()->setBold(true);
        $sheet->getStyle('I1')->getFont()->setBold(true);

        // Row 2 skipped (empty)

        // Row 3
        $sheet->setCellValue('A3', 'Справка за кредитен план и начисления - експорт към ' . $data['common']['date'] . 'г.');
        $sheet->getStyle('A3')->getFont()->setBold(true);

        // Row 4
        $sheet->setCellValue('A4', 'Кредитен план и начисления');
        $sheet->setCellValue('H4', 'Платежни операции');
        $sheet->setCellValue('P4', 'Салдо вземания');
        $sheet->getStyle('A4:G4')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('c1e5f5');
        $sheet->getStyle('H4:O4')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('d9f2d0');
        $sheet->getStyle('P4:T4')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('fbe3d6');
        $sheet->getStyle('A4:T4')->getFont()->setBold(true);

        // Row 5 - installment headers
        $col = 'A';
        foreach ($data['installments']['header'] as $header) {
            $sheet->setCellValue($col . '5', $header);
            $sheet->getStyle($col . '5')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('f2f2f2');
            $sheet->getStyle($col . '5')->getFont()->setBold(true);
            $sheet->getStyle($col . '5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $col++;
        }
        $sheet->getStyle('F5:T5')->getAlignment()->setWrapText(true);
        $sheet->getRowDimension(5)->setRowHeight(35);

        // Rows 6 - installment rows
        $rowIndex = 6;
        foreach ($data['installments']['rows'] as $row) {
            $col = 'A';
            foreach ($row as $colIndex => $value) {
                $cell = $col . $rowIndex;
                $sheet->setCellValue($cell, $value);

                if (!in_array($col, ['A', 'M', 'N'])) {
                    $sheet->getStyle($cell)->getNumberFormat()->setFormatCode('#,##0.00');
                }

                // Centered columns: A, B, M, N, O
                $centerCols = ['A', 'B', 'M', 'N', 'O'];
                if (in_array($col, $centerCols)) {
                    $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                } else {
                    $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                }

                $col++;
            }

            if (
                !empty($data['installments']['active_seq_num'])
                && $data['installments']['active_seq_num'] == $row['seq_num']
            ) {
                $sheet->getStyle('A' . $rowIndex . ':T' . $rowIndex)
                    ->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setRGB('fbe3d6');
            }

            $rowIndex++;
        }

        // Row X($rowIndex) installment totals
        $col = 'A';
        foreach ($data['installments']['totals'] as $v) {
            $cell = $col . $rowIndex;

            $sheet->setCellValue($cell, $v);
            $sheet->getStyle($cell)->getFont()->setBold(true);
            $sheet->getStyle($cell)->getNumberFormat()->setFormatCode('#,##0.00');

            // Centered columns: A, B, M, N, O
            $centerCols = ['A', 'B', 'M', 'N', 'O'];
            if (in_array($col, $centerCols)) {
                $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            } else {
                $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            }

            $col++;
        }
        $rowIndex++; // go to next row

        // Row X($rowIndex) installment due row
        $col = 'A';
        foreach ($data['installments']['due_row'] as $v) {
            $cell = $col . $rowIndex;

            $sheet->setCellValue($cell, $v);
            $sheet->getStyle($cell)->getFont()->setBold(true);
            $sheet->getStyle($cell)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('c1e5f5');

            // Centered columns: A, B, M, N, O
            $centerCols = ['A', 'B', 'M', 'N', 'O'];
            if (in_array($col, $centerCols)) {
                $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            } else {
                $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            }
            $sheet->getStyle($cell)->getNumberFormat()->setFormatCode('#,##0.00');

            $col++;
        }
        $rowIndex++; // go to next row
        $rowIndex++; // go to next row


        // Row X($rowIndex) payments title
        $cell = 'A' . $rowIndex;
        $sheet->setCellValue($cell, "Справка за извършени плащания по кредита и разпределение по партиди към  " . $data['common']['date'] . 'г.');
        $sheet->getStyle($cell)->getFont()->setBold(true);
        $rowIndex++; // go to next row

        // Row X($rowIndex) payments header
        $col = 'A';
        foreach ($data['payments']['header'] as $header) {
            $cell = $col . $rowIndex;
            $sheet->setCellValue($cell, $header);
            $sheet->getStyle($cell)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('f2f2f2');
            $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $col++;
        }
        $sheet->getStyle('G' . $rowIndex)->getFont()->setBold(true);
        $sheet->getStyle('A'.$rowIndex.':T' . $rowIndex)->getAlignment()->setWrapText(true);
        $sheet->getRowDimension($rowIndex)->setRowHeight(35);
        $rowIndex++; // go to next row

        // Row X($rowIndex) payments rows
        foreach ($data['payments']['rows'] as $row) {
            $col = 'A';
            foreach ($row as $colIndex => $value) {
                $cell = $col . $rowIndex;
                $sheet->setCellValue($cell, $value);
                if ($col != 'A') {
                    $sheet->getStyle($cell)->getNumberFormat()->setFormatCode('#,##0.00');
                }

                // Centered columns: A, B, M, N, O
                $centerCols = ['A', 'B'];
                if (in_array($col, $centerCols)) {
                    $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                } else {
                    $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                }

                $col++;
            }
            $sheet->getStyle('G' . $rowIndex)->getFont()->setBold(true);
            $rowIndex++;
        }

        // Row X($rowIndex) payments totals
        $col = 'A';
        foreach ($data['payments']['totals'] as $v) {
            $cell = $col . $rowIndex;

            $sheet->setCellValue($cell, $v);
            $sheet->getStyle($cell)->getFont()->setBold(true);
            $sheet->getStyle($cell)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('c1e5f5');

            // Centered columns: A, B, M, N, O
            $centerCols = ['A', 'B', 'M', 'N', 'O'];
            if (in_array($col, $centerCols)) {
                $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            } else {
                $sheet->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            }

            $sheet->getStyle($cell)->getNumberFormat()->setFormatCode('#,##0.00');

            $col++;
        }
        $rowIndex++; // go to next row
        $rowIndex++; // go to next row

        // Row X($rowIndex) - izgotvil
        $cell = 'A' . $rowIndex;
        $sheet->setCellValue($cell, "Изготвил:");
        $sheet->getStyle($cell)->getFont()->setBold(true);
        $rowIndex++; // go to next row

        $cell = 'B' . $rowIndex;
        $sheet->setCellValue($cell, $data['common']['company_name']);
        $sheet->getStyle($cell)->getFont()->setBold(true);
        $rowIndex++; // go to next row


        // Output to browser
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="installments_and_payments.xlsx"');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        return ;
    }
}
