<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Admin\Services\AdministratorService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\AdministratorClient;
use Modules\Head\Http\Requests\MvrReportRequest;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;
use Modules\ThirdParty\Services\MvrService;

final class MvrReportsController extends BaseController
{
    public function __construct(
        private readonly ClientService $clientService,
        private readonly LoanService $loanService,
        private readonly MvrService $mvrService,
        private readonly AdministratorService $administratorService
    ) {
        parent::__construct();
    }

    public function index(TabSwitchRequest $request): View
    {
        $client = $this->clientService->getClientById($request->get('clientId'));
        $loan = null;
        if ($request->has('loanId')) {
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }

        $reports = $this->mvrService->getAllByPin(
            $client->pin,
            ['created_at' => 'DESC']
        );

        $hasMvrReportPermission = $this->administratorService->hasAdminMoreAttempts(
            $client->getKey(),
            AdministratorClient::ADMINISTRATOR_CLIENT_TYPE_MVR_REPORT
        );

        $data = [
            'client' => $client,
            'loan' => $loan,
            'mvrReports' => $reports,
            'hasMvrReportPermission' => $hasMvrReportPermission,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::mvr-reports.index', $data);
    }

    public function createNewMvrReport(MvrReportRequest $request): View
    {
        $reportType = $request->get('reportType');

        $clientId = (int) $request->get('clientId');
        $client = $this->clientService->getClientById($clientId);

        $loan = null;
        $loanId = null;
        if ($request->has('loanId')) {
            $loanId = (int) $request->get('loanId');
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }

        list(
            $attemptResult,
            $attemptRestCount
            ) = $this->administratorService->reportAttempt(
            $client->getKey(),
            $reportType
        );

        if ($attemptResult) {
            $newReport = $this->mvrService->addMvrReport(
                $client->pin,
                $client->idcard_number,
                'manual-report'
            );

            if (!empty($newReport->mvr_report_id)) {

                if (!empty($loanId)) {
                    $mvrParsedData = json_decode($newReport->parsed_data ?? '[]', true);

                    $this->mvrService->linkReport(
                        $newReport->mvr_report_id,
                        $clientId,
                        $loanId,
                        $mvrParsedData
                    );
                }

                $this->mvrService->updateClientPicture($client);
            }
        }

        $mvrReports = $this->mvrService->getAllByPin(
            $client->pin,
            ['created_at' => 'DESC']
        );

        $data = [
            'client' => $client,
            'loan' => $loan,
            'mvrReports' => $mvrReports,
            'hasMvrReportPermission' => $attemptRestCount > 0,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::mvr-reports.index', $data);
    }
}
