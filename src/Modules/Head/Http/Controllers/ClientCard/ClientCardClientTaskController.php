<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Head\Application\Actions\PrepareClientCardDataAction;

class ClientCardClientTaskController extends BaseClientCardController
{
    public function index(
        PrepareClientCardDataAction $clientCardDataAction
    ): View {
        $routeParams = $this->getRouteParams();
        $data = $clientCardDataAction->execute($routeParams);

        return view('head::client-card-task.index', $data);
    }
}
