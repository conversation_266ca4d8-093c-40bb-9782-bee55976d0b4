<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Admin\Services\AdministratorService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\AdministratorClient;
use Modules\Head\Http\Requests\CreateCcrRequest;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;
use Modules\ThirdParty\Services\CcrService;

class CcrReportHistoryController extends BaseController
{

    public function __construct(
        private readonly LoanService          $loanService,
        private readonly ClientService        $clientService,
        private readonly CcrService           $ccrService,
        private readonly AdministratorService $administratorService
    )
    {
        parent::__construct();
    }

    public function index(TabSwitchRequest $request): View
    {
        $loan = null;
        if ($request->has('loanId')) {
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }
        $client = $this->clientService->getClientById($request->get('clientId'));

        if (!empty($loan->loan_id)) {
            $reports = $this->ccrService->getAllForLoan(
                $loan->loan_id,
                $client->client_id
            );
        } else {
            $reports = $this->ccrService->getAllByPin(
                $client->pin,
                ['created_at' => 'DESC']
            );
        }

        $hasCcrReportPermission = $this->administratorService->hasAdminMoreAttempts(
            $client->getKey(),
            AdministratorClient::ADMINISTRATOR_CLIENT_TYPE_CCR_REPORT
        );

        $data = [
            'client' => $client,
            'loan' => $loan,
            'ccrReports' => $reports,
            'hasCcrReportPermission' => $hasCcrReportPermission,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::ccr-report-history.index', $data);
    }

    /**
     * // CCR MANUAL REPORT (actual)
     */
    public function createNewCcrReport(CreateCcrRequest $request): View
    {
        $data = $request->validated();

        $client = $this->clientService->getClientById($data['clientId']);
        $loan = null;
        if (isset($data['loanId'])) {
            $loan = $this->loanService->getLoanById($data['loanId']);
        }

        list(
            $attemptResult,
            $attemptRestCount
            ) = $this->administratorService->reportAttempt(
            $client->getKey(),
            $data['reportType']
        );

        if ($attemptResult) {
            if (!empty($loan->loan_id)) {
                $ccrReports = $this->ccrService->createReportForLoan(
                    $loan,
                    'manual-report'
                );
            } else {
                $this->ccrService->getAndSaveNewReport(
                    $client->pin,
                    true,
                    'manual-report'
                );
            }
        }

        // get all reports again, including the new one
        if (!empty($loan->loan_id)) {
            $ccrReports = $this->ccrService->getAllForLoan(
                $loan->loan_id,
                $client->client_id
            );
        } else {
            $ccrReports = $this->ccrService->getAllByPin(
                $client->pin,
                ['created_at' => 'DESC']
            );
        }

        $data = [
            'client' => $client,
            'loan' => $loan,
            'ccrReports' => $ccrReports,
            'hasCcrReportPermission' => $attemptRestCount > 0,
            'reportTypes' => AdministratorClient::ADMINISTRATOR_CLIENT_ALL_TYPES
        ];

        return view('head::ccr-report-history.index', $data);
    }
}
