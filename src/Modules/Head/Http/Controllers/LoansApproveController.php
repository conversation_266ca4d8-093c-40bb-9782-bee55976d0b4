<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Approve\Application\Action\ExportLoansForApprovalAction;
use Modules\Approve\Exports\LoansForApprovalExport;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Application\Actions\LoansApproveDataAction;
use Modules\Head\Http\Requests\LoanSearchRequest;
use RuntimeException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class LoansApproveController extends BaseController
{

    public function list(
        LoanSearchRequest      $loanSearchRequest,
        LoansApproveDataAction $loansApproveDataAction
    ): View
    {
        return view(
            'head::loans-approve.list',
            $loansApproveDataAction->execute(
                $loanSearchRequest->getFilters(),
                $this->getPaginationLimit()
            )
        );
    }

    public function export(LoanSearchRequest $request, ExportLoansForApprovalAction $action): BinaryFileResponse
    {
        $rows = $action->execute($request->getFilters());
        return Excel::download(
            new LoansForApprovalExport($rows),
            'loan_for_approval_export_' . time() . '.xlsx'
        );
    }


    public function refreshApproved(
        LoanSearchRequest      $loanSearchRequest,
        LoansApproveDataAction $loansApproveDataAction
    ): View
    {
        $data = $loansApproveDataAction->fetchLoans($loanSearchRequest->getFilters(), $this->getPaginationLimit());
        $data['loans']->withPath(route('head.approveLoans.list'));

        return view('head::loans-approve.list-table', $data);
    }

    public function removeLoanAgent(Loan $loan): RedirectResponse
    {
        try {
            if (LoanStatus::PROCESSING_STATUS_ID !== $loan->loan_status_id) {
                throw new RuntimeException('Loan not in status processing, you cant reset admin');
            }

            $loan->setAttribute('loan_status_id', LoanStatus::SIGNED_STATUS_ID);
            $loan->setAttribute('administrator_id', null);
            $loan->saveQuietly();

            return back()->with('success', __('messages.LoansApproveController.successRemovedAgent'));
        } catch (\Exception $e) {
            \Log::debug($e);

            return back()->with('fail', $e->getMessage());
        }

        return back()->with('fail', __('messages.error'));
    }

}
