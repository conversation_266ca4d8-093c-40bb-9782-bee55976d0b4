<?php

namespace Modules\Head\Forms;

use <PERSON>\LaravelFormBuilder\Form;

class ClientCommunicationSettingsForm extends Form
{
    /**
     * @return void
     */
    public function buildForm(): void
    {
        $this->add('clientId', 'hidden', [
            'value' => $this->getData('clientId')
        ]);

        $mappedNSettings = $this->getData('mappedNotificationSettings', []);
        $isChecked = function ($key) use ($mappedNSettings) {
            $key = explode('.', $key);
            $type = $key[0];
            $channel = $key[1];
            if (isset($mappedNSettings[$type][$channel]['value'])) {
                return (int)$mappedNSettings[$type][$channel]['value'];
            }

            return 0;
        };

        //// system checkboxes
        $this
            ->add('system[call]', 'checkbox', [
                'label' => __('head::clientCard.call'),
                'checked' => $isChecked('system.call'),
                'attr' => []
            ])
            ->add('system[sms]', 'checkbox', [
                'label' => __('head::clientCard.sms'),
                'checked' => $isChecked('system.sms'),
                'attr' => []
            ])
            ->add('system[email]', 'checkbox', [
                'label' => __('head::clientCard.email'),
                'checked' => $isChecked('system.email'),
                'attr' => []
            ]);

//        //// information checkboxes
//        $this
//            ->add('information[call]', 'checkbox', [
//                'label' => __('head::clientCard.call'),
//                'checked' => $isChecked('information.call'),
//                'attr' => []
//            ])
//            ->add('information[sms]', 'checkbox', [
//                'label' => __('head::clientCard.sms'),
//                'checked' => $isChecked('information.sms'),
//                'attr' => []
//            ])
//            ->add('information[email]', 'checkbox', [
//                'label' => __('head::clientCard.email'),
//                'checked' => $isChecked('information.email'),
//                'attr' => []
//            ]);

        //// marketing checkboxes
        $this
            ->add('marketing[call]', 'checkbox', [
                'label' => __('head::clientCard.call'),
                'checked' => $isChecked('marketing.call'),
                'attr' => []
            ])
            ->add('marketing[sms]', 'checkbox', [
                'label' => __('head::clientCard.sms'),
                'checked' => $isChecked('marketing.sms'),
                'attr' => []
            ])
            ->add('marketing[email]', 'checkbox', [
                'label' => __('head::clientCard.email'),
                'checked' => $isChecked('marketing.email'),
                'attr' => []
            ]);

//        /// sales checkboxes
//        $this
//            ->add('sales[call]', 'checkbox', [
//                'label' => __('head::clientCard.call'),
//                'checked' => $isChecked('sales.call'),
//                'attr' => []
//            ])
//            ->add('sales[sms]', 'checkbox', [
//                'label' => __('head::clientCard.sms'),
//                'checked' => $isChecked('sales.sms'),
//                'attr' => []
//            ])
//            ->add('sales[email]', 'checkbox', [
//                'label' => __('head::clientCard.email'),
//                'checked' => $isChecked('sales.email'),
//                'attr' => []
//            ])
//            ->add('sales[mail]', 'checkbox', [
//                'label' => __('head::clientCard.mail'),
//                'checked' => $isChecked('sales.mail'),
//                'attr' => []
//            ]);
//
//        /// approve checkboxes
//        $this
//            ->add('approve[call]', 'checkbox', [
//                'label' => __('head::clientCard.call'),
//                'checked' => $isChecked('approve.call'),
//                'attr' => []
//            ])
//            ->add('approve[sms]', 'checkbox', [
//                'label' => __('head::clientCard.sms'),
//                'checked' => $isChecked('approve.sms'),
//                'attr' => []
//            ])
//            ->add('approve[email]', 'checkbox', [
//                'label' => __('head::clientCard.email'),
//                'checked' => $isChecked('approve.email'),
//                'attr' => []
//            ])
//            ->add('approve[mail]', 'checkbox', [
//                'label' => __('head::clientCard.mail'),
//                'checked' => $isChecked('approve.mail'),
//                'attr' => []
//            ]);
//
//        /// collect checkboxes
//        $this
//            ->add('collect[call]', 'checkbox', [
//                'label' => __('head::clientCard.call'),
//                'checked' => $isChecked('collect.call'),
//                'attr' => []
//            ])
//            ->add('collect[sms]', 'checkbox', [
//                'label' => __('head::clientCard.sms'),
//                'checked' => $isChecked('collect.sms'),
//                'attr' => []
//            ])
//            ->add('collect[email]', 'checkbox', [
//                'label' => __('head::clientCard.email'),
//                'checked' => $isChecked('collect.email'),
//                'attr' => []
//            ])
//            ->add('collect[mail]', 'checkbox', [
//                'label' => __('head::clientCard.mail'),
//                'checked' => $isChecked('collect.mail'),
//                'attr' => []
//            ]);
    }

    public function getFieldsByGroup($fieldType): array
    {
        $fields = [];
        foreach ($this->getFields() as $field) {
            if (strpos($field->getName(), $fieldType) !== false) {
                $fields[] = $field;
            }
        }

        return $fields;
    }
}
