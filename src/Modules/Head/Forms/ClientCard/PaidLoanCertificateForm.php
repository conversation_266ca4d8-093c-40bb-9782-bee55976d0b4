<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;
use <PERSON><PERSON><PERSON>\CashDesk\Enums\CashOperationalTransactionTypeEnum;

class PaidLoanCertificateForm extends Form
{
    public function buildForm(): void
    {

        $this->add('loan_id', 'hidden', [
            'value' => $this->getData('loan_id')
        ]);

        $this->add('payment_method_id', 'hidden', [
            'value' => $this->getData('payment_method_id'),
            'attr' => [
                'id' => 'payment_method_id',
            ]
        ]);
        $this->add('transaction_type', 'hidden', [
            'value' => CashOperationalTransactionTypeEnum::SERVICE_FEE,
        ]);

        $this->add('bank_account_id', 'select', [
            'label' => __('payments::payments.PaymentMethod'),
            'choices' => [],
            'attr' => [
                'required' => 'required'
            ]
        ]);

        $offices = getAdmin()->offices->pluck('name', 'office_id')->toArray();
        $officeIdOptions['label'] = __('head::clientCard.cashDesk');
        $officeIdOptions['selected'] = $this->request->get('office_id', $this->getData('office_id'));
        $officeIdOptions['choices'] = $offices;
        $officeIdOptions['attr'] = ['required' => 'required',];
        if (count($offices) > 1) {
            $officeIdOptions['empty_value'] = __('table.SelectOption');
            $officeIdOptions['attr'] = [
                'data-live-search' => 'true',
                'required' => 'required',
            ];
        }
        $this->add('office_id', 'select', $officeIdOptions);


        $this->add('amount', 'text', [
            'label' => __('head::clientCard.amount'),
            'attr' => [
                'required' => 'required',
                'data-amount-formatter' => 'true'
            ]
        ])
            ->add('document_number', 'text', [
                'label' => __('head::clientCard.documentNum')
            ])
            ->add('basis', 'textarea', [
                'label' => __('table.Basis'),
                'value' => __('table.taxFee'),
                'attr' => [
                    'rows' => 4,
                ]
            ]);

        if ($this->getData('client_id', false)) {
            $this->add('client_id', 'hidden', [
                'value' => $this->getData('client_id')
            ]);
        }
    }
}
