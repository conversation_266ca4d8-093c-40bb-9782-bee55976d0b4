<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Models\BlockReason;
use Modules\Head\Repositories\BlockReasonRepository;

class BlockClientForm extends Form
{
    public function __construct(
        private readonly BlockReasonRepository $blockReasonRepo
    ) {
    }

    public function buildForm(): void
    {
        $blockReasons = $this->blockReasonRepo->getIdNameArray();
        $otherReason = BlockReason::where('name', 'Друго')->first();
        if (isset($blockReasons[$otherReason?->getKey()])) {
            unset($blockReasons[$otherReason?->getKey()]);
            $blockReasons[$otherReason?->getKey()] = $otherReason?->name;
        }


        $this->add('block_reason_id', 'select', [
            'label' => __('table.Reason'),
            'choices' => $blockReasons,
            'empty_value' => __('table.SelectOption'),
            'attr' => [
                'required' => 'required'
            ]
        ]);

        $this->add('comment', 'textarea', [
            'label' => __('table.Comment'),
            'attr' => [
                'rows' => 4,
                'required' => 'required',
                'minlength' => 5,
            ]
        ]);

        if ($this->getData('client_id', false)) {
            $this->add('client_id', 'hidden', [
                'value' => $this->getData('client_id')
            ]);
        }
    }
}
