<?php

namespace Modules\Head\Forms;

use <PERSON>\LaravelFormBuilder\Field;
use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Models\BlockReason;
use Modules\Head\Repositories\BlockReasonRepository;

class BlockClientForm extends Form
{
    public function __construct(
        private readonly BlockReasonRepository $blockReasonRepo
    ) {
    }

    public function buildForm(): void
    {
        $blockReasons = $this->blockReasonRepo->getIdNameArray();
        $otherReason = BlockReason::where('name', 'Друго')->first();
        if (isset($blockReasons[$otherReason?->getKey()])) {
            unset($blockReasons[$otherReason?->getKey()]);
            $blockReasons[$otherReason?->getKey()] = $otherReason?->name;
        }


        $this->add('block_reason_id', 'select', [
            'label' => __('table.Reason'),
            'choices' => $blockReasons,
            'empty_value' => __('table.SelectOption'),
            'attr' => [
                'required' => 'required'
            ]
        ]);

        $this
            ->add('client_id', Field::SELECT, [
                'label' => __('table.Client'),
                'attr' => [
                    'data-find-client' => 'true',
                    'data-req-route' => route('head.clients.search', ['withActiveLoans' => false]),
                    'id' => 'clientId',
                ]
            ])
            ->add('to_date', Field::TEXT, [
                'label' => __('table.blockToDate'),
                'attr' => [
                    'data-date-picker-from-today-default-empty' => 'true'
                ]
            ])
            ->add('comment', Field::TEXTAREA, [
                'label' => __('table.Comment'),
                'attr' => [
                    'rows' => 5
                ]
            ]);
    }
}
