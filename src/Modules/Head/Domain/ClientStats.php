<?php

namespace Modules\Head\Domain;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientActualStats as DbModel;
use Modules\Common\Models\Loan;
use Modules\Head\Repositories\ClientActualStatsRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Payments\Repositories\PaymentRepository;

class ClientStats
{
    private $loan = null;
    private $loanActualStats = null;

    private Client $client;
    private DbModel $dbModel;

    /*********************************CONSTRUCT AND BUILD ****************************/
    public function __construct(
        private readonly ClientActualStatsRepository $repo,
        private readonly PaymentRepository $payRepo,
        private readonly LoanRepository $loanRepo
    ) {}

    public function buildFromLoan(Loan $loan): self
    {
        $this->loan = $loan;
        $this->loanStats = $loan->loanActualStats;

        $this->client = $loan->client;
        $this->dbModel = $this->client->clientActualStats ?? new DbModel();
        $this->dbModel->client_id = $this->client->getKey();

        return $this;
    }

    public function buildFromClient(Client $client): self
    {
        $this->client = $client;
        $this->dbModel = $client->clientActualStats ?? new DbModel();
        $this->dbModel->client_id = $this->client->getKey();

        return $this;
    }

    /*******************************SETTERS****************************************/

    public function setDate(): self
    {
        $this->dbModel->date = Carbon::today()->format('Y-m-d');

        return $this;
    }

    // counts
    public function setApplicationsCount(bool $recalc = false): self
    {
        if ($recalc) {
            $this->dbModel->applications_count = $this->loanRepo->getClientLoansCount($this->client->client_id);
            return $this;
        }

        // set default at begin if never exists
        if (empty($this->dbModel->applications_count)) {
            $this->dbModel->applications_count = 0;
        }

        $this->dbModel->applications_count = is_null($this->dbModel->applications_count)
            ? 0
            : (int) $this->dbModel->applications_count + 1;

        return $this;
    }

    public function setApprovedLoansCount(bool $recalc = false): self
    {
        if ($recalc) {
            $this->dbModel->approved_loans_count = $this->loanRepo->getClientApprovedLoansCount($this->client->client_id);

            return $this;
        }

        // set default at begin if never exists
        if (empty($this->dbModel->approved_loans_count)) {
            $this->dbModel->approved_loans_count = 0;
        }

        $this->dbModel->approved_loans_count = is_null($this->dbModel->approved_loans_count)
            ? 0
            : (int) $this->dbModel->approved_loans_count + 1;

        return $this;
    }

    public function setRepaidLoansCount(bool $recalc = false): self
    {
        if ($recalc) {
            $this->dbModel->repaid_loans_count = $this->loanRepo->getClientRepaidLoansCount($this->client->client_id);

            return $this;
        }

        // set default at begin if never exists
        if (empty($this->dbModel->repaid_loans_count)) {
            $this->dbModel->repaid_loans_count = 0;
        }

        $this->dbModel->repaid_loans_count = is_null($this->dbModel->repaid_loans_count)
            ? 0
            : (int) $this->dbModel->repaid_loans_count + 1;

        return $this;
    }

    public function setActivatedLoansCount(bool $recalc = false): self
    {
        if ($recalc) {
            $this->dbModel->activated_loans_count = $this->loanRepo->getClientActivatedLoansCount($this->client->client_id);

            return $this;
        }

        // set default at begin if never exists
        if (empty($this->dbModel->activated_loans_count)) {
            $this->dbModel->activated_loans_count = 0;
        }

        $this->dbModel->activated_loans_count = is_null($this->dbModel->activated_loans_count)
            ? 0
            : (int) $this->dbModel->activated_loans_count + 1;

        return $this;
    }

    public function setDisapprovedLoansCount(bool $recalc = false): self
    {
        if ($recalc) {
            $this->dbModel->disapproved_loans_count = $this->loanRepo->getClientCancelLoansCount($this->client->client_id);

            return $this;
        }

        // set default at begin if never exists
        if (empty($this->dbModel->disapproved_loans_count)) {
            $this->dbModel->disapproved_loans_count = 0;
        }

        $this->dbModel->disapproved_loans_count = is_null($this->dbModel->disapproved_loans_count)
            ? 0
            : (int) $this->dbModel->disapproved_loans_count + 1;

        return $this;
    }

    public function setCancelLoansCount(bool $recalc = false): self
    {
        // for now we just make it equal to disapproved count
        $this->dbModel->cancel_loans_count = $this->dbModel->disapproved_loans_count;
        // if ($recalc) {
        //     $this->dbModel->cancel_loans_count = $this->loanRepo->getClientCancelLoansCount($this->client->client_id);

        //     return $this;
        // }

        // $this->dbModel->cancel_loans_count = is_null($this->dbModel->cancel_loans_count)
        //     ? 0
        //     : (int) $this->dbModel->cancel_loans_count + 1;

        return $this;
    }



    public function setDaysWithoutLoan(): self
    {
        $this->dbModel->days_without_loan = 0;

        $loan = $this->loanRepo->getClientLastActivatedLoan($this->client->client_id);
        if (!$loan) {
            return $this;
        }

        if ($loan->isActive() || empty($loan->repaid_at)) {
            $this->dbModel->days_without_loan = -1;
            return $this;
        }

        $repaidDate = Carbon::parse($loan->repaid_at)->startOfDay();
        $this->dbModel->days_without_loan = $repaidDate->diffInDays(Carbon::today());

        return $this;
    }


    // first aims
    public function setFirstLoanCreatedAt(): self
    {
        if (!is_null($this->dbModel->first_loan_created_at)) {
            return $this;
        }

        $firstLoan = $this->loanRepo->getClientFirstLoan($this->client->client_id);
        if (!empty($firstLoan->created_at)) {
            $this->dbModel->first_loan_created_at = Carbon::parse($firstLoan->created_at)->format('Y-m-d');
        }

        return $this;
    }

    public function setFirstLoanActivatedAt(?Carbon $firstLoanActivatedAt = null): self
    {
        if (!is_null($this->dbModel->first_loan_activated_at)) {
            return $this;
        }

        $firstLoan = $this->loanRepo->getClientFirstActivatedLoan($this->client->client_id);
        if (!empty($firstLoan->activated_at)) {
            $this->dbModel->first_loan_activated_at = Carbon::parse($firstLoan->activated_at)->format('Y-m-d');
        }

        return $this;
    }

    public function setFirstLoanRepaidAt(?Carbon $firstLoanRepaidAt = null): self
    {
        if (!is_null($this->dbModel->first_loan_repaid_at)) {
            return $this;
        }

        $firstLoan = $this->loanRepo->getClientFirstRepaidLoan($this->client->client_id);
        if (!empty($firstLoan->repaid_at)) {
            $this->dbModel->first_loan_repaid_at = Carbon::parse($firstLoan->repaid_at)->format('Y-m-d');
        }

        return $this;
    }



    // overdue
    public function setCurrentOverdueDays(?int $overdueDays = null): self
    {
        if (!is_null($overdueDays)) {
            $this->dbModel->current_overdue_days = $overdueDays;
            return $this;
        }

        $this->dbModel->current_overdue_days = $this->loanRepo->getClientMaxFromActiveLoansCurrentOverdueDays($this->client->client_id);
        return $this;
    }

    public function setCurrentOverdueAmount(?float $overdueAmount = null): self
    {
        if (!is_null($overdueAmount)) {
            $this->dbModel->current_overdue_amount = $overdueAmount;
            return $this;
        }

        $this->dbModel->current_overdue_amount = $this->loanRepo->getClientTotalOverdueAmountOfActiveLoans($this->client->client_id);
        return $this;
    }

    public function setMaxOverdueDays(?int $maxOverdueDays = null): self
    {
        if (!is_null($maxOverdueDays)) {
            $this->dbModel->max_overdue_days = $maxOverdueDays;
            return $this;
        }

        if ($this->dbModel->current_overdue_days > $this->dbModel->max_overdue_days) {
            $this->dbModel->max_overdue_days = $this->dbModel->current_overdue_days;
            $this->dbModel->max_overdue_date = Carbon::now()->format('Y-m-d');
        }

        return $this;
    }

    public function setMaxOverdueAmount(?int $maxOverdueAmount = null): self
    {
        if (!is_null($maxOverdueAmount)) {
            $this->dbModel->max_overdue_amount = $maxOverdueAmount;
            return $this;
        }

        if ($this->dbModel->current_overdue_amount > $this->dbModel->max_overdue_amount) {
            $this->dbModel->max_overdue_amount = $this->dbModel->current_overdue_amount;
            $this->dbModel->max_overdue_date = Carbon::now()->format('Y-m-d');
        }

        return $this;
    }


    // lifetime
    public function setLifetimeValues(): self
    {
        $values = $this->loanRepo->getLifetimeValues($this->client->client_id);
        foreach ($values as $key => $val) {
            $this->dbModel->$key = $val;
        }

        return $this;
    }



    // bullshits

    public function setActive(?int $active = null): self
    {
        $this->dbModel->active = is_null($active) ? 1 : $active;
        return $this;
    }

    public function setDeleted(?int $deleted = null): self
    {
        $this->dbModel->deleted = is_null($deleted) ? 0 : $deleted;
        return $this;
    }

    public function setCreatedAt(?int $value = null): self
    {
        return $this;
    }

    public function setCreatedBy(?int $value = null): self
    {
        return $this;
    }

    public function setUpdatedAt(?int $value = null): self
    {
        return $this;
    }

    public function setUpdatedBy(?int $value = null): self
    {
        return $this;
    }

    public function setDeletedAt(?int $value = null): self
    {
        return $this;
    }

    public function setDeletedBy(?int $value = null): self
    {
        return $this;
    }

    public function setEnabledAt(?int $value = null): self
    {
        return $this;
    }

    public function setEnabledBy(?int $value = null): self
    {
        return $this;
    }

    public function setDisabledAt(?int $value = null): self
    {
        return $this;
    }

    public function setDisabledBy(?int $value = null): self
    {
        return $this;
    }

    public function setCreditLimits(): self
    {
        if (empty($this->loanStats->credit_limit)) {
            return $this;
        }

        if ($this->dbModel->credit_limit != $this->loanStats->credit_limit) {

            $key = 'prev_cr.limit_' . time();
            $val = [
                'credit_limit' => intval($this->dbModel->credit_limit),
                'credit_limit_updated_at' => (
                    !empty($this->dbModel->credit_limit_updated_at)
                    ? Carbon::parse($this->dbModel->credit_limit_updated_at)->format('Y-m-d H:i:s')
                    : ''
                ),
            ];
            $this->client->addMeta($key, json_encode($val));


            $this->dbModel->credit_limit = $this->loanStats->credit_limit;
            $this->dbModel->credit_limit_updated_at = (Carbon::now())->format('Y-m-d H:i:s');
        }

        return $this;
    }

    public function setCreditLimitsBasedOnHistory(): self
    {

        $row = DB::selectOne("
            select l.loan_id, l.created_at, las.credit_limit
            from loan l
            join loan_actual_stats las on las.loan_id = l.loan_id
            where
                l.client_id = " . $this->dbModel->client_id . "
                and l.loan_status_id in (6,7)
            order by l.created_at desc
            limit 1
        ");

        if (!empty($row->credit_limit)) {
            $this->dbModel->credit_limit = $row->credit_limit;
            $this->dbModel->credit_limit_updated_at = $row->created_at;
        }

        return $this;
    }

    /*****************************DOMAIN METHODS********************/
    public function save(): DbModel
    {
        return $this->repo->save($this->dbModel);
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

}
