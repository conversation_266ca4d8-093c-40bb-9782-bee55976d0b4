<?php

namespace Modules\Head\Services;

use Modules\Head\Repositories\ClientIdCardRepository;
use RuntimeException;
use Throwable;

class ClientIdCardService
{
    private ClientIdCardRepository $clientIdCardRepository;

    public function __construct(ClientIdCardRepository $clientIdCardRepository)
    {
        $this->clientIdCardRepository = $clientIdCardRepository;
    }

    public function getAllIdCardIssuedTypes()
    {
        try {
            return $this->clientIdCardRepository->getAllIdCardIssuedTypes();
        } catch (Throwable $t) {
            throw new RuntimeException($t->getMessage());
        }
    }
}
