<?php

namespace Modules\Head\Services;

use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\GuarantTypeRepository;

class GuarantTypeService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    private GuarantTypeRepository $guarantTypeRepository;

    public function __construct(
        GuarantTypeRepository $guarantTypeRepository
    ) {
        $this->guarantTypeRepository = $guarantTypeRepository;
        parent::__construct();
    }

    public function getAllGuarantTypes()
    {
        return \Cache::remember('guarant_types', self::CACHE_TTL, function () {
            return $this->guarantTypeRepository->getAllGuarantsType();
        });
    }
}
