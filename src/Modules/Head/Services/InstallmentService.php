<?php

namespace Modules\Head\Services;

use Modules\Admin\Traits\AdminTrait;
use Modules\Common\Models\Installment;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\InstallmentRepository;
use RuntimeException;

class InstallmentService extends BaseService
{
    use AdminTrait;

    public function __construct(
        private readonly InstallmentRepository $installmentRepository
    ) {
        parent::__construct();
    }

    public function getInstallmentById(int $installmentId): mixed
    {
        $installment = $this->installmentRepository->getById($installmentId);

        if (!$installment) {
            throw new RuntimeException(__('head::installment.installmentNotFound'));
        }

        return $installment;
    }

    public function save(Installment $installment): Installment
    {
        $installment->save();

        return $installment;
    }
}
