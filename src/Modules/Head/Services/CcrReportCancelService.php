<?php

namespace Modules\Head\Services;

use Illuminate\Support\Facades\DB;
use Modules\Common\Models\CcrReportOut;

class CcrReportCancelService
{
    // used for rebuilding report, and if loans there were already closed we need to unclose them temporary
    private array $closedToBeTmpUnclosed = [];

    // manual entering additional loan ids to cred report
    private array $toBeAddedToCredReport = [];

    // when cucr has errors related to overdue, we need to make cred with corrections
    private array $loanIdsForCred = [];
    private array $loanIdsForCucr = [];

    // Corrections:
    private array $customCRED = [
    ]; // НЕДОСТАТЪЧЕН ПЕРИОД НА ПРОСРОЧИЕ (CUCR_EXP_NOM) = 3112
    private array $badPeriod = [
    ]; // KРЕДИТ С ИЗТЕКЪЛ СРОК БЕЗ ПРОСРОЧЕНИ СУМИ = 3113
    private array $skipFromNewCred = [
    ];
    private array $toBeAdded = [
    ]; // ЛИПСВА ЗАПИС ЗА МЕСЕЧНО СЪСТОЯНИЕ ЗА АКТИВЕН КРЕДИТ = 3124
    private array $toBeSkippedCanceled = [
    ]; // ПОДАВА СЕ МЕСЕЧНО СЪСТОЯНИЕ ПРЕДИ ПОЯВАТЕ МУ (ВАЖНО! само отказаните при нас!) = 3111
    private array $skipHunterIds = [
    ]; // КРЕДИТЪТ НЕ Е НАМЕРЕН В РЕГИСТЪРА = 3104
    private array $skipHunterIds2 = [
    ]; // КРЕДИТЪТ Е ЗАКРИТ В РЕГИСТЪРА = 3105


    public function shouldRunCustomCredReport(): bool
    {
        return !empty($this->loanIdsForCred);
    }

    public function getLoanIdsForCustomCredReport(): array
    {
        return $this->loanIdsForCred;
    }

    public function getLoanIdsForCustomCucrReport(): array
    {
        return $this->loanIdsForCucr;
    }

    public function hasTmpOpenLoans(): bool
    {
        return !empty($this->closedToBeTmpUnclosed);
    }

    public function closeTmpOpenLoanIds(): void
    {
        $data = [];
        foreach ($this->closedToBeTmpUnclosed as $loanId => $finishedAt) {
            $data[] = [
                'loan_id' => $loanId,
                'ccr_finished' => 1,
                'ccr_finished_at' => $finishedAt,
            ];
        }

        DB::table('loan')->upsert(
            $data,
            ['loan_id'], // unique key
            ['ccr_finished', 'ccr_finished_at'] // fields to update
        );
    }

    public function cancelById(?int $reportId = null, bool $skipFixes = false): bool
    {
        // bad id
        if (empty($reportId)) {
            return false;
        }

        // not existing id
        $report = CcrReportOut::where('report_id', $reportId)->first();
        if (empty($report->report_id)) {
            return false;
        }

        // already re-used
        if ($report->deleted == 1) {
            return false;
        }

        return $this->cancelByReport($report, $skipFixes);
    }

    public function cancelByReport(CcrReportOut $report, bool $skipFixes = false): bool
    {
        if ($report->type == 'cucr') {

            $loanIds = [];
            $this->loanIdsForCred = [];

            // Corrections:
            $customCRED = $this->customCRED;
            $badPeriod = $this->badPeriod;
            $toBeAdded = $this->toBeAdded;
            $toBeSkippedCanceled = $this->toBeSkippedCanceled;
            $skipHunterIds = $this->skipHunterIds;
            $skipHunterIds2 = $this->skipHunterIds2;

            $tmpData = json_decode($report->data, true);
            $closedIds = $tmpData['loan']['closed'] ?? [];
            $normalIds = $tmpData['loan']['cucr_normal'] ?? [];


            if (!$skipFixes) {
                if (!empty($skipHunterIds)) {
                    $closedIds = array_diff($closedIds, $skipHunterIds);
                    $normalIds = array_diff($normalIds, $skipHunterIds);
                }
                if (!empty($skipHunterIds2)) {
                    $closedIds = array_diff($closedIds, $skipHunterIds2);
                    $normalIds = array_diff($normalIds, $skipHunterIds2);
                }
            }


            // restore closed loans with prev.report
            if (!empty($closedIds)) {
                DB::statement('
                    update loan
                    set ccr_finished = 0, ccr_finished_at = null
                    where loan_id in (' . implode(',', $closedIds) . ');'
                );
            }


            if (!$skipFixes) {
                // prepare list of ids, all from prev.report
                $loanIds = array_merge($closedIds, $normalIds);
                unset($closedIds);
                unset($normalIds);

                // it could be a case that we need to add loan to report but it close in db so we need to restore it
                if (!empty($toBeAdded)) {
                    $loanIds = array_merge($loanIds, $toBeAdded);

                    DB::statement('
                        update loan
                        set ccr_finished = 0, ccr_finished_at = null
                        where
                            loan_id in (' . implode(',', $toBeAdded) . ')
                            and ccr_finished = 1;
                    ');
                }

                // reduce array, leave only unique
                $loanIds = array_unique($loanIds);

                // unset loan_ids that should not be in report
                if (!empty($toBeSkippedCanceled)) {
                    $loanIds = array_diff($loanIds, $toBeSkippedCanceled);

                    // mark cancelled loans to be skipped forever
                    DB::statement('
                        update loan
                        set ccr_finished=1, ccr_finished_at=NOW(), skip_ccr=1, skip_ccr_at=NOW(), skip_ccr_by=1
                        where
                            loan_id in (' . implode(',', $toBeSkippedCanceled) . ')
                            and loan_status_id = 8;
                    ');
                }

                // skip that registered in cred, but should not be in cucr
                if (!empty($this->skipFromNewCred)) {
                    $loanIds = array_diff($loanIds, $this->skipFromNewCred);
                }

                // for this bug we also need to regenrate cred
                if (!empty($badPeriod)) {
                    $customCRED = array_merge($customCRED, $badPeriod);
                }

                // run cred report for changed in cucr - pulna kasha :D
                if (!empty($customCRED)) {
                    $this->loanIdsForCred = $customCRED;
                }
            }

            $this->loanIdsForCucr = $loanIds;

            $this->markReportAsDeleted($report);

            return true;
        }

        if ($report->type == 'cred') {

            $this->closedToBeTmpUnclosed = [];

            $tmpData = json_decode($report->data, true);
            $newLoans = $tmpData['loan']['first_time'] ?? [];
            $existingLoans = $tmpData['loan']['after_change'] ?? [];

            //////////////////// Default logic: ////////////////////

            $loanIds = [];
            if (!empty($newLoans)) {
                $loanIds = array_merge($loanIds, $newLoans);

                DB::statement('
                    update loan
                    set registered_in_ccr = 0, registered_in_ccr_at = null, need_ccr_sync = 1, set_need_ccr_sync_at = NOW()
                    where loan_id in (' . implode(',', $newLoans) . ');
                ');
            }
            if (!empty($existingLoans)) {
                $loanIds = array_merge($loanIds, $existingLoans);

                DB::statement('
                    update loan
                    set registered_in_ccr = 1, need_ccr_sync = 1, set_need_ccr_sync_at = NOW()
                    where loan_id in (' . implode(',', $existingLoans) . ');
                ');
            }

            //////////////////// Fixes: ////////////////////
            if (!$skipFixes) {
                // 1. when loans are closed(ccr_finished=1), but we still need to send them,
                // we need to unclose them temporary and then re-close after
                if (!empty($loanIds)) {
                    $closedLoanIds = DB::select('
                        select l.loan_id, l.ccr_finished_at
                        from loan l
                        where
                            l.loan_id in (' . implode(',', $loanIds) . ')
                            and l.ccr_finished = 1;
                    ');

                    if (count($closedLoanIds) > 0) {
                        foreach ($closedLoanIds as $closedLoanIdRow) {
                            $this->closedToBeTmpUnclosed[$closedLoanIdRow->loan_id] = $closedLoanIdRow->ccr_finished_at ?? now();
                        }
                    }

                    if (!empty($this->closedToBeTmpUnclosed)) {
                        DB::statement('
                            update loan
                            set ccr_finished = 0, ccr_finished_at = NULL
                            where loan_id in (' . implode(',', array_keys($this->closedToBeTmpUnclosed)) . ');
                        ');
                    }
                }

                // 2.(Manual) count them as already reported, but we send them as new, we need to fix them.
                if (!empty($this->toBeAddedToCredReport)) {
                    DB::statement('
                        update loan
                        set registered_in_ccr = 1, registered_in_ccr_at = created_at, need_ccr_sync = 1, set_need_ccr_sync_at = NOW()
                        where loan_id in (' . implode(',', $this->toBeAddedToCredReport) . ');
                    ');
                }
            }

            $this->markReportAsDeleted($report);

            return true;
        }

        if ($report->type == 'borr') {

            $tmpData = json_decode($report->data, true);
            $newGurarants = $tmpData['guarant']['first_time'] ?? [];
            $newClients = $tmpData['client']['first_time'] ?? [];
            $existingClients = $tmpData['client']['after_change'] ?? [];

            if (!empty($newGurarants)) {
                DB::statement('
                    update guarant
                    set registered_in_ccr = 0, registered_in_ccr_at = null
                    where guarant_id in (' . implode(',', $newGurarants) . ');
                ');
            }
            if (!empty($newClients)) {
                DB::statement('
                    update client
                    set registered_in_ccr = 0, registered_in_ccr_at = null, need_ccr_sync = 1, set_need_ccr_sync_at = NOW()
                    where client_id in (' . implode(',', $newClients) . ');
                ');
            }
            if (!empty($existingClients)) {
                DB::statement('
                    update client
                    set registered_in_ccr = 1, need_ccr_sync = 1, set_need_ccr_sync_at = NOW()
                    where client_id in (' . implode(',', $existingClients) . ');
                ');
            }

            $this->markReportAsDeleted($report);

            return true;
        }

        return false;
    }

    private function markReportAsDeleted(CcrReportOut $report): void
    {
        $report->deleted_by = getAdminId();
        $report->deleted_at = now();
        $report->deleted = 1;
        $report->active = 0;
        $report->saveQuietly();
    }
}
