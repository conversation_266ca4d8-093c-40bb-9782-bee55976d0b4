<?php

namespace Modules\Head\Services;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\NotificationSetting;
use Modules\Common\Services\BaseService;
use Modules\Common\Traits\DateBuilderTrait;
use Modules\Common\Traits\PinTrait;
use Modules\Common\Traits\ValidationTrait;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Head\Repositories\ClientBlockHistoryRepository;
use Modules\Head\Repositories\ClientDeleteHistoryRepository;
use Modules\Head\Repositories\ClientNameRepository;
use Modules\Head\Repositories\ClientPhoneRepository;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\ClientUnblockHistoryRepository;
use Modules\Head\Repositories\GuarantRepository;
use Modules\Head\Repositories\LoanRepository;
use RuntimeException;

final class ClientService extends BaseService
{
    use ValidationTrait;
    use PinTrait;

    public const CLIENT_BLOCK_NAMES_TYPE = 'clientNames';
    public const CLIENT_BLOCK_PIN_TYPE = 'clientPin';
    public const CLIENT_BLOCK_ID_CARD_NUMBER_TYPE = 'clientIdCardNumber';
    public const CLIENT_BLOCK_PHONE_NUMBER_TYPE = 'clientPhoneNumber';
    public const CLIENT_BLOCK_ADDRESS_TYPE = 'clientAddress';
    public const CLIENT_BLOCK_DEFAULT_TYPE = 'clientDefault';

    private $clientRepository = null;
    private $clientDeleteHistoryRepository = null;
    private $clientBlockHistoryRepository = null;
    private $clientUnblockHistoryRepository = null;
    private $clientAddressRepository = null;
    private $clientNameRepository = null;
    private $clientPhoneRepository = null;
    private $loanRepository = null;
    private $guarantRepository = null;

    public function hasActiveOrRepaidLoanForPeriod(int $clientId, int $subDays): bool
    {
        // check for active till current moment
        $hasActive = Loan::where('client_id', $clientId)
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->exists();
        if ($hasActive) {
            return true;
        }

        // check for repaid/written-off during last 180d
        $fromDate = now()->subDays($subDays);
        return Loan::where('client_id', $clientId)
            ->whereIn('loan_status_id', [LoanStatus::WRITTEN_OF_STATUS_ID, LoanStatus::REPAID_STATUS_ID])
            ->where('last_status_update_date', '>=', $fromDate)
            ->exists();
    }


    public function getClientsWithDiscount(
        ?int $limit,
        array $data
    ): LengthAwarePaginator {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        if (!empty($data['limit'])) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        if (!empty($data['client_id'])) {
            $where = []; // skip all filters if client id passed
            $data = ['client_id' => $data['client_id']]; // leave only that element
        } else {
            $where = [];
            if (!empty($data['percentFrom'])) {
                $where[] = [
                    'client_discount_actual.percent',
                    '>=',
                    $data['percentFrom'],
                ];
                unset($data['percentFrom']);
            }

            if (!empty($data['percentTo'])) {
                $where[] = [
                    'client_discount_actual.percent',
                    '<=',
                    $data['percentTo'],
                ];
                unset($data['percentTo']);
            }

            if (!empty($data['createdAt'])) {
                if (preg_match(self::$dateRangeRegex, $data['createdAt'])) {
                    $extractedDates = $this->extractDates($data['createdAt']);
                    $where[] = [
                        'client_discount_actual.created_at',
                        '>=',
                        $extractedDates['from'],
                    ];
                    $where[] = [
                        'client_discount_actual.created_at',
                        '<=',
                        $extractedDates['to'],
                    ];
                } elseif (preg_match(self::$dateRegex, $data['createdAt'])) {
                    $where[] = [
                        'client_discount_actual.created_at',
                        '>=',
                        $this->fmt($data['createdAt'], DateBuilderTrait::$formatDateRangeBegin),
                    ];
                    $where[] = [
                        'client_discount_actual.created_at',
                        '<=',
                        $this->fmt($data['createdAt'], DateBuilderTrait::$formatDateRangeEnd),
                    ];
                }
                unset($data['createdAt']);
            }

            if (!empty($data['valid']['from'])) {
                $where[] = [
                    'client_discount_actual.valid_from',
                    '>=',
                    dbDate($data['valid']['from'], '00:00:00'),
                ];
            }
            if (!empty($data['valid']['till'])) {
                $where[] = [
                    'client_discount_actual.valid_till',
                    '<=',
                    dbDate($data['valid']['till'], '23:59:59'),
                ];
            }
            if (isset($data['valid'])) {
                unset($data['valid']);
            }

            if (!empty($data['administrator_id'])) {
                $where[] = [
                    'client_discount_actual.created_by',
                    '=',
                    $data['administrator_id'],
                ];
                unset($data['administrator_id']);
            }

            if (!empty($data['product_id'])) {
                $where[] = [
                    'client_discount_actual.product_id',
                    '=',
                    $data['product_id'],
                ];
                unset($data['product_id']);
            }
        }

        // mandatory condition, to show only active discounts
        $where[] = ['client_discount_actual.active', '=', '1'];
        $where[] = ['client_discount_actual.deleted', '=', '0'];

        $whereClientFields = $this->getWhereConditions(
            $data,
            ['client.first_name', 'client.middle_name', 'client.last_name'],
            'client'
        );
        $where = array_merge($where, $whereClientFields);

        return $this->getClientRepository()->getClientsWithDiscount(
            $where,
            $limit,
            $order
        );
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(?int $limit, array $data)
    {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        if (!empty($data['limit'])) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions(
            $data,
            ['first_name', 'middle_name', 'last_name'],
            'client'
        );

        return $this->getClientRepository()->getAll(
            $limit,
            $joins,
            $whereConditions,
            $order
        );
    }

    protected function getWhereConditions(
        array $data,
        array $names = ['name'],
        string $prefix = ''
    ) {
        $where = [];

        return array_merge($where, parent::getWhereConditions($data, $names, $prefix));
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        $joins = [];
        if (!empty($data['block_reason_id'])) {
            $joins['join'][] = [
                'client_block_history',
                'client_block_history.client_id',
                '=',
                'client.client_id',
            ];
        }

        if (!empty($data['delete_reason_id'])) {
            $joins['join'][] = [
                'client_delete_history',
                'client_delete_history.client_id',
                '=',
                'client.client_id',
            ];
        }

        return $joins;
    }

    /**
     * @param int $id
     *
     * @return Client
     * @throws NotFoundException
     */
    public function getClientById(int $id, bool $withTrashed = false): Client
    {
        $client = $this->getClientRepository()->getById($id, $withTrashed);
        if (!$client) {
            throw new RuntimeException(__('head::clientCrud.clientNotFound'));
        }

        return $client;
    }

    public function delete(Client $client, array $data): string
    {
        try {
            $this->getClientDeleteHistoryRepository()
                ->addClientDeleteHistory($client->getKey(), $data);

            $this->getClientRepository()->delete($client);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('head::clientCrud.clientDeletionFailed'),
                $e
            );
        }

        return __('head::clientCrud.clientDeletedSuccessfully');
    }

    public function block(Client $client, array $data)
    {
        try {
            $this->getClientBlockHistoryRepository()
                ->addClientBlockHistory($client->getKey(), $data);

            $this->getClientRepository()->block($client);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('head::clientCrud.blockedFailed'),
                $e
            );
        }

        return __('head::clientCrud.blockedSuccessfully');
    }

    /**
     * @param Client $client
     * @param array $data
     *
     * @return string|null
     */
    public function unblock(Client $client, array $data)
    {
        try {
            $this->getClientBlockHistoryRepository()
                ->flushBlockHistory($client->getKey());

            $this->getClientUnblockHistoryRepository()
                ->addClientUnblockHistory($client->getKey(), $data);

            $this->getClientRepository()->unblock($client);
        } catch (\Exception $e) {
            throw new RuntimeException(
                __('head::clientCrud.unblockedFailed'),
                $e
            );
        }

        return __('head::clientCrud.unblockedSuccessfully');
    }

    public function getClientsBySearchString(string $value, bool $checkActiveLoans = true): array
    {
        return $this->getClientRepository()->searchClientsByString($value, $checkActiveLoans);
    }

    public function getClientsWithLastActiveLoanBySearchString(string $value)
    {
        return $this
            ->getClientRepository()
            ->getClientsWithLastActiveLoanBySearchString($value);
    }

    public function getClientsBySearchStringLite(string $value): array
    {
        return $this->getClientRepository()->searchClientsByStringLite($value);
    }

    public function searchClientsByKey(string $key, string $query)
    {
        return $this->getClientRepository()->searchClientsByKey($key, $query);
    }

    /**
     * Used in observer
     *
     * @param Client $client
     */
    public function updateClientName(Client $client)
    {
        $this->getClientNameRepository()->updateClientName($client);
    }

    /**
     * Used in observer
     *
     * @param Client $client
     */
    public function updateClientPhone(Client $client)
    {
        $this->getClientPhoneRepository()->updateClientPhone($client);
    }

    /**
     * Return last client product from inpup list of product ids.
     *
     * Example:
     * Means we send id of products ['1', '2', '3', '4'];
     * Our client had 2 loans:
     * - 1st loan with product - 4
     * - 2nd loan with product - 3
     * so the last client loan from the list of loans is "3"
     *
     * @param int $clientId
     * @param array $productIds
     *
     * @return int|null
     */
    public function getLastClientProductFromProducts(
        int $clientId,
        array $productIds
    ): ?int {
        return $this->getLoanRepository()->getLastLoanProductIdFromProducts(
            $clientId,
            $productIds
        );
    }

    public function getMaxSeqNum(int $clientId)
    {
        return $this->getClientRepository()->getMaxSeqNum($clientId);
    }

    public function getMaxSeqNumGuarant(int $clientId)
    {
        return $this->getClientRepository()->getMaxSeqNumGuarant($clientId);
    }

    public function getClientGuarantData(string $pin)
    {
        return $this->getClientRepository()->getClientGuarantData($pin);
    }

    public function deleteClientPhone(ClientPhone $clientPhone): bool
    {
        return $this->getClientPhoneRepository()->delete($clientPhone);
    }

    public function deleteClientAddress(ClientAddress $clientAddress): bool
    {
        return $this->getClientAddressRepository()->delete($clientAddress);
    }

    public function getClientLoans(Client $client, int $limit): Collection
    {
        return $this->getLoanRepository()->getClientLoans($client, $limit);
    }

    public function getActiveLoansByIds(Client $client, array $loanIds): EloquentCollection
    {
        return $this->getLoanRepository()->getActiveLoansWithRelationsByIds($client, $loanIds);
    }

    /**
     * @return array
     */
    public static function getDbClientBlockTypes(): array
    {
        return [
            'client.first_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'client_name.first_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'client.middle_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'client_name.middle_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'client.last_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'client_name.last_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'client.phone' => ClientService::CLIENT_BLOCK_PHONE_NUMBER_TYPE,
            'client_phone.number' => ClientService::CLIENT_BLOCK_PHONE_NUMBER_TYPE,
            'client.phone.1' => ClientService::CLIENT_BLOCK_PHONE_NUMBER_TYPE,
            'client.email' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'client_email.email' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'client_idcard.pin' => ClientService::CLIENT_BLOCK_PIN_TYPE,
            'client.pin' => ClientService::CLIENT_BLOCK_PIN_TYPE,
            'client.idcard_number' => ClientService::CLIENT_BLOCK_ID_CARD_NUMBER_TYPE,
            'client_idcard.idcard_number' => ClientService::CLIENT_BLOCK_ID_CARD_NUMBER_TYPE,
            'client_idcard.city_id' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_idcard.address' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_idcard.post_code' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_idcard.issue_date' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_idcard.valid_date' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_idcard.issued_by' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_idcard.sex' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'client_idcard.idcard_issued_id' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_address.city_id' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_address.post_code' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_address.address' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'client_bank_account.bank_id' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'client_bank_account.bic' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'client_bank_account.iban' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'client_bank_account.main' => ClientService::CLIENT_BLOCK_DEFAULT_TYPE,
            'guarant.pin' => ClientService::CLIENT_BLOCK_PIN_TYPE,
            'guarant.first_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'guarant.middle_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'guarant.last_name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'guarant.phone' => ClientService::CLIENT_BLOCK_PHONE_NUMBER_TYPE,
            'guarant.idcard_number' => ClientService::CLIENT_BLOCK_ID_CARD_NUMBER_TYPE,
            'guarant.idcard_issue_date' => ClientService::CLIENT_BLOCK_ID_CARD_NUMBER_TYPE,
            'guarant.idcard_valid_date' => ClientService::CLIENT_BLOCK_ID_CARD_NUMBER_TYPE,
            'guarant.address' => ClientService::CLIENT_BLOCK_ADDRESS_TYPE,
            'contact.name' => ClientService::CLIENT_BLOCK_NAMES_TYPE,
            'contact.phone' => ClientService::CLIENT_BLOCK_PHONE_NUMBER_TYPE,
        ];
    }

    public function getGuarantByPin(string $pin): ?Guarant
    {
        return $this->getGuarantRepository()->getGuarantByPin($pin);
    }

    public function simpleUpdate(
        Client $client,
        array $data
    ) {
        $updated = $this->getClientRepository()->simpleUpdate($client, $data);

        return $this->getClientById($updated->getKey());
    }

    public function updateClientNotification(
        int $clientId,
        int $notificationSettingId,
        string $value
    ): bool {
        $setting = NotificationSetting::where([
            ['notification_setting_id', '=', $notificationSettingId],
            ['client_id', '=', $clientId],
            ['active', '=', '1'],
        ])->first();


        if (empty($setting->client_id)) {
            return false;
        }

        $setting->value = ("1" == $value || "on" == $value ? 1 : 0);
        $setting->save();


        return true;
    }

    private function getClientRepository(): ClientRepository
    {
        if ($this->clientRepository == null) {
            $this->clientRepository = app(ClientRepository::class);
        }

        return $this->clientRepository;
    }

    private function getClientDeleteHistoryRepository(): ClientDeleteHistoryRepository
    {
        if ($this->clientDeleteHistoryRepository == null) {
            $this->clientDeleteHistoryRepository = app(ClientDeleteHistoryRepository::class);
        }

        return $this->clientDeleteHistoryRepository;
    }

    private function getClientBlockHistoryRepository(): ClientBlockHistoryRepository
    {
        if ($this->clientBlockHistoryRepository == null) {
            $this->clientBlockHistoryRepository = app(ClientBlockHistoryRepository::class);
        }

        return $this->clientBlockHistoryRepository;
    }

    private function getClientUnblockHistoryRepository(): ClientUnblockHistoryRepository
    {
        if ($this->clientUnblockHistoryRepository == null) {
            $this->clientUnblockHistoryRepository = app(ClientUnblockHistoryRepository::class);
        }

        return $this->clientUnblockHistoryRepository;
    }

    private function getClientAddressRepository(): ClientAddressRepository
    {
        if ($this->clientAddressRepository == null) {
            $this->clientAddressRepository = app(ClientAddressRepository::class);
        }

        return $this->clientAddressRepository;
    }

    private function getClientNameRepository(): ClientNameRepository
    {
        if ($this->clientNameRepository == null) {
            $this->clientNameRepository = app(ClientNameRepository::class);
        }

        return $this->clientNameRepository;
    }

    private function getClientPhoneRepository(): ClientPhoneRepository
    {
        if ($this->clientPhoneRepository == null) {
            $this->clientPhoneRepository = app(ClientPhoneRepository::class);
        }

        return $this->clientPhoneRepository;
    }

    private function getLoanRepository(): LoanRepository
    {
        if ($this->loanRepository == null) {
            $this->loanRepository = app(LoanRepository::class);
        }

        return $this->loanRepository;
    }

    private function getGuarantRepository(): GuarantRepository
    {
        if ($this->guarantRepository == null) {
            $this->guarantRepository = app(GuarantRepository::class);
        }

        return $this->guarantRepository;
    }

    public function markForCcrSync(Client $client): void
    {
        if ($client->new) {
            return;
        }

        $client->need_ccr_sync = 1;
        $client->set_need_ccr_sync_at = now();
        $client->saveQuietly();
    }
}
