<?php

namespace Modules\Head\Services;

use App\Exceptions\FrontEndExceptions;
use Auth;
use Carbon\Carbon;
use JsonException;
use Modules\Admin\Traits\AdminTrait;
use Modules\Collect\Domain\Entities\LoanForExtension;
use Modules\Common\Entities\ExtendLoanSnapshot;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\InstallmentActionLog;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanMeta;
use Modules\Common\Models\Tax;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\InstallmentRepository;
use Modules\Payments\Repositories\TaxRepository;

class ExtendLoanService extends BaseService
{
    use AdminTrait;

    public function __construct(
        protected PaymentPlanService           $paymentPlanService,
        private readonly LoanService           $loanService,
        private readonly InstallmentRepository $installmentRepository,
    ) {
        parent::__construct();
    }

    public function run(int $loanId, int $amount, int $days): bool
    {
        $loan = $this->loanService->getLoanById($loanId);

        if (!$loan->isExtendable()) {
            throw new FrontEndExceptions(__('payments::tax.productNotExtendable'));
        }

        if (!$loan->canExtendLoan()) {
            throw new FrontEndExceptions(__('head::clientCard.ExtendLoanExistsToday'));
        }

        $snapshot = $this->saveSnapshot($loan, $days);

        /// add extend tax + update installments due_date + update loan stats (LoanWasExtended)
        $extendFee = app(LoanForExtension::class)->buildFromExisting($loan)
            ->extend($amount, $days)
            ->getCreatedTax();

        /// set relation tax with snapshot
        $snapshot->setAttribute('tax_id', $extendFee->tax_id);
        $snapshot->saveQuietly();

        //// set loan has_custom_payment_schedule
        $loan->setAttribute('has_custom_payment_schedule', 'yes');
        $loan->saveQuietly(); /// save without fire events

        return true;
    }

    public function revert(Tax $tax): bool
    {
        $loan = $tax->loan;
        $days = $tax->extended_days;
        $oldInstallmentsCollection = $loan->installments->toArray();

        // old logic:
        // $installments = $loan->getUnpaidInstallments();
        // $isExtended = $this->installmentRepository->postponeInstallments($installments, $days, true);

        // new logic
        $isExtended = $this->installmentRepository->postponeInstallmentsBySnapshot($loan, $tax);

        if (!$isExtended) {
            return false;
        }

        $this->setLoanExtendedFlags(
            $loan,
            $days,
            $oldInstallmentsCollection,
            'loan revert extension',
            LoanMeta::LOAN_REVERT_EXTEND,
            $tax->getKey()
        );

        return true;
    }

    // save:
    // - installment_action_log
    // - loan_meta
    // update:
    // - loan.extended and loan.ccr flags
    // - loan_actual_stats. dates and extension counts
    private function setLoanExtendedFlags(
        Loan   $loan,
        int    $days,
        array  $oldInstallmentsCollection,
        string $action,
        string $metaType,
        int    $taxId
    ): void {

        if (!in_array($metaType, [LoanMeta::LOAN_REVERT_EXTEND, LoanMeta::LOAN_EXTEND_TASK], true)) {
            throw new JsonException('Invalid meta type');
        }

        $newInstallmentsCollection = $loan->installments->toArray();
        $this->log($loan, $oldInstallmentsCollection, $newInstallmentsCollection, $action);

        $loanStats = $loan->loanActualStats;
        $prev = $loanStats->repayment_date;

        //create meta data for loan extension event
        $this->loanService->addLoanExtensionData(
            $loan->getKey(),
            $loan->getFirstUnpaidInstallment()?->getKey(),
            $days,
            $metaType,
            $prev
        );

        // update loan & loan stats
        $loan->need_ccr_sync = 1;
        $loan->set_need_ccr_sync_at = Carbon::now();

        $lastInstallmentDate = $loan->installments()->orderByDesc('seq_num')->first()->due_date;
        $loanStats->prev_repayment_date = $prev;
        $loanStats->repayment_date = $lastInstallmentDate;
        $loanStats->last_installment_date = $lastInstallmentDate;

        switch ($metaType) {
            case LoanMeta::LOAN_EXTEND_TASK:
                $loan->extended = 1;

                $loanStats->days_amended += $days;
                $loanStats->loan_extension_count += 1;
                break;

            case LoanMeta::LOAN_REVERT_EXTEND:
                $isLastExtendTax = app(TaxRepository::class)->isLastExtendTax($loan->getKey(), $taxId);
                if ($isLastExtendTax) {
                    $loan->extended = 0;
                }

                $loanStats->loan_extension_count -= 1;
                $loanStats->days_amended -= $days;
                break;
        }

        $loanStats->save();
        $loan->save();
    }

    private function log(
        Loan   $loan,
        array  $oldInstallmentsCollection,
        array  $newInstallmentsCollection,
        string $action,
    ): InstallmentActionLog {

        $administratorId = Auth::check() ? Auth::user()->administrator_id : Administrator::SYSTEM_ADMINISTRATOR_ID;

        // prep log data
        $log = [
            'loan_id' => $loan->getKey(),
            'action' => $action,
            'date' => now(),
            'administrator_id' => $administratorId,
            'previous_plan' => json_encode($oldInstallmentsCollection, JSON_THROW_ON_ERROR),
            'current_plan' => json_encode($newInstallmentsCollection, JSON_THROW_ON_ERROR)
        ];

        return $this->installmentRepository->saveActionLog($log);
    }

    private function saveSnapshot(Loan $loan, int $days): ?ExtendLoanSnapshot
    {
        // snapshot
        $taxes = $loan->getAllTaxes();
        $client = $loan->client;
        $loanStats = $loan->loanActualStats;
        $clientStats = $client->clientActualStats;
        $installments = $loan->getAllInstallments();

        // set snapshot
        $snapshot = new ExtendLoanSnapshot();
        $snapshot->loan_id = $loan->loan_id;
        $snapshot->days = $days;
        $snapshot->created_at = now();
        $snapshot->created_by = getAdminId();

        $snapshot->loan = $loan->toArray();
        $snapshot->loan_stats = $loanStats->toArray();
        $snapshot->client_stats = $clientStats->toArray();

        $installmentsArray = [];
        foreach ($installments as $installment) {
            $attr = $installment->toArray();
            $attr['due_date'] = Carbon::parse($installment->due_date)->format('Y-m-d');

            $installmentsArray[$installment->installment_id] = $attr;
        }
        $snapshot->installments = $installmentsArray;

        $taxesArray = [];
        if ($taxes->count() > 0) {
            foreach ($taxes as $tax) {
                $taxesArray[$tax->tax_id] = $tax->toArray();
            }
        }
        $snapshot->taxes = $taxesArray;

        // save and go on
        $snapshot->save();

        return $snapshot;
    }
}
