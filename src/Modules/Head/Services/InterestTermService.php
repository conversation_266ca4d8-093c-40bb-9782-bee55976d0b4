<?php

namespace Modules\Head\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductHistory;
use Modules\Common\Services\StorageService;
use Modules\Head\Imports\InterestTermImport;
use Modules\Head\Repositories\InterestTermRepository;
use RuntimeException;
use Throwable;

class InterestTermService extends BaseTermService
{
    private $repo = null;

    /**
     * InterestTermService constructor.
     *
     * @param StorageService $storageService
     */
    public function __construct(
        private readonly StorageService $storageService = new StorageService()
    )
    {
        parent::__construct();
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(
        ?int  $limit,
        array $data
    )
    {
        $whereConditions = $this->getWhereConditions(
            $data,
            [],
            'product'
        );

        $this->replaceTableName(
            $whereConditions,
            'product',
            'interest_term',
            'period_from'
        );
        $this->replaceTableName(
            $whereConditions,
            'product',
            'interest_term',
            'period_to'
        );
        $this->replaceTableName(
            $whereConditions,
            'product',
            'interest_term',
            'interest_rate'
        );

        return $this->getInterestTermRepository()
            ->getAll(
                $limit,
                $whereConditions
            );
    }

    public function import(UploadedFile $file, Product $product)
    {
        try {
            // Save file and create log history
            $savedFile = $this->storageService->importTerms($file, 'interest');
            if (!$savedFile) {
                throw new RuntimeException(
                    __('head::storageService.FileIsNotSaved')
                );
            }

            // Retrieving data from a file and converting it into an array
            $data = Excel::toArray(new InterestTermImport(), $file);

            // Get the first element of the array
            $data = reset($data);
            // Remove product name row
            array_shift($data);

            // Fill in an array for massive insert
            $interestTermsData = $this->getInterestTermsDataForMassInsert(
                $data,
                $product->getKey(),
                $savedFile->getKey()
            );

            $oldInterestTerms = $product->interestTerms;

            $oldFile = null;
            if ($oldInterestTerms->isNotEmpty()) {
                $oldFile = $oldInterestTerms->first()->file;
                $this->logPreviousInterestTerms($oldInterestTerms);
            }

            $this->createProductHistory(
                $product,
                $savedFile,
                $oldFile,
                !empty($oldFile)
                    ? ProductHistory::TRANSLATION_KEY_INTEREST_TERM_CHANGED
                    : ProductHistory::TRANSLATION_KEY_INTEREST_TERM_ADDED
            );

            /// set max values to amount & period
            foreach ($interestTermsData as $key => $interestTermsDatum) {
                if (!$interestTermsDatum['period_to']) {
                    $interestTermsData[$key]['period_to'] = 10499;
                }
                if (!$interestTermsDatum['amount_to']) {
                    $interestTermsData[$key]['amount_to'] = 10000000;
                }
            }

            return $this->getInterestTermRepository()
                ->bulkCreate($interestTermsData);

        } catch (Throwable $t) {
            throw new RuntimeException(
                __('messages.errorOccurred'),
                $t
            );
        }
    }

    /**
     * @param array $data
     * @param int $productId
     * @param int $fileId
     *
     * @return array
     */
    private function getInterestTermsDataForMassInsert(
        array $data,
        int   $productId,
        int   $fileId
    ): array
    {
        // Get the periods from the file
        $allFilePeriods = array_filter($data[0]);
        // Remove the periods row
        array_shift($data);

        $newInterestTerms = [];
        $decreaseFactor = 0.01;

        for ($i = 1; $i <= count($allFilePeriods); $i++) {
            $periodFrom = $allFilePeriods[$i];
            $periodTo = isset($allFilePeriods[$i + 1]) ? $allFilePeriods[$i + 1] : null;

            for ($j = 0; $j < count($data); $j++) {
                $amountFrom = $data[$j][0];
                $amountTo = isset($data[$j + 1]) ? $data[$j + 1][0] : null;
                $investRate = $data[$j][$i];

                $newInterestTerms[] = [
                    'product_id' => $productId,
                    'period_from' => $periodFrom,
                    'period_to' => $periodTo ? $periodTo - $decreaseFactor : $periodTo,
                    'amount_from' => $amountFrom,
                    'amount_to' => $amountTo ? $amountTo - $decreaseFactor : $amountTo,
                    'interest_rate' => $investRate,
                    'created_at' => now(),
                    'created_by' => Auth::user()->administrator_id,
                    'file_id' => $fileId,
                ];
            }
        }

        return $newInterestTerms;
    }

    /**
     * @param CustomEloquentCollection $interestTerms
     */
    private function logPreviousInterestTerms(CustomEloquentCollection $interestTerms)
    {
        foreach ($interestTerms as $interestTerm) {
            // Move to history
            $isAdded = $this->getInterestTermRepository()
                ->addInterestTermHistory($interestTerm);

            if (true === $isAdded) {
                $interestTerm->delete();
            }
        }
    }

    private function getInterestTermRepository(): InterestTermRepository
    {
        if (null === $this->repo) {
            $this->repo = app(InterestTermRepository::class);
        }

        return $this->repo;
    }
}
