<?php

namespace Modules\Head\Services;

use Exception;
use Modules\Common\Enums\LogActionEnum;
use Modules\Common\Libraries\Logs\BankAccountState;
use Modules\Common\Models\BankAccount;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\BankAccountRepository;
use RuntimeException;

class BankAccountService extends BaseService
{
    protected BankAccountRepository $bankAccountRepository;

    /**
     * BankService constructor.
     *
     * @param BankAccountRepository $bankAccountRepository
     */
    public function __construct(BankAccountRepository $bankAccountRepository)
    {
        $this->bankAccountRepository = $bankAccountRepository;

        parent::__construct();
    }


    /**
     * @return mixed
     */
    public function getAll()
    {
        return $this->bankAccountRepository->getAll(null);
    }

    public function create(array $data)
    {
        try {
            $bankAccount = $this->bankAccountRepository->create($data);

            $office = new BankAccountState($bankAccount, LogActionEnum::create);
            $office->save();
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::bankCrud.bankCreationFailed'),
                $e
            );
        }
    }

    public function edit(BankAccount $bankAccount, array $data)
    {
        try {
            $state = new BankAccountState($bankAccount, LogActionEnum::update);
            $state->stateFrom();

            $this->bankAccountRepository->edit($bankAccount, $data);

            $state->save();
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::bankCrud.bankUpdateFailed'),
                $e
            );
        }
    }

    public function delete(BankAccount $bankAccount)
    {
        try {
            $this->bankAccountRepository->delete($bankAccount);

            $templateState = new BankAccountState($bankAccount, LogActionEnum::delete);
            $templateState->save();
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::bankCrud.bankDeletionFailed'),
                $e
            );
        }
    }

    public function enable(BankAccount $bankAccount)
    {
        if ($bankAccount->isActive()) {
            throw new RuntimeException(
                __('head::bankCrud.bankEnableForbidden')
            );
        }

        try {
            $this->bankAccountRepository->enable($bankAccount);

            $state = new BankAccountState($bankAccount, LogActionEnum::enable);
            $state->save();
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::bankCrud.bankEnableFailed'),
                $e
            );
        }
    }

    public function disable(BankAccount $bankAccount)
    {
        if (!$bankAccount->isActive()) {
            throw new RuntimeException(
                __('head::bankCrud.bankDisableForbidden')
            );
        }

        try {
            $this->bankAccountRepository->disable($bankAccount);

            $state = new BankAccountState($bankAccount, LogActionEnum::disable);
            $state->save();
        } catch (Exception $e) {
            throw new RuntimeException(
                __('head::bankCrud.bankDisableFailed'),
                $e
            );
        }
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        return $this->bankAccountRepository->getAll(
            $limit,
            $this->getJoins($data),
            $this->getWhereConditions($data),
            $order
        );
    }


    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }
}
