<?php

namespace Modules\Head\Services;

use Illuminate\Support\Collection;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Bank;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\BankRepository;
use RuntimeException;
use Throwable;

class BankService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    protected BankRepository $bankRepository;

    /**
     * BankService constructor.
     *
     * @param BankRepository $bankRepository
     */
    public function __construct(BankRepository $bankRepository)
    {
        $this->bankRepository = $bankRepository;

        parent::__construct();
    }

    /**
     * @param int $id
     *
     * @return Bank
     *
     * @throws NotFoundException
     */
    public function getBankById(int $id)
    {
        $bank = $this->bankRepository->getBankById($id);
        if (!$bank || $bank->isDeleted()) {
            throw new RuntimeException(__('head::bankCrud.bankNotFound'));
        }

        return $bank;
    }

    public function getAll(): Collection
    {
        try {
            $allBanks = $this->bankRepository->getAll();
            $allBanks = $this->convertArrayToCollection($allBanks);

            if ($allBanks->isEmpty()) {
                throw new RuntimeException(
                    __('head::bankCrud.banksNotFound')
                );
            }
        } catch (Throwable $t) {
            throw new RuntimeException($t->getMessage());
        }

        return $allBanks;
    }

    /**
     * @param int $iban
     *
     * @return Bank
     *
     * @throws NotFoundException
     */
    public function getBankByIban(string $iban)
    {
        $bankBicCode = substr($iban, 4, 4);

        $bank = $this->bankRepository->getBankByBic($bankBicCode);
        if (empty($bank)) {
            return false;
        }

        return $bank;
    }

}
