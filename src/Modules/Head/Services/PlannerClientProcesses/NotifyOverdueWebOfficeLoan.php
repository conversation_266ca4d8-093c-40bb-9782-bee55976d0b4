<?php

namespace Modules\Head\Services\PlannerClientProcesses;

use Carbon\Carbon;
use Modules\Common\Models\Loan;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Application\Enums\OverdueDaysEnum;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Services\EmailService;
use Modules\Communication\Services\SmsService;

class NotifyOverdueWebOfficeLoan
{
    public function __construct(
        public SmsService   $smsService,
        public EmailService $emailService,
    ) {}

    public function execute(Loan $loan): bool
    {
        if (empty($loan->current_overdue_days)) {
            return false;
        }

        if (!in_array($loan->current_overdue_days, OverdueDaysEnum::possibleDaysOnlineOffice())) {
            return false;
        }

        $vars = [];
        $templateSms = null;
        $templateEmail = null;
        switch ($loan->current_overdue_days) {
            case OverdueDaysEnum::OVERDUE_3_DAYS->value:
                $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_3->value;
                $templateEmail = EmailTemplateKeyEnum::CREDIT_OVERDUE_3->value;

                $inst = $loan->getUnpaidInstallmentByOverdueDays($loan->current_overdue_days);
                if (!empty($inst->due_date)) {
                    $vars['loan_last_unpaid_installment_date'] = Carbon::parse($inst->due_date)->format('d.m.Y');
                }

                break;
            case OverdueDaysEnum::OVERDUE_12_DAYS->value:
                $templateEmail = EmailTemplateKeyEnum::CREDIT_OVERDUE_12->value;
                break;
            case OverdueDaysEnum::OVERDUE_23_DAYS->value:
                $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_23->value;
                $templateEmail = EmailTemplateKeyEnum::CREDIT_OVERDUE_23->value;
                break;
            case OverdueDaysEnum::OVERDUE_45_DAYS->value:
                $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_X_DAYS->value;
                $templateEmail = EmailTemplateKeyEnum::CREDIT_OVERDUE_45->value;
                break;
            case OverdueDaysEnum::OVERDUE_60_DAYS->value:
                $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_60->value;
                $templateEmail = EmailTemplateKeyEnum::CREDIT_OVERDUE_60->value;
                break;
            case OverdueDaysEnum::OVERDUE_90_DAYS->value:
                $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_90->value;
                $templateEmail = EmailTemplateKeyEnum::CREDIT_OVERDUE_90->value;
                break;
            case OverdueDaysEnum::OVERDUE_10_DAYS->value:
            case OverdueDaysEnum::OVERDUE_30_DAYS->value:
            case OverdueDaysEnum::OVERDUE_37_DAYS->value:
            case OverdueDaysEnum::OVERDUE_52_DAYS->value:
            case OverdueDaysEnum::OVERDUE_67_DAYS->value:
            case OverdueDaysEnum::OVERDUE_75_DAYS->value:
            case OverdueDaysEnum::OVERDUE_82_DAYS->value:
            case OverdueDaysEnum::OVERDUE_97_DAYS->value:
                $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_X_DAYS->value;
                break;
        }

        if (empty($templateSms) && ($loan->current_overdue_days - 97) % 7 == 0) {
            $templateSms = SmsTemplateKeyEnum::SMS_TYPE_LOAN_OVERDUE_X_DAYS->value;
        }

        if (empty($templateSms) && empty($templateEmail)) {
            return false;
        }

        if ($templateSms) {

            $sms = $this->smsService
                ->withJuridicalLoanChecks()
                ->sendByTemplateKeyAndLoan($templateSms, $loan);
        }
        if ($templateEmail) {

            $email = $this->emailService
                ->withJuridicalLoanChecks()
                ->sendByTemplateKeyAndLoanId($templateEmail, $loan->loan_id, 0, $vars);
        }

        if (empty($sms->sms_id) && empty($email->email_id)) {
            return false;
        }

        return true;
    }
}
