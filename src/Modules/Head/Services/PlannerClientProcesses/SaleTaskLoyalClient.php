<?php

namespace Modules\Head\Services\PlannerClientProcesses;

use Modules\Common\Models\Client;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;

class SaleTaskLoyalClient
{
    public function execute(string $className, Client $client): bool
    {
        $days = $client->clientActualStats->days_without_loan;

        if (!in_array($days, SaleTask::NEW_SALE_TASK_DAYS_CASES)) {
            return false;
        }

        if (!$client->latestLoan) {
            return false;
        }

        $data = [
            'sale_task_type_id' => SaleTaskType::SALE_TASK_TYPE_ID_PRE_APPROVED,
            'client_id' => $client->getKey(),
            'loan_id' => $client->latestLoan->getKey(),
            'office_id' => $client->latestLoan->office->getKey(),
            'pin' => $client->pin,
            'client_full_name' => $client->getFullName(),
            'phone' => $client->phone,
            'email' => $client->email,
            'details' => "Created from $className script. Days without loan: $days",
            'status' => SaleTask::SALE_TASK_STATUS_NEW,
            'processed_by' => null,
            'show_after' => now(),
        ];

        $saleTask = new SaleTask();
        $saleTask->fill($data);
        $saleTask->save();

        return true;
    }
}