<?php

namespace Modules\Head\Services;

use Elastic\Elasticsearch\Client as EsClient;
use Modules\Common\Models\Client;
use RuntimeException;

final class ClientEsService
{
    public function __construct(private EsClient $es)
    {
    }

    public function upsert(Client $client): void
    {
        $response = $this->es->update([
            'index' => 'clients',
            'id' => $client->getKey(),
            'body' => [
                'doc' => [
                    'name' => $client->getFullName(),
                    'phone' => $client->phone,
                    'email' => $client->email,
                    'pin' => $client->pin,
                ],
                "doc_as_upsert" => true,
            ]
        ]);

        if (!$response->asBool()) {
            throw new RuntimeException('Failed to insert/update client in elasticsearch');
        }
    }
}
