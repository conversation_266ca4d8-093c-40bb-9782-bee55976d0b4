<?php

namespace Modules\Head\Services;

use Modules\Common\Models\Administrator;

class CreditService
{
    public function __construct()
    {
    }

    /**
     * Add credit from Cpay or update alredy existed cpay credit
     */
    public function syncCpayCredit(array $data)
    {
        // TODO: Adapt to our system, use firstOrCreate()
        // $isUpdate = true;
        // $creditPhone = CreditPhone::where('cpay_id', $request->id)->first();
        // if (empty($creditPhone)) {
        //     $isUpdate = false;
        //     $creditPhone = new CreditPhone();
        // }
        // $creditPhone->input_channel = 'cpay';
        // $creditPhone->firstname = ($request->has('fname') ? $request->fname : null);
        // $creditPhone->lastname = ($request->has('lname') ? $request->lname : null);
        // $creditPhone->email = ($request->has('mail') ? $request->mail : null);
        // $creditPhone->phone = ($request->has('gsm') ? $request->gsm : null);
        // $creditPhone->price = ($request->has('amount') ? $request->amount : null);
        // $creditPhone->days = ($request->has('period') ? $request->period : null);
        // $creditPhone->egn = ($request->has('egn') ? $request->egn : null);
        // $creditPhone->cpay_status = ($request->has('status') ? $request->status : null);
        // $creditPhone->cpay_id = $request->id;
        // $creditPhone->finished = false;
        // if ($request->service_type == CPay::$loanToSalary) {
        //     $creditPhone->credit_type = 'loan-to-salary';
        // } elseif ($request->service_type == CPay::$installmentCredit) {
        //     $creditPhone->credit_type = 'installment-credit';
        // }
        // $creditPhone->cpay = $inputData;
        // $creditPhone->save();


        // TODO: to message queue
        // if ($isUpdate) {
        //     /**
        //      * Известяване за ъпдейт от CPay
        //      */
        //     Mail::raw('Променен статус по кредит от CPay: ' . $creditPhone->id, function ($message) {
        //         $message->subject('Променен статус по кредит от CPay');
        //         $message->to(config('website.admin_email'));
        //     });
        // }


        // TODO
        // dispatch(new CreditPhoneAutomaticActions($creditPhone));
        // След добавяне/промяна по Cpay credit
        // - взимаме данни от ЦКР, или кеширани
        // - изчисляваме:
        //      $this->total_percent = $total_percent;
        //      $this->active_percent = $active_percent;
        //      $this->not_active_percent = $not_active_percent;
        // - взимаме ной/нси репорти и ги save'ме за кредита

        return true;
    }
}
