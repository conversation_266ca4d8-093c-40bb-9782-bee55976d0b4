<?php

namespace Modules\Head\Services;

use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanMeta;
use Modules\Common\Models\LoanOuterCollectorHistory;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Services\BaseService;
use Modules\Common\Statistics\Dashboard\LoansApprove;
use Modules\Common\Traits\ValidationTrait;
use Modules\Head\Repositories\InstallmentRepository;
use Modules\Head\Repositories\Loan\LoanMetaRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Product\Services\ProductService;
use RuntimeException;
use StikCredit\Calculators\LoanCalculator;

final class LoanService extends BaseService
{
    use ValidationTrait;

    private const CACHE_TTL = 60 * 60 * 24;

    public function __construct(
        protected readonly ProductService         $productService,
        protected readonly LoanRepository         $loanRepository,
        protected readonly InstallmentRepository  $installmentRepository,
        protected readonly LoanCalculator         $loanCalculator,
        protected readonly LoanMetaRepository     $loanMetaRepository,
    ) {
        parent::__construct();
    }

    /**
     * Calc (лихвен процент + неустойка процент) / 360 х 30 х оставаща главница по кредита
     */
    public function calculateExtendLoanFeeAmount(Loan $loan, int $extendWithDays = 30): int
    {
        if (!$loan->exists) {
            throw new Exception(__("Can't calculate extend loan fee, loan not available."));
        }

        return $loan->getExtendLoanFeeAmountDb($extendWithDays);
    }

    public function getClientLoanIds(Client $client): array
    {
        return $this->loanRepository->getRawClientLoanIds($client);
    }

    public function getLoanById(int $id): Loan
    {
        $loan = $this->loanRepository->getById($id);

        if (!$loan) {
            throw new RuntimeException(__('head::loanCrud.loanNotFound'));
        }

        return $loan;
    }

    public function getPaginatedLoansForClientCard(
        int $clientId,
        int $limit = 10
    ): array|\Illuminate\Pagination\LengthAwarePaginator
    {
        return $this->loanRepository->getPaginatedLoansForClientCard($clientId, $limit);
    }

    public function getByFilters(
        ?int  $limit,
        array $data
    ): mixed
    {
        $order = $this->getAdditionalOrderConditions($data);
        unset($data['order']);

        if (array_key_exists('limit', $data) && $data['limit'] !== null) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        $joins = $this->getJoins($order);

        $whereConditions = $this->getWhereConditions($data, ['name'], 'loan');

        return $this->loanRepository->getAll(
            $limit,
            $joins,
            $whereConditions,
            $order
        );
    }

    protected function getWhereConditions(
        array  $data,
        array  $names = ['name'],
        string $prefix = ''
    ): array
    {
        $where = [];

        if (!empty($data['pin'])) {
            $where[] = [
                'client.pin',
                '=',
                $data['pin'],
            ];
            unset($data['pin']);
        }

        if (!empty($data['amountApprovedFrom'])) {
            $where[] = [
                'amount_approved',
                '>=',
                $data['amountApprovedFrom'],
            ];
            unset($data['amountApprovedFrom']);
        }

        if (!empty($data['amountApprovedТо'])) {
            $where[] = [
                'amount_approved',
                '<=',
                $data['amountApprovedТо'],
            ];
            unset($data['amountApprovedТо']);
        }

        if (!empty($data['product_type_id'])) {
            $where[] = [
                'loan.product_type_id',
                '=',
                $data['product_type_id'],
            ];
            unset($data['product_type_id']);
        }

        return array_merge($where, parent::getWhereConditions($data, $names, $prefix));
    }

    public function getLogsForLoansClientsInstallments(
        int   $clientId,
        int   $pageNum,
        array $requestQuery,
        int   $limit
    ): LengthAwarePaginator
    {
        return $this->loanRepository->getLogsForLoansClientsInstallments(
            $clientId,
            $pageNum,
            $requestQuery,
            $limit
        );
    }

    public function getLoanStatuses(): Collection
    {
        return Cache::remember('loan_statuses', self::CACHE_TTL, function () {
            return $this->loanRepository->getLoanStatuses();
        });
    }

    public function getLoanProductTypes(): Collection
    {
        $loanProductGroups = Cache::remember('loan_product_types', self::CACHE_TTL, function () {
            return $this->loanRepository->getLoanProductTypes();
        });

        return collect($loanProductGroups);
    }

    public function getLoanTypes(): Collection
    {
        $loanTypes = Cache::remember('loan_types', self::CACHE_TTL, function () {
            return $this->loanRepository->getLoanTypes();
        });

        return collect($loanTypes);
    }

    public function getLoanPaymentMethods(): Collection
    {
        $loanPaymentMethods = Cache::remember('loan_payment_methods', self::CACHE_TTL, function () {
            return $this->loanRepository->getLoanPaymentMethods();
        });

        return collect($loanPaymentMethods);
    }

    public function getLoanOffices(): Collection
    {
        $loanOffices = Cache::remember('loan_offices', self::CACHE_TTL, function () {
            return $this->loanRepository->getLoanOffices();
        });

        return collect($loanOffices);
    }

    public function getStatsInfo(): array
    {
        return (new LoansApprove())->getStatsByUser(getAdmin(), now())->toArray();
    }

    public function getJoins(
        array $data
    ): array {

        $joins = [];

        if (isset($data['loan_type.name'])) {
            $joins['join'][] = [
                'loan_type',
                'loan.loan_type_id',
                '=',
                'loan_type.loan_type_id',
            ];
        }

        if (isset($data['loan_status.name'])) {
            $joins['join'][] = [
                'loan_status',
                'loan.loan_status_id',
                '=',
                'loan_status.loan_status_id',
            ];
        }

        if (isset($data['office.name'])) {
            $joins['join'][] = [
                'office',
                'loan.office_id',
                '=',
                'office.office_id',
            ];
        }

        return $joins;
    }

    public function getAdditionalOrderConditions($data): array
    {
        $order = $this->getOrderConditions($data);

        if (isset($order['loan.client.pin'])) {
            $order = array_merge($order, ['client.pin' => $order['loan.client.pin']]);
            unset($order['loan.client.pin']);
        }
        if (isset($order['loan.product_name'])) {
            $order = array_merge($order, ['product.name' => $order['loan.product_name']]);
            unset($order['loan.product_name']);
        }
        if (isset($order['product_name'])) {
            $order = array_merge($order, ['product_name' => $order['product_name']]);
            unset($order['product_name']);
        }
        if (isset($order['loan.client_full_name'])) {
            $order = array_merge($order, ['client_full_name' => $order['loan.client_full_name']]);
            unset($order['loan.client_full_name']);
        }
        if (isset($order['loan.loan_type.name'])) {
            $order = array_merge($order, ['loan_type.name' => $order['loan.loan_type.name']]);
            unset($order['loan.loan_type.name']);
        }
        if (isset($order['loan.loan_status.name'])) {
            $order = array_merge($order, ['loan_status.name' => $order['loan.loan_status.name']]);
            unset($order['loan.loan_status.name']);
        }
        if (isset($order['loan.payment_method.name'])) {
            $order = array_merge($order, ['payment_method.name' => $order['loan.payment_method.name']]);
            unset($order['loan.payment_method.name']);
        }

        if (isset($order['loan.office.name'])) {
            $order = array_merge($order, ['office.name' => $order['loan.office.name']]);
            unset($order['loan.office.name']);
        }

        if (empty($order)) {
            $order['loan.created_at'] = 'DESC';
        }

        return $order;
    }

    public function calculateEarlyRepayment(array $data): array
    {
        $loanId = $data['loanId'] ?? null;
        $repaymentDate = $data['repaymentDate'] ?? null;

        $loan = $this->getLoanById((int)$loanId);
        if (!$loan) {
            throw new RuntimeException('Loan not found');
        }

        $loanCalculator = $loan->getCredit($repaymentDate);
        $loanCarton = $loanCalculator->loanCarton();

        return [
            'principal' => $loanCarton->outstandingPrinciple,
            'interest' => $loanCarton->outstandingInterest,
            'penalty' => $loanCarton->outstandingPenalty,
            'lateInterest' => $loanCarton->outstandingLateInterest,
            'latePenalty' => $loanCarton->outstandingLatePenalty,
            'taxes' => $loanCarton->totalOutstandingFeesAmount,
            'sum' => $loanCarton->earlyRepaymentAmount
        ];
    }

    // add additional fields for refinance options in new application calculator
    // change format of fields
    public function prepareLoansForRefinance(
        array $loans,
        array $refinancedLoans
    ): array
    {
        $date = Carbon::today();
        $result = [];

        foreach ($loans as $loan) {
            if ($loan->loan_status_id !== LoanStatus::ACTIVE_STATUS_ID) {
                $loan->early_repayment_sum = 0;
            } else {
                $data = $this->calculateEarlyRepayment([
                    'loanId' => $loan->loan_id,
                    'repaymentDate' => $date->format('d.m.Y'),
                ]);

                $loan->early_repayment_sum = $data['sum'];
            }

            $loan->office_name = officeName($loan->office_name);

            $loan->last_installment_date = (Carbon::parse($loan->last_installment_date))->format('Y-m-d');

            $loan->checked = false;
            if (array_key_exists($loan->loan_id, $refinancedLoans)) {
                $loan->checked = true;
            }

            $result[] = (array)$loan;
        }

        return $result;
    }

    public function getClientLoanStatistics(Client $client): array
    {
        $allApplications = $client->getAllLoansCount();
        $activeLoans = $client->getActiveLoansCount();

        $lastApplicationDate = null;
        $lastLoan = $client->getLastLoan();
        if (!empty($lastLoan->created_at)) {
            $lastApplicationDate = Carbon::parse($lastLoan->created_at);
            $lastApplicationDate?->setHour(0)->setMinutes(0)->setSeconds(0)->setMilliseconds(0);
        }

        $activeLoansLastPayment = null;
        $lastPayment = $client->getLastPayment();
        if (!empty($lastPayment->created_at)) {
            $activeLoansLastPayment = Carbon::parse($lastPayment->created_at);
            $activeLoansLastPayment?->setHour(0)->setMinutes(0)->setSeconds(0)->setMilliseconds(0);
        }

        return compact(
            'allApplications',
            'lastApplicationDate',
            'activeLoans',
            'activeLoansLastPayment',
        );
    }

    /**
     * Creates metadata record for the loan extension (extend loan functionality)
     */
    public function addLoanExtensionData(
        int    $loanId,
        int    $installmentId,
        int    $days,
        string $loanMetaType = LoanMeta::LOAN_EXTEND_TASK,
        string $repaymentDate = ''
    ): LoanMeta
    {
        $loanExtensions = $this->loanMetaRepository->getByCriteria(
            [
                'key' => $loanMetaType,
                'loan_id' => $loanId,
                'active' => 1,
                'deleted' => 0,
            ]
        );


        if (!$loanExtensions?->getKey()) {
            return $this->loanMetaRepository->create(
                [
                    'key' => $loanMetaType,
                    'loan_id' => $loanId,
                    'value' => json_encode(
                        [
                            [
                                'installmentId' => $installmentId,
                                'days' => $days,
                                'date' => Carbon::now(),
                                'old_repayment_date' => $repaymentDate
                            ]
                        ]
                    ),
                ]
            );
        }

        $previousValueArr = json_decode($loanExtensions->value, true);
        $previousValueArr[] = [
            'installmentId' => $installmentId,
            'days' => $days,
            'date' => Carbon::now(),
            'old_repayment_date' => $repaymentDate
        ];
        $data = ['value' => json_encode($previousValueArr)];

        return $this->loanMetaRepository->update(
            $loanExtensions,
            $data
        );
    }

    public function removeFromOuterCollector(Loan $loan): void
    {
        $history = new LoanOuterCollectorHistory([
            'loan_id' => $loan->loan_id,
            'consultant_id' => $loan->consultant_id,
            'outer_collector_from_date' => $loan->outer_collector_from_date,
            'marked_as_outer_collector_by' => $loan->marked_as_outer_collector_by,
            'removed_by' => getAdminId(),
        ]);

        $loan->consultant_id = null;
        $loan->outer_collector = 0;
        $loan->outer_collector_from_date = null;
        $loan->marked_as_outer_collector_by = null;

        DB::transaction(static function () use ($history, $loan) {
            if (!$history->save()) {
                throw new RuntimeException(__('History save failed'));
            }

            if (!$loan->save()) {
                throw new RuntimeException(__('Loan update failed'));
            }
        });
    }
}
