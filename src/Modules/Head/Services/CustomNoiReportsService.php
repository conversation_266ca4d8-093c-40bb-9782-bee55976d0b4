<?php

namespace Modules\Head\Services;

use Illuminate\Http\UploadedFile;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\FileStorage;
use Modules\Common\Models\FileType;
use Modules\Common\Models\Noi2Stats;
use Modules\Common\Services\BaseService;
use Modules\Head\Jobs\ProcessCustomNoiReportJob;
use Modules\Head\Models\CustomNoiReports;
use Modules\Head\Repositories\FileRepository;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use RuntimeException;

class CustomNoiReportsService extends BaseService
{
    public function runNoiReports(CustomNoiReports $customNoiReports, int $adminId): void
    {
        ProcessCustomNoiReportJob::dispatch(
            $customNoiReports->custom_noi_report_id,
            $adminId
        )->onQueue('long-run');
    }

    public function import(UploadedFile $uploadedFile): ?CustomNoiReports
    {
        /// 1. read uploaded file
        $loanIds = [];
        if (($handle = fopen($uploadedFile->getRealPath(), 'r')) !== false) {
            // 1) read & discard header row
            fgetcsv($handle);

            // 2) loop through each remaining line
            while (($row = fgetcsv($handle)) !== false) {
                // cast & collect your first column
                $loanIds[] = (int) $row[0];
            }

            fclose($handle);
        }
        if (empty($loanIds)) {
            throw new RuntimeException('Error imported file is empty.');
        }

        /// 2. create file name and store it
        $fileName = date('Ymd-His') . '-' . $uploadedFile->getClientOriginalName();
        $resp = $uploadedFile->storeAs(
            'custom-noi-reports',
            $fileName
        );

        /// 3. create DB file row
        $file = app(FileRepository::class)->create([
            'file_storage_id' => FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID,
            'file_type_id' => FileType::getIdFromCode(FileTypeEnum::OTHER),
            'disk' => 'local',
            'hash' => md5_file(storage_path($resp)),
            'file_path' => 'custom-noi-reports/',
            'file_size' => $uploadedFile->getSize(),
            'file_type' => $uploadedFile->getType(),
            'file_name' => $uploadedFile->getClientOriginalName(),
        ]);

        /// 4. Create custom noi reports row
        return CustomNoiReports::create([
            'imported_file_id' => $file->file_id,
            'imported_loan_ids' => $loanIds,
            'created_by' => getAdminId(),
            'created_at' => now()
        ]);
    }


    public function createExportFileForUpdatedNoiReports(CustomNoiReports $customNoiReports): void
    {
        /**
         * Example query form task
         * SELECT
         * n1.loan_id,
         * n1.call_status,
         * n1.report_date,
         * n1.last_input_date,
         * n1.last_salary
         * FROM noi2_stats n1
         * WHERE n1.id = (
         * SELECT MAX(n2.id)
         * FROM noi2_stats n2
         * WHERE n2.loan_id = n1.loan_id
         * )
         * AND n1.loan_id IN (" . implode(',', $customNoiReports->imported_loan_ids) . ")
         * ORDER BY n1.loan_id ASC
         */
        $builder = Noi2Stats::whereIn('loan_id', $customNoiReports->imported_loan_ids)
            ->whereRaw('id = (SELECT MAX(n2.id) FROM noi2_stats n2 WHERE n2.loan_id = noi2_stats.loan_id)')
            ->orderBy('loan_id', 'asc')
            ->selectRaw('loan_id, call_status, report_date, last_input_date, last_salary');

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray([$this->getHeaders()], null, 'A1');
        $rowIndex = 1;

        $builder->chunk(100, function ($chunk) use (&$sheet, &$rowIndex) {
            /**
             * @var Noi2Stats $row
             */
            foreach ($chunk as $row) {
                $batchData = [
                    $row->loan_id,
                    $row->call_status,
                    $row->report_date,
                    $row->last_input_date,
                    $row->last_salary,
                ];

                $rowIndex++;
                $sheet->fromArray([$batchData], null, 'A' . $rowIndex);
            }
        });

        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        $fileName = 'export-noi-reports-' . date('Ymd-His') . '.xlsx';
        $filePath = storage_path('custom-noi-reports/' . $fileName);
        $writer->save($filePath);

        $file = app(FileRepository::class)->create([
            'file_storage_id' => FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID,
            'file_type_id' => FileType::getIdFromCode(FileTypeEnum::OTHER),
            'disk' => 'local',
            'hash' => md5_file($filePath),
            'file_path' => 'custom-noi-reports/',
            'file_size' => filesize($filePath),
            'file_type' => mime_content_type($filePath),
            'file_name' => $fileName,
        ]);

        $customNoiReports->setAttribute('exported_file_id', $file->file_id);
        $customNoiReports->saveQuietly();
    }

    private function getHeaders(): array
    {
        return [
            'LoanId',
            'Call status',
            'Report date',
            'Last input date',
            'Last salary',
        ];
    }
}
