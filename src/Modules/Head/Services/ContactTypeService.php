<?php

namespace Modules\Head\Services;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\ContactTypeRepository;

class ContactTypeService extends BaseService
{
    private ContactTypeRepository $contactTypeRepository;

    public function __construct(
        ContactTypeRepository $contactTypeRepository
    ) {
        $this->contactTypeRepository = $contactTypeRepository;
        parent::__construct();
    }

    public function getAllContactTypes()
    {
            $contactTypes = \Cache::remember('all_contact_types', 120, function () {
                return $this->contactTypeRepository->getContactsType();
            });
            return  $this->collect(Collection::class, $contactTypes);
    }
}
