<?php

namespace Modules\Head\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\CancelLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Models\A4EReport;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\AutoProcess;
use Modules\Common\Models\AutoProcessRule;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Services\BaseService;
use Modules\Common\Traits\PinTrait;
use Modules\Discounts\Models\PreApprovedClient;
use Modules\Discounts\Models\PreApprovedLoan;
use Modules\Sales\Application\Actions\UpdateLoanFromAutoProcessBecauseOfCreditLimitAction;

final class AutoProcessService extends BaseService
{
    use PinTrait;

    const MAX_OVERDUE_DAYS_FOR_REFINANCE = 5;

    // used in tryRule()
    public $tryLog = [
        'failed' => [],
        'passed' => [],
    ];

    public function __construct()
    {
        parent::__construct();
    }

    ///////////////////////// AUTO PROCESSING //////////////////////////////////

    public function autoProcess(
        Loan $loan,
        ?A4EReport $report = null,
        bool $stopWhenFind = false // used for manual checks only
    ): bool {

        // special logic, where on client level we add amounts
        // loan is processing inside in action: preApproved();
        $approved = $this->preApproved($loan);
        if ($approved) {
            $loan->addMeta('autoProcess', 'Success: pre-approved logic');
            return true;
        }


        // exit if loan is in wrong status
        if (
            $stopWhenFind == false
            && !in_array($loan->loan_status_id, [LoanStatus::SIGNED_STATUS_ID])
        ) {
            $loan->addMeta('autoProcess', 'Fail: wrong loan status: ' . $loan->loan_status_id);
            return false;
        }


        /// approve when (re_contracted_overdue=1)
        if (
            1 === intval($loan->re_contracted_overdue->value) &&
            !$loan->hasAnyApproveTask([
//                Loan::APPROVE_TASK_IMNR,
                Loan::APPROVE_TASK_IMWD,
                Loan::APPROVE_TASK_IMVD,
            ])
        ) {
            $loan->addMeta('autoProcess', 'Success: re_contracted_overdue');

            /// process loan by system admin
            $loan = $this->processLoanBySystemAdministrator($loan);

            // make auto approve
            app(ApproveLoanAction::class)->execute(DecisionDto::from([
                'loan_id' => $loan->getKey(),
                'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'decision' => ApproveDecision::APPROVE_DECISION_APPROVED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_RE_CONTRACTED_OVERDUE,
            ]));

            /// exit
            return true;
        }


        // will collect data here for future comparison
        $loanParams = $this->compileLoanParams($loan, $report);


        // get all active DISAPPROVE rules for office
        $rulesD = $this->getAutoProcessRulesForOffice(
            $loan->office_id,
            AutoProcessRule::TYPE_DISAPPROVE
        );

        $exit = false;
        if (!empty($rulesD)) {
            // loop and try to disapprove first
            foreach ($rulesD as $rule) {
                if (true === $this->tryRule($rule, $loanParams)) {
                    if ($stopWhenFind) {
                        dd(
                            'Found DISSAPPROVE rule',
                            $rule->toArray(),
                            $this->tryLog,
                            $loanParams
                        );
                    }

                    $exit = true;

                    return $this->saveAndProcess(
                        $loan,
                        $rule,
                        $loanParams
                    );
                }
            }
        }


        // add this stupid thing to be sure that we go out if have cancel rule
        if ($exit) {
            return true;
        }


        // skip 1
        if (empty($report->a4e_report_id)) {
            $loan->addMeta('autoProcess', 'Fail: no a4e report');
            return false;
        }

        // skip 2
        if ($loan->isCustomProduct()) {
            $loan->addMeta('autoProcess', 'Fail: Custom product');
            return false;
        }

        // skip 3
        if ($loan->wait_verification->isTrue()) {
            $loan->addMeta('autoProcess', 'Fail: wait_verification - no mvr pic');
            return false;
        }


        $creditLimit = $loan->getCreditLimit()?->amount;
        if (empty($creditLimit)) {
            $loan->addMeta('autoProcess', 'Fail: no credit limit');
            return false;
        }

        $client = $loan->client;
        $prevLoan = $client->getLastActiveOrRepaidLoan();
        $isNewClient = empty($prevLoan->loan_id) ? true : false; // client->isNew()

        // auto-approve logic, based on credit limit
        $creditLimit = floatToInt($creditLimit);
        if ($creditLimit < $loan->amount_approved) {

            // exit for new client
            if ($isNewClient) {

                $loan->addMeta('autoProcess', 'Fail: credit_limit < amount (' . intToFloat($creditLimit) . ' < ' . intToFloat($loan->amount_approved) . ')');
                return false;

            } else {
                // for old clients we have another process
                // task: https://trello.com/c/qF4P3UDd

                DB::beginTransaction();
                try {
                    // new logic:
                    // set New loan amount
                    // change status to new
                    // gen new docs
                    // send email/sms
                    //
                    // scenario #1: normal loan - do new logic
                    // refinance:
                    // scenario #2A:if credit_limit > refinance_amount + 50 -> do new logic
                    // scenario #2B:else cancel loan and send sms/email

                    $this->processLoanBySystemAdministrator($loan);

                    $refinancedAmount = $loan->getTotalAmountForRefinance();
                    // scenarios #2A & #2B
                    if ($refinancedAmount > 0) {
                        // get active loans max_overdue, if more then 5 days -> cancel
                        $currentOverdue = $client->getMaxCurrentOverdueOfActiveLoans();
                        if ($currentOverdue > self::MAX_OVERDUE_DAYS_FOR_REFINANCE) {
                            $data = [
                                'loan_id' => $loan->getKey(),
                                'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                                'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_AUTO_PROCESS_OVERDUE_REFINANCE,
                                'description' => 'Prev loan has overdue = ' . $currentOverdue,
                            ];
                            $action = app(CancelLoanAction::class);
                            $action->execute(DecisionDto::from($data));

                            $loan->addMeta('autoProcess', 'Success: cancel ref. based on overdue(' . $currentOverdue . '>' . self::MAX_OVERDUE_DAYS_FOR_REFINANCE . ')');

                        } else {
                            // we need to check that client will receive something for new loan
                            $refinancedAmountAndNewAmount = getMinRefinanceAmount($refinancedAmount);

                            // scenario #2A: when credit_limit >= refinance_amount -> change amount & need re-sign
                            if ($creditLimit >= $refinancedAmountAndNewAmount) {
                                $action = app(UpdateLoanFromAutoProcessBecauseOfCreditLimitAction::class);
                                $action->executeRefinance(
                                    $loan,
                                    $creditLimit,
                                    $refinancedAmount
                                );

                                $loan->addMeta('autoProcess', 'Skip: need re-sign for lower amount(1)');

                            } else { // scenario #2B: when credit_limit < refinance_amount  -> cancel loan

                                $action = app(UpdateLoanFromAutoProcessBecauseOfCreditLimitAction::class);
                                $action->executeRefinanceCancel(
                                    $loan,
                                    $creditLimit,
                                    $refinancedAmount
                                );

                                $loan->addMeta('autoProcess', 'Success: cancel ref. based on amount(' . $creditLimit . '<' . $refinancedAmountAndNewAmount . ')');
                            }
                        }
                    } else { // scenario #1 -> change amount & need re-sign

                        $action = app(UpdateLoanFromAutoProcessBecauseOfCreditLimitAction::class);
                        $action->executeNormal($loan, $creditLimit);

                        $loan->addMeta('autoProcess', 'Skip: need re-sign for lower amount(2)');
                    }

                    DB::commit();

                    return true;

                } catch (\Throwable $e) {
                    DB::rollBack();

                    $loan->addMeta('autoProcess', 'Fail: exc.1 ' . $e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

                    return false;
                }
            }

        } else {

            $refinancedAmount = 0;
            if (!$isNewClient) {
                $refinancedAmount = $loan->getTotalAmountForRefinance();
            }

            // only for old clients, since new have $refinancedAmount == 0
            if ($refinancedAmount > 0) {

                $currentOverdue = $client->getMaxCurrentOverdueOfActiveLoans();
                if ($currentOverdue > self::MAX_OVERDUE_DAYS_FOR_REFINANCE) {

                    DB::beginTransaction();
                    try {

                        $this->processLoanBySystemAdministrator($loan);

                        $data = [
                            'loan_id' => $loan->getKey(),
                            'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                            'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                            'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_AUTO_PROCESS_OVERDUE_REFINANCE,
                            'description' => 'Prev loan has overdue = ' . $currentOverdue,
                        ];
                        $action = app(CancelLoanAction::class);
                        $action->execute(DecisionDto::from($data));

                        DB::commit();

                        return true;

                    } catch (\Throwable $e) {
                        DB::rollBack();

                        $loan->addMeta('autoProcess', 'Fail: exc.2 ' . $e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

                        return false;
                    }

                }
            }
        }

        // Related to: https://trello.com/c/9BvHaQWF
        // If client is new, and loan amount were changed
        // So when client signed it second time we do auto-process
        $meta = $loan->getMeta('changed_amount_from_approve');
        if (
            $client->isNew()
            && !empty($loan->loan_changed_at)
            && !empty($meta->meta_id)
            && $loan->getLastHistoryForStatus(LoanStatus::SIGNED_STATUS_ID)
        ) {

            $loan->addMeta('autoProcess', 'Success: new client with changed amount');

            $this->processLoanBySystemAdministrator($loan);

            app(ApproveLoanAction::class)->execute(DecisionDto::from([
                'loan_id' => $loan->getKey(),
                'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'decision' => ApproveDecision::APPROVE_DECISION_APPROVED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_AUTO_PROCESS,
            ]));

            /// exit
            return true;
        }


        // get all active APPROVE rules for office
        $rulesA = $this->getAutoProcessRulesForOffice(
            $loan->office_id,
            AutoProcessRule::TYPE_APPROVE
        );
        $exit = false;
        if (!empty($rulesA)) {
            // loop and try to approve
            foreach ($rulesA as $rule) {
                if (true === $this->tryRule($rule, $loanParams)) {
                    if ($stopWhenFind) {
                        dd(
                            'Found APPROVE rule',
                            $rule->toArray(),
                            $this->tryLog,
                            $loanParams
                        );
                    }

                    $exit = true;

                    return $this->saveAndProcess(
                        $loan,
                        $rule,
                        $loanParams
                    );
                }
            }
        }


        if ($stopWhenFind) {
            dd(
                'Nothing found',
                $this->tryLog
            );
        }


        return $exit;
    }

    public function compileLoanParams(
        Loan $loan,
        ?A4EReport $report = null
    ): array {

        $loanParams = [];
        $loanParams['card1'] = !empty($report->gb) ? $report->gb : null;
        $loanParams['card2'] = !empty($report->gbr) ? $report->gbr : null;
        $loanParams['amount_requested'] = intToFloat($loan->amount_requested);

        $client = $loan->client;
        $loanParams['new_client'] = $client->new;
        $loanParams['isRefinance'] = $loan->isRefinanceLoan();

        $loanParams['client_age'] = null;
        try {
            $age = (int) $client->getAgeAndSex($client->pin)['age'];
            if ($age) {
                $loanParams['client_age'] = $age;
            }
        } catch (\Throwable $e) {
        }


        $stats = $client->getActualStats();
        $loanParams['max_overdue_days'] = $stats->max_overdue_days;
        $loanParams['repaid_loans_count'] = $stats->repaid_loans_count;

        $ccrReport = $client->getLastCcrReport();
        $loanParams['ccr_total_percent'] = $ccrReport->total_percent ?? null;

        if (!empty($ccrReport->parsed_data)) {
            $ccrParsedData = json_decode($ccrReport->parsed_data);
            $bCredCount = $ccrParsedData->bank->active_credits->stats->count ?? 0;
            $nbCredCount = $ccrParsedData->non_bank->active_credits->stats->count ?? 0;

            $loanParams['credit_in_bank'] = ($bCredCount > 0 ? 1 : 0);
            $loanParams['credit_in_nobank'] = ($nbCredCount > 0 ? 1 : 0);

            $ccrOverdueDataBank = [];
            if (!empty($ccrParsedData->bank->overdue_credits->rows->active)) {
                $ccrOverdueDataBank = $ccrParsedData->bank->overdue_credits->rows->active;
            }
            $ccrOverdueDataNoBank = [];
            if (!empty($ccrParsedData->non_bank->overdue_credits->rows->active)) {
                $ccrOverdueDataNoBank = $ccrParsedData->non_bank->overdue_credits->rows->active;
            }
            $loanParams['ccr_overdue_data'] = array_merge(
                $ccrOverdueDataBank,
                $ccrOverdueDataNoBank
            );
        }

        $income = 0;
        $noiReports = $client->getLastNoiReports(['noi2']);
        foreach ($noiReports as $noiReport) {
            if ($noiReport->name == 'noi2' && !empty($noiReport->parsed_data)) {
                $data = json_decode($noiReport->parsed_data, true);

                if (!empty($data['data']['stats'])) {
                    $start = Carbon::now()->startOfMonth();
                    $end = $start->subMonths(12);

                    foreach ($data['data']['stats'] as $dataNoi2) {
                        $date = $dataNoi2['year'] . '-' . $dataNoi2['month'] . '-01';
                        $rowDate = Carbon::parse($date);

                        if ($rowDate->gte($end)) {
                            $income += floatval($dataNoi2['salary']);
                        }
                    }

                    $income = round(($income / 12), 2);
                }
            }
        }
        $loanParams['income'] = $income > 0 ? 1 : 0;
        $loanParams['income_avg'] = $income;

        $mvrReport = $loan->client->getLastMvrReport();
        $mvrParsedData = json_decode($mvrReport?->parsed_data ?? '[]', true);

        if (!empty($mvrParsedData['client_address']['settlement_code'])) {
            $loanParams['settlement_code'] = mvrCodeToOurDbCode($mvrParsedData['client_address']['settlement_code']);
        }
        if (!empty($mvrParsedData['client_address']['location_code'])) {
            $loanParams['location_code'] = mvrCodeToOurDbCode($mvrParsedData['client_address']['location_code']);
        }

        return $loanParams;
    }

    public function tryRule(
        AutoProcessRule $rule,
        array $loanParams,
        bool $dump = false // used for manual debug
    ): bool {

        $this->tryLog = [];

        // Ако има age restriction, гледаме само него, др параметри по правило не ни интересува
        if (!empty($rule->age_border_from) && !empty($rule->age_border_to) && !empty($loanParams['client_age'])) {
            // age restriction е само за отказ бе!
            if ($rule->type != 'disapprove') {
                return false;
            }

            if ($loanParams['client_age'] < $rule->age_border_from) {
                return true;
            }

            if ($loanParams['client_age'] >= $rule->age_border_to) {
                return true;
            }

            return false;
        }


        if (
            !empty($rule->age_border_from)
            && !empty($loanParams['client_age'])
            && $loanParams['client_age'] < $rule->age_border_from
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'age_border_from',
                'rule' => $rule->age_border_from,
                'loan' => $loanParams['client_age'],
            ];

            return true;
        }
        if (
            !empty($rule->age_border_to)
            && !empty($loanParams['client_age'])
            && $loanParams['client_age'] > $rule->age_border_to
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'age_border_to',
                'rule' => $rule->age_border_to,
                'loan' => $loanParams['client_age'],
            ];

            return true;
        }


        // simple checks
        if (
            is_numeric($rule->new_client)
            && $rule->new_client != $loanParams['new_client']
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'new_client',
                'rule' => $rule->new_client,
                'loan' => $loanParams['new_client'],
            ];

            return false;
        }

        if (
            is_numeric($rule->credit_in_bank)
            && $rule->credit_in_bank != $loanParams['credit_in_bank']
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'credit_in_bank',
                'rule' => $rule->credit_in_bank,
                'loan' => $loanParams['credit_in_bank'],
            ];

            return false;
        }

        if (
            is_numeric($rule->credit_in_nobank)
            && $rule->credit_in_nobank != $loanParams['credit_in_nobank']
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'credit_in_nobank',
                'rule' => $rule->credit_in_nobank,
                'loan' => $loanParams['credit_in_nobank'],
            ];

            return false;
        }

        if (
            is_numeric($rule->income)
            && $rule->income != $loanParams['income']
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'income',
                'rule' => $rule->income,
                'loan' => $loanParams['income'],
            ];

            return false;
        }

        // mid checks

        if (
            !empty($rule->income_avg_from)
            && !empty($loanParams['income_avg'])
            && $loanParams['income_avg'] < $rule->income_avg_from
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'income_avg_from',
                'rule' => $rule->income_avg_from,
                'loan' => $loanParams['income_avg'],
            ];

            return false;
        }

        if (
            !empty($rule->income_avg_to)
            && !empty($loanParams['income_avg'])
            && $loanParams['income_avg'] > $rule->income_avg_to
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'income_avg_to',
                'rule' => $rule->income_avg_to,
                'loan' => $loanParams['income_avg'],
            ];

            return false;
        }

        if (
            !empty($rule->repaid_loans_count_from)
            && !empty($loanParams['repaid_loans_count'])
            && $loanParams['repaid_loans_count'] < $rule->repaid_loans_count_from
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'repaid_loans_count_from',
                'rule' => $rule->repaid_loans_count_from,
                'loan' => $loanParams['repaid_loans_count'],
            ];

            return false;
        }

        if (
            !empty($rule->repaid_loans_count_to)
            && !empty($loanParams['repaid_loans_count'])
            && $loanParams['repaid_loans_count'] > $rule->repaid_loans_count_to
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'repaid_loans_count_to',
                'rule' => $rule->repaid_loans_count_to,
                'loan' => $loanParams['repaid_loans_count'],
            ];

            return false;
        }

        if (
            !empty($rule->max_overdue_days_from)
            && !empty($loanParams['max_overdue_days'])
            && $loanParams['max_overdue_days'] < $rule->max_overdue_days_from
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'max_overdue_days_from',
                'rule' => $rule->max_overdue_days_from,
                'loan' => $loanParams['max_overdue_days'],
            ];

            return false;
        }

        if (
            !empty($rule->max_overdue_days_to)
            && !empty($loanParams['max_overdue_days'])
            && $loanParams['max_overdue_days'] > $rule->max_overdue_days_to
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'max_overdue_days_to',
                'rule' => $rule->max_overdue_days_to,
                'loan' => $loanParams['max_overdue_days'],
            ];

            return false;
        }

        if (
            !empty($rule->amount_requested_from)
            && !empty($loanParams['amount_requested'])
            && $loanParams['amount_requested'] < $rule->amount_requested_from
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'amount_requested_from',
                'rule' => $rule->amount_requested_from,
                'loan' => $loanParams['amount_requested'],
            ];

            return false;
        }

        if (
            !empty($rule->amount_requested_to)
            && !empty($loanParams['amount_requested'])
            && $loanParams['amount_requested'] > $rule->amount_requested_to
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'amount_requested_to',
                'rule' => $rule->amount_requested_to,
                'loan' => $loanParams['amount_requested'],
            ];

            return false;
        }

        if (
            !empty($rule->ccr_total_percent_from)
            && (!empty($loanParams['ccr_total_percent']) || $loanParams['ccr_total_percent'] == 0)
            && $loanParams['ccr_total_percent'] < $rule->ccr_total_percent_from
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'ccr_total_percent_from',
                'rule' => $rule->ccr_total_percent_from,
                'loan' => $loanParams['ccr_total_percent'],
            ];

            return false;
        }

        if (
            !empty($rule->ccr_total_percent_to)
            && (!empty($loanParams['ccr_total_percent']) || $loanParams['ccr_total_percent'] == 0)
            && $loanParams['ccr_total_percent'] > $rule->ccr_total_percent_to
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'ccr_total_percent_to',
                'rule' => $rule->ccr_total_percent_to,
                'loan' => $loanParams['ccr_total_percent'],
            ];

            return false;
        }

        // heavy checks
        if (
            !empty($rule->cards1)
            &&
            (
                (
                    !empty($loanParams['card1'])
                    && !preg_match('/(' . $loanParams['card1'] . ')/', $rule->cards1)
                ) || empty($loanParams['card1'])
            )
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'cards1',
                'rule' => $rule->cards1,
                'loan' => $loanParams['card1'],
            ];

            return false;
        }

        if (
            !empty($rule->cards2)
            &&
            (
                (
                    !empty($loanParams['card2'])
                    && !preg_match('/(' . $loanParams['card2'] . ')/', $rule->cards2)
                ) || empty($loanParams['card2'])
            )
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'cards2',
                'rule' => $rule->cards2,
                'loan' => $loanParams['card2'],
            ];

            return false;
        }

        // super complex checks

        if (
            !empty($rule->ccr_overdue_days_from)
            || !empty($rule->ccr_overdue_days_to)
        ) {
            if (empty($loanParams['ccr_overdue_data'])) {
                return false;
            }


            $totalOverdueAmount = 0;
            foreach ($loanParams['ccr_overdue_data'] as $row) {
                // sum only rows with period
                if (empty($row->period) || $row->period == '-') {
                    continue;
                }

                $overdueAmount = floatval($row->outstanding_principal);
                if ($overdueAmount > 0) {
                    $totalOverdueAmount += $overdueAmount;
                }
            }


            $ccrResult = $this->checkRuleCcrCriterias(
                $rule,
                $loanParams,
                $totalOverdueAmount
            );

            // if failed ccr complex checks -> exit
            if (false === $ccrResult) {
                $this->tryLog['failed'][] = [
                    'rule_id' => $rule->rule_id,
                    'key' => 'ccr_overdue_data',
                    'rule' => '',
                    'loan' => '',
                ];

                return false;
            }
        }


        // check for refinance
        if (
            isset($rule->refinance_request)
            && intval($rule->refinance_request) == 1
            && (!isset($loanParams['isRefinance']) || $loanParams['isRefinance'] !== true)
        ) {
            $this->tryLog['failed'][] = [
                'rule_id' => $rule->rule_id,
                'key' => 'refinance_request',
                'rule' => $rule->refinance_request,
                'loan' => $loanParams['isRefinance'],
            ];

            return false;
        }

        // Step 1: settlement_code check
        if (!empty($rule->settlement_code)) {
            $loanSettlement = $loanParams['settlement_code'] ?? null;

            // If loan has no settlement_code or it's not in the allowed rule list
            if (empty($loanSettlement) || !in_array($loanSettlement, $rule->settlement_code)) {
                $this->tryLog['failed'][] = [
                    'rule_id' => $rule->rule_id,
                    'key' => 'settlement_code',
                    'rule' => json_encode($rule->settlement_code),
                    'loan' => $loanSettlement ?? 'none',
                ];
                return false;
            }

            // Step 2: location_code check (only if provided in rule)
            if (!empty($rule->location_code)) {
                // Only validate location if rule has location codes for this settlement
                $settlementLocations = $rule->location_code[$loanSettlement] ?? [];

                if (!empty($settlementLocations)) {
                    $loanLocation = $loanParams['location_code'] ?? null;

                    if (empty($loanLocation) || !in_array($loanLocation, $settlementLocations)) {
                        $this->tryLog['failed'][] = [
                            'rule_id' => $rule->rule_id,
                            'key' => 'location_code',
                            'rule' => json_encode($rule->location_code),
                            'loan' => $loanLocation ?? 'none',
                        ];
                        return false;
                    }
                }
            }
        }


        return true;
    }

    private function checkRuleCcrCriterias(
        AutoProcessRule $rule,
        array $loanParams,
        float $totalOverdueAmount = 0
    ): bool {
        ////////////////////////////////////////
        // first check OR conditions
        // since it faster and could save time
        ////////////////////////////////////////
        if (
            empty($rule->ccr_or_overdue_principal_to)
            && !empty($rule->ccr_or_overdue_principal_from)
            && $totalOverdueAmount >= floatval($rule->ccr_or_overdue_principal_from)
        ) {
            return true; // direct exit, since OR rule for first param works
        }

        if (
            empty($rule->ccr_or_overdue_principal_from)
            && !empty($rule->ccr_or_overdue_principal_to)
            && $totalOverdueAmount <= floatval($rule->ccr_or_overdue_principal_to)
        ) {
            return true; // direct exit, since OR rule for first param works
        }

        if (
            !empty($rule->ccr_or_overdue_principal_from)
            && !empty($rule->ccr_or_overdue_principal_to)
            && $totalOverdueAmount >= floatval($rule->ccr_or_overdue_principal_from)
            && $totalOverdueAmount <= floatval($rule->ccr_or_overdue_principal_to)
        ) {
            return true; // direct exit, since OR rule for first param works
        }


        if (null == $rule->ccr_overdue_days_from && null == $rule->ccr_overdue_days_to) {
            return true; // direct exit, since no rule defined for these fields, we cannot check AND
        }


        //////////////////////////////////////////////////////////////
        // check ig range from overdue table is in rule range
        //////////////////////////////////////////////////////////////

        // since we check range in range, we always need both limits
        $ruleFrom = (
        !empty($rule->ccr_overdue_days_from)
        || $rule->ccr_overdue_days_from == 0
            ? intval($rule->ccr_overdue_days_from) : 0
        );
        $ruleTo = (
        !empty($rule->ccr_overdue_days_to)
            ? intval($rule->ccr_overdue_days_to)
            : 9999999
        );

        $filtered = [];
        if (!empty($loanParams['ccr_overdue_data'])) {
            foreach ($loanParams['ccr_overdue_data'] as $row) {
                if (
                    !isset($row->outstanding_principal)
                    || floatval($row->outstanding_principal) <= 0
                ) {
                    continue; // skip if there is no ovedue principal
                }

                // check only only rows with period
                if (empty($row->period) || $row->period == '-') {
                    continue;
                }

                $from = null;
                $to = null;

                if (preg_match('/(от ([0-9]+) до ([0-9]+) дни)/i', $row->period, $m)) {
                    $from = (int) $m[2];
                    $to = (int) $m[3];
                }

                if (preg_match('/(над ([0-9]+) дни)/i', $row->period, $m)) {
                    $from = (int) $m[2];
                    $to = 99999; // just huge number associated with infinity
                }

                if ($from == null || $to == null) {
                    continue; // skip if did not found the range from certain period
                }

                if (
                    ($from < $ruleFrom && $to < $ruleFrom)
                    || ($from > $ruleTo && $to > $ruleTo)
                ) {
                    continue;
                }

                $filtered[] = $row;
            }

            // if nothing found for the filter, we cannot continue
            if (
                $rule->type == 'disapprove'
                && empty($filtered)
                // && empty($rule->ccr_and_overdue_principal_to)
                // && empty($rule->ccr_and_overdue_principal_from)
            ) {
                return false;
            }

            // if nothing found for the filter, we cannot continue
            if (
                $rule->type == 'approve'
                && !empty($filtered)
                && empty($rule->ccr_and_overdue_principal_to)
                && empty($rule->ccr_and_overdue_principal_from)
            ) {
                return false;
            }
        } else {
            if ($rule->type == 'disapprove') {
                return false; // exit, since our rule has mandatory check on date range
            }

            if (
                $rule->type == 'approve'
                && empty($rule->ccr_and_overdue_principal_to)
                && empty($rule->ccr_and_overdue_principal_from)
            ) {
                return true; // exit, since we do not go to any range which is COOL and do not have AND rule
            }
        }


        ///////////////////////////////
        // time to check AND rules
        ///////////////////////////////
        if (
            empty($rule->ccr_and_overdue_principal_to)
            && !empty($rule->ccr_and_overdue_principal_from) && $rule->ccr_and_overdue_principal_from != 0
            && $totalOverdueAmount < floatval($rule->ccr_and_overdue_principal_from)
        ) {
            return false; // direct exit
        }

        if (
            empty($rule->ccr_and_overdue_principal_from)
            && !empty($rule->ccr_and_overdue_principal_to)
            && $totalOverdueAmount > floatval($rule->ccr_and_overdue_principal_to)
        ) {
            return false; // direct exit
        }

        if (
            !empty($rule->ccr_and_overdue_principal_from)
            && !empty($rule->ccr_and_overdue_principal_to)
            && (
                $totalOverdueAmount < floatval($rule->ccr_and_overdue_principal_from)
                || $totalOverdueAmount > floatval($rule->ccr_and_overdue_principal_to)
            )
        ) {
            return false; // direct exit
        }


        return true;
    }

    public function saveAndProcess(
        Loan $loan,
        AutoProcessRule $rule,
        array $loanParams
    ): bool {

        // make sure that we take decision on signed loan
        if ($loan->loan_status_id != LoanStatus::SIGNED_STATUS_ID) {
            return false;
        }

        return DB::transaction(function () use ($loan, $rule, $loanParams) {
            $autoProcess = $this->saveAutoProcessAction(
                $loan,
                $rule,
                $loanParams
            );

            if (!empty($autoProcess->type)) {
                //Processing loan
                $loan = $this->processLoanBySystemAdministrator($loan);

                $data = [
                    'loan_id' => $loan->getKey(),
                    'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                    'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_AUTO_PROCESS
                ];

                //Approving
                if ($autoProcess->type == 'approve') {
                    $data['decision'] = ApproveDecision::APPROVE_DECISION_APPROVED;
                    $loan = app(ApproveLoanAction::class)->execute(DecisionDto::from($data));
                }

                //Canceling
                if ($autoProcess->type == 'disapprove') {
                    $data['decision'] = ApproveDecision::APPROVE_DECISION_CANCELED;
                    $action = app(CancelLoanAction::class);
                    $action->execute(DecisionDto::from($data));
                    $loan = $action->dbLoan();
                }
            }

            $processed = !empty($autoProcess->auto_process_id)
                && !empty($loan->loan_status_id)
                && in_array($loan->loan_status_id, [
                    LoanStatus::APPROVED_STATUS_ID,
                    LoanStatus::CANCELLED_STATUS_ID
                ]);


            // remove approve action task - used in Approve listing and in Client Card
            if ($processed) {
                $loan->removeManualProcessApproveTask();
                $loan->addMeta('autoProcess', 'Success: rule #' . $rule->rule_id);
            }


            return $processed;
        });
    }

    private function saveAutoProcessAction(
        Loan $loan,
        AutoProcessRule $rule,
        array $loanParams
    ): ?AutoProcess {
        $obj = new AutoProcess();
        $obj->client_id = $loan->client_id;
        $obj->loan_id = $loan->loan_id;
        $obj->rule_id = $rule->rule_id;
        $obj->type = $rule->type;
        $obj->rule_params = json_encode($rule->getImportantProperties());
        $obj->loan_params = json_encode($loanParams);
        $obj->save();

        return $obj;
    }


    ///////////////////////// AUTO PROCESS RULES ///////////////////////////////

    public function getRulesByFilters(
        int $limit,
        array $filters,
        array $order = ['rule_id' => 'DESC']
    ) {
        $whereRaw = [];
        $where = [
            ['auto_process_rule.deleted', '=', '0'],
        ];

        if (!empty($filters)) {
            foreach ($filters as $field => $value) {
                if (!empty($value)) {
                    if ($field == 'name') {
                        $where[] = ['auto_process_rule.' . $field, 'ILIKE', '%' . $value . '%'];
                        continue;
                    }

                    if ($field == 'office_id') {
                        $where[] = ['auto_process_rule.office_ids', 'LIKE', '%"' . $value . '"%'];
                        continue;
                    }

                    if ($field == 'created_by') {
                        $whereRaw[] = "CONCAT(a1.first_name, ' ', a1.last_name)::text ILIKE '%" . $value . "%'";
                        continue;
                    }

                    if ($field == 'updated_by') {
                        $whereRaw[] = "CONCAT(a2.first_name, ' ', a2.last_name)::text ILIKE '%" . $value . "%'";
                        continue;
                    }

                    if ($field == 'created_at' && preg_match(self::$dateRangeRegex, $value)) {
                        $extractedDates = $this->extractDates($value);
                        $where[] = [
                            'auto_process_rule.created_at',
                            '>=',
                            $extractedDates['from'],
                        ];
                        $where[] = [
                            'auto_process_rule.created_at',
                            '<=',
                            $extractedDates['to'],
                        ];
                        continue;
                    }

                    if ($field == 'updated_at' && preg_match(self::$dateRangeRegex, $value)) {
                        $extractedDates = $this->extractDates($value);
                        $where[] = [
                            'auto_process_rule.updated_at',
                            '>=',
                            $extractedDates['from'],
                        ];
                        $where[] = [
                            'auto_process_rule.updated_at',
                            '<=',
                            $extractedDates['to'],
                        ];
                        continue;
                    }

                    // type, active, new_client
                    $where[] = ['auto_process_rule.' . $field, '=', $value];
                } else {
                    if ($value == "0") {
                        $where[] = ['auto_process_rule.' . $field, '=', $value];
                    }
                }
            }
        }

        $builder = DB::table('auto_process_rule');

        $builder->select(
            DB::raw("
                auto_process_rule.*,
                CONCAT(a1.first_name, ' ', a1.last_name) as created_by,
                CONCAT(a2.first_name, ' ', a2.last_name) as updated_by
            ")
        );
        $builder->join(
            'administrator AS a1',
            'a1.administrator_id',
            '=',
            'auto_process_rule.created_by'
        );

        $builder->join(
            'administrator AS a2',
            'a2.administrator_id',
            '=',
            'auto_process_rule.created_by'
        );

        $builder->where($where);

        if (!empty($whereRaw)) {
            foreach ($whereRaw as $wr) {
                $builder->whereRaw($wr);
            }
        }

        if (!empty($order)) {
            foreach ($order as $oK => $oV) {
                $builder->orderBy($oK, $oV);
            }
        }

        return $builder->paginate($limit);
    }

    public function createRule(array $data): AutoProcessRule
    {
        if (!empty($data['office_ids'])) {
            $data['office_ids'] = json_encode($data['office_ids']);
        }

        if (!empty($data['cards1'])) {
            $data['cards1'] = json_encode($data['cards1']);
        }

        if (!empty($data['cards2'])) {
            $data['cards2'] = json_encode($data['cards2']);
        }

        $rule = new AutoProcessRule();
        $rule->fill($data);
        $rule->created_at = now();
        $rule->created_by = getAdminId();
        $rule->save();

        return $rule;
    }

    public function updateRule(
        AutoProcessRule $rule,
        array $data
    ): AutoProcessRule {
        $rule->fill($data);
        $rule->updated_at = now();
        $rule->updated_by = getAdminId();
        $rule->save();

        return $rule;
    }

    public function getAutoProcessRulesForOffice(
        int $officeId,
        string $type
    ): ?array {
        return AutoProcessRule::where([
            ['type', '=', $type],
            ['active', '=', '1'],
        ])
            ->whereRaw('office_ids::text LIKE \'%"' . $officeId . '"%\'')
            ->get()
            ->all();
    }

    ////////////////////////// AUTO PROCESS RULE HISTORY ///////////////////////

    public function getRuleHistory(AutoProcessRule $rule, int $limit = 25)
    {
        $builder = DB::table('auto_process_rule_log');
        $builder->select(
            DB::raw(
                "
            auto_process_rule_log.created_at,
            auto_process_rule_log.action,
            auto_process_rule_log.data,
            CONCAT(administrator.first_name, ' ', administrator.last_name) as created_by
        "
            )
        );
        $builder->join(
            'administrator',
            'auto_process_rule_log.created_by',
            '=',
            'administrator.administrator_id'
        );
        $builder->orderBy('log_id', 'DESC');

        return $builder->paginate($limit);
    }

    public static function getFormatedLog($log)
    {
        return (object) [
            'created_at' => $log->created_at,
            'created_by' => $log->created_by,
            'action' => $log->action,
            'data' => self::extractLogData($log->data, $log->action),
        ];
    }

    private static function extractLogData(string $data, string $action): array
    {
        $result = [];

        if ('change' == $action) {
            foreach (json_decode($data, true) as $key => $r) {
                if (preg_match("/^(\[)/", $r['old']) && preg_match("/^(\[)/", $r['new'])) {
                    $subArOld = [];
                    $subArNew = [];

                    if ($key == 'office_ids') {
                        $subArOld = getOfficesFromJson($r['old']);
                        $subArNew = getOfficesFromJson($r['new']);
                    } else {
                        foreach (json_decode($r['old'], true) as $or) {
                            $subArOld[] = underScoreToText($or);
                        }
                        foreach (json_decode($r['new'], true) as $nr) {
                            $subArNew[] = underScoreToText($nr);
                        }
                    }

                    $result[$key] = 'От: ' . implode(', ', $subArOld) . ' към: ' . implode(', ', $subArNew);
                } else {
                    $result[$key] = 'От: ' . $r['old'] . ' към: ' . $r['new'];
                }
            }
        }

        if ('add' == $action) {
            $rows = [];
            foreach (json_decode($data, true) as $key => $r) {
                if (preg_match("/^(\[)/", $r)) {
                    $subAr = [];

                    if ($key == 'office_ids') {
                        $subAr = getOfficesFromJson($r);
                    } else {
                        foreach (json_decode($r, true) as $or) {
                            $subAr[] = underScoreToText($or);
                        }
                    }

                    $result[$key] = implode(', ', $subAr);
                } else {
                    $result[$key] = $r;
                }
            }
        }

        return $result;
    }

    /**
     * Auto-approve based on pre_approved_clients row
     */
    private function preApproved(Loan $loan): bool
    {
        $preApprovedId = PreApprovedClient::where('client_id', $loan->client_id)
            ->whereRaw("'" . now() . "' BETWEEN from_date AND to_date")
            ->where('amount', '>=', $loan->amount_requested)
            ->whereJsonContains('product_ids', $loan->product_id)
            ->value('id');

        $result = false;
        if ($preApprovedId) {
            DB::transaction(function () use ($loan, $preApprovedId, &$result) {
                // make log
                PreApprovedLoan::create(['loan_id' => $loan->getKey(), 'pre_approved_id' => $preApprovedId]);

                $this->processLoanBySystemAdministrator($loan);

                // make auto approve
                // start loan processing by default admin
                $data = [
                    'loan_id' => $loan->getKey(),
                    'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                    'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_AUTO_PROCESS_PRE_APPROVED,
                    'decision' => ApproveDecision::APPROVE_DECISION_APPROVED
                ];
                app(ApproveLoanAction::class)->execute(DecisionDto::from($data));

                $result = true;
            });
        }

        return $result;
    }

    private function processLoanBySystemAdministrator(Loan $loan): Loan
    {
        return app(ProcessLoanAction::class)
            ->execute($loan, Administrator::SYSTEM_ADMINISTRATOR_ID)
            ->dbModel();
    }
}
