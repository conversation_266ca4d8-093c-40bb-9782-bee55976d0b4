<?php

namespace Modules\Head\Services;

use Illuminate\Support\Collection;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\LoanTypeRepository;

class LoanTypeService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;
    /**
     * @var LoanTypeRepository
     */
    private LoanTypeRepository $loanTypeRepository;

    /**
     * LoanTypeService constructor.
     *
     * @param LoanTypeRepository $loanTypeRepository
     */
    public function __construct(LoanTypeRepository $loanTypeRepository)
    {
        $this->loanTypeRepository = $loanTypeRepository;

        parent::__construct();
    }

    public function getAllLoanTypes()
    {
        $data =  \Cache::remember('all_loan_types', self::CACHE_TTL, function () {
            return $this->loanTypeRepository->getAll();
        });
        return $this->collect(Collection::class, $data);
    }
}
