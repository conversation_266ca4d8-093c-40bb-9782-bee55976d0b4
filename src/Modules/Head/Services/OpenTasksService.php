<?php

namespace Modules\Head\Services;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Approve\Repositories\ApproveTaskRepository;
use Modules\Collect\Repositories\CollectTaskRepository;
use Modules\Common\Enums\ClientCardRouteParamEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Models\SaleTask;
use Modules\Common\Services\BaseService;
use Modules\Sales\Repositories\SaleTaskRepository;
use Modules\Payments\Repositories\PaymentTaskRepository;
use RuntimeException;

final class OpenTasksService extends BaseService
{
    public const MODULE_SALES_NEW_APPLICATION = 'module_sales_new_application';
    public const MODULE_APPROVE_WAITING_ANSWER = 'module_approve_waiting_answer';
    public const MODULE_COLLECT_WAITING_ANSWER = 'module_collect_waiting_answer';
    public const NO_TASKS = 'no_tasks';

    public function __construct(
        private readonly ApproveTaskRepository $approveTaskRepository = new ApproveTaskRepository,
        private readonly CollectTaskRepository $collectTaskRepository = new CollectTaskRepository,
        private readonly PaymentTaskRepository $paymentTaskRepository = new PaymentTaskRepository,
        private readonly SaleTaskRepository    $saleTaskRepository = new SaleTaskRepository
    )
    {
        parent::__construct();
    }

    public function getAllClientOpenTasks(
        int $clientId,
        int $limit = 5
    ): array
    {

        $openTasksModuleApprove = $this->approveTaskRepository->getOpenTasks($clientId, $limit, true);
        $openTasksModuleCollect = $this->collectTaskRepository->getOpenTasks($clientId, $limit, true);
        $openTasksModuleSales = $this->saleTaskRepository->getOpenTasks($clientId, $limit, true);
        $openTasksModulePayment = $this->paymentTaskRepository->getOpenTasks($clientId, $limit);

        $allOpenTasks = array_merge(
            $openTasksModuleSales,
            $openTasksModuleApprove,
            $openTasksModuleCollect,
            $openTasksModulePayment
        );

        $this->sortByDateTimeField($allOpenTasks, 'created_at', 'DESC');

        $allOpenTasks = array_slice($allOpenTasks, 0, 10);

        return $this->prepareOpenTasks($allOpenTasks);
    }

    /**
     * @param array $array
     * @param string $field
     * @param string $direction
     */
    private function sortByDateTimeField(
        array  &$array,
        string $field,
        string $direction = 'ASC'
    )
    {
        $direction = strtoupper($direction);

        usort(
            $array,
            function ($a, $b) use ($field, $direction) {
                if ($direction === 'ASC') {
                    return strtotime($a->{$field}) - strtotime($b->{$field});
                } elseif ($direction === 'DESC') {
                    return strtotime($b->{$field}) - strtotime($a->{$field});
                }

                return 0;
            }
        );
    }

    /**
     * @todo remove nahui and give pizdiulei to Georgi Bojilov for this shit!!!!!
     */
    private function prepareOpenTasks(array $openTasks): array
    {
        $currentAdminId = getAdminId();

        foreach ($openTasks as $task) {

            $routeParams = [
                $task->client_id,
                $task->loan_id,
            ];
            $comeFrom = $task->come_from ?? '';

            switch ($comeFrom) {

                case ClientCardRouteParamEnum::SALES->value:
                    $routeParams[] = ClientCardRouteParamEnum::SALES->value;
                    $routeParams[] = $task->sale_task_id;

                    $text = __(
                        'sales::saleTaskTypeVars.' . $task->sale_task_type_id,
                        [
                            'amount' => intToFloat($task->amount ?? 0), // TODO
                            'percent' => $task->discount,
                            'loan_id' => $task->loan_id
                        ]
                    );

                    $task->task_is_processing = false;
                    $task->could_process = true;

                    if ($task->status == SaleTask::SALE_TASK_STATUS_NEW) {
                        $text .= __('head::clientCard.openTaskNew');
                    }
                    if ($task->status == SaleTask::SALE_TASK_STATUS_PROCESSING) {

                        if ($task->processed_by == $currentAdminId) {
                            $task->task_is_processing = true;
                        } else {
                            $task->could_process = false;
                        }

                        $text .= __('head::clientCard.openTaskProcessing');
                        $text .= $this->getProcessingAdminName($task->processed_by);
                    }

                    if (!empty($task->last_decision_id)) {
                        $text .= ' - ' . __('head::clientCard.openTaskExit');
                        $text .= ' ' . __('sales::saleDecision.' . $task->last_decision_id);
                    }

                    break;

                case ClientCardRouteParamEnum::APPROVE->value:
                    $routeParams[] = ClientCardRouteParamEnum::APPROVE->value;

                    $text = __('head::clientCard.request') . $task->loan_id;

                    if ($task->loan_status_id === LoanStatus::SIGNED_STATUS_ID) {
                        $text .= __('head::clientCard.openTaskNew');
                    } elseif ($task->loan_status_id === LoanStatus::PROCESSING_STATUS_ID) {
                        $text .= __('head::clientCard.openTaskProcessing');
                        $text .= $this->getProcessingAdminName($task->last_status_update_administrator_id);
                    }

                    if (!is_null($task->approve_decision_name)) {
                        $text .= ' - ' . __('head::clientCard.openTaskExit');
                        $text .= ' ' . __('approve::approveDecision.' . $task->approve_decision_name);
                    }

                    $taskIsProcessing = $task->loan_status_id === LoanStatus::PROCESSING_STATUS_ID;
                    $isCurrentAdminProcessing =
                        $task->last_status_update_administrator_id === $currentAdminId;
                    if ($taskIsProcessing && $isCurrentAdminProcessing) {
                        $taskIsProcessing = false;
                    }
                    $task->processed_by = null;
                    if ($task->loan_status_id === LoanStatus::PROCESSING_STATUS_ID) {
                        $task->processed_by = $task->last_status_update_administrator_id;
                    }
                    $task->task_is_processing = $taskIsProcessing;

                    $task->could_process = (
                    $task->loan_status_id === LoanStatus::SIGNED_STATUS_ID
                    || (
                        $task->loan_status_id === LoanStatus::PROCESSING_STATUS_ID
                        && $task->last_status_update_administrator_id === $currentAdminId
                    )
                        ? true
                        : false
                    );

                    break;

                case ClientCardRouteParamEnum::COLLECT->value:
                    $routeParams[] = ClientCardRouteParamEnum::COLLECT->value;
                    $routeParams[] = $task->bucket_task_id;

                    // скоро падеж
                    if ($task->bucket_id === 0) {
                        $text = __('head::clientCard.dueDateComming');
                    } else {
                        $from = $task->from_days;
                        $to = $task->to_days;
                        $text = __('head::clientCard.overdue') . " - $from - $to дни ";

                        $lastDecision = $task->last_decision ? ' ' . __($task->last_decision) : '';
                    }

                    $task->could_process = false;
                    $task->task_is_processing = false;

                    if ($task->status === BucketTask::STATUS_NEW) {
                        $text .= __('head::clientCard.openTaskNew');
                        $task->could_process = true;
                        $task->processed_by = null;
                    } elseif ($task->status === BucketTask::STATUS_PROCESSING) {
                        $text .= __('head::clientCard.openTaskProcessing');
                        $text .= $this->getProcessingAdminName($task->processed_by);

                        if ($task->processed_by == $currentAdminId) {
                            $task->could_process = true;
                        }

                        $task->task_is_processing = true;
                    }

                    if (!empty($lastDecision)) {
                        $text .= ' - ' . __('head::clientCard.openTaskExit') . $lastDecision;
                    }

                    break;

                case ClientCardRouteParamEnum::PAYMENT->value:
                    $routeParams[] = ClientCardRouteParamEnum::PAYMENT->value;
                    $routeParams[] = $task->payment_task_id;

                    $task->task_is_processing = false;
                    $task->could_process = true;

                    $text = $task->type->label();
                    if ($task->status->value == 'new') {
                        $text .= __('head::clientCard.openTaskNew');
                        $task->could_process = true;
                    }
                    if ($task->status->value == 'processing') {

                        if ($task->handled_by == $currentAdminId) {
                            $task->task_is_processing = true;
                        } else {
                            $task->could_process = false;
                        }

                        $text .= __('head::clientCard.openTaskProcessing');
                        $text .= $this->getProcessingAdminName($task->handled_by);
                    }

                    $exit = $task->getLastAttemptDecisionLabel();
                    if (!empty($exit)) {
                        $text .= ' - ' . __('head::clientCard.openTaskExit');
                        $text .= ' ' . $exit;
                    }

                    break;

                default:
                    throw new RuntimeException(__('messages.errorOccurred'));
            }

            $task->text = $text;
            $task->route_params = $routeParams;

            if ($routeParams[2] === 'approve') {
                $task->route = route('approve.loan-decision.process', $routeParams[1]);
            } else if ($comeFrom === 'payment') {
                if ($task instanceof PaymentTask && $task->payment_method_id === PaymentMethod::PAYMENT_METHOD_CASH) {
                    $task->route = route('head.loan-task.index', [
                        $task->client_id,
                        $task->loan_id,
                        'approve'
                    ]);
                } else {
                    $task->route = route('payment.paymentsTasks.processing', $task->payment_task_id);
                }
            } else {
                $task->route = route('head.clients.cardProfile', $routeParams);
            }
        }

        return $openTasks;
    }

    private function getProcessingAdminName(int $adminId): string
    {
        $admin = Administrator::where('administrator_id', $adminId)->first();
        if (empty($admin->administrator_id)) {
            return '';
        }

        return $admin->getName();
    }
}
