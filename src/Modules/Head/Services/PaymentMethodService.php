<?php

namespace Modules\Head\Services;

use Illuminate\Support\Collection;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\PaymentMethodRepository;

class PaymentMethodService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    /**
     * @var PaymentMethodRepository
     */
    protected PaymentMethodRepository $paymentMethodRepository;

    /**
     * PaymentMethodService constructor.
     *
     * @param PaymentMethodRepository $paymentMethodRepository
     */
    public function __construct(PaymentMethodRepository $paymentMethodRepository)
    {
        $this->paymentMethodRepository = $paymentMethodRepository;

        parent::__construct();
    }

    public function getAllPaymentsMethods()
    {
        $data =  \Cache::remember('all_payment_methods', self::CACHE_TTL, function () {
            return $this->paymentMethodRepository->getAllPayments();
        });
        return $this->collect(Collection::class, $data);
    }
}
