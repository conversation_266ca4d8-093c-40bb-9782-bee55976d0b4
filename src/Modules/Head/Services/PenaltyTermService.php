<?php

namespace Modules\Head\Services;

use Illuminate\Http\UploadedFile;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Admin\Traits\AdminTrait;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductHistory;
use Modules\Common\Services\StorageService;
use Modules\Head\Imports\PenaltyTermImport;
use Modules\Head\Repositories\PenaltyTermRepository;
use Modules\Product\Services\ProductService;
use RuntimeException;
use Throwable;

class PenaltyTermService extends BaseTermService
{
    use AdminTrait;

    private $repo = null;

    /**
     * @var ProductService
     */
    private ProductService $productService;

    /**
     * PenaltyTermService constructor.
     *
     * @param StorageService $storageService
     */
    public function __construct(
        private readonly StorageService $storageService = new StorageService
    )
    {
        parent::__construct();
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(
        ?int  $limit,
        array $data
    )
    {
        $whereConditions = $this->getWhereConditions(
            $data,
            [],
            'product'
        );

        $this->replaceTableName(
            $whereConditions,
            'product',
            'penalty_term',
            'period_from'
        );
        $this->replaceTableName(
            $whereConditions,
            'product',
            'penalty_term',
            'period_to'
        );
        $this->replaceTableName(
            $whereConditions,
            'product',
            'penalty_term',
            'penalty_rate'
        );

        return $this->getPenaltyTermRepository()
            ->getAll($limit, $whereConditions);
    }

    public function import(UploadedFile $file, Product $product)
    {
        try {
            // Save file and create log history
            $savedFile = $this->storageService->importTerms($file, 'penalty');
            if (!$savedFile) {
                throw new RuntimeException(
                    __('head::storageService.FileIsNotSaved')
                );
            }

            // Retrieving data from a file and converting it into an array
            $data = Excel::toArray(new PenaltyTermImport(), $file);

            // Get the first element of the array
            $data = reset($data);
            // Remove product name row
            array_shift($data);

            // Fill in an array for massive insert
            $penaltyTermsData = $this->getPenaltyTermsDataForMassInsert(
                $data,
                $product->getKey(),
                $savedFile->getKey()
            );

            $oldPenaltyTerms = $product->penaltyTerms;

            $oldFile = null;
            if ($oldPenaltyTerms->isNotEmpty()) {
                $oldFile = $oldPenaltyTerms->first()->file;
                $this->logPreviousPenaltyTerms($oldPenaltyTerms);
            }

            $this->createProductHistory(
                $product,
                $savedFile,
                $oldFile,
                !empty($oldFile)
                    ? ProductHistory::TRANSLATION_KEY_PENALTY_TERM_CHANGED
                    : ProductHistory::TRANSLATION_KEY_PENALTY_TERM_ADDED
            );

            /// set max values to amount & period
            foreach ($penaltyTermsData as $key => $penaltyTermsDatum) {
                if (!$penaltyTermsDatum['period_to']) {
                    $penaltyTermsData[$key]['period_to'] = 10499;
                }
                if (!$penaltyTermsDatum['amount_to']) {
                    $penaltyTermsData[$key]['amount_to'] = 10000000;
                }
            }

            return $this->getPenaltyTermRepository()
                ->bulkCreate($penaltyTermsData);

        } catch (Throwable $t) {
            throw new RuntimeException(
                __('messages.errorOccurred'),
                $t
            );
        }
    }

    /**
     * @param array $data
     * @param int $productId
     * @param int $fileId
     *
     * @return array
     */
    private function getPenaltyTermsDataForMassInsert(
        array $data,
        int   $productId,
        int   $fileId
    ): array
    {
        // Get the periods from the file
        $allFilePeriods = array_filter($data[0]);
        // Remove the periods row
        array_shift($data);

        $newPenaltyTerms = [];
        $decreaseFactor = 0.01;

        for ($i = 1; $i <= count($allFilePeriods); $i++) {
            $periodFrom = $allFilePeriods[$i];
            $periodTo = isset($allFilePeriods[$i + 1]) ? $allFilePeriods[$i + 1] : null;

            for ($j = 0; $j < count($data); $j++) {
                $amountFrom = $data[$j][0];
                $amountTo = isset($data[$j + 1]) ? $data[$j + 1][0] : null;
                $penaltyRate = $data[$j][$i];

                $newPenaltyTerms[] = [
                    'product_id' => $productId,
                    'period_from' => $periodFrom,
                    'period_to' => $periodTo ? $periodTo - $decreaseFactor : $periodTo,
                    'amount_from' => $amountFrom,
                    'amount_to' => $amountTo ? $amountTo - $decreaseFactor : $amountTo,
                    'penalty_rate' => $penaltyRate,
                    'created_at' => now(),
                    'created_by' => getAdminId(),
                    'file_id' => $fileId,
                ];
            }
        }

        return $newPenaltyTerms;
    }

    /**
     * @param CustomEloquentCollection $penaltyTerms
     */
    private function logPreviousPenaltyTerms(CustomEloquentCollection $penaltyTerms)
    {
        foreach ($penaltyTerms as $penaltyTerm) {
            // Move to history
            $isAdded = $this->getPenaltyTermRepository()
                ->addPenaltyTermHistory($penaltyTerm);

            if (true === $isAdded) {
                $penaltyTerm->delete();
            }
        }
    }

    private function getPenaltyTermRepository(): PenaltyTermRepository
    {
        if (null === $this->repo) {
            $this->repo = app(PenaltyTermRepository::class);
        }

        return $this->repo;
    }
}
