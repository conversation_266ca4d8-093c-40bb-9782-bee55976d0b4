<?php

namespace Modules\Head\Services;

use Illuminate\Support\Collection;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\CityRepository;
use RuntimeException;

class CityService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;
    /**
     * @var CityRepository
     */
    protected CityRepository $cityRepository;

    /**
     * CityService constructor.
     *
     * @param CityRepository $cityRepository
     */
    public function __construct(CityRepository $cityRepository)
    {
        $this->cityRepository = $cityRepository;

        parent::__construct();
    }

    public function getCities(): Collection
    {
        $cities = \Cache::remember('all_cities', self::CACHE_TTL, function () {
            return $this->cityRepository->getAll();
        });
        $cities = $this->collect(\Illuminate\Database\Eloquent\Collection::class, $cities);

        if ($cities->isEmpty()) {
            throw new RuntimeException(__('head::cityCrud.citiesNotFound'));
        }

        return $cities;
    }

    public function getCitiesByName(string $name): Collection
    {
        return $this->cityRepository->getCityByName($name);
    }
}
