<?php

namespace Modules\Head\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\A4EReport;
use Modules\Common\Models\Loan;
use Modules\Common\Traits\AutoProcessVerification;
use Modules\Head\Jobs\AutoProcessJob;

class RetryAutoProcessJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, AutoProcessVerification;

    private const META_KEY = 'RetryAutoProcessJob';

    public $tries = 3;
    public $timeout = 90; // seconds

    public function __construct(protected Loan $loan) {}

    public function handle()
    {
        if ($this->attempts() > $this->tries) {
            throw new \Exception('Max attempts exceeded for.');
        }

        try {

            // main params
            $try = $this->attempts();
            $loan = $this->loan;
            $a4eReport = $loan->getA4eReport();
            $creditLimit = $loan->getCreditLimit();


            $loan->addMeta(self::META_KEY . '(' . $this->attempts() . ')',  'Start!');


            // validation
            [$metaMsg, $errorMsg, $shouldRetry] = $this->getAutoProcessValidationErrors(
                $loan,
                $a4eReport,
                $creditLimit,
                ($try == $this->tries) // true for last attempt
            );

            // looks like we have a problem
            if (!empty($metaMsg)) {

                if ($shouldRetry && $try <= $this->tries) {
                    $loan->addMeta(self::META_KEY . '(' . $this->attempts() . ')',  'Fail: ' . $metaMsg . '. Re-try started.');

                    $msg = "Fail to autoprocess(" . $metaMsg . "), started re-try(" . $try . ")"
                        . ". Loan: " . $loan->loan_id
                        . ", A4EReport: " . ($a4eReport->a4e_report_id ?? 'None')
                        . ", CreditLimit: " . ($creditLimit->credit_limit_id ?? 'None');
                    Log::channel('autoProcessError')->debug($msg);

                    $this->release(20);
                    return;
                }

                throw new \Exception('Loan can not be autoprocessed: ' . $metaMsg);
            }

	       $loan->addMeta(
                self::META_KEY . '(' . $this->attempts() . ')',
                'Succes: start autoprocess, a4e #'
                . ($a4eReport->a4e_report_id ?? 'None')
                . ', cr #' . ($creditLimit->credit_limit_id ?? 'None')
            );

            AutoProcessJob::dispatch($loan, $a4eReport, $creditLimit)->onQueue('reports');

            return true;

        } catch (\Throwable $e) {

            $this->loan->addMeta(self::META_KEY . '(' . $this->attempts() . ')', 'Fail: ' . getMetaException($e->getMessage()));

            $this->loan->updateSkipTill(null, 'RetryAutoProcessJob - failed');

            $msg = 'Error RetryAutoProcessJob(' . $this->attempts() . ') failed'
                . ', loan #' . $this->loan->loan_id
                . ', msg: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();
            Log::channel('autoProcessError')->debug($msg);

            $this->fail();

            return ;
        }
    }
}
