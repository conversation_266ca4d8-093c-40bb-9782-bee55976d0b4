<?php

namespace Modules\Head\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ClientActualStats;
use Modules\Common\Models\ClientActualStatsHistory;

class DailyArchiveClientActualStatsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public function __construct(
        public Collection $clientActualStats
    )
    {
        //
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        $rowsToArchive = $this->clientActualStats->map(function (ClientActualStats $clientActualStats){
            $data = $clientActualStats->toArray();

            /// tuned format
            $data['created_at'] = Carbon::parse($data['created_at'])->format('Y-m-d H:i:s');
            $data['updated_at'] = Carbon::parse($data['updated_at'])->format('Y-m-d H:i:s');

            /// set default vals for nullable fields
            $data['credit_limit'] = (int) $data['credit_limit'] ?? 0;
            $data['lifetime_value_total'] = $data['lifetime_value_total'] ?? 0;

            //// set archived date
            $data['archived_at'] = now();
            $data['archived_by'] = Administrator::SYSTEM_ADMINISTRATOR_ID;

            return $data;
        });

        $ids = $rowsToArchive->pluck('client_actual_stats_id');


        DB::beginTransaction();
        try {
            ClientActualStatsHistory::insert($rowsToArchive->toArray());
            ClientActualStats::query()->whereIn('client_actual_stats_id', $ids)->update(['date' => Carbon::now()]);
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }
}
