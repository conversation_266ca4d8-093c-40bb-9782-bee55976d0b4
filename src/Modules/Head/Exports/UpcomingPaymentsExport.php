<?php

declare(strict_types=1);

namespace Modules\Head\Exports;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\Exportable;

final class UpcomingPaymentsExport implements FromQuery, WithMapping, WithHeadings, WithChunkReading
{
    use Exportable;

    public function __construct()
    {
        // override the default chunk size for this export only
        Config::set('excel.exports.chunk_size', 5000);
    }

    public function query(): Builder
    {
        $now   = Carbon::now();
        $start = $now->copy()->startOfMonth()->toDateString(); // e.g. "2025-06-01"
        $end   = $now->copy()->endOfMonth()->toDateString();   // e.g. "2025-06-30"

        return DB::table('loan as l')
            ->join('installment as i', 'i.loan_id', 'l.loan_id')
            ->join('loan_actual_stats as las', 'las.loan_id', 'l.loan_id')
            ->join('product as p', 'p.product_id', 'l.product_id')
            ->join('client as c', 'c.client_id', 'l.client_id')
            ->leftJoin('office as o', 'o.office_id', 'l.office_id')
            ->leftJoin('affiliate_attempt as aa','aa.loan_id','l.loan_id')
            ->leftJoin('affiliate as a', 'a.id', 'aa.affiliate_id')
            ->leftJoin('client_idcard as ci', function($join){
                $join->on('ci.client_id','l.client_id')
                     ->where('ci.active',1)
                     ->where('ci.last',1);
            })
            ->leftJoin('city as city_idcard','city_idcard.city_id','ci.idcard_issued_id')
            ->leftJoin('mvr_report_pivot as mrp', function($j){
                $j->on('mrp.loan_id','l.loan_id')->where('mrp.last',1);
            })
            ->leftJoin('client_address as ca', function($join){
                $join->on('ca.client_id','l.client_id')
                     ->where('ca.type', 'current')
                     ->where('ca.active',1)
                     ->where('ca.last',1);
            })
            ->leftJoin('consultant as ct','ct.consultant_id','l.consultant_id')
            ->whereIn('l.loan_status_id',[6,9])
            ->whereBetween('i.due_date', [$start, $end])
            ->selectRaw('
                l.loan_id as "Номер на кредит",
                p.name as "Продукт",
                round(l.amount_approved::numeric/100, 2) as "Отпусната сума",
                (
                    las.due_amount_total_principal
                    + las.due_amount_total_interest
                    + las.due_amount_total_penalty
                ) as "Номинална стойност",
                las.total_installments_count as "Общ брой на вноски",
                las.outstanding_amount_total as "Сумата, за редовно погасяване",
                i.due_date as "Дата на падеж",
                i.seq_num,
                (i.principal + i.interest + i.penalty) as "Размер на вноска",
                las.accrued_amount_total as "Сума за погасяване",
                las.last_paid_date as "Дата на последно плащане",
                (
                    i.paid_principal
                    + i.paid_interest
                    + i.paid_penalty
                    + i.paid_late_interest
                    + i.paid_late_penalty
                ) as "Събрана сума по текуща вноска",
                CASE
                    WHEN i.due_date >= CURRENT_DATE THEN \'Активна\'
                    WHEN (CURRENT_DATE - i.due_date) <= 30 THEN \'Забавена\'
                    ELSE \'Просрочена\'
                END as "Статус на вноската",
                c.pin as "ЕГН на клиент",
                COALESCE(c.first_name, \'\') || \' \' || COALESCE(c.middle_name, \'\') || \' \' || COALESCE(c.last_name, \'\') AS "Име на клиент",
                ca.address as "CurrentAddress",
                CASE
                    WHEN l.outer_collector = 1 OR l.loan_status_id = 9 THEN ct.name
                    ELSE \'\'
                END as "Агенция за събиране",
                CASE
                    WHEN l.office_id != 1 THEN \'офис \' || o.name
                    WHEN l.source = \'affiliate\' THEN \'афилиейт \' || a.name
                    ELSE l.source
                END as "Канал на кандидатстване",
                CASE
                    WHEN las.current_overdue_days > 0 THEN \'Просрочен\'
                    ELSE \'Нормален\'
                END as "Статус на кредита",
                CASE
                    WHEN las.previous_loans_count > 0 THEN \'REPEAT\'
                    WHEN las.previous_applications_count > 0 THEN \'REPEAT WITHOUT HIST\'
                    ELSE \'NEW\'
                END as "Тип клиент",
                l.activated_at::DATE as "Дата на усвояване",
                CASE
                    WHEN ci.idcard_issued_id is not null THEN \'МВР \' || city_idcard.name
                    ELSE \'МВР\'
                END as "МВР",
                mrp.district as "Area",
                \'\' as "LastCollectionAssingmentDate",
                mrp.municipality as "RXCity",
                las.contract_end_date as "Дата на матуриране"
            ')
            ->orderBy('i.due_date')
            ->orderBy('l.loan_id');
    }

    public function map($row): array
    {
        return [
            $row->{"Номер на кредит"},
            $row->{"Продукт"},
            $row->{"Отпусната сума"},
            $row->{"Номинална стойност"},
            $row->{"Общ брой на вноски"},
            $row->{"Сумата, за редовно погасяване"},
            $row->{"Дата на падеж"},
            $row->seq_num,
            $row->{"Размер на вноска"},
            $row->{"Сума за погасяване"},
            $row->{"Дата на последно плащане"},
            $row->{"Събрана сума по текуща вноска"},
            $row->{"Статус на вноската"},
            $row->{"ЕГН на клиент"},
            $row->{"Име на клиент"},
            $row->{"CurrentAddress"},
            $row->{"Агенция за събиране"},
            $row->{"Канал на кандидатстване"},
            $row->{"Статус на кредита"},
            $row->{"Тип клиент"},
            $row->{"Дата на усвояване"},
            $row->{"МВР"},
            $row->{"Area"},
            $row->{"LastCollectionAssingmentDate"},
            $row->{"RXCity"},
            $row->{"Дата на матуриране"},
        ];
    }

    public function headings(): array
    {
        return [
            'Номер на кредит',
            'Продукт',
            'Отпусната сума',
            'Номинална стойност',
            'Общ брой на вноски',
            'Сумата, за редовно погасяване',
            'Дата на падеж',
            'Вноска поред по погасителен план',
            'Размер на вноска',
            'Сума за погасяване на всички неплатени и падежирали вноски',
            'Дата на последно плащане',
            'Събрана сума по текуща вноска',
            'Статус на вноската',
            'ЕГН на клиент',
            'Име на клиент',
            'CurrentAddress',
            'Агенция за събиране',
            'Канал на кандидатстване',
            'Статус на кредита',
            'Тип клиент',
            'Дата на усвояване',
            'МВР',
            'Area',
            'LastCollectionAssingmentDate',
            'RXCity',
            'Дата на матуриране',
        ];
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
