<?php

declare(strict_types=1);

namespace Modules\Head\Exports;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\Exportable;

final class ClientsInOverdueExport implements FromQuery, WithMapping, WithHeadings, WithChunkReading
{
    use Exportable;

    public function __construct()
    {
        // override the default chunk size for this export only
        Config::set('excel.exports.chunk_size', 5000);
    }

    public function query(): Builder
    {
        return DB::table('loan as l')
            ->join('loan_actual_stats as las', 'las.loan_id', 'l.loan_id')
            ->join('product as p', 'p.product_id', 'l.product_id')
            ->join('client as c', 'c.client_id', 'l.client_id')
            ->leftJoin('office as o', 'o.office_id', 'l.office_id')
            ->leftJoin('affiliate_attempt as aa','aa.loan_id','l.loan_id')
            ->leftJoin('affiliate as a', 'a.id', 'aa.affiliate_id')
            ->leftJoin('loan_bucket as lb', 'lb.loan_id', 'l.loan_id')
            ->leftJoin('consultant as ct','ct.consultant_id','l.consultant_id')
            ->leftJoin('mvr_report_pivot as mrp', function($j){
                $j->on('mrp.loan_id','l.loan_id')->where('mrp.last',1);
            })
            ->leftJoin('client_idcard as ci', function($join){
                $join->on('ci.client_id','l.client_id')
                     ->where('ci.active',1)
                     ->where('ci.last',1);
            })
            ->leftJoin('city as city_idcard', 'city_idcard.city_id', 'ci.idcard_issued_id')
            ->leftJoin('client_address as ca', function($join){
                $join->on('ca.client_id','l.client_id')
                     ->where('ca.type', 'current')
                     ->where('ca.active',1)
                     ->where('ca.last',1);
            })
            ->whereIn('l.loan_status_id',[6,9])
            ->where('las.current_overdue_days','>',0)
            ->selectRaw('
                l.loan_id as "Номер на кредит",
                l.loan_status_id,
                p.name as "Продукт",
                round(l.amount_approved::numeric/100, 2) as "Отпусната сума",
                las.total_installments_count as "Общ брой вноски",
                (
                    las.due_amount_total_principal
                    + las.due_amount_total_interest
                    + las.due_amount_total_penalty
                ) as "Номинална стойност",
                las.accrued_amount_total as "Падежирала и неплатена сума",
                -- inst.penalty_till_now as "Размер на вноска по обезпечение",
                -- inst.max_due_date_till_now as "Дата на падеж",
                -- inst.min_due_date as "Неплатена вноска поред",
                -- inst.inst_amount as "Размер на вноска с обезпечение",
                las.repaid_amount_total as "Платена сума",
                las.last_paid_date as "Дата на последно плащане",
                CASE
                    WHEN las.current_overdue_days = 0 THEN \'Активен\'
                    WHEN las.current_overdue_days <= 30 THEN \'Забавен\'
                    ELSE \'Просрочен\'
                END as "Статус на вноската",
                COALESCE(c.first_name, \'\') || \' \' || COALESCE(c.middle_name, \'\') || \' \' || COALESCE(c.last_name, \'\') AS "Име на клиент",
                ca.address as "CurrentAddress",
                c.pin as "ЕГН на клиент",
                las.current_overdue_days as "Дни в забава",
                las.overdue_installments as "Брой вноски в забава",
                CASE
                    WHEN l.office_id != 1 THEN \'офис \' || o.name
                    WHEN l.source = \'affiliate\' THEN \'афилиейт \' || a.name
                    ELSE l.source
                END as "Канал на кандидатстване",
                CASE
                    WHEN l.outer_collector = 1 OR l.loan_status_id = 9 THEN ct.name
                    ELSE \'\'
                END as "Агенция за събиране",
                c.phone as "Мобилен телефон на клиента",
                \'\' as "Реална дата на цесия",
                CASE
                    WHEN las.previous_loans_count > 0 THEN \'REPEAT\'
                    WHEN las.previous_applications_count > 0 THEN \'REPEAT WITHOUT HIST\'
                    ELSE \'NEW\'
                END as "Тип клиент",
                l.activated_at::DATE as "Дата на усвояване",
                lb.bucket_id as "Bucket",
                CASE
                    WHEN ci.idcard_issued_id is not null THEN \'МВР \' || city_idcard.name
                    ELSE \'МВР\'
                END as "МВР",
                mrp.district as "Area",
                \'\' as "LastCollectionAssingmentDate",
                mrp.municipality as "RXCity",
                las.contract_end_date as "Дата на матуриране"
            ')
            ->orderBy('l.loan_id');
    }

    public function map($row): array
    {
        $loanId = (int) $row->{"Номер на кредит"};
        $inst = $this->getInstallmentCounts($loanId);
//        $pay = $this->getPaidAmount($loanId);

        $bucketId = $row->{"Bucket"};
        $bucketText = '';
        if (is_numeric($bucketId)) {
            $bucketText = __('collect::buckets.bucket_' . $row->{"Bucket"});
        }
        if (empty($bucketText) && $row->loan_status_id == 9) {
            $bucketText = 'Отписан/стар';
        }

        return [
            $row->{"Номер на кредит"},
            $row->{"Продукт"},
            $row->{"Отпусната сума"},
            $row->{"Общ брой вноски"},
            $row->{"Номинална стойност"},
            $row->{"Падежирала и неплатена сума"},
            ($row->{"Номинална стойност"} - $row->{"Платена сума"}), // Номинална стойност за DF
            $inst->penalty_till_now, //$row->{"Размер на вноска по обезпечение"},
            $inst->max_due_date_till_now, //$row->{"Дата на падеж"},
            $inst->min_due_date, //$row->{"Неплатена вноска поред"},
            $inst->inst_amount, //$row->{"Размер на вноска с обезпечение"},
            $row->{"Платена сума"}, //$row->{"Платена сума"}, // $pay->paid_amount
            $row->{"Дата на последно плащане"},
            $row->{"Статус на вноската"},
            $row->{"Име на клиент"},
            $row->{"CurrentAddress"},
            $row->{"ЕГН на клиент"},
            $row->{"Дни в забава"},
            $row->{"Брой вноски в забава"}, //$row->{"Брой вноски в забава"}, // $inst->count_overdue
            $row->{"Канал на кандидатстване"},
            $row->{"Агенция за събиране"},
            $row->{"Мобилен телефон на клиента"},
            $row->{"Реална дата на цесия"},
            $row->{"Тип клиент"},
            $row->{"Дата на усвояване"},
            $bucketText,
            $row->{"МВР"},
            $row->{"Area"},
            $row->{"LastCollectionAssingmentDate"},
            $row->{"RXCity"},
            $row->{"Дата на матуриране"},
        ];
    }

    public function headings(): array
    {
        return [
            'Номер на кредит',
            'Продукт',
            'Отпусната сума',
            'Общ брой вноски',
            'Номинална стойност',
            'Падежирала и неплатена сума',
            'Номинална стойност за DF',
            'Размер на вноска по обезпечение',
            'Дата на падеж',
            'Неплатена вноска поред по погасителен план',
            'Размер на вноска с обезпечение',
            'Платена сума',
            'Дата на последно плащане',
            'Статус на вноската',
            'Име на клиент',
            'CurrentAddress',
            'ЕГН на клиент',
            'Дни в забава',
            'Брой вноски в забава',
            'Канал на кандидатстване',
            'Агенция за събиране',
            'Мобилен телефон на клиента',
            'Реална дата на цесия',
            'Тип клиент',
            'Дата на усвояване',
            'Bucket',
            'МВР',
            'Area',
            'LastCollectionAssingmentDate',
            'RXCity',
            'Дата на матуриране',
        ];
    }

    public function chunkSize(): int
    {
        return 5000;
    }

    private function getInstallmentCounts(int $loanId)
    {
        $sql = <<<'SQL'
            SELECT
              coalesce(
                sum(i.rest_penalty)
                  FILTER (WHERE i.paid = 0 AND i.due_date <= CURRENT_DATE),
                0
              ) AS penalty_till_now,
              max(i.due_date) FILTER (WHERE i.paid = 0 AND i.due_date <= CURRENT_DATE) AS max_due_date_till_now,
              min(i.due_date) FILTER (WHERE i.paid = 0) AS min_due_date,
              (array_agg(
                  i.principal + i.interest + i.penalty
                  ORDER BY i.seq_num
              ))[1] AS inst_amount
            FROM installment i
            WHERE i.loan_id = ?
        SQL;

        // Returns a stdClass with the five columns as properties
        return DB::selectOne($sql, [$loanId]);
    }

    private function getPaidAmount(int $loanId)
    {
        $sql = <<<'SQL'
            SELECT coalesce(round(sum(p.amount)::numeric/100, 2), 0) as paid_amount
            FROM payment p
            WHERE
                p.loan_id = ?
                AND p.direction = 'in'
                AND p.status = 'delivered'
                AND p.active = 1
        SQL;

        // Returns a stdClass with the five columns as properties
        return DB::selectOne($sql, [$loanId]);
    }
}
