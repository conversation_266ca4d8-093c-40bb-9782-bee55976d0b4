<?php

namespace Modules\Head\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Modules\Common\Models\BankAccount;
use Modules\Common\Models\BlockReason;
use Modules\Common\Models\City;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Models\Contact;
use Modules\Common\Models\DeleteReason;
use Modules\Common\Models\File;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\Loan;
use Modules\Common\Models\SaleTask;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The module namespace to assume when generating URLs to actions.
     *
     * @var string
     */
    protected $moduleNamespace = 'Modules\Head\Http\Controllers';

    /**
     * Called before routes are registered.
     *
     * Register any model bindings or pattern based filters.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Route::model('file', File::class);
        Route::model('loan', Loan::class);
        Route::model('client', Client::class);
        Route::model('contact', Contact::class);
        Route::model('guarant', Guarant::class);
        Route::model('clientPhone', ClientPhone::class);
        Route::model('clientAddress', ClientAddress::class);
        Route::model('bankAccount', BankAccount::class);
        Route::model('blockReason', BlockReason::class);
        Route::model('deleteReason', DeleteReason::class);
        Route::model('city', City::class);
        Route::model('saleTask', SaleTask::class);
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/web.php'));

        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/clientCard.php'));

        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/loan.php'));

        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/bank.php'));

        Route::middleware(['web', 'auth'])
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/client.php'));

        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/interestTerm.php'));

        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/penaltyTerm.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
            ->middleware('api')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Head', '/Routes/api.php'));
    }
}
