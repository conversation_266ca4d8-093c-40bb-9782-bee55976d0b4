<?php

namespace Modules\Head\Interfaces;

use Carbon\Carbon;
use RuntimeException;
use StikCredit\Calculators\Calculator;

abstract class PaymentPlanCalculator
{
    protected const MONTH_DAYS = 30;

    protected const DIVIDERS = [
        30 => 12,
        14 => 26,
        7 => 52,
        1 => 360,
    ];

    abstract public function generatePaymentPlan(
        ?int $clientId,
        ?int $loanId,
        float $amount,
        int $period,
        int $periodDays,
        float $yearInterest,
        float $yearPenalty,
        Carbon $startDate,
        float $discount
    ): array;

    /**
     * Discount must be in % (5, 10, 50, etc)
     *
     * @param float $sum
     * @param float $discount
     *
     * @return float|int
     */
    protected function calcWithDiscount(float $sum, float $discount)
    {
        return Calculator::round($sum * (1 - $discount / 100), 4);
    }

    protected function getDivider(int $periodDays)
    {
        if (!isset(self::DIVIDERS[$periodDays])) {
            throw new RuntimeException('Divider not set.');
        }

        return self::DIVIDERS[$periodDays];
    }
}
