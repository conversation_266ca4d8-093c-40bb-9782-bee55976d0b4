<?php

namespace Modules\Payments\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Payment;
use Modules\Payments\Application\Actions\DeleteUndistributedPaymentAction;
use Modules\Payments\Application\Actions\ManualPaymentDataAction;
use Modules\Payments\Application\Actions\ManualPaymentIndexDataAction;
use Modules\Payments\Application\Actions\ManualPaymentSaveAction;
use Modules\Payments\Http\Dto\LoanInfoDTO;
use Modules\Payments\Http\Requests\LoadLoansByClientIdRequest;
use Modules\Payments\Http\Requests\LoadLoansInfoRequest;
use Modules\Payments\Http\Requests\ManualPaymentClientRequest;
use Modules\Payments\Http\Requests\ManualPaymentSaveRequest;

class ManualPaymentController extends BaseController
{
    public function index(
        ManualPaymentClientRequest   $clientRequest,
        ManualPaymentIndexDataAction $indexDataAction
    ): View|RedirectResponse {

        $data = $indexDataAction->execute($clientRequest->validated());

        if (!empty($data['redirectRoute'])) {
            return redirect()
                ->route($data['redirectRoute'])
                ->with('success', __('payments::paymentTask.paymentTaskAlreadyProcessed'));
        }

        return view('payments::manual-payments.index', $data);
    }

    public function storeManualPayment(
        ManualPaymentSaveRequest $request,
        ManualPaymentSaveAction  $manualPaymentSaveAction
    ): JsonResponse {

        if (count($request->messages())) {
            return response()->json($request->messages());
        }

        $data = $request->validated();
        $data['admin_id'] = getAdminId();
        $data['payment_amount'] = floatToInt($data['payment_amount']);
        $data['loanPaymentAmount'] = $data['loanPaymentAmount'] ?? [];

        /// if no have loanAction return false, this case is valid when we try to make early repayment with smaller amount
        /// ManualDataAction throws error with message smallAmountForTactic, and remove loanAction data from incoming array.
        if (!$request->has('loanAction')) {
            return response()->json([
                'status' => false,
                'message' => __('payments::manualPayment.smallAmountForTactic'),
            ]);
        }

        $resp = $manualPaymentSaveAction->execute($data);

        if ($resp) {
            $downloadRoute = '';
            $payments = $manualPaymentSaveAction->payments;
            if (!empty($payments) && count($payments) == 1 && !empty($payments[0]->payment_id)) {
                /** @var \Modules\Common\Models\Payment $payment */
                $payment = $payments[0];
                if (!empty($payment->cash_operational_transaction_id)) {
                    $downloadRoute = $payment->getDownloadTransactionDocLink();
                }
            }

            session()->flash('success', __('payments::payments.paymentSuccessfullyCreated'));
            return response()->json([
                'status' => true,
                'reload' => true,
                'downloadRoute' => $downloadRoute,
            ]);
        }


        session()->flash('error', __('Oops!!! Something wrong please contact your admin.'));
        return response()->json([
            'status' => false,
            'refresh' => false,
        ]);
    }

    public function loadLoansByClientId(
        LoadLoansByClientIdRequest $request,
        ManualPaymentDataAction    $paymentDataAction
    ): JsonResponse {

        if (is_null($request->validated('clientIds'))) {
            return response()->json([]);
        }

        $data = $paymentDataAction->executeLoadLoansByClientId($request->validated('clientIds'));

        return response()->json($data);
    }

    public function loadLoansInfo(
        LoadLoansInfoRequest    $request,
        ManualPaymentDataAction $paymentDataAction
    ): View {

        try {
            $data = [];
            if (!is_null($request->validated('selectedLoanIds'))) {
                $reqData = $request->validated();
                $reqData['payment_amount'] = floatToInt($request->validated('payment_amount'));

                $loanInfoDto = LoanInfoDTO::from($reqData);

                $data = $paymentDataAction->executeLoadLoansInfo($loanInfoDto);
                $data['loanAction'] = $request->validated('loanAction');
            }
        } catch (\Throwable $e) {
            $data = [
                'status' => false,
                'error' => $e->getMessage()
            ];
        }

        return view('payments::manual-payments.loan-info', $data);
    }

    public function deleteUndistributedPayment(Payment $payment, DeleteUndistributedPaymentAction $action): RedirectResponse
    {
        $action->execute($payment);
        return redirect(route('payment.paymentsTasks.list'));
    }
}
