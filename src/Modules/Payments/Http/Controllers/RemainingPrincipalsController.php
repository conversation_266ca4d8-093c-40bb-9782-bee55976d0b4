<?php

namespace Modules\Payments\Http\Controllers;

use Illuminate\Http\Response;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Payments\Application\Actions\RemainingPrincipalsDataAction;
use Modules\Payments\Http\Requests\RemainingPrincipalsSearchRequest;

class RemainingPrincipalsController extends BaseController
{
    private string $indexRoute = 'remaining-principals.index';

    public function __construct() {
        parent::__construct();
    }

    public function index(
        RemainingPrincipalsSearchRequest $request,
        RemainingPrincipalsDataAction $action
    ): View {

        $filters = $request->validated();
        $data = $action->execute($filters);

        return view('payments::remaining-principals.list', $data);
    }

    public function export(
        RemainingPrincipalsSearchRequest $request,
        RemainingPrincipalsDataAction $action
    ): Response {
        $filters = $request->validated();
        $exportData = $action->export($filters);

        // Create a response with the file content
        $fileContent = file_get_contents($exportData['file_path']);

        // Clean up the temporary file
        unlink($exportData['file_path']);

        return response($fileContent)
            ->header('Content-Type', $exportData['content_type'])
            ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
            ->header('Cache-Control', 'max-age=0');
    }
}
