<?php

namespace Modules\Payments\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\PaymentTaskDecision;
use Modules\Payments\Services\PaymentTaskDecisionService;
use Modules\Sales\Http\Requests\SaleDecisionCrudRequest;

class PaymentTaskDecisionController extends BaseController
{
    private const INDEX_ROUTE = 'payments.paymentTaskDecision.list';

    private PaymentTaskDecisionService $paymentTaskDecisionService;

    public function __construct(PaymentTaskDecisionService $paymentTaskDecisionService)
    {
        $this->paymentTaskDecisionService = $paymentTaskDecisionService;

        parent::__construct();
    }

    /**
     * @return RedirectResponse|View
     * @throws Exception
     */
    public function list()
    {
        return view(
            'payments::payment-task-decisions.list',
            [
                'paymentTaskDecisions' => $this->refresh(),
                'types' => $this->paymentTaskDecisionService->getTypes(),
            ]
        );
    }

    /**
     * @return Renderable|RedirectResponse
     * @throws Exception
     */
    public function create()
    {
        $types = $this->paymentTaskDecisionService->getTypes();

        return view(
            'payments::payment-task-decisions.crud',
            compact('types'),
        );
    }

    public function store(SaleDecisionCrudRequest $request): RedirectResponse
    {
        $this->paymentTaskDecisionService->store($request->validated());

        return redirect()
            ->route(self::INDEX_ROUTE)
            ->with(
                'success',
                __('payments::paymentTaskDecision.paymentTaskDecisionCreationSuccess')
            );
    }

    /**
     * @return Application|Factory|View
     * @throws Exception
     */
    public function edit(PaymentTaskDecision $paymentTaskDecision)
    {
        $types = $this->paymentTaskDecisionService->getTypes();

        return view(
            'payments::payment-task-decisions.crud',
            compact('paymentTaskDecision', 'types'),
        );
    }

    public function update(
        PaymentTaskDecision $paymentTaskDecision,
        SaleDecisionCrudRequest $request
    ): RedirectResponse {
        $this->paymentTaskDecisionService->update($paymentTaskDecision, $request->validated());

        return redirect()
            ->route(self::INDEX_ROUTE)
            ->with(
                'success',
                __('payments::paymentTaskDecision.paymentTaskDecisionUpdatedSuccessfully')
            );
    }

    public function delete(PaymentTaskDecision $paymentTaskDecision): RedirectResponse
    {
        $this->paymentTaskDecisionService->delete($paymentTaskDecision);

        return redirect()
            ->route(self::INDEX_ROUTE)
            ->with(
                'success',
                __('payments::paymentTaskDecision.paymentTaskDecisionDeletionSuccessfully')
            );
    }

    /**
     * @return mixed
     */
    private function refresh()
    {
        return $this->paymentTaskDecisionService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @throws Exception
     */
    public function enable(PaymentTaskDecision $paymentTaskDecision): RedirectResponse
    {
        $this->paymentTaskDecisionService->enable($paymentTaskDecision);

        return redirect()
            ->route(self::INDEX_ROUTE)
            ->with(
                'success',
                __('payments::paymentTaskDecision.paymentTaskDecisionEnabledSuccessfully')
            );
    }

    public function disable(PaymentTaskDecision $paymentTaskDecision): RedirectResponse
    {
        $this->paymentTaskDecisionService->disable($paymentTaskDecision);

        return redirect()
            ->route(self::INDEX_ROUTE)
            ->with(
                'success',
                __('payments::paymentTaskDecision.paymentTaskDecisionDisabledSuccessfully')
            );
    }
}
