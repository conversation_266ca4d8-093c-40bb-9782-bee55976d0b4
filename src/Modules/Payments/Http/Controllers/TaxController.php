<?php

namespace Modules\Payments\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Payment;
use Modules\Common\Models\Tax;
use Modules\Head\Services\ExtendLoanService;
use Modules\Head\Services\LoanService;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedForStats;
use Modules\Payments\Http\Requests\CalculateLoanExtendFeeRequest;
use Modules\Payments\Http\Requests\ExtendLoanRequest;
use Modules\Payments\Http\Requests\TaxCreateRequest;
use Modules\Payments\Services\TaxService;

class TaxController extends BaseController
{
    public function __construct(
        private readonly TaxService        $taxService,
        private readonly LoanService       $loanService,
        private readonly ExtendLoanService $extendLoanService,
    ) {
        parent::__construct();
    }

    /**
     * @throws Exception
     */
    public function addAmount(TaxCreateRequest $request): RedirectResponse
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {

            $tax = $this->taxService->create([
                'client_id' => $data['client_id'],
                'loan_id' => $data['loan_id'],
                'amount' => floatToInt($data['amount']),
                'rest_amount' => floatToInt($data['amount']),
                'currency_id' => Currency::BGN_CURRENCY_ID,
                'status' => Tax::TAX_STATUS_SCHEDULED,
                'comment' => $data['comment'],
                'isJuridical' => $data['isJuridical'] ?? 0,
                'type' => Tax::TAX_TYPE_MANUALLY_ADDED_EXPENSE
            ]);

            LoanPaymentWasReceivedForStats::dispatch(
                new Payment(),
                $tax->loan
            );

            DB::commit();

            return $this->backSuccess('payments::tax.taxSuccessfullyCreated')->withFragment('#paymentschedule');

        } catch (\Throwable $e) {
            DB::rollBack();

            return back()->with('fail', __('payments::tax.taxCreationFailed') . '. ' . $e->getMessage());
        }
    }

    public function extendLoan(ExtendLoanRequest $request): RedirectResponse
    {
        $requestValidated = $request->validated();
        $requestValidated['extendFeeAmount'] = floatToInt($requestValidated['extendFeeAmount']);

        DB::beginTransaction();
        try {
            $this->extendLoanService->run(
                $requestValidated['loan_id'],
                $requestValidated['extendFeeAmount'],
                $requestValidated['extendWithDays']
            );
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();

            return back()->with('fail', __('payments::tax.taxFailedLoanExtension') . '. ' . $e->getMessage());
        }

        return $this->backSuccess('payments::tax.taxSuccessfullyExtendedLoan')->withFragment('#paymentschedule');
    }

    public function destroy(Tax $tax): RedirectResponse
    {
        if ($tax->exists) {

            DB::beginTransaction();
            try {

                if ($tax->paid_amount > 0) {
                    $msg = 'payments::tax.taxAmountRemovalSucceeded';

                    $tax->amount = $tax->paid_amount;
                    $tax->rest_amount = 0;
                    $tax->paid = 1;
                    if (empty($tax->paid_at)) {
                        $tax->paid_at = now();
                    }
                    $tax->save();

                } else {
                    $msg = 'payments::tax.taxRemovalSucceeded';

                    $tax->update([
                        'status' => Tax::TAX_STATUS_DELETED,
                        'active' => 0,
                        'deleted' => 1,
                        'deleted_at' => now(),
                        'deleted_by' => getAdminId(),
                    ]);

                    if ($tax->isExtendFee()) {
                        $this->extendLoanService->revert($tax);
                    }

                    LoanPaymentWasReceivedForStats::dispatch(
                        new Payment(),
                        $tax->loan
                    );
                }

                DB::commit();

                return $this->backSuccess($msg)->withFragment('#paymentschedule');

            } catch (\Throwable $e) {
                DB::rollBack();

                return back()->with('fail', __('payments::tax.taxRemovalFailed') . '. ' . $e->getMessage())->withFragment('#paymentschedule');
            }
        }

        return $this->backError('payments::tax.taxRemovalFailed')->withFragment('#paymentschedule');
    }

    public function calculateLoanExtendFee(CalculateLoanExtendFeeRequest $request): JsonResponse
    {
        $data = $request->validated();

        $loan = $this->loanService->getLoanById($data['loan_id']);
        $data['extendLoanFeeAmount'] = $this->loanService->calculateExtendLoanFeeAmount($loan, $data['extendWithDays']);

        return response()->json($data);
    }

}
