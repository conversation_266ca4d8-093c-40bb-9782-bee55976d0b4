<?php

namespace Modules\Payments\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\Http\Response;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Payments\Application\Actions\ApsUpdatedLoanExportAction;
use Modules\Payments\Application\Actions\LmfUpdatedLoanExportAction;

class OuterCollectorLoansController extends BaseController
{
    public function index(): View
    {
        return view('payments::outer-collector-loans.index');
    }

    public function export($type): Response
    {
        $action = match ($type) {
            'aps' => app(ApsUpdatedLoanExportAction::class),
            'lmf' => app(LmfUpdatedLoanExportAction::class),
            default => throw new \Exception('Грешен тип за експорт'),
        };

        $exportData = $action->execute();

        // Check if export data is available
        if (empty($exportData) || !isset($exportData['file_path'])) {
            return back()->with('warning', 'Няма данни за експорт');
        }

        // Create a response with the file content
        $fileContent = file_get_contents($exportData['file_path']);

        // Clean up the temporary file
        unlink($exportData['file_path']);

        return response($fileContent)
            ->header('Content-Type', $exportData['content_type'])
            ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
            ->header('Cache-Control', 'max-age=0');
    }
}
