<?php

namespace Modules\Payments\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\Http\Response;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Payments\Application\Actions\ReceivedPaymentsDataAction;
use Modules\Payments\Application\Actions\ReceivedPaymentsDataActionSHIBAN;
use Modules\Payments\Http\Requests\ReceivedPaymentsSearchRequest;

class ReceivedPaymentsController extends BaseController
{
    private string $indexRoute = 'received-payments.index';

    public function __construct() {
        parent::__construct();
    }

    public function index(
        ReceivedPaymentsSearchRequest $request,
        ReceivedPaymentsDataAction $action
    ): View {

        $filters = $request->validated();
        $data = $action->execute($filters, $this->getPaginationLimit());

        return view('payments::received-payments.list', $data);
    }

    public function export(
        ReceivedPaymentsSearchRequest $request,
        ReceivedPaymentsDataAction $action,
        ReceivedPaymentsDataActionSHIBAN $actionShiban,
    ): Response {
        $filters = $request->validated();

        // Use the regular action's exportXlsx2 method
        $exportData = $action->exportXlsx2($filters);

        // Create a response with the file content
        $fileContent = file_get_contents($exportData['file_path']);

        // Clean up the temporary file
        unlink($exportData['file_path']);

        return response($fileContent)
            ->header('Content-Type', $exportData['content_type'])
            ->header('Content-Disposition', 'attachment; filename="' . $exportData['file_name'] . '"')
            ->header('Cache-Control', 'max-age=0');

        // Alternative SHIBAN export: $actionShiban->export($filters);
        // Alternative CSV export: $action->exportCsv($filters);
        // Alternative XLSX export: $action->exportXlsx($filters);
    }
}
