<?php

namespace Modules\Payments\Repositories;

use Exception;
use Modules\Common\Models\PaymentTaskDecision;
use Modules\Common\Repositories\BaseRepository;

class PaymentTaskDecisionRepository extends BaseRepository
{
    /**
     * @param null|int $limit
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        ?int $limit,
        array $where = [],
        array $order = ['active' => 'DESC', 'payment_task_decision_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = PaymentTaskDecision::orderByRaw(implode(', ', $this->prepareOrderStatement($order)));

        if (!empty($where)) {
            $builder->where($where);
        }

        return $limit == null ? $builder->get() : $builder->paginate($limit);
    }

    public function getById(int $id): ?PaymentTaskDecision
    {
        return PaymentTaskDecision::where(
            'payment_task_decision_id',
            '=',
            $id
        )->first();
    }

    public function getTypes(): array
    {
        return [
            PaymentTaskDecision::PAYMENT_TASK_DECISION_TYPE_WAITING,
            PaymentTaskDecision::PAYMENT_TASK_DECISION_TYPE_FINAL,
        ];
    }

    public function create(array $data): PaymentTaskDecision
    {
        $paymentTaskDecision = new PaymentTaskDecision();
        $paymentTaskDecision->fill($data);
        $paymentTaskDecision->save();

        return $paymentTaskDecision;
    }

    public function update(PaymentTaskDecision $paymentTaskDecision, array $data): PaymentTaskDecision
    {
        $paymentTaskDecision->fill($data);
        $paymentTaskDecision->save();

        return $paymentTaskDecision;
    }

    /**
     * @throws Exception
     */
    public function delete(PaymentTaskDecision $paymentTaskDecision): PaymentTaskDecision
    {
        $paymentTaskDecision->delete();

        return $paymentTaskDecision;
    }

    public function disable(PaymentTaskDecision $paymentTaskDecision): PaymentTaskDecision
    {
        $paymentTaskDecision->disable();

        return $paymentTaskDecision;
    }

    public function enable(PaymentTaskDecision $paymentTaskDecision): PaymentTaskDecision
    {
        $paymentTaskDecision->enable();

        return $paymentTaskDecision;
    }
}
