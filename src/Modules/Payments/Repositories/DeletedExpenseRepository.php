<?php

declare(strict_types=1);

namespace Modules\Payments\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Common\Entities\DelExpenseSnapshot;
use Modules\Common\Repositories\BaseRepository;

final class DeletedExpenseRepository extends BaseRepository
{
    public function __construct(
        private readonly DelExpenseSnapshot $model,
    ) {
    }

    public function getPaginator(
        array $filters = [],
        int $perPage = 10
    ): LengthAwarePaginator {
        return $this->builder($filters)->paginate($perPage);
    }

    private function builder(array $filters = []): Builder
    {
        /** @phpstan-ignore-next-line */
        return $this->model
            ->query()
            ->with([
                'loanRelation:loan_id,client_id,office_id',
                'loanRelation.office:office_id,name',
                'loanRelation.client:client_id,first_name,last_name,middle_name,pin',
                'createdBy:administrator_id,first_name,last_name,middle_name',
            ])
            ->orderBy('del_expense_snapshot.created_at', 'desc')
            ->filterBy($filters);
    }

    public function getBuilder(array $filters = []): Builder
    {
        return $this->builder($filters);
    }
}
