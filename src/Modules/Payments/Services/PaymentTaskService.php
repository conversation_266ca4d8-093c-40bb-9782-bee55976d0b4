<?php

namespace Modules\Payments\Services;

use Carbon\Carbon;
use Exception;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Services\BaseService;
use Modules\Head\Repositories\ClientBankAccountRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\LoanService;
use Modules\Payments\Repositories\PaymentTaskRepository;
use Modules\Payments\Repositories\PaymentTaskStatRepository;

class PaymentTaskService extends BaseService
{
    const RESEND_TAKS_DELAY = 5; // min

    public function __construct(
        protected LoanService                 $loanService,
        protected LoanRepository              $loanRepository,
        protected PaymentService              $paymentService,
        protected PaymentTaskRepository       $paymentTaskRepository,
        protected PaymentTaskStatRepository   $paymentTaskStatRepository,
        protected ClientBankAccountRepository $clientBankAccountRepository,
    )
    {

        parent::__construct();
    }

    public function create(array $data)
    {
        try {
            return $this->paymentTaskRepository->create($data);
        } catch (Exception $e) {
            throw new \RuntimeException(
                __('payments::payment.createPaymentTaskFailed'),
                $e
            );
        }
    }

    public function closeTask(
        PaymentTask $paymentTask,
        int         $paymentId = null
    )
    {
        $data = [
            'end_at' => Carbon::now(),
            'duration' => $this->calcDuration($paymentTask->paymentTaskStats->last()->start_at),
            'payment_id' => (!empty($paymentId) ? $paymentId : $paymentTask->payment_id),
            'status' => TaskStatusEnum::DONE,
        ];

        // $paymentTask->active = 0; // TODO: check with Svetlin do we need to deactivate here? - одобряваме задача за плащане

        return $this->paymentTaskRepository->update($paymentTask, $data);
    }

    public function renewTask(
        PaymentTask $paymentTask,
        int         $paymentId = null
    ): ?PaymentTask
    {

        $data = [
            'payment_id' => (empty($paymentId) ? $paymentTask->payment_id : $paymentId),
            'client_id' => $paymentTask->client_id,
            'loan_id' => $paymentTask->loan_id,
            'payment_method_id' => $paymentTask->payment_method_id,
            'amount' => $paymentTask->amount,
            'basis' => $paymentTask->basis,
            'status' => TaskStatusEnum::NEW,
            'direction' => $paymentTask->direction,
            'type' => $paymentTask->type,
            'show_after' => Carbon::now()->addMinutes(self::RESEND_TAKS_DELAY)->toDateTimeString(),
        ];

        // $paymentTask->active = 0; // TODO: check with Svetlin do we need to deactivate here? - одобряваме задача за плащане

        return $this->create($data);
    }

    protected function getWhereConditions(
        array  $data,
        array  $names = ['name'],
        string $prefix = ''
    )
    {
        $where = [];

        if (!empty($data['amount'])) {
            if (!empty($data['amount']['from'])) {
                $where[] = [
                    'payment_task.amount',
                    '>=',
                    $data['amount']['from'],
                ];
            }

            if (!empty($data['amount']['to'])) {
                $where[] = [
                    'payment_task.amount',
                    '<=',
                    $data['amount']['to'],
                ];
            }

            unset($data['amount']);
        }

        if (!empty($data['name'])) {
            $where['client_full_name'] = $data['name'];
            unset($data['name']);
        }

        unset($data['name']);

        $where[] = [
            'payment_task.show_after',
            '<=',
            Carbon::now()->format('Y-m-d H:i:s'),
        ];

        return array_merge($where, parent::getWhereConditions($data, $names, $prefix));
    }

    public function exitTask(
        PaymentTask $task
    )
    {
        $data = [
            'end_at' => Carbon::now(),
            'duration' => $this->calcDuration($task->paymentTaskStats->last()->start_at),
        ];

        $this->paymentTaskRepository->disableHandledBy($task);
        $this->paymentTaskStatRepository->updateTimeStats($task->paymentTaskStats->last(), $data);
    }

    public function getOrderingTaskById(
        int $id
    )
    {
        return $this->paymentTaskRepository->getById($id);
    }

    public function calcDuration(
        string $startAt
    )
    {
        $startedAt = Carbon::parse($startAt);
        $now = Carbon::parse(Carbon::now());

        return $now->diffInSeconds($startedAt);
    }

    public function createManualPaymentTask(
        ?Payment             $payment,
        PaymentProblemEnum   $type,
        ?PaymentTaskNameEnum $name = null
    ): PaymentTask {

        // default values
        $paymentData = [
            'status' => TaskStatusEnum::NEW,
            'type' => $type,
            'amount' => $payment->amount,
            'direction' => PaymentTask::DIRECTION_OUT,
        ];

        if (!empty($payment)) {
            $paymentData['basis'] = $payment->description;
            $paymentData['direction'] = $payment->direction->value;
            $paymentData['payment_id'] = $payment->getKey();
            $paymentData['currency_id'] = $payment->currency_id;
            $paymentData['payment_method_id'] = $payment->payment_method_id;
            $paymentData['imported_payment_id'] = (
                !empty($payment->imported_payment_id)
                ? $payment->imported_payment_id
                : null
            );
            $paymentData['easypay_rid'] = (
                !empty($payment->document_number) && $payment->payment_method_id = PaymentMethod::PAYMENT_METHOD_EASYPAY
                ? $payment->document_number
                : null
            );
        }

        if (!empty($name)) {
            $paymentData['name'] = $name;
        }

        return $this->paymentTaskRepository->create($paymentData);
    }

    private function formatPaymentTaskData(Loan $loan, string $type): array
    {
        $account = $loan->bankAccount()->first();
        $basis = __('payments::payments.creditNumberGranting');

        return array(
            'bic' => $account->bic,
            'iban' => $account->iban,
            'type' => $type,
            'basis' => "$basis $loan->loan_id",
            'amount' => $loan->amount_approved,
            'loan_id' => $loan->getKey(),
            'client_id' => $loan->client->getKey(),
            'direction' => 'out',
            'handled_at' => Carbon::now(),
            'payment_method_id' => $loan->paymentMethod()->first()->payment_method_id,
        );
    }
}
