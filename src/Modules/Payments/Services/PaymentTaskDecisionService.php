<?php

namespace Modules\Payments\Services;

use Exception;
use Modules\Common\Models\PaymentTaskDecision;
use Modules\Common\Services\BaseService;
use Modules\Payments\Repositories\PaymentTaskDecisionRepository;
use RuntimeException;

class PaymentTaskDecisionService extends BaseService
{
    public function __construct(
        private readonly PaymentTaskDecisionRepository $paymentTaskDecisionRepository = new PaymentTaskDecisionRepository,
    ) {
        parent::__construct();
    }

    public function getTypes(): array
    {
        return $this->paymentTaskDecisionRepository->getTypes();
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(?int $limit, array $data)
    {
        $whereConditions = $this->getWhereConditions($data);

        return $this->paymentTaskDecisionRepository->getAll(
            $limit,
            $whereConditions
        );
    }

    public function store(array $data): ?PaymentTaskDecision
    {
        try {
            $paymentTaskDecision = $this->paymentTaskDecisionRepository->create($data);

        } catch (Exception $exception) {
            throw new RuntimeException(
                __('payments::paymentTaskDecision.paymentTaskDecisionCreationFailed'),
                $exception
            );
        }

        return $paymentTaskDecision;
    }

    public function update(PaymentTaskDecision $paymentTaskDecision, array $data): PaymentTaskDecision
    {
        try {
            $paymentTaskDecision = $this->paymentTaskDecisionRepository->update($paymentTaskDecision, $data);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('payments::paymentTaskDecision.paymentTaskDecisionUpdateFailed'),
                $exception
            );
        }

        return $paymentTaskDecision;
    }

    public function getById(int $id)
    {
        $paymentTaskDecision = $this->paymentTaskDecisionRepository->getById($id);

        if (!$paymentTaskDecision) {
            throw new RuntimeException(__('payments::paymentTaskDecision.paymentTaskDecisionNotFound'));
        }


        return $paymentTaskDecision;
    }

    public function delete(PaymentTaskDecision $paymentTaskDecision): bool
    {
        try {
            $this->paymentTaskDecisionRepository->delete($paymentTaskDecision);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('payments::paymentTaskDecision.paymentTaskDecisionDeletionFailed'),
                $exception
            );
        }

        return true;
    }
    public function enable(PaymentTaskDecision $paymentTaskDecision): bool
    {
        try {
            if ($paymentTaskDecision->isActive()) {
                throw new RuntimeException(
                    __('payments::paymentTaskDecision.paymentTaskDecisionEnableForbidden')
                );
            }

            $this->paymentTaskDecisionRepository->enable($paymentTaskDecision);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('payments::paymentTaskDecision.paymentTaskDecisionEnableFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(PaymentTaskDecision $paymentTaskDecision): bool
    {
        try {
            if (!$paymentTaskDecision->isActive()) {
                throw new RuntimeException(
                    __('payments::paymentTaskDecision.paymentTaskDecisionDisabledForbidden')
                );
            }

            $this->paymentTaskDecisionRepository->disable($paymentTaskDecision);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('payments::paymentTaskDecision.paymentTaskDecisionDisableFailed'),
                $exception
            );
        }

        return true;
    }
}
