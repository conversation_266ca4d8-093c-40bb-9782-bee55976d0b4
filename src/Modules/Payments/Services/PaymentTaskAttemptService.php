<?php

namespace Modules\Payments\Services;

use Carbon\Carbon;
use Modules\Admin\Services\SettingService;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\AbstractDecision;
use Modules\Common\Models\AbstractTask;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Models\PaymentTaskAttempt;
use Modules\Common\Models\PaymentTaskDecision;
use Modules\Common\Models\UnclaimedMoney;
use Modules\Common\Services\BaseService;
use Modules\Payments\Repositories\PaymentTaskAttemptRepository;
use Modules\Payments\Repositories\PaymentTaskRepository;

class PaymentTaskAttemptService extends BaseService
{
    public function __construct(
        protected PaymentTaskDecisionService   $paymentTaskDecisionService,
        protected SettingService               $settingService,
        protected PaymentTaskRepository        $paymentTaskRepository,
        protected PaymentTaskAttemptRepository $paymentTaskAttemptRepository,
    )
    {
//        parent::__construct($settingService);
    }


    public function storeIncomingPaymentTaskAttempt(
        PaymentTask $paymentTask,
        array       $data
    ): PaymentTaskAttempt {

        $paymentTaskDecision = $this->paymentTaskDecisionService->getById($data['payment_task_decision_id']);
        $payment = $paymentTask->payment;
        if (empty($payment->payment_id)) {
            throw new \Exception('No payment for payment task');
        }

        $officeId = Office::OFFICE_ID_WEB;
        if (!empty($payment->office_id)) {
            $officeId = $payment->office_id;
        }
        if (!empty($paymentTask->office_id)) {
            $officeId = $paymentTask->office_id;
        }

        $now = Carbon::now();
        $comment = !empty($data['comment']) ? $data['comment'] : null;
        $paymentTaskStartedAt = Carbon::parse($paymentTask->last_status_update_date);

        $taskAttemptData = [
            'administrator_id' => getAdminId(),
            'office_id' => $officeId,
            'payment_task_id' => $paymentTask->payment_task_id,
            'payment_task_decision_id' => $data['payment_task_decision_id'],
            'comment' => $comment,
            'start_at' => $paymentTaskStartedAt,
            'end_at' => $now,
            'total_time' => $now->diffInMinutes($paymentTaskStartedAt),
            'skip_time' => 0,
            'skip_till' => null,
            'skip_counter' => 1,
            'details' => null,
            'last' => 1,
        ];


        // ---- 1. final decision on attempt ----

        if ($paymentTaskDecision->type === $paymentTaskDecision::PAYMENT_TASK_DECISION_TYPE_FINAL) {


            // save attempt
            $taskAttempt = $this->paymentTaskAttemptRepository->create($taskAttemptData);


            $deleted = 0;
            // Не е превод от клиент - Задачата се маркира като приключена и пеймент се затваря, все едно го нямаше
            if ($data['payment_task_decision_id'] == $paymentTaskDecision::PAYMENT_TASK_DECISION_ID_NOT_FROM_CLIENT) {
                $deleted = 1;
            }


            // close payment
            $this->closePayment($payment, $deleted);


            // close payment task
            $this->closePaymentTask($paymentTask);


            // Превода се налива в Непотърсени пари aко изхода е:
            // Откажи плащане
            // Не може да се идентифицира
            // Грешен телефон
            // Друго
            if (
                in_array(
                    $data['payment_task_decision_id'],
                    [
                        $paymentTaskDecision::PAYMENT_TASK_DECISION_ID_DECLINE_PAYMENT,
                        $paymentTaskDecision::PAYMENT_TASK_DECISION_ID_UNIDENTIFIED,
                        $paymentTaskDecision::PAYMENT_TASK_DECISION_ID_WRONG_PHONE,
                        $paymentTaskDecision::PAYMENT_TASK_DECISION_ID_OTHER,
                    ]
                )
            ) {
                $this->saveUnclaimedMoney($payment, $comment);
            }


            // exit from the game
            return $taskAttempt;
        }

        // ---- 2. tmp decision on attempt ----

        // 2a. strict: Обаждане по-късно
        if ($data['payment_task_decision_id'] == $paymentTaskDecision::PAYMENT_TASK_DECISION_ID_RECALL) {

            if (empty($data['show_after'])) {
                throw new \Exception('No show_after timing for re-call');
            }


            $skipTill = Carbon::parse($data['show_after']);
            $skipTime = (Carbon::now())->diffInSeconds($skipTill);
            $taskAttemptData['skip_till'] = $skipTill;
            $taskAttemptData['skip_time'] = $skipTime;


            // save attempt
            $taskAttempt = $this->paymentTaskAttemptRepository->create($taskAttemptData);


            // nullate payment_task
            $paymentTask->show_after = $skipTill;
            $paymentTask->handled_at = null;
            $paymentTask->handled_by = null;
            $paymentTask->start_at = null;
            $paymentTask->end_at = null;
            $paymentTask->duration = null;
            $paymentTask->status = AbstractTask::TASK_STATUS_NEW;
            $paymentTask->save();


            return $taskAttempt;
        }


        // 2b. flexible:
        // Няма отговор
        // Заето

        $maxAttemptSetting = $this
            ->settingService
            ->getSetting(SettingsEnum::max_allowed_payment_tasks_postponements_payments)
            ->default_value ?: 8;


        $prevAttemptsCount = $this->paymentTaskAttemptRepository->getAttemptsCount($paymentTask->payment_task_id);


        // if first attempt, get small period
        if ($prevAttemptsCount < 1) {
            $addMinutes = $this
                ->settingService
                ->getSetting($this->getFirstAddTimeSetting()[$paymentTaskDecision->getKey()])
                ->default_value ?: 5;
        } else {
            $addMinutes = $this
                ->settingService
                ->getSetting(SettingsEnum::multiple_skip_payment_task_attempts_minutes_interval_payments)
                ->default_value ?: 60;
        }


        // actualize attempts
        $actualAttempCount = 1 + $prevAttemptsCount;
        $taskAttemptData['skip_counter'] = 1 + $prevAttemptsCount;


        // when exceed attempts -> close payment, payment_task, save attempt and save money to unclaimed buffer
        if ($actualAttempCount >= $maxAttemptSetting) {

            // save attempt
            $taskAttempt = $this->paymentTaskAttemptRepository->create($taskAttemptData);


            // close payment
            $this->closePayment($payment);


            // close payment task
            $this->closePaymentTask($paymentTask);


            // save money to unclaimed buffer
            $this->saveUnclaimedMoney($payment, $comment);


            return $taskAttempt;
        }


        // flexible postpone


        $skipTill = Carbon::now()->addMinutes($addMinutes);
        $skipTime = $addMinutes * 60;


        // save attempt
        $taskAttemptData['skip_till'] = $skipTill;
        $taskAttemptData['skip_time'] = $skipTime;
        $taskAttempt = $this->paymentTaskAttemptRepository->create($taskAttemptData);


        // nullate payment_task
        $paymentTask->show_after = $skipTill;
        $paymentTask->handled_at = null;
        $paymentTask->handled_by = null;
        $paymentTask->start_at = null;
        $paymentTask->end_at = null;
        $paymentTask->duration = null;
        $paymentTask->status = AbstractTask::TASK_STATUS_NEW;
        $paymentTask->save();


        return $taskAttempt;
    }

    private function closePayment(Payment $payment, int $deleted = 0)
    {
        $adminId = getAdminId();

        $payment->status = PaymentStatusEnum::UNKNOWN;
        $payment->processing_by = $adminId;
        $payment->handled_by = $adminId;
        $payment->handled_at = Carbon::now();

        if ($deleted) {
            $payment->deleted_by = $adminId;
            $payment->deleted_at = Carbon::now();
            $payment->deleted = 1;
            $payment->active = 0;
        }

        $payment->save();
    }

    private function closePaymentTask(PaymentTask $paymentTask)
    {
        $now = now();
        $taskStartEndAt = $now;
        $taskStartAt = $paymentTask->start_at;
        $duration = $now->diffInSeconds(Carbon::parse($taskStartAt));

        $this->paymentTaskRepository->update(
            $paymentTask,
            [
                'end_at' => $taskStartEndAt,
                'duration' => $duration,
                'handled_by' => getAdminId(),
                'last_status_update_date' => $now,
                'status' => AbstractTask::TASK_STATUS_DONE,
            ]
        );
    }

    private function saveUnclaimedMoney(Payment $payment, ?string $comment = null)
    {
        // prepare desc
        $details = '';
        $client = $payment->client;
        if (!empty($client->client_id)) {
            $details .= 'Клиент: ' . $client->getFullName() . ';';
            $details .= 'Егн: ' . $client->pin . ';';
        }
        $loan = $payment->loan;
        if (!empty($loan->loan_id)) {
            $details .= 'Заем №: ' . $loan->loan_id . ';';
        }
        $details .= 'Плащане №: ' . $payment->payment_id . ';';
        if (!empty($payment->description)) {
            $details .= 'Описание на плащане: ' . $payment->description . ';';
        }
        if (!empty($comment)) {
            $details .= 'Коментар: ' . $comment . ';';
        }

        $obj = new UnclaimedMoney();
        $obj->payment_id = $payment->payment_id;
        $obj->direction = 'in';
        $obj->amount = $payment->amount;
        $obj->details = $details;
        $obj->created_at = now();
        $obj->created_by = getAdminId();
        $obj->save();
    }

    public function storePaymentTaskAttempt(
        PaymentTask $paymentTask,
        array       $data
    ): PaymentTaskAttempt {

        $paymentTaskDecision = $this->paymentTaskDecisionService->getById($data['payment_task_decision_id']);

        return $this->storeAttempt(
            $paymentTask,
            $data,
            $paymentTaskDecision,
            $this->paymentTaskAttemptRepository,
            $this->paymentTaskRepository
        );
    }

    public function storeAttempt(
        AbstractTask                 $attemptTask,
        array                        $data,
        AbstractDecision             $abstractDecision,
        PaymentTaskAttemptRepository $taskAttemptRepository = new PaymentTaskAttemptRepository,
        PaymentTaskRepository        $taskRepository = new PaymentTaskRepository
    ): PaymentTaskAttempt {

        $now = Carbon::now();
        $taskAttemptData = [
            'administrator_id' => getAdminId(),
            'office_id' => $attemptTask->office_id,

            $attemptTask->getKeyName() => $attemptTask->getKey(),
            $abstractDecision->getKeyName() => $abstractDecision->getKey(),

            'comment' => !empty($data['comment']) ? $data['comment'] : null,
            'start_at' => $attemptTask->last_status_update_date,
            'end_at' => $now,
            'total_time' => $now->diffInMinutes($attemptTask->last_status_update_date),
            'skip_time' => 0,
            'skip_till' => null,
            'skip_counter' => 0,
            'details' => null,
            'payment_task_id' => !empty($attemptTask->payment_task_id) ? $attemptTask->payment_task_id : null
        ];

        if ($abstractDecision->type === $abstractDecision::TASK_DECISION_TYPE_WAITING) {
            $maxAttemptSetting = $this
                ->settingService
                ->getSetting(SettingsEnum::max_allowed_payment_tasks_postponements_payments)
                ->default_value ?: 8;

            $addTimeFirstTimeSetting = $this
                ->settingService
                ->getSetting($this->getFirstAddTimeSetting()[$abstractDecision->getKey()])
                ->default_value ?: 5;

            /**
             * @var PaymentTask $parentTask
             */
            $parentTask = $attemptTask->parent;
            $skipCounter = 1;
            $skipTime = $addTimeFirstTimeSetting;

            if (
                !empty($parentTask)
                && !empty($previousTaskAttempt = $parentTask->taskAttempt)
                && $previousTaskAttempt->decision->inSkipCounterCondition()
            ) {
                $addTimeMultiplePostPoneSetting = $this
                    ->settingService
                    ->getSetting(SettingsEnum::multiple_skip_payment_task_attempts_minutes_interval_payments)
                    ->default_value ?: 60;

                $skipCounter = $previousTaskAttempt->skip_counter;
                $skipTime = $addTimeMultiplePostPoneSetting;
            }

            if ($skipCounter < ($maxAttemptSetting - 1)) {
                $skipTill = !empty($data['show_after'])
                    ? Carbon::parse($data['show_after']) : Carbon::now()->addMinutes($skipTime);

                $newTaskData = $attemptTask->toArray();
                unset($newTaskData['payment_task_id']);

                $newTaskData['show_after'] = $skipTill;
                $newTaskData['parent_task_id'] = $attemptTask->getKey();
                $newTaskData['handled_at'] = null;
                $newTaskData['handled_by'] = null;
                $newTaskData['status'] = AbstractTask::TASK_STATUS_NEW;

                $taskAttemptData['skip_counter'] =
                    !empty($previousTaskAttempt)
                    && $previousTaskAttempt->decision->inSkipCounterCondition()
                    && $abstractDecision->inSkipCounterCondition()
                        ? ++$skipCounter : 1;
                $taskAttemptData['skip_time'] = $skipTime;
                $taskAttemptData['skip_till'] = $skipTill;

                $taskRepository->create($newTaskData);
            }
        }

        $taskAttempt = $taskAttemptRepository->create($taskAttemptData);


        $taskStartAt = $attemptTask->start_at;
        $taskStartEndAt = $now;
        $duration = $now->diffInMinutes(Carbon::parse($taskStartAt));

        $taskRepository->update(
            $attemptTask,
            [
                'end_at' => $taskStartEndAt,
                'duration' => $duration,
                'handled_by' => getAdminId(),
                'last_status_update_date' => $now,
                'status' => AbstractTask::TASK_STATUS_DONE,
            ]
        );

        return $taskAttempt;
    }


    public function getFirstAddTimeSetting(): array
    {
        return [
            PaymentTaskDecision::PAYMENT_TASK_DECISION_ID_NO_ANSWER => SettingsEnum::one_skip_payment_task_attempt_no_answer_minutes_interval_payments,
            PaymentTaskDecision::PAYMENT_TASK_DECISION_ID_BUSY => SettingsEnum::one_skip_payment_task_attempt_busy_minutes_interval_payments,
            PaymentTaskDecision::PAYMENT_TASK_DECISION_ID_RECALL => SettingsEnum::one_skip_payment_task_attempt_busy_minutes_interval_payments
        ];
    }
}
