<?php

namespace Modules\Payments\Services;

use Exception;
use Modules\Common\Models\Tax;
use Modules\Common\Services\BaseService;
use Modules\Head\Services\InstallmentService;
use Modules\Payments\Repositories\TaxRepository;

class TaxService extends BaseService
{
    /**
     * @var array
     */
    protected static $order = [
        Tax::TAX_TYPE_LOAN_EXTENSION_FEE,
        Tax::TAX_TYPE_MANUALLY_ADDED_EXPENSE,
        Tax::TAX_TYPE_MAIL,
        Tax::TAX_TYPE_SMS,
        Tax::TAX_TYPE_COLLECTOR,
    ];

    public const DELETE_TAX_ROUTE = 'payments.tax.deleteTax';
    public const UPDATE_TAX_ROUTE = 'payments.tax.updateTaxes';

    public function __construct(
        protected readonly TaxRepository $taxRepository,
        protected readonly InstallmentService $installmentService
    ) {
        parent::__construct();
    }

    public function create(array $data, bool $isExtended = true): Tax
    {
        if ($isExtended === false) {
            throw new \RuntimeException(__('payments::tax.taxFailedLoanExtension'));
        }

        $loanFee = $this->taxRepository->create($data);
        if (!$loanFee->exists) {
            throw new Exception(__('payments::tax.taxCreationFailed'));
        }


        return $loanFee;
    }

    /**
     * @param Tax $tax
     * @return bool
     */
    public function createTaxHistory(Tax $tax): bool
    {
        $saves = [];
        $changedFields = $tax->getChanges();

        foreach ($changedFields as $field => $newVal) {
            if (!in_array($field, array_keys($tax->getImportantProperties()))) {
                continue;
            }

            $oldVal = $tax->getOriginal($field);

            if ($newVal == $oldVal) {
                continue;
            }

            $options = [
                'field' => $field,
                'newVal' => $newVal,
                'oldVal' => $oldVal,
            ];
            $saves[] = $this->taxRepository->createTaxHistory($tax, $options);
        }

        if (count(array_unique($saves)) === 1) {
            return current($saves);
        }

        return false;
    }
}
