<?php

namespace Modules\Payments\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Entities\AutoProcessSnapshot;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\UnclaimedMoney;
use Modules\Common\Services\BaseService;
use Modules\Payments\Domain\Events\LoanPaymentWasDeleted;
use Modules\Payments\Domain\Events\LoanPaymentAfterDeleted;
use Modules\Payments\Repositories\PaymentRepository;
use Modules\ThirdParty\Services\FiscalDevice\PHPProcessingService;
use Throwable;

final class PaymentService extends BaseService
{
    public function __construct(
        protected PaymentRepository $paymentRepository
    )
    {
        parent::__construct();
    }

    public function getAllPaymentsByFilters(
        ?int  $limit,
        array $data
    )
    {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        if (array_key_exists('limit', $data) && $data['limit'] !== null) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        $whereConditionsAdditional = [];

        if (!empty($data['sumFrom'])) {
            $whereConditionsAdditional[] = [
                'payment.amount',
                '>=',
                $data['sumFrom'],
            ];
            unset($data['sumFrom']);
        }

        if (!empty($data['sumTo'])) {
            $whereConditionsAdditional[] = [
                'payment.amount',
                '<=',
                $data['sumTo'],
            ];
            unset($data['sumTo']);
        }

        $whereConditions = $this->getWhereConditionsAllPayments($data, ['name'], 'payment');

        // Reverse sorting if we have client_new order direction
        foreach ($order as $field => $direction) {
            $order[$field] = $this->reverseOrderDirection($direction);
            break;
        }

        $whereConditions = array_merge($whereConditions, $whereConditionsAdditional);

        return $this->paymentRepository
            ->getAll(
                $limit,
                $whereConditions,
                !empty($order) ? $order : ['payment_id' => 'DESC', 'active' => 'DESC']
            );
    }

    protected function getWhereConditionsAllPayments(
        array $data,
        array $names = ['name'],
              $prefix = ''
    )
    {
        $where = [];
        if (!empty($data['payment']['sumFrom'])) {
            $where[] = [
                'payment.amount',
                '>=',
                $data['payment']['sumFrom'],
            ];
            unset($data['payment']['sumFrom']);
        }

        if (!empty($data['payment']['sumTo'])) {
            $where[] = [
                'payment.amount',
                '<=',
                $data['payment']['sumTo'],
            ];
            unset($data['payment']['sumTo']);
        }

        if (!empty($data['payment']['clientPin'])) {
            $where[] = [
                'client.pin',
                '=',
                $data['payment']['clientPin'],
            ];
            unset($data['payment']['clientPin']);
        }

        if (!empty($data['payment']['loanId'])) {
            $where[] = [
                'loan.loan_id',
                '=',
                $data['payment']['loanId'],
            ];
            unset($data['payment']['loanId']);
        }


        if (!empty($data['processed'])) {
            $where['processed'] = $data['processed'];
            unset($data['processed']);
        }

        if (!empty($data['payment']['clientFullName'])) {
            $where['client_full_name'] = $data['payment']['clientFullName'];
            unset($data['payment']['clientFullName']);
        }

        if (!empty($data['paymentSources'])) {
            $where['payment_sources'] = $data['paymentSources'];
            unset($data['paymentSources']);
        }

        return array_merge($where, parent::getWhereConditions($data, $names, $prefix));
    }

    private function reverseOrderDirection(string $direction): string
    {
        $reverse = [
            'ASC' => 'DESC',
            'DESC' => 'ASC',
        ];
        $direction = strtoupper($direction);

        if (!array_key_exists($direction, $reverse)) {
            return 'ASC';
        }

        return $reverse[$direction];
    }

    private function rollBackCustom()
    {
        // if (1458891 == $payment->payment_id && getAdminId() == 2) {
        //     DB::beginTransaction();
        //     try {

        //         // create same payment with negative amount and negative delivery
        //         $nowDT = now();
        //         $delPayment = $payment->replicate();
        //         unset($delPayment->payment_id);

        //         $delPayment->created_at = $nowDT;
        //         $delPayment->created_by = getAdminId();
        //         $delPayment->updated_at = $nowDT;
        //         $delPayment->updated_by = getAdminId();
        //         $delPayment->handled_at = $nowDT;
        //         $delPayment->handled_by = getAdminId();
        //         $delPayment->amount = -1 * $delPayment->amount;
        //         if (!empty($delPayment->amount_received)) {
        //             $delPayment->amount_received = -1 * $delPayment->amount_received;
        //         }

        //         if (!empty($delPayment->delivery)) {
        //             $delDelivery = [];

        //             if (!is_array($delPayment->delivery)) {

        //                 $delivery = json_decode($delPayment->delivery, true);
        //                 if (is_string($delivery)) {
        //                     $delivery = json_decode($delivery, true);
        //                 }
        //             } else {
        //                 $delivery = $delPayment->delivery;
        //             }

        //             foreach ($delivery as $delElKey => $deliveryEl) {
        //                 $deliveryEl['amount'] = -1 * $deliveryEl['amount'];
        //                 $delDelivery[$delElKey] = $deliveryEl;
        //             }

        //             $delPayment->delivery = $delDelivery;
        //         }

        //         $delPayment->parent_payment_id = $payment->payment_id;
        //         $delPayment->description = 'негативно плащане покриващо плащане #' . $payment->payment_id;
        //         $delPayment->save();

        //         // create negative payment distribution
        //         $distibutionRows = $payment->paymentDistribution();
        //         if ($distibutionRows->count() > 0) {
        //             $todayDate = Carbon::today()->format('Y-m-d');

        //             foreach ($distibutionRows->get() as $distibutionRow) {

        //                 $delDistibutionRow = $distibutionRow->replicate();
        //                 unset($delDistibutionRow->payment_distribution_id);

        //                 $delDistibutionRow->payment_id = $delPayment->payment_id;
        //                 $delDistibutionRow->distributed_amount = -1 * $distibutionRow->distributed_amount;
        //                 $delDistibutionRow->distributed_amount_before = $distibutionRow->distributed_amount_after;
        //                 $delDistibutionRow->distributed_amount_after = $distibutionRow->distributed_amount_before;
        //                 $delDistibutionRow->created_at = $nowDT;
        //                 $delDistibutionRow->updated_at = $nowDT;
        //                 $delDistibutionRow->payment_date = $todayDate;
        //                 $delDistibutionRow->distribution_date = $todayDate;

        //                 $delDistibutionRow->save();
        //             }
        //         }

        //         // accounting row with negative amount
        //         LoanPaymentWasDeleted::dispatch($delPayment);

        //         DB::commit();

        //     } catch (Throwable $e) {
        //         DB::rollBack();
        //         dd($e);
        //     }

        //     dd('done!');
        // }
    }

    /**
     * Rollback loan, installment, taxes, stats to state before payment
     * Cancel payment
     * Return unclaimed money (optional)
     * Create negative accounting row (optional - not for refinance)
     * Delete cashdesk transaction + print fiscal bon (optional)
     *
     * Used In:
     * - CashDeskController->deleteTransaction() - delete from listing of transactions
     * - IncomingPayment->restoreBySnapshot() - buildFromRefinancePaymentOnRestore
     * - PaymentController->rollBack() - delete from listing of payments
     *
     * @param Payment $payment
     *
     * @throws Throwable
     */
    public function rollBack(Payment $payment): bool
    {
        if (!$payment->canBeReverted()) {
            throw new \RuntimeException(__('payments::payments.deleteForbidden'));
        }

        if (!$payment->isLast()) {
            throw new \RuntimeException(__('payments::payments.deleteForbiddenLast'));
        }

        $snapshot = $payment->getPaymentSnapshot();
        if (empty($snapshot->id)) {
            throw new \RuntimeException(__('payments::payments.deleteForbiddenNoSnapshot'));
        }

        $loan = $payment->loan;


        // when we approve refinancing loan that cover active loan, we add interest & penalty for 1st inst if loan is grace or 1st inst is accrued
        // so on rollback(refund) we need to rollback prev.state of grace loan,
        // so we use AutoProcessSnapshot instead of PaymentSnapshot since its done before paymentSnapshot
        // and contains real state of refinanced grace loan
        // logic for task: https://trello.com/c/qF4P3UDd
        if (!empty($payment->parent_payment_id)) {
            $parentPayment = $payment->getParentPayment();
            $refinancingLoan = $parentPayment->loan;

            if ($refinancingLoan->isOnlineLoan()) {
                $snapshotAutoProcess = AutoProcessSnapshot::getSnapshotForLoan($loan);
                if (!empty($snapshotAutoProcess->id) && empty($snapshotAutoProcess->reverted_at)) {
                    $loan->addMeta(
                        'AutoProcessSnapshot::getSnapshotForLoan',
                        'Used autoProcessSnapshotSnapshot #' . $snapshotAutoProcess->id . ' instead of paymentSnapshot #' . $snapshot->id
                    );

                    $snapshot = $snapshotAutoProcess;
                }
            }
        }

        $loanSnapshot = $snapshot->loan;
        $taxesSnapshot = $snapshot->taxes;
        $loanStatsSnapshot = $snapshot->loan_stats;
        $clientStatsSnapshot = $snapshot->client_stats;
        $installmentsSnapshot = $snapshot->installments;
        if (empty($loanSnapshot) || empty($loanStatsSnapshot) || empty($clientStatsSnapshot) || empty($installmentsSnapshot)) {
            throw new \RuntimeException(__('payments::payments.deleteForbiddenBadSnapshot'));
        }

        $taxes = $loan->getAllTaxes();
        $client = $loan->client;
        $loanStats = $loan->loanActualStats;
        $clientStats = $client->clientActualStats;
        $installments = $loan->getAllInstallments();
        $unclaimedMoney = $payment->getUnclaimedMoney();

        if (!empty($unclaimedMoney->id) && !empty($unclaimedMoney->return_id)) {
            throw new \RuntimeException(__('payments::payments.deleteForbiddenHasRevertedUnclaimedMoney'));
        }

        DB::beginTransaction();
        try {

            // cashdesk: remove transaction + print fiscal bon
            if ($payment->payment_method_id == PaymentMethod::PAYMENT_METHOD_CASH) {
                $cashTransaction = $payment->cashOperationalTransaction;
                if (empty($cashTransaction->cash_operational_transaction_id)) {
                    throw new \RuntimeException(__('payments::payments.deleteForbiddenNoCashDeskTransaction'));
                }

                // requirenments of task #3559 - create negative copy
                $balancingTransaction = $cashTransaction->replicate();
                $balancingTransaction->created_at = now();
                $balancingTransaction->updated_at = now();
                $balancingTransaction->created_by = getAdminId();
                $balancingTransaction->updated_by = getAdminId();
                $balancingTransaction->amount = -1 * $balancingTransaction->amount;
                $balancingTransaction->amount_signed = -1 * $balancingTransaction->amount_signed;
                $balancingTransaction->save();

                $cashTransaction->active = 0;
                $cashTransaction->deleted = 1;
                $cashTransaction->deleted_at = now();
                $cashTransaction->deleted_by = getAdminId();
                $cashTransaction->save();
            }


            // set snapshot usage
            $snapshot->reverted_at = now();
            $snapshot->reverted_by = getAdminId();
            $snapshot->save();


            // return objects to prev.state
            if (isset($loanSnapshot['created_at'])) {
                unset($loanSnapshot['created_at']);
            }
            // Restore CCR state if it was previously finished
            if ($loan->ccr_finished == 1) {
                $loanSnapshot['ccr_finished'] = 1;
                $loanSnapshot['ccr_finished_at'] = $loan->ccr_finished_at;
            }
            $loan->fill($loanSnapshot);
            $loan->saveQuietly();

            $loanStats->fill($loanStatsSnapshot);
            $loanStats->saveQuietly();

            $clientStats->fill($clientStatsSnapshot);
            $clientStats->saveQuietly();

            foreach ($installments as $inst) {
                $instData = $installmentsSnapshot[$inst->installment_id];
                $instData['due_date'] = Carbon::parse($instData['due_date'])->format('Y-m-d'); // костъль, because of: 'due_date' => 'date:d-m-Y',

                $inst->fill($instData);
                $inst->saveQuietly();
            }

            if ($taxes->count() > 0) {
                foreach ($taxes as $tax) {
                    if (isset($taxesSnapshot[$tax->tax_id])) {
                        $taxData = $taxesSnapshot[$tax->tax_id];
                        $tax->fill($taxData);
                        $tax->save();
                    } else {
                        /** @var \Modules\Common\Models\Loan $loan */
                        $loan = $tax->loan;
                        if ($tax->isExtendFee() && !empty($loan->grace_period_finished_at)) {
                            $loan->setAttribute('grace_period_finished_at', null);
                            $loan->saveQuietly();
                        }

                        DB::statement('DELETE FROM tax_history WHERE tax_id = ' . $tax->tax_id);
                        DB::statement('DELETE FROM tax WHERE tax_id = ' . $tax->tax_id);
                    }
                }
            }

            // revert row for unclaimed money if exists
            if (!empty($unclaimedMoney->id) && $unclaimedMoney->canBeReturned()) {
                $unclaimedMoneyReturn = new UnclaimedMoney();
                $unclaimedMoneyReturn->payment_id = $unclaimedMoney->payment_id;
                $unclaimedMoneyReturn->return_id = $unclaimedMoney->id;
                $unclaimedMoneyReturn->direction = 'out';
                $unclaimedMoneyReturn->amount = $unclaimedMoney->amount;
                $unclaimedMoneyReturn->details = $unclaimedMoney->details . '; Изтрито плащане;';
                $unclaimedMoneyReturn->created_at = now();
                $unclaimedMoneyReturn->created_by = getAdminId();
                $unclaimedMoneyReturn->save();
            }


            // create same payment with negative amount and negative delivery
            $nowDT = now();
            $delPayment = $payment->replicate();
            unset($delPayment->payment_id);

            $delPayment->created_at = $nowDT;
            $delPayment->created_by = getAdminId();
            $delPayment->updated_at = $nowDT;
            $delPayment->updated_by = getAdminId();
            $delPayment->handled_at = $nowDT;
            $delPayment->handled_by = getAdminId();
            $delPayment->amount = -1 * $delPayment->amount;
            if (!empty($delPayment->amount_received)) {
                $delPayment->amount_received = -1 * $delPayment->amount_received;
            }

            if (!empty($delPayment->delivery)) {
                $delDelivery = [];

                if (!is_array($delPayment->delivery)) {

                    $delivery = json_decode($delPayment->delivery, true);
                    if (is_string($delivery)) {
                        $delivery = json_decode($delivery, true);
                    }
                } else {
                    $delivery = $delPayment->delivery;
                }

                foreach ($delivery as $delElKey => $deliveryEl) {
                    $deliveryEl['amount'] = -1 * $deliveryEl['amount'];
                    $delDelivery[$delElKey] = $deliveryEl;
                }

                $delPayment->delivery = $delDelivery;
            }

            $delPayment->parent_payment_id = $payment->payment_id;
            $delPayment->description = 'негативно плащане покриващо плащане #' . $payment->payment_id;
            $delPayment->save();

            // create negative payment distribution
            $distibutionRows = $payment->paymentDistribution();
            if ($distibutionRows->count() > 0) {
                $todayDate = Carbon::today()->format('Y-m-d');

                foreach ($distibutionRows->get() as $distibutionRow) {

                    $delDistibutionRow = $distibutionRow->replicate();
                    unset($delDistibutionRow->payment_distribution_id);

                    $delDistibutionRow->payment_id = $delPayment->payment_id;
                    $delDistibutionRow->distributed_amount = -1 * $distibutionRow->distributed_amount;
                    $delDistibutionRow->distributed_amount_before = $distibutionRow->distributed_amount_after;
                    $delDistibutionRow->distributed_amount_after = $distibutionRow->distributed_amount_before;
                    $delDistibutionRow->created_at = $nowDT;
                    $delDistibutionRow->updated_at = $nowDT;
                    $delDistibutionRow->payment_date = $todayDate;
                    $delDistibutionRow->distribution_date = $todayDate;

                    $delDistibutionRow->save();
                }
            }

            // accounting row with negative amount
            LoanPaymentWasDeleted::dispatch($delPayment);


            // mark main payment as deleted-canceled
            $payment->status = PaymentStatusEnum::CANCELED;
            $payment->active = 0;
            $payment->deleted = 1;
            $payment->deleted_at = now();
            $payment->deleted_by = getAdminId();
            $payment->description = (!empty($payment->description) ? $payment->description . "; " : "") . "Изтрито плащане с покриващо негативно плащане #" . $delPayment->payment_id;
            $payment->save();


            // mark negative payment as deleted-canceled
            $delPayment->status = PaymentStatusEnum::CANCELED;
            $delPayment->active = 0;
            $delPayment->deleted = 1;
            $delPayment->deleted_at = now();
            $delPayment->deleted_by = getAdminId();
            $delPayment->save();


            // update loan stats - recalc
            LoanPaymentAfterDeleted::dispatch($delPayment);

            DB::commit();

            if ($payment->payment_method_id == PaymentMethod::PAYMENT_METHOD_CASH) {
                $terminal = app(PHPProcessingService::class);
                $terminal->makeStornoReceipt($payment->office->fiscalDevice, $payment);
            }

            return true;

        } catch (Throwable $e) {
            DB::rollBack();

            throw $e;
        }
    }

    public function getLoanLastPayments(?Loan $loan, int $limit)
    {
        if (empty($loan)) {
            return [];
        }

        return Payment::with(['office'])
            ->where('direction', Payment::PAYMENT_DIRECTION_IN)
            ->where('loan_id', $loan?->getKey())
            ->where('active', 1)
            ->where('deleted', 0)
            ->orderBy('payment_id', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function getEasypayRequestLogs(
        int   $limit = 10,
        array $where = [],
        array $order = ['easypay_request_id' => 'DESC']
    )
    {
        if (isset($where['limit'])) {
            $limit = (int)$where['limit'];
            unset($where['limit']);
        }

        $where['deleted'] = '0';

        $builder = DB::table('easypay_request');
        $builder->select(
            DB::raw(
                "
            easypay_request.easypay_request_id,
            easypay_request.payment_id,
            easypay_request.loan_id,
            easypay_request.client_id,
            client.pin,
            TRIM(CONCAT_WS(' ', client.first_name, client.middle_name, client.last_name)) AS client_name,
            easypay_request.method,
            easypay_request.amount,
            easypay_request.request_data,
            easypay_request.request_status,
            easypay_request.sent_at,
            easypay_request.response_data,
            easypay_request.response_status,
            easypay_request.received_at,
            easypay_request.sys_code,
            easypay_request.details
        "
            )
        );
        $builder->join('client', 'client.client_id', '=', 'easypay_request.client_id');
        foreach ($where as $whereKey => $whereVal) {
            if (null === $whereVal) {
                continue;
            }

            if (in_array($whereKey, ['sent_at', 'received_at']) && preg_match(self::$dateRangeRegex, $whereVal)) {
                $extractedDates = $this->extractDates($whereVal);
                $builder->where("easypay_request." . $whereKey, ">=", $extractedDates['from']);
                $builder->where("easypay_request." . $whereKey, "<=", $extractedDates['to']);
                continue;
            }

            if ('pin' == $whereKey) {
                $builder->whereRaw('client.' . $whereKey . " ILIKE '%" . $whereVal . "%'");

                continue;
            }

            if ('client_name' == $whereKey) {
                $builder->whereRaw(
                    "(
                    client.first_name ILIKE '%" . $whereVal . "%'
                    OR client.middle_name ILIKE '%" . $whereVal . "%'
                    OR client.last_name ILIKE '%" . $whereVal . "%'
                )"
                );

                continue;
            }

            $builder->whereRaw("easypay_request." . $whereKey . " = '" . $whereVal . "'");
        }
        if ($order) {
            foreach ($order as $oK => $oV) {
                $builder->orderBy($oK, $oV);
            }
        }

        return $builder->paginate($limit);
    }
}
