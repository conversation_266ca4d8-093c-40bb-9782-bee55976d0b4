<?php

namespace Modules\Payments\Console;

use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Payment;
use Modules\Payments\Domain\Entities\OutgoingPayment;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;

// Для кейсов, когда мъ успешно отправили деньги но не смогли обработать коректно респонс
class EasyPayContinueAfterSendingCommand extends CommonCommand
{
    protected $name = 'script:create-easy-fix-after-sending';
    protected $signature = 'script:create-easy-fix-after-sending {payment_id} {status}'; // status = ok|ko
    protected $description = 'Manual run of ChangeEasyPayStatusAfterSendingListener';

    protected string $logChannel = 'easypay_refund';

    public function __construct() {
        parent::__construct();
    }

    public function handle()
    {
        $paymentId = (int) $this->argument('payment_id');
        $status = $this->argument('status');

        if (!in_array($status, ['ok', 'ko'])) {
            $this->info('Wrong status provide: ' . $status);
            return ;
        }

        $payment = Payment::where('payment_id', $paymentId)->first();
        if (empty($payment->payment_id)) {
            $this->info('Wrong payment_id provide: ' . $paymentId);
        }


        if (
            $payment->status == PaymentStatusEnum::EASY_PAY_SENT
            || $payment->status == PaymentStatusEnum::DELIVERED
        ) {
            $this->info('Wrong payment status: ' . $payment->status->value);
        }



        DB::beginTransaction();
        try {

            if ($status == 'ok') {
                $action = app(ConfirmEasyPaySendingAction::class);
                $action->execute($payment);
            } else {
                $outgoing = app(OutgoingPayment::class)->buildFromExisting($payment);
                $outgoing->changeStatusToFailure(getAdminId());
            }

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();

            $this->info('Failed processing, ', $e);
        }


        $this->info('The END: success');
    }
}
