<?php

namespace Modules\Payments\Application\Listeners\Source;

use App\Providers\EventServiceProvider;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Api\Domain\Events\EasyPayPaymentRequestWasProcessed;
use Modules\CashDesk\Domain\Entities\IncomingCashTransaction;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Models\Currency;
use Modules\Common\Models\EasyPayAttempt;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Payments\Application\Dto\NewPaymentDto;
use Modules\Payments\Domain\Entities\IncomingPayment;
use Throwable;

/** @see IncomingCashTransaction::dispatchCashReceived() */

/** @see EventServiceProvider */
readonly class CreateIncomingPaymentFromEasyPayApiListener
{
    public function __construct(private IncomingPayment $incomingPayment)
    {
    }

    public function handle(EasyPayPaymentRequestWasProcessed $event): void
    {
        try {

            DB::beginTransaction();
            $this->incomingPayment->buildFromDtoForAutomaticPayment(
                $this->createDto($event->easyPayAttempt)
            );
            DB::commit();

        } catch (Throwable $e) {
            DB::rollBack();

            throw $e;
        }
    }

    public function createDto(EasyPayAttempt $easyPayAttempt): NewPaymentDto
    {
        $env = app()->environment();
        $bankAccountId = config("bank_accounts." . $env  . "." . PaymentMethod::PAYMENT_METHOD_EASYPAY . ".income");

        $data = [
            'source' => PaymentSourceEnum::EASY_PAY_API,
            'method' => PaymentMethodEnum::EASY_PAY,
            'amount' => $easyPayAttempt->amount,
            'easy_pay_request' => $easyPayAttempt,
            'bank_account_id'  => $bankAccountId,

            'client_id' => $easyPayAttempt->client_id,
            'office_id' => Office::OFFICE_ID_WEB,

            'created_by' => $easyPayAttempt->created_by,
        ];

        return NewPaymentDto::from($data);
    }
}

