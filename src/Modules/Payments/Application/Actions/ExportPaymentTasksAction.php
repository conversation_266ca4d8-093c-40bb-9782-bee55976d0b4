<?php

namespace Modules\Payments\Application\Actions;

use Illuminate\Support\Collection;
use Modules\Payments\Repositories\PaymentTaskRepository;

readonly class ExportPaymentTasksAction
{
    public function __construct(private PaymentTaskRepository $repo) {}

    public function execute(array $filters = []): Collection
    {
        $rows = new Collection();

        /** @var \Modules\Common\Models\PaymentTask $paymentTask * */
        foreach ($this->repo->getByFilters($filters) as $paymentTask) {
            $row = [
                __('table.Id') => $paymentTask->getKey(),
                __('table.Type') => $paymentTask->type->label(),
                __('table.Trans') => __('payments::PaymentDirection.' . $paymentTask->direction),
                __('table.Source') => __('payments::paymentMethods.' . $paymentTask->payment_method_id),
                __('table.Amount') => amount($paymentTask->amount),
                __('table.clientNames') => $paymentTask->client?->getFullName(),
                __('table.Pin') => $paymentTask->client?->pin,
                __('table.Phone') => $paymentTask->client?->phone,
                __('table.CreatedAt') => formatDate($paymentTask->created_at, 'd.m.y H:i:s') ?? '',
                __('table.Timer') => $paymentTask->timer(),
                __('table.Status') => $paymentTask->getStatusLabel()
            ];

            $rows->push((object)$row);
        }

        return $rows;
    }
}
