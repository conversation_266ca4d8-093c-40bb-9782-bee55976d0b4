<?php

namespace Modules\Payments\Application\Actions\Task;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Approve\Application\Action\CancelLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\AbstractTask;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Models\SaleAttempt;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Models\SaleDecisionReason;
use Modules\Head\Repositories\Loan\LoanRefinanceRepository;
use Modules\Payments\Domain\Entities\IncomingPayment;
use Modules\Payments\Domain\Events\LoanPayoutWasRefunded;

// Първоначално това беше само за Изипей, но след това почнахме да го преизползваме за ръчно отказване на активни кредити.
// Използва се в:
// - RequestEasyPayRefundAction
// - ManualRefundCommand
readonly class ConfirmRefundAction
{
    public function __construct(
        private CancelLoanAction $cancelLoanAction,
        private IncomingPayment  $incomingPayment
    ) {}

    public function execute(int $paymentId): Payment
    {
        $payment = Payment::find($paymentId);

        return $this->executeCommon($payment);
    }

    public function executeByPayment(
        Payment $payment,
        ?PaymentTask $paymentTask = null,
        ?string $comment = null
    ): Payment {
        return $this->executeCommon($payment, $paymentTask, $comment);
    }

    private function executeCommon(
        Payment $payment,
        ?PaymentTask $paymentTask = null,
        ?string $comment = null
    ) {
        DB::beginTransaction();
        try {

            // restore closed loans for refinance
            $loan = $payment->loan;
                if (empty($loan->loan_id)) {
                throw new \Exception('Loan not found, payment #' . $payment?->payment_id ?? 'none');
            }
            if ($loan->isRefinanceLoan()) {

                $incomingPayments = $payment->getChildRefinancePayments(PaymentStatusEnum::DELIVERED->value);
                if ($incomingPayments->count() < 1) {
                    throw new \Exception('Failed to cancel refinance incoming payments, no payments found for parent payment #' . $payment->payment_id);
                }

                $refLoans = LoanRefinance::where('refinancing_loan_id', $loan->loan_id)
                    ->where('active', 1)
                    ->get();
                if ($incomingPayments->count() != $refLoans->count()) {
                    throw new \Exception('Failed to cancel refinance incoming payments, count of payments is not equal to the count of loans');
                }


                // cancel incoming payments
                // restore by snapshots
                foreach ($incomingPayments as $incomingPayment) {
                    $this->incomingPayment->new()->buildFromRefinancePaymentOnRestore($incomingPayment);
                }


                // deactivate and move loan_refinance int log + delete
                $refLogRepo = app(LoanRefinanceRepository::class);
                foreach ($refLoans as $refLoanRow) {
                    $refLogRepo->delete($refLoanRow);
                }
            }

            // OLD LOGIC: create incoming payment (RETURN) - not working anymore
            // NEW LOGIC: create copy of same payment but with negative amount.
            $newPayment = $this->incomingPayment
                ->new()
                ->buildRefundFromPayment($payment);

            // close loan
            $this->cancelLoanAction->execute(new DecisionDto(
                $payment->loan_id,
                getAdminId(),
                ApproveDecision::APPROVE_DECISION_CANCELED,
                ApproveDecisionReason::APPROVE_DECISION_REASON_PAYMENT_CANCELED,
                (!empty($comment) ? $comment : 'Неполучеи пари по Изипей'),
                null
            ));

            // update payment refund state
            $payment->refund_state = Payment::EASYPAY_REFUND_DONE;
            $payment->save();


            $now = now();
            $adminId = getAdminId();


            // close payment task
            if (!empty($paymentTask->payment_task_id)) {
                $taskStartEndAt = $now;
                $taskStartAt = $paymentTask->start_at;
                $duration = $now->diffInSeconds(Carbon::parse($taskStartAt));

                $paymentTask->end_at = $taskStartEndAt;
                $paymentTask->duration = $duration;
                $paymentTask->status = TaskStatusEnum::DONE;
                $paymentTask->handled_at = Carbon::now();
                $paymentTask->handled_by = $adminId;
                $paymentTask->save();


                // close sale task if exists
                $saleTask = $paymentTask->getRefundSaleTask();
                if (!empty($saleTask->sale_task_id)) {
                    $saleTask->last_status_update_date = $now;
                    $saleTask->status = AbstractTask::TASK_STATUS_DONE;
                    $saleTask->save();

                    // also create sale attempt
                    $duration2 = $now->diffInSeconds(Carbon::parse($saleTask->processed_at));
                    $saleAttempt = new SaleAttempt();
                    $saleAttempt->fill([
                        'administrator_id' => $adminId,
                        'sale_task_id' => $saleTask->sale_task_id,
                        'office_id' => $payment->office_id,
                        'sale_decision_id' => SaleDecision::SALE_DECISION_ID_OTHER,
                        'sale_decision_reason_id' => SaleDecisionReason::SALE_DECISION_REASON_ID_NO_INTEREST,
                        'start_at' => $saleTask->processed_at,
                        'end_at' => $now,
                        'total_time' => $duration2,
                        'last' => 1,
                        'processing_time' => $duration2,
                        'comment' => 'Изипей сторно',
                    ]);
                    $saleAttempt->save();
                }
            }

            LoanPayoutWasRefunded::dispatch($newPayment, $newPayment->loan);

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();

            Log::debug('ERROR ConfirmEasyPayRefundAction - ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            throw new \Exception('Failed to refund payment');
        }

        return $payment;
    }
}
