<?php

namespace Modules\Payments\Application\Actions\Task;
use Modules\Common\Models\Payment;
use Modules\Payments\Domain\Entities\OutgoingPayment;
use Modules\Payments\Application\Actions\DeliverIncomingRefinancePaymentsAction;

class ConfirmEasyPaySendingAction
{
    public function __construct(
        private OutgoingPayment $outgoingPayment
    ) {}

    public function execute(Payment $payment)
    {
        if (empty($payment->payment_id)) {
            throw new \Exception('Payment not found');
        }

        $loan = $payment->loan;
        if (empty($loan->loan_id)) {
            throw new \Exception('Loan not found, payment #' . $payment->payment_id);
        }

        // deliver incoming refinance payments (optional)
        if ($loan->isRefinanceLoan()) {
            $subAction = app(DeliverIncomingRefinancePaymentsAction::class);
            if (!$subAction->executeByParentLoan($loan, $payment)) {
                throw new \Exception('Failed to repay refinanced loans');
            }
        }

        return $this->outgoingPayment
            ->buildFromExisting($payment)
            ->changeStatusToSent(getAdminId())
            ->dbModel();
    }
}
