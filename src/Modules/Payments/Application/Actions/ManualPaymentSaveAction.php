<?php

namespace Modules\Payments\Application\Actions;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Admin\Repositories\SettingRepository;
use Modules\CashDesk\Domain\Entities\IncomingCashTransaction;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDescriptionEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Communication\Jobs\SendSmsPaymentWasReceived;
use Modules\Head\Repositories\LoanRepository;
use Modules\Payments\Application\Dto\NewPaymentDto;
use Modules\Payments\Domain\Entities\IncomingPayment;
use Modules\Payments\Domain\Exception\NoLoansProvidedForIncomingPayment;
use Throwable;

class ManualPaymentSaveAction
{
    public array $payments = [];

    public function __construct(
        private readonly IncomingPayment $incomingPayment,
        private readonly LoanRepository  $loanRepository
    ) {}

    public function execute(array $data): bool
    {
        $loanIds = $data['loans'];
        if (empty($loanIds)) {
            throw new NoLoansProvidedForIncomingPayment();
        }

        if (empty($data['payment_amount'])) {
            throw new \Exception('No amount provided for the payment(s)');
        }

        if (empty($data['bank_account_id'])) {
            throw new \Exception('No bank account provided for the payment(s)');
        }

        if (empty($data['office_id'])) {
            throw new \Exception('No office provided for the payment(s)');
        }
        if (
            (!empty($data['payment_id']) && empty($data['payment_task_id']))
            || (empty($data['payment_id']) && !empty($data['payment_task_id']))
        ) {
            throw new \Exception('No payment/payment task objects');
        }


        $totalAmount = (int) $data['payment_amount'];
        $totalAmountCheck = $totalAmount;
        $totalAmountSms = (float) intToFloat($totalAmount);


        // prevent multi request(s) of same loan/amount
        $cacheKey = 'blocked_payment_' . $totalAmount . '_' . implode('_', $loanIds);
        if (\Cache::has($cacheKey)) {
            throw new \Exception('Плащането с тази сума/заем се обработва в момента.');
        }
        \Cache::put($cacheKey, true, 5);


        // max allowed amount for Cash!
        // if due is bigger -> all operations are forbidden
        $maxVal = 100 * app(SettingRepository::class)->getSetting(
            SettingsEnum::maximum_loan_sum_cash_common
        )->default_value;


        // first check if total amount could cover all delivery amounts
        // write into the program validated amounts and rest to last element, etc
        $loanIdsMap = [];
        $loanRepaidMap = [];
        $loanRestAmountMap = [];
        $loanTacticProgram = [];
        $loanAmountProgram = [];
        $itterationTactic = null;
        foreach ($loanIds as $loanId) {

            $loan = $this->loanRepository->getById($loanId);
            if (empty($loan->loan_id)) {
                throw new \Exception('Unexisting loan #' . $loanId);
            }

            if (!$loan->isActive()) {
                throw new \Exception('Payment for non-active loan is forbidden #' . $loanId);
            }

            // validation of front passed params
            if (empty($data['loanAction'][$loanId])) {
                throw new \Exception('Invalid payment dalivery type for processing.');
            }
            $itterationTactic = $data['loanAction'][$loanId];


            $dbAmountForTactic = match ($itterationTactic) {
                PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value => $loan->getRegularRepaymentDebtDb(),
                PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value => $loan->getExtendLoanFeeAmountDb(),
                PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value => $loan->getEarlyRepaymentDebtDb(),
                default => throw new \Exception('Unknown delivery type for loan #' . $loanId)
            };


            // check, that if loan due amount > 10K, we can not take money for any type of payment
            if ($data['payment_method_id'] == PaymentMethod::PAYMENT_METHOD_CASH) {
                $checkCashAmount = $dbAmountForTactic;
                if ($itterationTactic != PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value ) {
                    $checkCashAmount = $loan->getRegularRepaymentDebtDb();
                }

                if ($checkCashAmount > $maxVal) {
                    throw new \Exception(__('payments::payments.amountGreaterThan10000'));
                }
            }


            // if agent skip spreading, we have only tactic so we need to calc amount here
            $itterationAmount = (
            !empty($data['loanPaymentAmount'][$loanId])
                ? (int)$data['loanPaymentAmount'][$loanId]
                : $dbAmountForTactic
            );

            // check for case where agent select loan payment and put lower amount then full repayment, so we go to partial repayment
            if (
                $itterationTactic == PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value
                && $itterationAmount > $totalAmount
            ) {
                $itterationAmount = $totalAmount;
            }

            // since early repayment and loan extension are strict amounts, we need to validate it
            if (
                in_array(
                    $itterationTactic,
                    [PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value, PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value]
                ) && $itterationAmount < $dbAmountForTactic
            ) {
                throw new \Exception('Small amount for: ' . PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value . ' of #' . $loanId);
            }


            // compare total amount VS delivery amount
            if ($totalAmount < $itterationAmount) {
                throw new \Exception('Not enough total amount for whole delivery');
            }


            // actualize total amount
            $totalAmount = $totalAmount - $itterationAmount;


            // set programs
            $loanIdsMap[$loanId] = $loan;
            $loanTacticProgram[$loanId] = $itterationTactic;
            $loanAmountProgram[$loanId] = $itterationAmount;

            // if the last element take total rest
            if ($loanId === end($loanIds) && $totalAmount > 0) {
                $loanRestAmountMap[$loanId] = $totalAmount;
            }

            // set repaid flag is possible
            if (
                in_array(
                    $itterationTactic,
                    [PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value, PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value]
                ) && $itterationAmount >= $dbAmountForTactic
            ) {
                $loanRepaidMap[$loanId] = $loanId;
            }
        }

        $totalDueAmount = 0;
        foreach ($loanAmountProgram as $loanId => $loanAmount) {
            $totalDueAmount += $loanAmount;
        }

        if ($data['payment_method_id'] == PaymentMethod::PAYMENT_METHOD_CASH) {
            if ($totalAmountCheck > $totalDueAmount) {
                if ($itterationTactic == PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value) {
                    throw new \Exception(__('payments::payments.amountGreaterThanExtendAmount'));
                }

                throw new \Exception(__('payments::payments.amountGreaterThanRequired'));
            }

            if ($totalAmountCheck > $maxVal) {
                throw new \Exception(__('payments::payments.amountGreaterThan10000'));
            }
        }

        DB::beginTransaction();
        try {

            foreach ($loanIdsMap as $loanId => $loan) {

                $amount = $loanAmountProgram[$loanId];
                $tactic = $loanTacticProgram[$loanId];
                $restAmount = !empty($loanRestAmountMap[$loanId]) ? $loanRestAmountMap[$loanId] : 0;

                $arrayDto = [
                    'source' => PaymentSourceEnum::MANUAL_CREATION,
                    'method' => PaymentMethodEnum::fromId($data['payment_method_id']),
                    'amount' => $amount,
                    'rest_amount' => $restAmount,
                    'purpose' => PaymentPurposeEnum::from($tactic),
                    'bank_account_id' => $data['bank_account_id'],

                    'loan' => $loan,
                    'loan_id' => $loan->getKey(),
                    'client_id' => $loan->client_id,
                    'office_id' => $data['office_id'],
                    'description' => ("Плащане по к-т: {$loanId}; " . ($data['description'] ?? '')) ?? PaymentDescriptionEnum::LOAN_REPAYMENT->text($loanId),
                    'document_number' => $data['document_number'] ?? null,

                    'created_by' => getAdminId(),
                ];
                if (isset($loanRepaidMap[$loanId])) {
                    $arrayDto['is_repaid'] = true;
                }

                $dto = NewPaymentDto::from($arrayDto);


                // switch if we have undistr.payment and payment task
                // we reuse the payment for 1st loan in delivery list
                // other loan(s) will go by normal flow - we lose relation to task(Confirmed with Svetlin)
                $hasUndistributedPayment = (!empty($data['payment_id']) && !empty($data['payment_task_id']));
                if ($hasUndistributedPayment) {

                    $this->payments[] = $this->incomingPayment
                        ->new()
                        ->buildFromDtoForManualPaymentWithPaymentAndTask($dto, $data['payment_id'], $data['payment_task_id'])
                        ->dbModel();

                    // if payment handled unset, so we next loop itteration could create a new one
                    unset($data['payment_task_id']);
                    unset($data['payment_id']);

                    continue;
                }

                $this->payments[] = $this->incomingPayment
                    ->new()
                    ->buildFromDtoForManualPayment($dto)
                    ->dbModel();
            }

            DB::commit();

            ///  if everything OK run print receipt
            collect($this->payments)->each(function (Payment $payment) {
                app(IncomingCashTransaction::class)->printReceiptFromPayment($payment);
            });

            // when everything is done, send sms to client - thanks for payment
            if (!empty($loan->loan_id)) {
                app(SendSmsPaymentWasReceived::class)->sendForLoan($loan, $totalAmountSms);
            }

        } catch (Throwable $e) {
            DB::rollBack();

            Log::error(
                'ManualPaymentSaveAction - ROLLBACK: '
                . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine()
            );

            throw $e;
        }


        return (bool)count($this->payments);
    }
}
