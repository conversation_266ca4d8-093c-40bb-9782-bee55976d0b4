<?php

namespace Modules\Payments\Application\Actions;

use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Traits\DateBuilderTrait;
use Modules\Head\Repositories\LoanRepository;
use Modules\Payments\Http\Dto\LoanInfoDTO;
use Modules\Payments\Services\DeliveryService;

class ManualPaymentDataAction
{
    use DateBuilderTrait;

    public function __construct(
        private readonly LoanRepository $loanRepository
    ) {}

    public function executeLoadLoansByClientId(array $clientIds): array
    {
        $data = [];
        $selectedOffice = null;
        $selectedBankAccountId = null;
        $selectedPaymentMethodId = null;

        // used for header panel with loans total info
        $activeLoans = $this->loanRepository->getActiveLoansWithRelationsByClientIds($clientIds, ['client', 'loanActualStats']);
        foreach ($activeLoans as $activeLoan) {

            if (!$selectedOffice) {
                $selectedOffice = $activeLoan->office;
                $selectedBankAccountId = $activeLoan->bank_account_id;
            }

            $trimurti = $activeLoan->getTriMurti();
            $client   = $activeLoan->client;
            $stats    = $activeLoan->loanActualStats;

            $data[] = [
                'clientId' => $activeLoan->client->getKey(),
                'pin' => $client->pin,
                'clientFullName' => $client->getFullName(),
                'loanId' => $activeLoan->getKey(),
                'approvedAmount' => intToFloat($activeLoan->amount_approved),
                'regularRepaymentAmount' => intToFloat($trimurti['regular']),
                'earlyRepaymentAmount' => intToFloat($trimurti['extension']),
                'extensionAmount' => intToFloat($trimurti['early']),
                'currentOverdueDays' => $stats->current_overdue_days,
                'accruedTotalAmount' => $stats->accrued_amount_total,
            ];
        }


        // used for top-right with payment details, load offices, bank coounts, etc
        $officeData = [];
        // if (!empty($selectedOffice->office_id)) {
        //     $officeData['paymentMethods'] = $selectedOffice->bankAccount->pluck('name', 'bank_account_id')->toArray();

        //     if (!empty($officeData['paymentMethods'])) {
        //         $officeData['paymentMethodBankAccount'] = $selectedOffice->bankAccount->pluck('paymentMethod.payment_method_id', 'bank_account_id')->toArray();

        //         // if loan has no bank_account(old or migrated) - use the first one
        //         if (empty($selectedBankAccountId) && !empty($officeData['paymentMethods'])) {
        //             $selectedBankAccountId = key($officeData['paymentMethods']);
        //         }

        //         if ($selectedBankAccountId && isset($officeData['paymentMethodBankAccount'][$selectedBankAccountId])) {
        //             $selectedPaymentMethodId = $officeData['paymentMethodBankAccount'][$selectedBankAccountId];
        //         }

        //         $officeData['selectedOfficeId'] = (int) $selectedOffice->office_id;
        //         $officeData['selectedBankAccountId'] = (int) $selectedBankAccountId;
        //         $officeData['selectedPaymentMethodId'] = (int) $selectedPaymentMethodId;
        //         $officeData['selectPaymentMethodsHtml'] = view('common::payment-methods.select-options', $officeData)->render();
        //     }
        // }


        return [
            'loans' => $data,
            'officeData' => $officeData,
        ];
    }

    public function executeLoadLoansInfo(LoanInfoDTO $loanInfoDTO): array
    {
        $amount = (int) $loanInfoDTO->payment_amount;
        $isTestDelivery = (!empty($loanInfoDTO->loanAction));

        if ($isTestDelivery && empty($amount)) {
            throw new \Exception(__('payments::manualPayment.NoAmountEnteredForDelivery'));
        }

        $data = [];
        $data['loans'] = $this->loanRepository->getLoansWithClients($loanInfoDTO->selectedLoanIds);

        $data['loanAmountDistribution'] = [];
        $data['totalAmountForDelivery'] = 0;

        if ($isTestDelivery) {

            foreach ($data['loans'] as $loan) {

                $tactic = $loanInfoDTO->loanAction[$loan->getKey()];
                $dbAmount = $this->getLoanAmountByTactic($tactic, $loan);

                // ако агент избрал тактика - предсорчно или удължаване и не е достаточна сума -> грешка
                if (
                    in_array($tactic, [PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value, PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value])
                    && $amount < $dbAmount
                ) {
                    throw new \Exception(__('payments::manualPayment.smallAmountForTactic') . $tactic);
                }


                $iterrationAmount = 0;
                if (in_array($tactic, [
                        PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value,
                        PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value
                ])) {
                    $iterrationAmount = $dbAmount;
                } else {
                    // в случая с нормално плащане има 2 варианта:
                    // 1. aко сума по-малка от пълно погасяване, намаляме я и отиваме на частично
                    if ($amount <= $dbAmount) {
                        $iterrationAmount = $amount;
                    }
                    // 2. aко обща сума е по-голяма от пълнои погасяване, взимаме сума на пълно погасяване
                    if ($amount > $dbAmount) {
                        $iterrationAmount = $dbAmount;
                    }
                }


                // баланс на обща сумма
                $amount = $amount - $iterrationAmount;


                $service = app(DeliveryService::class);
                $result = $service->getFakeDeliveredInstallmentsAndTaxes(
                    $loan,
                    $iterrationAmount,
                    $tactic
                );


                // prepare fake paid installments
                $data['installments'][$loan->getKey()] = $result['installments'];


                // prepare fake paid taxes
                $taxes = $result['taxes'];
                $otherTaxes = [];
                $collectorTaxes = [];
                $collectorTaxesSum = [];
                if ($taxes->count() > 0) {
                    foreach ($taxes as $tax) {

                        if (!$tax->isCollector()) {

                            // if (!empty($tax->tax_id) && $tax->paid == 1) {
                            //     continue; // skip paid non-collector taxes, we need them on installments even if they are paid
                            // }

                            $otherTaxes[] = $tax;
                            continue;
                        }

                        if (!isset($collectorTaxesSum[$tax->installment_id])) {
                            $collectorTaxes[$tax->installment_id] = [];

                            $collectorTaxesSum[$tax->installment_id] = [
                                'amount' => 0,
                                'rest_amount' => 0,
                                'paid_amount' => 0,
                            ];
                        }

                        $collectorTaxes[$tax->installment_id][] = $tax;

                        $collectorTaxesSum[$tax->installment_id]['amount'] = $tax->amount;
                        $collectorTaxesSum[$tax->installment_id]['rest_amount'] = $tax->rest_amount;
                        $collectorTaxesSum[$tax->installment_id]['paid_amount'] = $tax->paid_amount;
                    }
                }
                $data['taxes'][$loan->getKey()] = [];
                $data['taxes'][$loan->getKey()]['otherTaxes'] = $otherTaxes;
                $data['taxes'][$loan->getKey()]['collectorTaxes'] = $collectorTaxes;
                $data['taxes'][$loan->getKey()]['collectorTaxesSum'] = $collectorTaxesSum;

                $data['totalAmountForDelivery'] += $dbAmount;

                $data['loanAmountDistribution'][$loan->getKey()] = [
                    'type' => $tactic,
                    'amount' => $iterrationAmount,
                    'originAmount' => $dbAmount,
                ];
            }

        } else {

            foreach ($data['loans'] as $loan) {

                // prepare installments
                $data['installments'][$loan->getKey()] = $loan->getAllInstallments();

                // prepare taxes
                $taxes = $loan->getAllTaxes();
                $otherTaxes = [];
                $collectorTaxes = [];
                $collectorTaxesSum = [];
                if ($taxes->count() > 0) {
                    foreach ($taxes as $tax) {

                        if (!$tax->isCollector()) {

                            // if ($tax->paid == 1) {
                            //     continue; // skip paid non-collector taxes, we need them on installments even if they are paid
                            // }

                            $otherTaxes[] = $tax;
                            continue;
                        }

                        if (!isset($collectorTaxesSum[$tax->installment_id])) {
                            $collectorTaxes[$tax->installment_id] = [];

                            $collectorTaxesSum[$tax->installment_id] = [
                                'amount' => 0,
                                'rest_amount' => 0,
                                'paid_amount' => 0,
                            ];
                        }

                        $collectorTaxes[$tax->installment_id][] = $tax;

                        $collectorTaxesSum[$tax->installment_id]['amount'] = $tax->amount;
                        $collectorTaxesSum[$tax->installment_id]['rest_amount'] = $tax->rest_amount;
                        $collectorTaxesSum[$tax->installment_id]['paid_amount'] = $tax->paid_amount;
                    }
                }
                $data['taxes'][$loan->getKey()] = [];
                $data['taxes'][$loan->getKey()]['otherTaxes'] = $otherTaxes;
                $data['taxes'][$loan->getKey()]['collectorTaxes'] = $collectorTaxes;
                $data['taxes'][$loan->getKey()]['collectorTaxesSum'] = $collectorTaxesSum;

                $dbAmount = $iterrationAmount = $this->getLoanAmountByTactic(
                    PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value,
                    $loan
                );

                $data['totalAmountForDelivery'] += $dbAmount;

                $data['loanAmountDistribution'][$loan->getKey()] = [
                    'type' => PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value,
                    'amount' => $iterrationAmount,
                    'originAmount' => $dbAmount,
                ];
            }
        }


        // ако има остатък, връщаме и него
        if ($amount > 0) {
            $data['rest_payment_amount'] = $amount;
        }


        return $data;
    }

    protected function getLoanAmountByTactic(string $tactic, Loan $loan): int
    {
        if ($tactic === PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value) {
            return $loan->getRegularRepaymentDebtDb();
        }

        if ($tactic === PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value) {
            return $loan->getExtendLoanFeeAmountDb();
        }

        if ($tactic === PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value) {
            return $loan->getEarlyRepaymentDebtDb();
        }

        return 0;
    }

}
