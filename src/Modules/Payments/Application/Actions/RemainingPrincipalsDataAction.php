<?php

namespace Modules\Payments\Application\Actions;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Payments\FilterForms\RemainingPrincipalsFilterForm;
use Modules\Payments\Repositories\RemainingPrincipalsRepository;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class RemainingPrincipalsDataAction
{
    public function __construct(
        private readonly RemainingPrincipalsRepository $repo,
        private readonly FormBuilder $formBuilder
    ) {}

    public function execute(array $filters = []): array
    {
        $data = [];
        $data['rows'] = $this->repo->getBuilderByFilters($filters);
        $data['filterForm'] = $this->getRemainingPrincipalsFilterForm();

        return $data;
    }

    public function export(array $filters = [])
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            'Офис име',
            'Брой кредити',
            'Оставаща главница',
            'Брой просрочени',
            'Просрочена главница',
            'Ост. съдебна главница',
            'Брой съдебни',
            'Консултант',
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;

        $rows = $this->repo->getBuilderByFilters($filters);
        foreach ($rows as $row) {

            $r = [
                getOfficeName($row->office_id),
                $row->loans_count,
                (float) $row->remaining_principal,
                $row->loans_count_overdue,
                (float) $row->remaining_principal_overdue,
                (float) $row->remaining_principal_overdue_juridical,
                $row->loans_count_overdue_juridical,
                ($row->consultant_name ?? ''),
            ];

            // Populate data from the array
            $rowIndex++;
            foreach ($r as $columnIndex => $value) {
                $sheet->setCellValue(
                    [$columnIndex + 1, $rowIndex],
                    $value
                );
            }
        }

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'remaining_principals_' . time() . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
    }

    protected function getRemainingPrincipalsFilterForm(): Form
    {
        $options = ['route' => 'remaining-principals.index'];
        if (getAdmin()->hasPermissionTo('remaining-principals.export')) {
            $options['exportRoute'] = 'remaining-principals.export';
        }

        return $this->formBuilder->create(RemainingPrincipalsFilterForm::class, $options);
    }
}
