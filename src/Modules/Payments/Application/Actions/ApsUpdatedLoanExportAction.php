<?php

namespace Modules\Payments\Application\Actions;

use Modules\Collect\Models\OuterCollectorReport;
use Modules\Head\Repositories\LoanRepository;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ApsUpdatedLoanExportAction
{
    public function __construct(
        private LoanRepository $repository
    ) {}

    public function execute(): array|false
    {
        $rows = $this->repository->getApsExportRows(
            OuterCollectorReport::APS_CONSULTANT_ID
        );
        if (!count($rows)) {
            return false;
        }

        $headers = array_keys((array)$rows[0]);

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;

        ///// write xls here

        collect($rows)->each(function ($row) use (&$sheet, &$rowIndex) {
            $sheet->fromArray((array)$row, null, 'A' . ++$rowIndex);
        });

        // Calculate the maximum width for each column
        foreach (range('A', 'Z') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // Adjust row height based on content
        foreach ($sheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(true);

            $maxHeight = 15; // Default row height, adjust as needed

            foreach ($cellIterator as $cell) {
                $cellValue = $cell->getValue();
                if (is_string($cellValue)) {
                    $lineCount = substr_count($cellValue, "\n") + 1;
                    $estimatedHeight = $lineCount * 15; // Estimate 15 points per line, adjust as needed
                    if ($estimatedHeight > $maxHeight) {
                        $maxHeight = $estimatedHeight;
                    }
                }
            }
            $sheet->getRowDimension($row->getRowIndex())->setRowHeight($maxHeight);
        }

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'aps_loans_' . time() . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
    }
}
