<?php

namespace Modules\Payments\Application\Actions;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Payments\FilterForms\ReceivedPaymentsFilterForm;
use Modules\Payments\Repositories\ReceivedPaymentsRepository;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReceivedPaymentsDataActionSHIBAN
{
    public function __construct(
        private readonly ReceivedPaymentsRepository $repo,
        private readonly FormBuilder $formBuilder
    ) {}

    public function execute(array $filters = [], int $limit = 10): array
    {
        $filters = $this->actualizeFilters($filters);

        $data = [];
        $data['rows'] = $this->repo->getRowsByFilters($filters, $limit);
        $data['filterForm'] = $this->getPaymentsFilterForm();

        return $data;
    }

    public function export(array $filters = []): array
    {
        $filters = $this->actualizeFilters($filters);

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            'migration_db',
            'contract_number',
            'payment_id',
            'pmt_date',
            'product_type',
            'loan_date',
            'loan_amount',
            'capital_payment',
            'interest_payment',
            'forfeit_payment',
            'late_interest_payment',
            'fees_payment',
            'total_payment',
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;

        $builder = $this->repo->getBuilderByFiltersSHIBAN($filters);
        $builder->getQuery()->chunkById(
            100,
            function ($rows) use (&$sheet, &$rowIndex) {

                foreach ($rows as $row) {

                    $amounts = getExplainedPaymentDetails($row->delivery_string);

                    $r = [
                        $row->migration_db,
                        $row->contract_number,
                        $row->payment_id,
                        $row->pmt_date,
                        $row->product_type,
                        $row->loan_date,
                        $row->loan_amount,
                        (float) $amounts['principal'],
                        (float) $amounts['interest'],
                        (float) $amounts['penalty'],
                        (float) $amounts['late_interest'],
                        (float) $amounts['late_penalty_and_taxes'],
                        $row->total_paid_amount,
                    ];

                    // Populate data from the array
                    $rowIndex++;
                    foreach ($r as $columnIndex => $value) {
                        $sheet->setCellValue(
                            [$columnIndex + 1, $rowIndex],
                            $value
                        );
                    }
                }
            },
            'payment.payment_id',
            'payment_id'
        );

        // Create a writer object
        $writer = new Xlsx($spreadsheet);

        // Generate filename
        $fileName = 'export_cessia_plashtania_' . time() . '.xlsx';

        // Create a temporary file to store the Excel content
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_');
        $writer->save($tempFile);

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
    }

    protected function getPaymentsFilterForm(): Form
    {
        $options = ['route' => 'received-payments.index'];
        if (getAdmin()->hasPermissionTo('received-payments.export')) {
            $options['exportRoute'] = 'received-payments.export';
        }

        return $this->formBuilder->create(ReceivedPaymentsFilterForm::class, $options);
    }

    private function actualizeFilters(array $filters): array
    {
        // mandatory filter, we show only these statuses
        $filters['paymentStatus'] = [
            PaymentStatusEnum::DELIVERED->value, // изпратени и усвоени от клиент
            // PaymentStatusEnum::UNKNOWN->value, // остатъка при получени плащания
            // PaymentStatusEnum::EASY_PAY_SENT->value, // изпратени по Изипей
        ];
        $filters['direction'] = 'in';
        if (empty($filters['createdAt'])) {
            $filters['createdAt'] = Carbon::today()->format('d-m-Y');
        }
        // if (empty($filters['offices'])) {
        //     $filters['offices'] = getAdminOfficeIds();
        // }

        return $filters;
    }
}
