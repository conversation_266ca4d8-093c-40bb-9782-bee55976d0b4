<?php

namespace Modules\Payments\Application\Actions;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\Head\Repositories\LoanRepository;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Modules\Collect\Exports\OuterCollectorLmfLoansFullExportXlsx;

class LmfUpdatedLoanExportAction
{
    public function __construct(
        private LoanRepository $repository
    ) {}

    public function execute(): array
    {
        return $this->downloadCsv();
    }

    public function downloadXlsxViaMaatwebsite()
    {
        $fileName = 'lmf_loans_' . time() . '.xlsx';
        return (new OuterCollectorLmfLoansFullExportXlsx($this->repository))->download($fileName);
    }

    public function downloadXlsxViaPhpOffice()
    {
        $headers = [
            "Account Number",
            "EGN_EIK",
            "Product's name",
            "Gender",
            "Customer Name",
            "Due Date",
            "Total_Debt_Amount_at_Cession",
            "Principal",
            "Contractual_Interest",
            "Insurance",
            "Penalty_Charges_and_Expenditures",
            "Contractual_Penalty_Interest",
            "e-mail",
            "Phone_MOBILE_1",
            "Phone_MOBILE_2",
            "Phone_MOBILE_3",
            "Permanent Address City",
            "Permanent Address Street 1",
            "Current_Address_Postcode",
            "Current_Address_City",
            "Current_Address_Street 1",
            "Contract_Date",
            "Име на Лице за Контакт 1",
            "Телефонен Номер на Лице за Контакт 1",
            "Име на Лице за Контакт 2",
            "Телефонен Номер на Лице за Контакт 2",
            "Original Currency",
            "DPD at Cession",
            "Initial Loan Amount",
            "N Instalments by Contract",
            "Host_Last_Payment_Date",
            "Host_Last_Payment_Amount",
            "Брой неплатени вноски",
            "Сума Плащания за Последите 6M",
            "Сума Плащания за Последите 12M",
            "Сума Плащания за Последите 24M",
            "Сума на Всички Плащания",
        ];

        // Create a response with streaming
        $response = new StreamedResponse(function() use ($headers) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->fromArray($headers, null, 'A1');

            // Fetch and process the data in chunks
            $rowIndex = 1;
            $this->repository->getLmfExportRows()->chunk(200, function ($rows) use ($sheet, &$rowIndex) {

                /** @var \Modules\Common\Models\Loan $row **/
                foreach ($rows as $row) {
                    try {
                        $client = $row->client;
                        $contacts = $client->getLastContactsCollection(2);
                        $clientPhones = $client->allClientPhonesExceptMain(2);

                        $loanStats = $row->loanActualStats;
                        $lastPayment = $row->getLastPayment();
                        $overdueAmounts = $row->getOverdueAmounts();
                        $periodPayments = $row->getPaymentsByPeriods();

                        $phone2 = $clientPhones->get(0)?->number ?? '';
                        $phone3 = $clientPhones->get(1)?->number ?? '';

                        $addrCurrent = $client->clientLastAddressCurrent();
                        $addrIdCard = $client->clientLastAddressIdcard();

                        $contact1 = $contacts->get(0);
                        $contact2 = $contacts->get(1);

                        $gender = $client->gender == 'female' ? 'жена' : 'мъж';

                        $r = [
                            'account_number' => $row->loan_id,
                            'client_pin' => $client->pin,
                            'product_name' => $row->product_type_id == 1 ? 'до заплата' : 'на вноски',
                            'client_gender' => $gender,
                            'client_name' => $client->getFullName(),
                            'due_date' => $loanStats->last_installment_date,
                            'total_overdue_amount' => (
                                $overdueAmounts->overdue_principal_amount +
                                $overdueAmounts->overdue_interest_amount +
                                $overdueAmounts->overdue_penalty_amount +
                                $overdueAmounts->overdue_late_interest_amount +
                                $overdueAmounts->overdue_late_penalty_amount +
                                $overdueAmounts->overdue_tax_amount
                            ),
                            'overdue_principal_amount' => $overdueAmounts->overdue_principal_amount,
                            'overdue_interest_amount' => $overdueAmounts->overdue_interest_amount,
                            'insurance' => '0.00',
                            'overdue_charges' => (
                                $overdueAmounts->overdue_late_penalty_amount +
                                $overdueAmounts->overdue_late_interest_amount +
                                $overdueAmounts->overdue_tax_amount
                            ),
                            'overdue_penalty_amount' => $overdueAmounts->overdue_penalty_amount,
                            'client_email' => $client->email,
                            'client_phone1' => $client->phone,
                            'client_phone2' => $phone2,
                            'client_phone3' => $phone3,
                            'addr_idcard_city' => $addrIdCard?->city?->name,
                            'addr_idcard_address' => $addrIdCard?->getAddress(),
                            'addr_current_postcode' => $addrCurrent?->post_code,
                            'addr_current_city' => $addrCurrent?->city?->name,
                            'addr_current_address' => $addrCurrent?->getAddress(),
                            'contract_date' => Carbon::parse($row->created_at)->format('d.m.Y'),
                            'contact1_name' => $contact1?->name ?? '',
                            'contact1_phone' => $contact1?->phone ?? '',
                            'contact2_name' => $contact2?->name ?? '',
                            'contact2_phone' => $contact2?->phone ?? '',
                            'currency' => 'BGN',
                            'overdue_days' => $loanStats->current_overdue_days,
                            'loan_amount' => intToFloat($row->amount_approved),
                            'installments_count' => $loanStats->total_installments_count,
                            'last_payment_date' => !empty($lastPayment->created_at) ? Carbon::parse($lastPayment->created_at)->format('d.m.Y') : '',
                            'last_payment_amount' => !empty($lastPayment->amount) ? intToFloat($lastPayment->amount) : '',
                            'unpaid_installments_count' => $loanStats->unpaid_installments_count,
                            'paid_last_6m' => $periodPayments?->paid_180 ?? 0,
                            'paid_last_365' => $periodPayments?->paid_365 ?? 0,
                            'paid_last_730' => $periodPayments?->paid_730 ?? 0,
                            'total_paid' => $periodPayments?->total_paid ?? 0,
                        ];

                        $sheet->fromArray($r, null, 'A' . ++$rowIndex);
                    } catch (\Throwable $e) {
                        Log::error('Failed to process row: ' . $e->getMessage(), ['exception' => $e]);
                    }
                }
            });

            // Create a writer object
            $writer = new Xlsx($spreadsheet);

            // Write the spreadsheet to the output
            $writer->save('php://output');
        });

        $fileName = 'lmf_loans_' . time() . '.xlsx';
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment;filename="' . $fileName . '"');
        $response->headers->set('Cache-Control', 'max-age=0');

        return $response->send();
    }

    public function downloadCsv(): array
    {
        // Create a temporary file to store the CSV content
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_export_');
        $fp = fopen($tempFile, 'w');

        // Add BOM for UTF-8
        fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));

        $header = [
            "Account Number",
            "EGN_EIK",
            "Product's name",
            "Gender",
            "Customer Name",
            "Due Date",
            "Total_Debt_Amount_at_Cession",
            "Principal",
            "Contractual_Interest",
            "Insurance",
            "Penalty_Charges_and_Expenditures",
            "Contractual_Penalty_Interest",
            "e-mail",
            "Phone_MOBILE_1",
            "Phone_MOBILE_2",
            "Phone_MOBILE_3",
            "Permanent Address City",
            "Permanent Address Street 1",
            "Current_Address_Postcode",
            "Current_Address_City",
            "Current_Address_Street 1",
            "Contract_Date",
            "Име на Лице за Контакт 1",
            "Телефонен Номер на Лице за Контакт 1",
            "Име на Лице за Контакт 2",
            "Телефонен Номер на Лице за Контакт 2",
            "Original Currency",
            "DPD at Cession",
            "Initial Loan Amount",
            "N Instalments by Contract",
            "Host_Last_Payment_Date",
            "Host_Last_Payment_Amount",
            "Брой неплатени вноски",
            "Сума Плащания за Последите 6M",
            "Сума Плащания за Последите 12M",
            "Сума Плащания за Последите 24M",
            "Сума на Всички Плащания",
        ];
        fputcsv($fp, $header);

        $chunkSize = 100;
        $this->repository->getLmfExportRows()->chunk($chunkSize, function ($rows) use($fp) {
            /** @var \Modules\Common\Models\Loan $row */
            foreach ($rows as $row) {
                try {
                    $loanStats = $row->loanActualStats;
                    $overdueAmounts = $row->getOverdueAmounts();
                    $lastPayment = $row->getLastPayment();
                    $periodPayments = $row->getPaymentsByPeriods();
                    $client = $row->client;
                    $clientPhones = $client->allClientPhonesExceptMain(2);
                    $contacts = $client->getLastContactsCollection(2);

                    $phone2 = $clientPhones->get(0)?->number ?? '';
                    $phone3 = $clientPhones->get(1)?->number ?? '';

                    $addrCurrent = $client->clientLastAddressCurrent();
                    $addrIdCard = $client->clientLastAddressIdcard();

                    $contact1 = $contacts->get(0);
                    $contact2 = $contacts->get(1);

                    $gender = $client->gender == 'female' ? 'жена' : 'мъж';

                    $r = [
                        'account_number' => $row->loan_id,
                        'client_pin' => $client->pin,
                        'product_name' => $row->product_type_id == 1 ? 'до заплата' : 'на вноски',
                        'client_gender' => $gender,
                        'client_name' => $client->getFullName(),
                        'due_date' => $loanStats->last_installment_date,
                        'total_overdue_amount' => (
                            $overdueAmounts->overdue_principal_amount +
                            $overdueAmounts->overdue_interest_amount +
                            $overdueAmounts->overdue_penalty_amount +
                            $overdueAmounts->overdue_late_interest_amount +
                            $overdueAmounts->overdue_late_penalty_amount +
                            $overdueAmounts->overdue_tax_amount
                        ),
                        'overdue_principal_amount' => $overdueAmounts->overdue_principal_amount,
                        'overdue_interest_amount' => $overdueAmounts->overdue_interest_amount,
                        'insurance' => '0.00',
                        'overdue_charges' => (
                            $overdueAmounts->overdue_late_penalty_amount +
                            $overdueAmounts->overdue_late_interest_amount +
                            $overdueAmounts->overdue_tax_amount
                        ),
                        'overdue_penalty_amount' => $overdueAmounts->overdue_penalty_amount,
                        'client_email' => $client->email,
                        'client_phone1' => $client->phone,
                        'client_phone2' => $phone2,
                        'client_phone3' => $phone3,
                        'addr_idcard_city' => $addrIdCard?->city?->name,
                        'addr_idcard_address' => $addrIdCard?->getAddress(),
                        'addr_current_postcode' => $addrCurrent?->post_code,
                        'addr_current_city' => $addrCurrent?->city?->name,
                        'addr_current_address' => $addrCurrent?->getAddress(),
                        'contract_date' => Carbon::parse($row->created_at)->format('d.m.Y'),
                        'contact1_name' => $contact1?->name ?? '',
                        'contact1_phone' => $contact1?->phone ?? '',
                        'contact2_name' => $contact2?->name ?? '',
                        'contact2_phone' => $contact2?->phone ?? '',
                        'currency' => 'BGN',
                        'overdue_days' => $loanStats->current_overdue_days,
                        'loan_amount' => intToFloat($row->amount_approved),
                        'installments_count' => $loanStats->total_installments_count,
                        'last_payment_date' => !empty($lastPayment->created_at) ? Carbon::parse($lastPayment->created_at)->format('d.m.Y') : '',
                        'last_payment_amount' => !empty($lastPayment->amount) ? intToFloat($lastPayment->amount) : '',
                        'unpaid_installments_count' => $loanStats->unpaid_installments_count,
                        'paid_last_6m' => $periodPayments?->paid_180 ?? 0,
                        'paid_last_365' => $periodPayments?->paid_365 ?? 0,
                        'paid_last_730' => $periodPayments?->paid_730 ?? 0,
                        'total_paid' => $periodPayments?->total_paid ?? 0,
                    ];

                    fputcsv($fp, $r);
                } catch (\Throwable $e) {
                    Log::error('Failed to process row: ' . $e->getMessage(), ['exception' => $e]);
                }
            }
        });

        fclose($fp);

        // Generate filename
        $fileName = 'lmf_loans_' . time() . '.csv';

        // Return the file path and filename for the controller to handle the download
        return [
            'file_path' => $tempFile,
            'file_name' => $fileName,
            'content_type' => 'text/csv; charset=UTF-8'
        ];
    }
}
