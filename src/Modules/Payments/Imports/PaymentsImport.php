<?php

namespace Modules\Payments\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithEvents;
use Modules\Head\Services\LoanService;
use Modules\Common\Models\Currency;
use Modules\Common\Models\ImportedPayment;
use Throwable;

class PaymentsImport implements toCollection, WithEvents, WithBatchInserts, WithChunkReading
{
    use RegistersEventListeners;

    protected const BATCH_SIZE = 1000;

    protected array $payments;
    protected LoanService $loanService;

    public function __construct(
        LoanService $loanService
    )
    {
        $this->payments = [];
        $this->loanService = $loanService;
    }

    public function getPayments(): array
    {
        return $this->payments;
    }

    public function collection(Collection $rows)
    {
        /*
         *  Rows have 10 columns
         *
         *  0 - Reference Number
         *  1 - Accounting Date
         *  2 - Value Date
         *  3 - Debit
         *  4 - Credit
         *  5 - Document
         *  6 - Sender
         *  7 - Payment Basis
         *  8 - Details (May contain PIN and/or LoanId)
         *  9 - More Details (Usually just IBAN)
         *
         */

        foreach ($rows as $num => $row) {

            // skip headers
            if (
                !$row[0]
                || empty($row[4])
                || !is_numeric($row[4])
                || !preg_match('/(превод|плащане|вноска|получен)/iu', $row[5])
            ) {
                continue;
            }

            $loan = null;
            $basisAndDetails = $row[7] . ' ' . $row[8];
            $pinData = ImportedPayment::extractPinData($basisAndDetails);
            $iban = ImportedPayment::extractIBAN($row[9] ?? $row[8]) ?? '';

            $pin = $pinData['clearedPin'] ?? '';
            if (!empty($pin) && !empty($pinData['originalFind'])) {
                $basisAndDetails = str_replace($pinData['originalFind'] ?? '', '', $basisAndDetails);
            }

            $loanId = ImportedPayment::extractLoanId($basisAndDetails);

            try {
                $loan = $this->loanService->getLoanById((int)$loanId);
                if (!$loan->isActive()) {
                    $loan = null;
                }
            } catch (Throwable $t) {
                // Avoid exception because not found loan
            }

            $currencyId = null;
            $currencyCheck = strtolower($row[5]);

            if (strpos($currencyCheck, 'лев')) {
                $currencyId = Currency::BGN_CURRENCY_ID;
            }

            $payment = new ImportedPayment();
            $rData = [
                'iban' => $iban,
                'debit' => !empty($row[3]) ? intval($row[3]) : 0,
                'credit' => floatToInt($row[4]),
                'sender' => $row[6] ?? '',
                'details' => $row[8] ?? '',
                'loan_id' => (!empty($loan->loan_id) ? $loan->loan_id : null),
                'document' => $row[5],
                'value_date' => $row[2],
                'client_pin' => $pin,
                'currency_id' => $currencyId,
                'more_details' => $row[9],
                'payment_basis' => $row[7],
                'reference_code' => $row[0],
                'accounting_date' => $row[1],
            ];
            $payment->fill($rData);

            array_push($this->payments, $payment);
        }
    }

    public function batchSize(): int
    {
        return self::BATCH_SIZE;
    }

    public function chunkSize(): int
    {
        return self::BATCH_SIZE;
    }
}
