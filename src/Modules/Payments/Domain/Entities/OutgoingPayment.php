<?php

namespace Modules\Payments\Domain\Entities;

use Illuminate\Support\Carbon;
use Modules\Approve\Domain\Events\LoanWasActivated;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Enums\PaymentDescriptionEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment as DbModel;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\PaymentTask;
use Modules\Payments\Application\Dto\NewPaymentDto;
use Modules\Payments\Domain\Entities\Loan\LoanForActivation;
use Modules\Approve\Domain\Entities\Loan\LoanForCancellation;
use Modules\Payments\Domain\Events\LoanPayoutWasDelivered;
use Modules\Payments\Domain\Exception\PaymentHasIncorrectDirection;
use Modules\Payments\Domain\Exception\PaymentHasIncorrectMethod;
use Modules\Payments\Domain\Exception\PaymentMustBeOutgoing;
use Modules\Payments\Domain\Exception\PaymentNotFoundById;
use Modules\Payments\Domain\Exception\PaymentNotSaved;
use Modules\Payments\Repositories\PaymentRepository as Repo;

class OutgoingPayment extends DomainModel implements PaymentInterface
{
    public function __construct(
        private DbModel             $dbModel,
        private Repo                $repo,
        private Task                $paymentTask,
        private LoanForActivation   $loanForActivation,
        private LoanForCancellation $loanForCancellation,
        public CurrentDate          $currentDate
    )
    {
    }

    /***************************** BUILDING ***********************/

    public static function loadSelf(int $id): self
    {
        return app()->make(self::class)->loadById($id);
    }

    public function loadById(int $id): self
    {
        $dbModel = $this->repo->getById($id);
        if (!$dbModel) {
            throw new PaymentNotFoundById($id);
        }
        return $this->buildFromExisting($dbModel);
    }

    public function buildFromExisting(DbModel $dbModel): self
    {
        if ($dbModel->direction !== PaymentDirectionEnum::OUT) {
            throw new PaymentHasIncorrectDirection($dbModel->getKey(), $dbModel->getAttribute('direction'));
        }
        $this->dbModel = $dbModel;
        return $this;
    }

    public function buildFromDto(NewPaymentDto $dto): self
    {
        $paymentMethodId = $dto->method->id();
        if (empty($paymentMethodId)) {
            throw new \Exception('No payment method passed for outgoing payment creation');
        }


        $this->setLoan($dto->loan)
            ->setAdminId($dto->created_by)
            ->setBankAccountId($dto->loan->bank_account_id)
            ->setClient($dto->loan->client)
            ->setOffice()
            ->setCurrency()
            ->setMethod($paymentMethodId)
            ->setSource($dto->source)
            ->setPurpose($dto->purpose)
            ->setDirection()
            ->setParentPaymentId($dto->parent_payment?->getKey())
            ->setAmount($dto->amount)
            ->setDescription($dto->description)
            ->setStatus(PaymentStatusEnum::NEW)
            ->checkIfAlreadyExists()
            ->save();


        // create task for easypay & bank & cash
        if (
            in_array(
                $paymentMethodId,
                [
                    PaymentMethod::PAYMENT_METHOD_CASH, // неусвоени пари за офиси без одобрение
                    PaymentMethod::PAYMENT_METHOD_BANK, // ръчно изпращане по банка
                    PaymentMethod::PAYMENT_METHOD_EASYPAY, // new преди изпращане
                ]
            )
        ) {
            $this->createNewPaymentTask();
        }


        return $this;
    }

    public function checkIfAlreadyExists(): self
    {
        if (empty($this->dbModel->loan_id)) {
            return $this;
        }

        $outPayment = DbModel::where('loan_id', $this->dbModel->loan_id)
            ->where('direction', 'out')
            ->where('source', PaymentSourceEnum::LOAN_APPROVAL)
            ->first();
        if (!empty($outPayment->payment_id)) {
            throw new \Exception('Out payment is already exists');
        }


        return $this;
    }

    public function delete()
    {
        $this->repo->delete($this->dbModel);
    }

    /************************* EasyPay only statuses ******************/

    public function changeStatusToSent(int $adminId): self
    {
        // change status
        // close payment task
        // activate loan
        $this
            ->changeStatus(PaymentStatusEnum::EASY_PAY_SENT, $adminId)
            ->activateLoan();

        // create accounting row
        LoanPayoutWasDelivered::dispatch($this->dbModel);

        return $this;

        // ->deliverRefinancing($adminId);
    }

    public function changeStatusToFailure(int $adminId): self
    {
        return $this->changeStatus(PaymentStatusEnum::EASY_PAY_SENDING_FAILED, $adminId);
    }

    /************************** COMMON statuses ********************/
    public function changeStatusToCanceled(
        int     $adminId,
        ?string $comment = null,
        ?string $reason = null
    ): self
    {

        $this->changeStatus(PaymentStatusEnum::CANCELED, $adminId, $comment);

        $this->cancelLoan($comment, $reason);

        return $this->closePaymentAndPaymentTask();
    }

    public function closePaymentTask()
    {
        $task = $this->paymentTask->loadFromPayment($this->dbModel);
        if (is_null($task)) {
            return $this;
        }

        $task->close(false);

        return $this;
    }

    public function changeStatusToDelivered(int $adminId): self
    {
        $paymentMethod = $this->dbModel->method();

        // on easypay we're just changing payment status and exit
        if ($paymentMethod === PaymentMethodEnum::EASY_PAY) {
            $this->changeStatus(PaymentStatusEnum::DELIVERED, $adminId);

            return $this;
        }


        // for bank we change payment status
        // activate loan + update stats, client.new flag
        // create accounting row
        if ($paymentMethod === PaymentMethodEnum::BANK || $paymentMethod === PaymentMethodEnum::CASH) {

            $this->changeStatus(PaymentStatusEnum::DELIVERED, $adminId);

            $this->activateLoan();

            $payment = $this->dbModel->refresh();

            // create accounting row
            LoanPayoutWasDelivered::dispatch($payment);

            return $this;
        }


        return $this;

        //->deliverRefinancing($adminId);
    }

    public function processTask(PaymentTask $paymentTask, int $adminId): self
    {

        $this->paymentTask->buildFromExisting($paymentTask)
            ->process($adminId)
            ->dbModel();

        return $this->setAdminId($adminId)->save();
    }

    /**************************** END OF INTERFACE *******************/

    private function changeStatus(PaymentStatusEnum $status, int $adminId, ?string $comment = null): self
    {
        return $this
            ->setStatus($status)
            ->setAdminId($adminId)
            ->setDirection()
            ->save()
            ->createNextTask($comment);
    }

    public function setBankAccountId(?int $bankAccountId = null): self
    {
        if ($bankAccountId) {
            $this->dbModel->setAttribute('bank_account_id', $bankAccountId);
        }

        return $this;
    }

    private function setLoan(DbLoan $dbLoan): self
    {
        $this->dbModel->loan_id = $dbLoan->loan_id;
        return $this;
    }

    private function setClient(DbClient $dbClient): self
    {
        $this->dbModel->client_id = $dbClient->getKey();
        return $this;
    }

    private function setStatus(PaymentStatusEnum $newStatus)
    {
        if (!$this->dbModel->exists) {
            $this->dbModel->status = $this->dbModel->method() === PaymentMethodEnum::OFFSET
                ? $newStatus
                : PaymentStatusEnum::NEW;

            return $this;
        }

        $this->dbModel->status = $newStatus;
        if ($this->dbModel->status === PaymentStatusEnum::EASY_PAY_SENT) {
            $this->dbModel->sent_at = $this->currentDate->now()->toDateTimeString();
        }
        if ($this->dbModel->status === PaymentStatusEnum::DELIVERED) {
            $this->dbModel->handled_at = $this->currentDate->now()->toDateTimeString();

            // we need to register for Epay when client took money
            $paymentMethod = $this->dbModel->method();
            if ($paymentMethod === PaymentMethodEnum::EASY_PAY) {
                $this->dbModel->delivered_at = $this->currentDate->now()->toDateTimeString();
            }
        }

        return $this;
    }

    private function setMethod(?int $paymentMethodId = null): self
    {
        if ($this->dbModel->payment_method_id && $this->dbModel->payment_method_id !== $this->dbModel->loan->payment_method_id) {
            throw new PaymentHasIncorrectMethod(
                $this->dbModel->getKey(),
                PaymentMethodEnum::fromId($this->dbModel->loan->payment_method_id)
            );
        }

        $this->dbModel->payment_method_id = $paymentMethodId ?: $this->dbModel->loan->payment_method_id;

        return $this;
    }

    private function setAmount(?int $singleRefinanceAmount = null): self
    {
        $this->dbModel->amount = match ($this->dbModel->method()) {
            PaymentMethodEnum::CASH => ($this->dbModel->loan->getAmountToPaid()),
            PaymentMethodEnum::EASY_PAY,
            PaymentMethodEnum::BANK => ($this->dbModel->loan->amount_rest -= $this->dbModel->loan->insurance_amount),
            PaymentMethodEnum::OFFSET => $singleRefinanceAmount
        };

        return $this;
    }

    private function setDescription(?string $description = null): self
    {
        if ($description) {
            $this->dbModel->description = $description;
            return $this;
        }

        $description = $this->dbModel->amount === $this->dbModel->loan->amount_approved
            ? PaymentDescriptionEnum::LOAN_REPAYMENT
            : PaymentDescriptionEnum::DIFF_OUTGOING;
        $this->dbModel->description = $description->text($this->dbModel->loan->getKey());
        return $this;
    }

    private function setOffice(?int $officeId = null): self
    {
        $this->dbModel->office_id = $officeId ?: $this->dbModel->loan->office_id;
        return $this;
    }

    private function setCurrency(?int $currencyId = null): self
    {
        $this->dbModel->currency_id = $currencyId ?: $this->dbModel->loan->currency_id;
        return $this;
    }

    private function setDirection(): self
    {
        $this->dbModel->direction = 'out';

        return $this;
    }

    private function setParentPaymentId(?int $parentId = null): self
    {
        if ($parentId) {
            $this->dbModel->parent_payment_id = $parentId;
        }
        return $this;
    }

    private function setPurpose(PaymentPurposeEnum $purpose): self
    {
        $this->dbModel->purpose = $purpose;

        return $this;
    }

    private function setSource(?PaymentSourceEnum $source = null): self
    {
        if (!$source) {
            $source = PaymentSourceEnum::LOAN_APPROVAL;
        }
        $this->dbModel->source = $source;

        return $this;
    }

    private function setAdminId(int $adminId): self
    {
        $this->dbModel->handled_by = $adminId;
        return $this;
    }

    private function save(): self
    {
        $payment = $this->repo->save($this->dbModel);
        if (!$payment) {
            throw new PaymentNotSaved();
        }

        return $this;
    }

    private function createNewPaymentTask(): self
    {
        if ($this->dbModel->source === PaymentSourceEnum::LOAN_REFINANCE) {
            return $this;
        }

        $this->paymentTask->buildFromPayment($this->dbModel);

        return $this;
    }

    private function createNextTask(?string $comment): self
    {
        if ($this->dbModel->source === PaymentSourceEnum::LOAN_REFINANCE) {
            return $this;
        }

        $task = $this->paymentTask->loadFromPayment($this->dbModel);
        if (is_null($task)) {
            return $this;
        }

        $this->paymentTask = $this->dbModel->shouldHaveNoTask()
            ? $task->close($this->dbModel->status === PaymentStatusEnum::CANCELED, $comment)
            : $task->createNext();

        return $this;
    }

    private function activateLoan(): self
    {
        if ($this->dbModel->method() === PaymentMethodEnum::EASY_PAY && $this->dbModel->status !== PaymentStatusEnum::EASY_PAY_SENT) {
            return $this;
        }
        if ($this->dbModel->method() === PaymentMethodEnum::OFFSET) {
            return $this;
        }


        $loan = $this->loanForActivation
            ->buildFromExisting($this->dbModel->loan)
            ->activate($this->dbModel);


        // updates client stats
        // updates client.new flag to = 0
        LoanWasActivated::dispatchIf(
            $loan->dbModel()->loan_status_id === LoanStatus::ACTIVE_STATUS_ID,
            $loan->dbModel()
        );


        return $this;
    }

    private function cancelLoan(
        ?string $description = null,
        ?string $reason = null
    ): self
    {

        $dto = new DecisionDto(
            $this->dbModel->loan_id,
            getAdminId(),
            ApproveDecision::APPROVE_DECISION_CANCELED,
            $reason ?? '',
            $description ?? '',
            null
        );

        $this->loanForCancellation
            ->loadById($this->dbModel->loan_id)
            ->process($dto);

        return $this;
    }

    protected function closePaymentAndPaymentTask(): self
    {
        // close payment
        $payment = $this->dbModel;
        $payment->status = PaymentStatusEnum::CANCELED;
        $payment->disabled_at = Carbon::now();
        $payment->disabled_by = getAdminId();
        $payment->save();

        $paymentTask = $payment->paymentTask;
        if (empty($paymentTask->payment_task_id)) {
            return $this;
        }

        // close payment task
        $taskStartEndAt = now();
        $taskStartAt = $paymentTask->start_at;
        $duration = $taskStartEndAt->diffInSeconds(Carbon::parse($taskStartAt));

        $paymentTask->end_at = $taskStartEndAt;
        $paymentTask->duration = $duration;
        $paymentTask->status = TaskStatusEnum::DONE;
        $paymentTask->handled_at = Carbon::now();
        $paymentTask->handled_by = getAdminId();
        $paymentTask->save();


        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function direction(): PaymentDirectionEnum
    {
        return PaymentDirectionEnum::OUT;
    }

    public function paymentTask(): Task
    {
        return $this->paymentTask;
    }

    public function loan(): LoanForActivation
    {
        return $this->loanForActivation;
    }
}
