<?php

namespace Modules\Payments\Domain\Entities;

use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\ImportedPayment as DbModel;
use Modules\Common\Models\Loan;
use Modules\Head\Repositories\ClientBankAccountRepository;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Payments\Repositories\ImportedPaymentRepository as Repo;

class BankTransfer
{
    private DbModel $dbModel;
    private ?Loan $loan = null;
    public function __construct(
        private readonly Repo $repo,
        private readonly LoanRepository $loanRepo,
        private readonly ClientRepository $clientRepo
    ){}
    public function paymentExists(DbModel $importedPayment): bool
    {
        return $this->repo
            ->getByConditions([
                'reference_code' => $importedPayment->reference_code,
                'credit' => $importedPayment->credit,
                'value_date' => $importedPayment->value_date,
            ])
            ->isNotEmpty();
    }

    public function buildFromPartiallyFilled(DbModel $importedPayment): self
    {
        return $this->setDbModel($importedPayment)
            ->setPaymentBasis()
            ->setLoan()
            ->setClient()
            ->save();
    }

    private function setDbModel(?DbModel $importedPayment = null): self
    {
        $this->dbModel = $importedPayment ? : new DbModel();
        return $this;
    }

    private function setPaymentBasis(): self
    {
        $purpose = PaymentPurposeEnum::tryFrom($this->dbModel->payment_basis);
        if($purpose){
            return $this;
        }
        $this->dbModel->details = $this->dbModel->payment_basis.' '.$this->dbModel->details;
        $this->dbModel->payment_basis = $this->dbModel->guessPurpose()->value;
        return $this;
    }

    private function setLoan(?Client $client = null): self
    {
        if(PaymentPurposeEnum::SERVICE_FEE->value === $this->dbModel->payment_basis){
            $this->dbModel->loan_id = null;
            return $this;
        }
        if($client && $client->loans->count() === 1){
            $this->loan = $client->loans->first();
            return $this;
        }
        if($this->dbModel->loan_id){
            $this->loan = $this->loanRepo->getById($this->dbModel->loan_id);
            if(! $this->loan){
                $this->dbModel->more_details .= 'INCORRECT LOAN_ID: '.$this->dbModel->loan_id;
                $this->dbModel->loan_id = null;
            }
        }

        return $this;
    }

    private function setClient(): self
    {
        //BY LOAN
        if($this->loan){
            $this->dbModel->client_id = $this->loan->client_id;
            return $this;
        }

        //BY PIN
        $pin = $this->dbModel->client_pin;
        $client = $pin ? $this->clientRepo->getByPin($pin) : null;

        //BY SENDER

        $clientData = [];
        if (!$client && $this->dbModel->sender) {
            $clients = $this->clientRepo->searchClientsByString($this->dbModel->sender, false);
            if (count($clients) === 1) {
                $clientData = $clients[0];
            }
            if (!empty($clientData['client_id'])) {
                $client = $this->clientRepo->getById($clientData['client_id']);
            }
        }

        //BY IBAN
        if (!$client && $this->dbModel->iban) {
            $clientIds = app(ClientBankAccountRepository::class)
                ->getByIBAN($this->dbModel->iban)
                ->pluck('client_id')
                ->toArray();

            if (count(array_unique($clientIds)) === 1) {
                $client = $this->clientRepo->getById($clientIds[0]);
            }
        }

        if ($client) {
            $this->dbModel->client_id = $client->getKey();
            $this->setLoan($client);
        }

        return $this;
    }

    private function save(): self
    {
        $this->repo->save($this->dbModel);
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
