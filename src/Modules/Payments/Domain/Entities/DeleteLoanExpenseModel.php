<?php

namespace Modules\Payments\Domain\Entities;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Entities\DelExpenseSnapshot;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Tax;
use Modules\Payments\Application\Dto\DeleteLoanExpenseRequestDto;
use Modules\Payments\Repositories\DeletedLoanAmountRepository;

class DeleteLoanExpenseModel
{
    private $amountStart = null;
    private $skipTaxes = true;
    private $manual = true;

    private $snapshotId = null;
    private $snapshot = null;

    public function __construct(
        private Loan $loan,
        private DeletedLoanAmountRepository $deletedLoanAmountRepository,
        private CustomEloquentCollection $loanFeesCollection,
        private CustomEloquentCollection $unpaidInstallments,
        private Collection $deletedAmounts,
    ) {
    }

    public function build(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        $this->amountStart = $deleteLoanExpenseRequestDto->amount;

        $amount = (float) intToFloat($deleteLoanExpenseRequestDto->amount);
        $this->saveSnapshot($amount);

        if (!$this->skipTaxes) {
            $this->setUnpaidTaxes();
        } else {
            $this->setUnpaidTaxesCollector();
            $this->deleteCollectorLoanFees($deleteLoanExpenseRequestDto);
        }

        $this->setUnpaidInstallments($deleteLoanExpenseRequestDto);

        if (!$this->skipTaxes) {
            $this->deleteExtendLoanFees($deleteLoanExpenseRequestDto)
                ->deleteManualLoanFees($deleteLoanExpenseRequestDto)
                ->deleteCollectorLoanFees($deleteLoanExpenseRequestDto);
        }

        return $this
            ->deleteLoanLatePenalty($deleteLoanExpenseRequestDto)
            ->deleteLoanLateInterest($deleteLoanExpenseRequestDto)
            ->deleteLoanPenalty($deleteLoanExpenseRequestDto)
            ->deleteLoanInterest($deleteLoanExpenseRequestDto)
            ->saveDeletedLoanAmounts($deleteLoanExpenseRequestDto)
            ->saveDeletedDataToSnapshot();
    }

    private function saveDeletedDataToSnapshot(): self
    {
        if (!$this->deletedAmounts->count()) {
            return $this;
        }

        $deletedData = $this->deletedAmounts->reduce(function ($newArray, $row) {
            /// if we have installments_seq
            if (!empty($row['installment_seq_num'])) {
                $newArray['installments'][$row['installment_seq_num']][] = [
                    'title' => $row['title'],
                    'amount' => intToFloat($row['deletedAmount']),
                ];
            } else {
                $newArray['tax']['tax_' . $row['modelId']][] = [
                    'title' => $row['title'],
                    'amount' => intToFloat($row['deletedAmount']),
                ];
            }

            return $newArray;
        }, []);

        if ($this->snapshot) {
            $this->snapshot->setAttribute('deleted_data', $deletedData);
            $this->snapshot->saveQuietly();
        }

        return $this;
    }


    private function deleteExtendLoanFees(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$this->loanFeesCollection->count()) {
            return $this;
        }

        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->loanFeesCollection
            ->where('type', Tax::TAX_TYPE_LOAN_EXTENSION_FEE)
            ->each(function (Tax $tax, int $index) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false; // skip
                }

                if ($tax->rest_amount == 0 || $tax->amount == 0) {
                    return true; // skip
                }

                if ($tax->rest_amount <= $deleteLoanExpenseRequestDto->amount) {
                    $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $tax->rest_amount);

                    $tax->amount = $tax->amount - $tax->rest_amount;
                    $tax->rest_amount = 0;
                    if ($tax->paid_amount == 0) {
                        $tax->active = 0;
                        $tax->deleted = 1;
                        $tax->deleted_at = now();
                        $tax->deleted_by = getAdminId();
                    } else {
                        $tax->status = 'paid';
                        $tax->paid = 1;
                        $tax->paid_at = now();
                    }
                    $tax->save();


                    $this->deletedAmounts->push([
                        'modelType' => Tax::class,
                        'modelId' => $tax->getKey(),
                        'title' => $tax->comment,
                        'deletedAmount' => $tax->rest_amount,
                    ]);

                    return true;
                } else {
                    $tax->amount = $tax->amount - $deleteLoanExpenseRequestDto->amount;
                    $tax->rest_amount = $tax->rest_amount - $deleteLoanExpenseRequestDto->amount;
                    $tax->save();

                    $this->deletedAmounts->push([
                        'modelType' => Tax::class,
                        'installment_seq_num' => null,
                        'modelId' => $tax->getKey(),
                        'title' => $tax->comment,
                        'deletedAmount' => $deleteLoanExpenseRequestDto->amount,
                    ]);

                    $deleteLoanExpenseRequestDto->amount = 0;
                }
            });

        return $this;
    }

    private function deleteManualLoanFees(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$this->loanFeesCollection->count()) {
            return $this;
        }

        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->loanFeesCollection
            ->where('type', Tax::TAX_TYPE_MANUALLY_ADDED_EXPENSE)
            ->each(function (Tax $tax, int $index) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false; // skip
                }

                if ($tax->rest_amount == 0 || $tax->amount == 0) {
                    return true; // skip
                }

                if ($tax->rest_amount <= $deleteLoanExpenseRequestDto->amount) {
                    $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $tax->rest_amount);

                    $tax->amount = $tax->amount - $tax->rest_amount;
                    $tax->rest_amount = 0;
                    if ($tax->paid_amount == 0) {
                        $tax->active = 0;
                        $tax->deleted = 1;
                        $tax->deleted_at = now();
                        $tax->deleted_by = getAdminId();
                    } else {
                        $tax->status = 'paid';
                        $tax->paid = 1;
                        $tax->paid_at = now();
                    }
                    $tax->save();


                    $this->deletedAmounts->push([
                        'modelType' => Tax::class,
                        'modelId' => $tax->getKey(),
                        'title' => $tax->comment,
                        'deletedAmount' => $tax->rest_amount,
                    ]);

                    return true;
                } else {
                    $tax->amount = $tax->amount - $deleteLoanExpenseRequestDto->amount;
                    $tax->rest_amount = $tax->rest_amount - $deleteLoanExpenseRequestDto->amount;
                    $tax->save();

                    $this->deletedAmounts->push([
                        'modelType' => Tax::class,
                        'installment_seq_num' => null,
                        'modelId' => $tax->getKey(),
                        'title' => $tax->comment,
                        'deletedAmount' => $deleteLoanExpenseRequestDto->amount,
                    ]);

                    $deleteLoanExpenseRequestDto->amount = 0;
                }
            });

        return $this;
    }

    private function deleteCollectorLoanFees(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$this->loanFeesCollection->count()) {
            return $this;
        }

        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->loanFeesCollection
            ->where('type', Tax::TAX_TYPE_COLLECTOR)
            ->each(function (Tax $tax, int $index) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false; // skip
                }

                if ($tax->rest_amount == 0 || $tax->amount == 0) {
                    return true; // skip
                }

                if ($tax->rest_amount <= $deleteLoanExpenseRequestDto->amount) {
                    $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $tax->rest_amount);

                    $tax->amount = $tax->amount - $tax->rest_amount;
                    $tax->rest_amount = 0;
                    if ($tax->paid_amount == 0) {
                        $tax->active = 0;
                        $tax->deleted = 1;
                        $tax->deleted_at = now();
                        $tax->deleted_by = getAdminId();
                    } else {
                        $tax->status = 'paid';
                        $tax->paid = 1;
                        $tax->paid_at = now();
                    }
                    $tax->save();


                    $this->deletedAmounts->push([
                        'modelType' => Tax::class,
                        'modelId' => $tax->getKey(),
                        'title' => $tax->comment,
                        'deletedAmount' => $tax->rest_amount,
                    ]);

                    return true;
                } else {
                    $tax->amount = $tax->amount - $deleteLoanExpenseRequestDto->amount;
                    $tax->rest_amount = $tax->rest_amount - $deleteLoanExpenseRequestDto->amount;
                    $tax->save();

                    $this->deletedAmounts->push([
                        'modelType' => Tax::class,
                        'modelId' => $tax->getKey(),
                        'installment_seq_num' => null,
                        'title' => $tax->comment,
                        'deletedAmount' => $deleteLoanExpenseRequestDto->amount,
                    ]);

                    $deleteLoanExpenseRequestDto->amount = 0;
                }
            });

        return $this;
    }


    private function deleteLoanLatePenalty(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->unpaidInstallments
            ->each(function (Installment $installment) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false;
                }

                $restLatePenalty = floatToInt($installment->rest_late_penalty);
                if ($restLatePenalty <= 0) {
                    return true;
                }


                $diff = null;
                if ($restLatePenalty <= $deleteLoanExpenseRequestDto->amount) {
                    $diff = $restLatePenalty;
                } else {
                    $diff = $deleteLoanExpenseRequestDto->amount;
                }

                $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $diff);

                $installment->rest_late_penalty = (float) intToFloat(
                    (floatToInt($installment->rest_late_penalty) - $diff)
                );
                $installment->late_penalty = (float) intToFloat((floatToInt($installment->late_penalty) - $diff));
                $installment->save();

                $this->deletedAmounts->push([
                    'modelType' => Loan::class,
                    'modelId' => $this->loan->getKey(),
                    'installment_seq_num' => $installment->seq_num,
                    'title' => __('Late penalty'),
                    'deletedAmount' => $diff,
                ]);
            });

        return $this;
    }

    private function deleteLoanLateInterest(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->unpaidInstallments
            ->each(function (Installment $installment) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false;
                }

                $rest = floatToInt($installment->rest_late_interest);
                if ($rest <= 0) {
                    return true;
                }


                $diff = null;
                if ($rest <= $deleteLoanExpenseRequestDto->amount) {
                    $diff = $rest;
                } else {
                    $diff = $deleteLoanExpenseRequestDto->amount;
                }

                $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $diff);

                $installment->rest_late_interest = (float) intToFloat(
                    (floatToInt($installment->rest_late_interest) - $diff)
                );
                $installment->late_interest = (float) intToFloat((floatToInt($installment->late_interest) - $diff));
                $installment->save();

                $this->deletedAmounts->push([
                    'modelType' => Loan::class,
                    'installment_seq_num' => $installment->seq_num,
                    'modelId' => $this->loan->getKey(),
                    'title' => __('Late interest'),
                    'deletedAmount' => $diff,
                ]);
            });

        return $this;
    }

    private function deleteLoanPenalty(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->unpaidInstallments
            ->each(function (Installment $installment) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false;
                }

                $rest = floatToInt($installment->rest_penalty);
                if ($rest <= 0) {
                    return true;
                }


                $diff = null;
                if ($rest <= $deleteLoanExpenseRequestDto->amount) {
                    $diff = $rest;
                } else {
                    $diff = $deleteLoanExpenseRequestDto->amount;
                }

                $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $diff);

                if ($installment->accrued_penalty > 0) {
                    $accrPenalty = floatToInt($installment->accrued_penalty);
                    if ($accrPenalty >= $diff) {
                        $installment->accrued_penalty = (float) intToFloat(($accrPenalty - $diff));
                    } else {
                        $installment->accrued_penalty = 0;
                    }
                }

                $installment->rest_penalty = (float) intToFloat((floatToInt($installment->rest_penalty) - $diff));
                $installment->penalty = (float) intToFloat((floatToInt($installment->penalty) - $diff));
                $installment->total_amount = (float) intToFloat((floatToInt($installment->total_amount) - $diff));
                $installment->save();

                $this->deletedAmounts->push([
                    'modelType' => Loan::class,
                    'installment_seq_num' => $installment->seq_num,
                    'modelId' => $this->loan->getKey(),
                    'title' => __('Penalty'),
                    'deletedAmount' => $diff,
                ]);
            });

        return $this;
    }

    private function deleteLoanInterest(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (!$deleteLoanExpenseRequestDto->checkAmount()) {
            return $this;
        }

        $this->unpaidInstallments
            ->each(function (Installment $installment) use ($deleteLoanExpenseRequestDto) {
                if (!$deleteLoanExpenseRequestDto->checkAmount()) {
                    return false;
                }

                $rest = floatToInt($installment->rest_interest);
                if ($rest <= 0) {
                    return true;
                }


                $diff = null;
                if ($rest <= $deleteLoanExpenseRequestDto->amount) {
                    $diff = $rest;
                } else {
                    $diff = $deleteLoanExpenseRequestDto->amount;
                }

                $deleteLoanExpenseRequestDto->amount = ($deleteLoanExpenseRequestDto->amount - $diff);

                if ($installment->accrued_interest > 0) {
                    $accrInterest = floatToInt($installment->accrued_interest);
                    if ($accrInterest >= $diff) {
                        $installment->accrued_interest = (float) intToFloat(($accrInterest - $diff));
                    } else {
                        $installment->accrued_interest = 0;
                    }
                }

                $installment->rest_interest = (float) intToFloat((floatToInt($installment->rest_interest) - $diff));
                $installment->interest = (float) intToFloat((floatToInt($installment->interest) - $diff));
                $installment->total_amount = (float) intToFloat((floatToInt($installment->total_amount) - $diff));
                $installment->save();

                $this->deletedAmounts->push([
                    'modelType' => Loan::class,
                    'modelId' => $this->loan->getKey(),
                    'installment_seq_num' => $installment->seq_num,
                    'title' => __('Interest'),
                    'deletedAmount' => $diff,
                ]);
            });

        return $this;
    }

    public function getAmountRest(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): int
    {
        $restAmount = $deleteLoanExpenseRequestDto->amount;
        if ($restAmount > 0 && !empty($this->amountStart) && !empty($this->snapshot->id)) {
            if ($restAmount > $this->amountStart) {
                throw new \Exception('Imposible del expense variant');
            }

            // nothing deleted -> remove snapshot
            if ($restAmount == $this->amountStart) {
                DB::statement('DELETE FROM del_expense_snapshot WHERE id = ' . $this->snapshot->id);
            } else { // actualize deleted amount
                $deleted = $this->amountStart - $restAmount;
                $this->snapshot->amount = intToFloat($deleted);
                $this->snapshot->save();
            }
        }

        return $restAmount;
    }

    private function saveDeletedLoanAmounts(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        $dbModel = $this->deletedLoanAmountRepository->getDbModel();

        $dbModel->setAttribute('loan_id', $deleteLoanExpenseRequestDto->loan_id);
        $dbModel->setAttribute('amount', $this->deletedAmounts->sum('deletedAmount'));
        $dbModel->setAttribute('comment', $deleteLoanExpenseRequestDto->comment);
        $dbModel->setAttribute('deleted_from', $this->deletedAmounts);

        if ($this->deletedAmounts->count()) {
            $this->loan->setAttribute('has_custom_payment_schedule', 'yes');
            $this->loan->save();
        }

        $this->deletedLoanAmountRepository->save($dbModel);

        return $this;
    }


    // --------------- SETTERS ----------------

    private function setUnpaidTaxes(): self
    {
        $this->loanFeesCollection = $this->loan->getUnpaidTaxes();

        return $this;
    }

    private function setUnpaidTaxesCollector(): self
    {
        $this->loanFeesCollection = $this->loan->getUnpaidTaxes([Tax::TAX_TYPE_COLLECTOR]);

        return $this;
    }

    private function setUnpaidInstallments(DeleteLoanExpenseRequestDto $deleteLoanExpenseRequestDto): self
    {
        if (empty($deleteLoanExpenseRequestDto->installments)) {
            throw new \Exception('No installments selected for deleting expense');
        }

        $this->unpaidInstallments = $this->loan->getUnpaidInstallmentsByIds(
            $deleteLoanExpenseRequestDto->installments
        );

        return $this;
    }

    public function setLoan(Loan $loan): self
    {
        $this->loan = $loan;

        return $this;
    }

    public function setSkipTaxes(bool $skipTaxes): self
    {
        $this->skipTaxes = $skipTaxes;

        return $this;
    }

    public function setManual(bool $manual): self
    {
        $this->manual = $manual;

        return $this;
    }

    public function getSnapshotId(): ?int
    {
        return $this->snapshotId;
    }

    private function saveSnapshot(float $amount): void
    {
        $loan = $this->loan;

        // snapshot
        $taxes = $loan->getAllTaxes();
        $client = $loan->client;
        $loanStats = $loan->loanActualStats;
        $clientStats = $client->clientActualStats;
        $installments = $loan->getAllInstallments();

        // set snapshot
        $snapshot = new DelExpenseSnapshot();
        $snapshot->loan_id = $loan->loan_id;
        $snapshot->amount = $amount;
        $snapshot->manual = ($this->manual ? 1 : 0);
        $snapshot->created_at = now();
        $snapshot->created_by = getAdminId();

        $snapshot->loan = $loan->toArray();
        $snapshot->loan_stats = $loanStats->toArray();
        $snapshot->client_stats = $clientStats->toArray();

        $installmentsArray = [];
        foreach ($installments as $installment) {
            $installmentsArray[$installment->installment_id] = $installment->toArray();
        }
        $snapshot->installments = $installmentsArray;

        $taxesArray = [];
        if ($taxes->count() > 0) {
            foreach ($taxes as $tax) {
                $taxesArray[$tax->tax_id] = $tax->toArray();
            }
        }
        $snapshot->taxes = $taxesArray;

        // save and go on
        $snapshot->save();

        $this->snapshot = $snapshot;
        $this->snapshotId = $snapshot->id;
    }
}
