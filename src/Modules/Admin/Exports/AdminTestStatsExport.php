<?php

namespace Modules\Admin\Exports;

use App\Models\AdminTestStats;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\Exportable;

class AdminTestStatsExport implements FromQuery, WithHeadings, WithMapping, WithChunkReading, ShouldQueue
{
    use Exportable;

    protected $query;

    public function __construct(Builder $query)
    {
        $this->query = $query;
    }

    public function query(): Builder
    {
        return $this->query;
    }

    public function headings(): array
    {
        return [
            'Client ID',
            'Loan ID',
            'Is Gypsy',
            'Details',
        ];
    }

    // Map each row's data for the Excel sheet
    public function map($stat): array
    {
        return [
            $stat->client_id,
            $stat->loan_id,
            $stat->is_gypsy ? 'Yes' : 'No',
            $stat->details,
        ];
    }

    // Define chunk size for chunked reading
    public function chunkSize(): int
    {
        return 20; // You can adjust the chunk size based on your needs
    }
}
