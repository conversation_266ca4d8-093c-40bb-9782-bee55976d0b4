<?php

namespace Modules\Admin\Services;

use Cache;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Modules\Admin\Repositories\AdministratorClientRepository;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Admin\Repositories\PermissionRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\LoginLog;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\StorageService;
use RuntimeException;

class AdministratorService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    private AdministratorRepository $administratorRepository;
    private AdministratorClientRepository $administratorClientRepository;
    private StorageService $storageService;
    private RoleService $roleService;
    private PermissionRepository $permissionRepository;

    /**
     * AdministratorService constructor.
     *
     * @param AdministratorRepository $administratorRepository
     * @param AdministratorClientRepository $administratorClientRepository
     * @param StorageService $storageService
     * @param RoleService $roleService
     * @param PermissionRepository $permissionRepository
     */
    public function __construct(
        AdministratorRepository       $administratorRepository,
        AdministratorClientRepository $administratorClientRepository,
        StorageService                $storageService,
        RoleService                   $roleService,
        PermissionRepository          $permissionRepository
    )
    {
        $this->administratorRepository = $administratorRepository;
        $this->administratorClientRepository = $administratorClientRepository;
        $this->storageService = $storageService;
        $this->roleService = $roleService;
        $this->permissionRepository = $permissionRepository;

        parent::__construct();
    }

    /**
     * @return Administrator
     */
    public function create(array $data, Administrator $administrator)
    {
        if (!empty($data['roles'])) {
            $this->roleService->canManageRoles(
                $administrator,
                $data['roles']
            );
        }

        $data['password'] = Hash::make($data['password']);

        try {
            $admin = $this->administratorRepository->create($data);
            if (!empty($data['permission_additional_info'])) {
                $this->syncPermissionsAdditionalInfo($administrator, $data['permission_additional_info']);
            }
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::adminCrud.adminCreationFailed'),
                $exception
            );
        }

        return $admin;
    }

    /**
     * @return bool
     */
    public function update(
        Administrator $administrator,
        array         $data,
        Administrator $loggedAdministrator
    ) {

        // unset role, permission before save


        if (!empty($data['role'])) {

            // we need such logic for checking if admin can set such role + adopt roles
            // before admin could have several roles, now only one with custom settings preset
            $data['roles'] = [$data['role']];

            $this->roleService->canManageRoles(
                $loggedAdministrator,
                [$data['role']]
            );
        }

        if (empty($data['password'])) {
            unset($data['password']);
        } else {
            $data['password'] = Hash::make($data['password']);
        }

        try {
            $administrator = $this->administratorRepository->update($administrator, $data);

            if (!empty($data['permission_additional_info'])) {
                $this->syncPermissionsAdditionalInfo($administrator, $data['permission_additional_info']);
            }
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::adminCrud.adminEditionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @return bool
     */
    public function delete(
        Administrator $administrator,
        Administrator $loggedAdministrator
    ) {
        $this->canControl($loggedAdministrator, $administrator);

        try {
            $this->administratorRepository->delete($administrator);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::adminCrud.adminDeletionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @return bool
     */
    public function enable(
        Administrator $administrator,
        Administrator $loggedAdministrator
    ): bool {

        $this->canControl($loggedAdministrator, $administrator);

        if ($administrator->isActive()) {
            throw new RuntimeException(
                __('admin::adminCrud.adminEnableForbidden')
            );
        }

        try {
            $this->administratorRepository->enable($administrator);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::adminCrud.adminEnableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @return bool
     */
    public function disable(
        Administrator $administrator,
        Administrator $loggedAdministrator
    ) {
        $this->canControl($loggedAdministrator, $administrator);
        if (!$administrator->isActive()) {
            throw new RuntimeException(
                __('admin::adminCrud.adminEnableForbidden')
            );
        }

        try {
            $this->administratorRepository->disable($administrator);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::adminCrud.adminDisableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return LengthAwarePaginator
     */
    public function getByWhereConditions(int $limit, array $data)
    {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        $whereConditions = $this->getWhereConditions(
            $data,
            [
                'first_name',
                'middle_name',
                'last_name',
            ]
        );

        return $this->administratorRepository->getAll($limit, $whereConditions, $order);
    }

    /**
     * @param int $id
     *
     * @return Administrator
     *
     * @throws NotFoundException
     */
    public function getAdministratorById(int $id)
    {
        $administrator = $this->administratorRepository->getById($id);
        if (!$administrator) {
            throw new RuntimeException(__('admin::adminCrud.adminNotFound'));
        }

        return $administrator;
    }

    /**
     * @param string $username
     *
     * @return Model|Administrator
     */
    public function getAdministratorByUsername(string $username)
    {
        return $this->administratorRepository->getAdministratorByUsername(
            $username
        );
    }

    /**
     * [addAvatar description]
     *
     * @param Administrator $admin
     * @param UploadedFile $file
     *
     * @return  bool
     */
    public function addAvatar(Administrator $admin, UploadedFile $file): bool
    {
        $avatarName = $this->storageService->uploadAvatar($admin, $file);
        $admin->update(['avatar' => $avatarName]);

        return true;
    }

    /**
     * [changeAvatar description]
     *
     * @param Administrator $admin
     * @param UploadedFile $file
     *
     * @return bool
     *
     */
    public function changeAvatar(Administrator $admin, UploadedFile $file): bool
    {
        $avatarName = $this->storageService->uploadAvatar($admin, $file);
        $admin->update(['avatar' => $avatarName]);

        return true;
    }

    /**
     * @return bool
     */
    public function canControl(
        Administrator $loggedAdministrator,
        Administrator $administrator,
        bool          $throwException = true
    ) {
        $result = ($loggedAdministrator
                ->getMaxPriority() >= $administrator->getMaxPriority());

        if (!$result && $throwException) {
            throw new RuntimeException(__('admin::adminCrud.adminAccessDenied'));
        }

        return $result;
    }

    public function logLogin(array $data): LoginLog
    {
        return $this->administratorRepository->createLoginLog($data);
    }

    public function hasAdminMoreAttempts(
        int    $clientId,
        string $reportType
    ): bool {
        $admin = getAdmin();

        if ($admin->isSuperAdmin()) {

            return true;
        }

        $adminClient = $this->administratorClientRepository
            ->getByConditions(
                [
                    'administrator_id' => $admin->getKey(),
                    'client_id' => $clientId,
                    'type' => $reportType,
                    ['created_at', '>=', Carbon::now()->startOfMonth()],
                    ['created_at', '<=', Carbon::now()]
                ]
            );

        if ($adminClient) {
            $currentAttempts = (int)$adminClient->value;
            $reportCountSetting = $admin
                ->settings
                ->firstWhere(
                    'setting_key',
                    'count_of_reports_per_month_for_a_given_client_administrator'
                );
            $maxAttempts = !empty($reportCountSetting)
                ? $reportCountSetting->pivot->value
                : Administrator::DEFAULT_MAX_REPORTS_PER_MONTH_REGULAR_ADMIN;

            return $currentAttempts < $maxAttempts;
        }

        return true;
    }

    /**
     * @param int $clientId
     * @param $reportType
     * @return array
     */
    public function reportAttempt(int $clientId, $reportType): array
    {
        $admin = getAdmin();

        if ($admin->isSuperAdmin()) {
            $this->administratorClientRepository->create([
                'administrator_id' => $admin->getKey(),
                'client_id' => $clientId,
                'type' => $reportType,
                'value' => 1,
            ]);

            return [true, 1];
        }

        $adminClient = $this->administratorClientRepository
            ->getByConditions(
                [
                    'administrator_id' => $admin->getKey(),
                    'client_id' => $clientId,
                    'type' => $reportType,
                    ['created_at', '>=', Carbon::now()->startOfMonth()],
                    ['created_at', '<=', Carbon::now()]
                ]
            );

        $adminAttemptsCount = $admin->getAdminReportsCount();

        $maxAttempts = !empty($adminAttemptsCount)
            ? $adminAttemptsCount
            : Administrator::DEFAULT_MAX_REPORTS_PER_MONTH_REGULAR_ADMIN;

        if ($adminClient) {
            $currentAttempts = (int)$adminClient->value;

            if ($currentAttempts >= $maxAttempts) {
                return [false, 0];
            }

            $adminClient->value++;
            $adminClient->save();

            return [true, ($maxAttempts - ($currentAttempts))];
        }

        $this->administratorClientRepository->create(
            [
                'administrator_id' => $admin->getKey(),
                'client_id' => $clientId,
                'type' => $reportType,
                'value' => 1,
            ]
        );

        return [true, $maxAttempts - 1];
    }

    /**
     * @param Administrator $administrator
     * @param int $clientId
     * @param string $reportType
     *
     * @return int
     */
    public function incrementReportCounter(
        Administrator $administrator,
        int           $clientId,
        string        $reportType
    ): int
    {
        return $this->administratorClientRepository
            ->incrementReportCounter($administrator, $clientId, $reportType);
    }

    public function getActiveAdmins(): array
    {
        return $this->administratorRepository->getActiveAdmins();

        // return Cache::remember('active_admins_id_name', self::CACHE_TTL, function () {
        //     return $this->administratorRepository->getActiveAdmins();
        // });
    }

    private function syncPermissionsAdditionalInfo(
        Administrator $administrator,
        array         $additionalInfo
    ): void
    {
        foreach ($additionalInfo as $permissionId => $value) {
            $permission = $this->permissionRepository->getPermissionById($permissionId);
            if ($permission === null) {
                continue;
            }
            $administratorPermission = $this
                ->permissionRepository
                ->getAdministratorPermission($administrator, $permission);
            if (empty($administratorPermission)) {
                continue;
            }

            $this->permissionRepository->updateAdministratorPermission(
                $administratorPermission,
                $value
            );
        }
    }
}
