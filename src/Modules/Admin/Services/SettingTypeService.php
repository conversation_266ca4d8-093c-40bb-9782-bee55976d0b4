<?php

namespace Modules\Admin\Services;

use Modules\Admin\Repositories\SettingTypeRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\SettingType;
use Modules\Common\Services\BaseService;
use RuntimeException;

class SettingTypeService extends BaseService
{
    protected SettingTypeRepository $settingTypeRepository;

    /**
     * SettingTypeService constructor.
     *
     * @param SettingTypeRepository $settingTypeRepository
     */
    public function __construct(SettingTypeRepository $settingTypeRepository)
    {
        $this->settingTypeRepository = $settingTypeRepository;

        parent::__construct();
    }

    /**
     * @param array $where
     *
     * @return \Modules\Common\Models\SettingType[]
     */
    public function getSettingTypes(array $where = [])
    {
        return $this->settingTypeRepository->getAll($where);
    }

    public function create(array $data)
    {
        try {
            $this->settingTypeRepository->create($data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::settingTypeCrud.CreationFailed'),
                $exception
            );
        }

        return true;
    }

    public function edit(SettingType $settingType, array $data)
    {
        try {
            $this->settingTypeRepository->update($settingType, $data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::settingTypeCrud.UpdatingFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(SettingType $settingType)
    {
        if ($settingType->isActive()) {
            throw new RuntimeException(__('admin::settingTypeCrud.Enable'));
        }

        try {
            $this->settingTypeRepository->enable($settingType);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::settingTypeCrud.ActivationFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(SettingType $settingType)
    {
        if (!$settingType->isActive()) {
            throw new RuntimeException(__('admin::settingTypeCrud.Disable'));
        }

        try {
            $this->settingTypeRepository->disable($settingType);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::settingTypeCrud.DeactivationFailed'),
                $exception
            );
        }

        return true;
    }

    public function delete(SettingType $settingType)
    {
        try {
            $this->settingTypeRepository->delete($settingType);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::settingTypeCrud.DeletionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $id
     *
     * @return SettingType
     *
     * @throws NotFoundException
     */
    public function getSettingTypeById(int $id)
    {
        $settingType = $this->settingTypeRepository->getById($id);
        if (!$settingType || $settingType->isDeleted()) {
            throw new RuntimeException(__('admin::settingTypeCrud.NotFound'));
        }

        return $settingType;
    }
}
