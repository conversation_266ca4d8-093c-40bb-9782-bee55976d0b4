<?php

namespace Modules\Admin\Services;

use Exception;
use Modules\Common\Models\LandingSection;
use Modules\Common\Services\BaseService;
use Modules\Common\Traits\DateBuilderTrait;
use RuntimeException;

/**
 * Class LandingSectionsService
 *
 * @package Modules\Admin\Services
 */
class LandingSectionsService extends BaseService
{
    use DateBuilderTrait;

    private $landingSection;

    /**
     * LandingSectionsService constructor.
     *
     */
    public function __construct() {

        $this->landingSection = new LandingSection;
        parent::__construct();
    }

    public function getById($landingSectionId)
    {
        $landingSectionId = intval($landingSectionId);
        $landingSection = null;

        if (empty($landingSectionId)) {
            return $landingSection;
        }

        $landingSection = $this->landingSection->find($landingSectionId);

        return $landingSection;
    }

    /**
     * @param string $orderBy
     * @param string $order
     * @param bool $onlyActive
     *
     * @return array<LandingSection>
     */
    public function getAll($orderBy = 'priority', $order = 'ASC', $onlyActive = false)
    {
        $list = (
            !empty($onlyActive) ?
            $this->landingSection->where('active', '=', 1)->orderBy($orderBy, $order)->get() :
            $this->landingSection->orderBy($orderBy, $order)->get()
        );

        return $list;
    }

    /**
     * @return LandingSection
     */
    public function getByPriority(int $priority, bool $direction)
    {
        $condition = (
            !$direction ?
            '>' :
            '<'
        );

        $order = (
            !$direction ?
            'ASC' :
            'DESC'
        );

        return $this->landingSection->where('priority', $condition, $priority)->orderBy('priority', $order)->first();
    }

    /**
     * @param array $data
     *
     * @return bool
     */
    public function create(array $data)
    {
        try {

            $landingSection = (
                empty($data['landing_section_id']) ||
                $data['landing_section_id'] === '0' ?
                new LandingSection :
                $this->getById($data['landing_section_id'])
            );

            if (
                empty($data['landing_section_id']) ||
                $data['landing_section_id'] === '0'
            ) {
                $data['priority'] = 0;
            }

            $landingSection->fill($data);
            $landingSectionCreateState = $landingSection->save();

            if (
                (
                    empty($data['landing_section_id']) ||
                    $data['landing_section_id'] === '0'
                ) &&
                $landingSectionCreateState
            ) {

                $landingSection->fill([
                    'landing_section_id' => $landingSection['landing_section_id'],
                    'priority' => $landingSection['landing_section_id']
                ]);

                $landingSectionCreateState = $landingSection->save();
            }
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('LandingSectionCreationFailed'),
                $exception
            );
        }

        return $landingSectionCreateState;
    }

}
