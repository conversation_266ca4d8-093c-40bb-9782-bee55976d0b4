<?php

namespace Modules\Admin\Services;

use Modules\Admin\Repositories\TmpRequestStepRepository;
use Modules\Common\Models\TmpRequestStep;
use Modules\Common\Services\BaseService;
use RuntimeException;

class TmpRequestStepService extends BaseService
{
    private TmpRequestStepRepository $tmpRequestStepRepository;

    /**
     * AgreementService constructor.
     *
     * @param TmpRequestStepRepository $tmpRequestStepRepository
     */
    public function __construct(TmpRequestStepRepository $tmpRequestStepRepository)
    {
        $this->tmpRequestStepRepository = $tmpRequestStepRepository;

        parent::__construct();
    }

    public function update(TmpRequestStep $tmpRequestStep, array $data)
    {
        try {
            $this->tmpRequestStepRepository->update($tmpRequestStep, $data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::tmpRequestStepCrud.EditionFailed'),
                $exception
            );
        }

        return $tmpRequestStep;
    }

    public function create(array $data)
    {
//        try {
            $this->tmpRequestStepRepository->create($data);
//        } catch (\Exception $exception) {
//            throw new RuntimeException(__('admin::tmpRequestStepCrud.CreationFailed'));
//        }

        return true;
    }

    public function delete(TmpRequestStep $tmpRequestStep)
    {
        try {
            $this->tmpRequestStepRepository->delete($tmpRequestStep);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::tmpRequestStepCrud.DeletionFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(TmpRequestStep $tmpRequestStep)
    {
        if (!$tmpRequestStep->isActive()) {
            throw new RuntimeException(__('admin::tmpRequestStepCrud.DisableForbidden'));
        }

        try {
            $this->tmpRequestStepRepository->disable($tmpRequestStep);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::tmpRequestStepCrud.DisableFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(TmpRequestStep $tmpRequestStep)
    {
        try {
            $this->tmpRequestStepRepository->enable($tmpRequestStep);
        } catch (\Exception  $exception) {
            throw new RuntimeException(
                __('admin::tmpRequestStepCrud.EnableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->tmpRequestStepRepository->getAll(
            $limit,
            $joins,
            $whereConditions
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }
}
