<?php

namespace Modules\Admin\Services;

use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\Setting;
use Modules\Common\Models\SettingType;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\WherePipeline\DataWrapper;
use Modules\Common\Services\WherePipeline\WherePipeline;
use RuntimeException;

class SettingService extends BaseService
{
    public function __construct(
        protected SettingRepository $settingRepository = new SettingRepository()
    )
    {
        parent::__construct();
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     *
     * @throws Exception
     */
    public function getByFilters(
        int $limit,
        array $data
    ) {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        $where = (new WherePipeline(new DataWrapper($data), Setting::class))->run();

        $settings = $this->settingRepository->getAll(
            $where,
            $limit,
            $this->getJoins($data),
            $order
        );

        return $this->getPrettyValueAttribute($settings);
    }

    /**
     * @return int|mixed|string
     */
    public function getPrettyValueAttribute(LengthAwarePaginator $settings)
    {
        foreach ($settings as $setting) {
            // Check is it a whole number
            if (is_numeric($setting->default_value) && $setting->default_value == intval($setting->default_value)) {
                switch (true) {
                    // Under hour
                    case $setting->default_value < 60:
                        $setting->default_value = $setting->default_value . 'm';
                        break;
                    // Between hour and a day
                    case $setting->default_value >= 60 && $setting->default_value < 1440:
                        $hours = intval($setting->default_value / 60);
                        $minutes = $setting->default_value % 60;

                        $setting->default_value = $hours . 'h';
                        if ($minutes > 0) {
                            $setting->default_value .= ' ' . $minutes . 'm';
                        }

                        break;
                    // Day+
                    case $setting->default_value >= 1440:
                        $hours = intval($setting->default_value / 60);
                        $days = intval($hours / 24);
                        $hours = $hours % $days;
                        $minutes = $setting->default_value % 60;

                        $setting->default_value = $days . 'd ';
                        if ($hours > 0) {
                            $setting->default_value .= $hours . 'h ';
                        }
                        if ($minutes > 0) {
                            $setting->default_value .= $minutes . 'm';
                        }

                        break;
                }
            }
        }

        return $settings;
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    public function create(array $data)
    {
        $data['setting_key'] = $this->transformIntoKey(
            $data['name'],
            $data['setting_type_id']
        );
        $data['name'] = SettingsEnum::from($data['setting_key'])->title();
        $setting = Setting::where(['setting_key'=>$data['setting_key']])->first();
        try {
            $setting
                ? $this->settingRepository->update($setting, $data)
                : $this->settingRepository->create($data);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::settingCrud.CreationFailed'),
                $exception
            );
        }

        return true;
    }

    public function edit(Setting $setting, array $data)
    {
        // $data['setting_key'] = $this->transformIntoKey(
        //     $data['name'],
        //     $data['setting_type_id']
        // );
        // $data['name'] = SettingsEnum::from($data['setting_key'])->title();

        try {
            $this->settingRepository->update($setting, $data);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::settingCrud.UpdatingFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(Setting $setting)
    {
        if ($setting->isActive()) {
            throw new RuntimeException(__('admin::settingCrud.Enable'));
        }

        try {
            $this->settingRepository->enable($setting);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::settingCrud.ActivationFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(Setting $setting)
    {
        if (!$setting->isActive()) {
            throw new RuntimeException(__('admin::settingCrud.Disable'));
        }

        try {
            $this->settingRepository->disable($setting);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::settingCrud.DeactivationFailed'),
                $exception
            );
        }

        return true;
    }

    public function delete(Setting $setting)
    {
        try {
            $this->settingRepository->delete($setting);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('admin::settingCrud.DeletionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $typeId
     *
     * @return Collection
     */
    public function getByType(int $typeId, ?string $key = null): Collection
    {
        return $this->settingRepository->getBySettingTypeId($typeId, $key);
    }

    protected function transformIntoKey(string $name, int $settingTypeId)
    {
        return strtolower(
            str_replace(
                ' ',
                '_',
                ($name . '_' . SettingType::SETTING_TYPES[$settingTypeId])
            )
        );
    }

    public function getSetting(SettingsEnum $settingsEnum): Setting
    {
        return $this->settingRepository->getSetting($settingsEnum);
    }
}
