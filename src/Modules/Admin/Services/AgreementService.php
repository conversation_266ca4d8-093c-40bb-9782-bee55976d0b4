<?php

namespace Modules\Admin\Services;

use Modules\Admin\Repositories\AgreementRepository;
use Modules\Common\Models\Agreement;
use Modules\Common\Services\BaseService;
use RuntimeException;

class AgreementService extends BaseService
{
    private AgreementRepository $agreementRepository;

    /**
     * AgreementService constructor.
     *
     * @param AgreementRepository $agreementRepository
     */
    public function __construct(AgreementRepository $agreementRepository)
    {
        $this->agreementRepository = $agreementRepository;

        parent::__construct();
    }

    public function update(Agreement $agreement, array $data)
    {
        try {
            $this->agreementRepository->update($agreement, $data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::agreementCrud.EditionFailed'),
                $exception
            );
        }

        return $agreement;
    }

    public function create(array $data)
    {
        try {
            $this->agreementRepository->create($data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::agreementCrud.CreationFailed'),
                $exception
            );
        }

        return true;
    }

    public function delete(Agreement $agreement)
    {
        try {
            $this->agreementRepository->delete($agreement);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::agreementCrud.DeletionFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(Agreement $agreement): bool
    {
        if (!$agreement->isActive()) {
            throw new RuntimeException(__('admin::agreementCrud.DisableForbidden'));
        }

        try {
            $this->agreementRepository->disable($agreement);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::agreementCrud.DisableFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(Agreement $agreement)
    {
        try {
            $this->agreementRepository->enable($agreement);
        } catch (\Exception  $exception) {
            throw new RuntimeException(
                __('admin::agreementCrud.EnableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->agreementRepository->getAll(
            $limit,
            $joins,
            $whereConditions
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }
}
