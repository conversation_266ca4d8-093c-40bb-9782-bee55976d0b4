<?php

namespace Modules\Admin\Services;

use Cache;
use Exception;
use Illuminate\Support\Collection;
use Modules\Admin\Repositories\FiscalDeviceRepository;
use Modules\Admin\Repositories\OfficeRepository;
use Modules\Common\Enums\LogActionEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Libraries\Logs\OfficeState;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\FiscalDevice;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeCheck;
use Modules\Common\Models\Role;
use Modules\Common\Models\SettingType;
use Modules\Common\Services\BaseService;
use RuntimeException;
use Throwable;

class OfficeService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    public function __construct(
        private readonly OfficeRepository       $officeRepository,
        private readonly FiscalDeviceRepository $fiscalDeviceRepository,
    ) {
        parent::__construct();
    }


    /**
     * Returns sms wotking time
     * Format: "Ponedelnik - Petak оt hh:mm do hh:mm, Sabota ot hh:mm do hh:mm, Nedelq ot hh:mm do hh:mm"
     *         Ако офиса почива и не работи, " ot hh:mm do hh:mm" се заменя с "- pochiven den".
     *
     * @param  Office|null $office
     * @return string
     */
    public static function getWorkingTimeForSms(?Office $office = null): string
    {
        $wt = self::getWorkingTime($office);
        if (empty($wt)) {
            return '';
        }

        $str = 'Ponedelnik - Petak';
        if (!empty($wt['mon-fri']['from']) && !empty($wt['mon-fri']['to'])) {
            $str .= ' оt ' . $wt['mon-fri']['from'] . ' do ' . $wt['mon-fri']['to'];
        }
        $str .= ', Sabota';
        if (!empty($wt['sat']['from']) && !empty($wt['sat']['to'])) {
            $str .= ' оt ' . $wt['sat']['from'] . ' do ' . $wt['sat']['to'];
        } else {
            $str .= ' - pochiven den';
        }
        $str .= ', Nedelq';
        if (!empty($wt['sun']['from']) && !empty($wt['sun']['to'])) {
            $str .= ' оt ' . $wt['sun']['from'] . ' do ' . $wt['sun']['to'];
        } else {
            $str .= ' - pochiven den';
        }

        return $str;
    }

    public static function getWorkingTime(?Office $office = null): array
    {
        // working time based on office setting
        if (!empty($office->office_id)) {
            $officeSetting = $office->getOfficeSettings();
            $officeWt = $officeSetting->filter(function ($item) {
                return $item->setting_key === SettingsEnum::work_time_office->value;
            })->first();

            if (
                !empty($officeWt->value)
                && preg_match('/^\s*[\[{].*[\]}]\s*$/', $officeWt->value)
            ) {
                return json_decode($officeWt->value, true);
            }
        }


        // default working time
        $defaultSettings = app(SettingService::class)->getByType(
            SettingType::ID_OFFICE,
            SettingsEnum::work_time_office->value
        );

        if ($defaultSettings->count() != 1) {
            return [];
        }

        $defaultSettings = $defaultSettings->first();
        if (
            empty($defaultSettings->default_value)
            || !preg_match('/^\s*[\[{].*[\]}]\s*$/', $defaultSettings->default_value)
        ) {
            return [];
        }

        return json_decode($defaultSettings->default_value, true);
    }

    public function create(array $data): bool
    {
        $dbOffice = null;
        try {
            $dbOffice = $office = $this->officeRepository->create($data);
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('officeCrud.officeCreationFailed'),
                $exception
            );
        }

        try {
            if (empty($data['bank_account_id'])) {
                $data['bank_account_id'] = [];
            }

            $office->adopt('bankAccount', $data['bank_account_id']);

            $requestFormData = $data['check_service'] ?? [];
            $this->syncCheckServices($office, $requestFormData);

            $office = new OfficeState($office, LogActionEnum::create);
            $office->save();

            $accountingRole = Role::where('name', 'Счетоводител')->first();
            if ($accountingRole) {
                $roleAdmins = $accountingRole->users;

                $roleAdmins->each(function (Administrator $administrator) use ($dbOffice) {
                    $administrator->officesRelation()->attach($dbOffice?->getKey());
                });
            }

        } catch (Exception $exception) {
            throw new RuntimeException(
                __('officeCrud.officeEditionFailed'),
                $exception
            );
        }

        return true;
    }

    public function update(Office $office, array $data): bool
    {
        if (!empty($data['modules'])) {
            $this->updatePermissions($office, $data['modules']);
        } else {
            $state = new OfficeState($office, LogActionEnum::update);
            $state->stateFrom();


            $services = $data['check_service'] ?? [];
            if (isset($data['check_service'])) {
                unset($data['check_service']);
            }


            $settings = $data['settings'] ?? [];
            if (isset($data['settings'])) {
                unset($data['settings']);
            }

            if (!empty($data['workTime'])) {
                $setingRow = [
                    'setting_key' => SettingsEnum::work_time_office->value,
                    'value' => json_encode($data['workTime']),
                ];
                $settings[] = $setingRow;
            }
            if (isset($data['workTime'])) {
                unset($data['workTime']);
            }

            $bankAccounts = $data['bank_account_id'] ?? [];
            if (isset($data['bank_account_id'])) {
                unset($data['bank_account_id']);
            }

            $this->officeRepository->update($office, $data);

            $office->adopt('settings', $settings);

            $office->adopt('bankAccount', $bankAccounts);

            $this->syncCheckServices($office, $services);

            $state->save();
        }


        return true;
    }

    public function syncCheckServices(Office $office, array $checkServices)
    {
        OfficeCheck::query()
            ->where('office_id', $office->office_id)
            ->delete();

        if (!empty($checkServices)) {
            $addCheckServices = [];

            foreach ($checkServices as $sKey) {
                $addCheckServices[] = [
                    'service_key' => $sKey,
                    'office_id' => $office->office_id
                ];
            }

            OfficeCheck::query()->insert($addCheckServices);
        }
    }

    public function delete(Office $office): bool
    {
        try {
            $this->officeRepository->delete($office);
            $templateState = new OfficeState($office, LogActionEnum::delete);
            $templateState->save();
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('officeCrud.officeDeletionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        if (array_key_exists('limit', $data) && $data['limit'] !== null) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        $order = $this->getOrderConditions($data);
        unset($data['order']);

        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->officeRepository->getAll(
            $limit,
            $joins,
            $whereConditions,
            $order
        );
    }

    /**
     * Returns array with joins to be implemented in repository
     *
     * @param array $data - filters from list
     *
     * @return array
     *
     *  Implementatio format:
     *
     *   key - type of join (join, leftJoin, etc)
     *   should contains element which is array of 4 elements:
     *   - reference table
     *   - reference column
     *   - join sign
     *   - join condition
     *
     *   Example:
     *   $joins['join'][] = [
     *       'office_type',
     *       'office.office_type_id',
     *       '=',
     *       'office_type.office_type_id'
     *       . ' AND office.office_type_id = ' . (int) ($data['office-type'])
     *   ];
     *
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    /**
     * @param int $id
     *
     * @return Office
     *
     * @throws NotFoundException
     */
    public function getOfficeById(int $id)
    {
        $office = $this->officeRepository->getById($id);
        if (!$office) {
            throw new RuntimeException(__('admin::officeCrud.officeNotFound'));
        }

        return $office;
    }

    public function disable(Office $office): bool
    {
        if (!$office->isActive()) {
            throw new RuntimeException(__('admin::officeCrud.officeDisableForbidden'));
        }

        try {
            $this->officeRepository->disable($office);

            $state = new OfficeState($office, LogActionEnum::disable);
            $state->save();
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('officeCrud.officeDisableFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(Office $office): bool
    {
        if ($office->isActive()) {
            throw new RuntimeException(__('admin::officeCrud.officeEnableForbidden'));
        }

        try {
            $this->officeRepository->enable($office);
            $state = new OfficeState($office, LogActionEnum::enable);
            $state->save();
        } catch (Exception  $exception) {
            throw new RuntimeException(
                __('officeCrud.officeEnableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @return array|Office[]|Collection
     */
    public function getAllOffices($forAdmin = true)
    {
        $offices = $this->officeRepository->getAllOffices($forAdmin);

        if (!$offices) {
            throw new RuntimeException(__('officeCrud.officesNotFound'));
        }

        return $offices;
    }

    /**
     * @return Collection
     */
    public function getOfficeTypes()
    {
        return Cache::remember('office_types', self::CACHE_TTL, function () {
            return $this->officeRepository->getOfficeTypes();
        });
    }

    /**
     * @param int $officeId
     *
     * @return bool
     */
    public function isSelfApproveOffice(int $officeId): bool
    {
        return $this->officeRepository->isSelfApproved($officeId);
    }

    public function getOfficesConsultants(int $loanId): array
    {
        return $this->officeRepository->getOfficesConsultants($loanId);
    }

    public function getOfficeNames(int $loanId): array
    {
        return $this->officeRepository->getOfficeNames($loanId);
    }


    public function updateOfficeConsultant(int $officeId, int $consultantId, int $loanId, int $clientId): void
    {
        $this->officeRepository->updateOfficeConsultant($officeId, $consultantId, $loanId, $clientId);
    }

    protected function updatePermissions(Office $office, array $modules)
    {
        $this->officeRepository->removeMissingModules($office, $modules);

        $this->officeRepository->updateOrCreateOfficeModules($office, $modules);
    }

    public function getOffices()
    {
        try {
            $offices = Cache::remember('all_offices', self::CACHE_TTL, function () {
                return $this->officeRepository->getAll();
            });

            $offices = $this->collect(
                \Illuminate\Database\Eloquent\Collection::class,
                $offices,
            );

            if ($offices->isEmpty()) {
                throw new RuntimeException(__('head::officeCrud.officeNotFound'));
            }
        } catch (Throwable $t) {
            throw new RuntimeException(
                __('head::officeCrud.officeGeneralError'),
                $t
            );
        }

        return $offices;
    }

    public function getOfficeNamesByIds(array $ids)
    {
        return Office::whereIn('office_id', $ids)->pluck('name');
    }

    public function updateFiscalDevice(FiscalDevice $fiscalDevice, array $data): FiscalDevice
    {
        if (!isset($data['operator_pass'])) {
            unset($data['operator_pass']);
        }

        if (!isset($data['password'])) {
            unset($data['password']);
        }

        return $this->fiscalDeviceRepository->update($fiscalDevice, $data);
    }

    public function createFiscalDevice(array $data): FiscalDevice
    {
        return $this->fiscalDeviceRepository->create($data);
    }

    /**
     * @throws Exception
     */
    public function deleteFiscalDevice(FiscalDevice $fiscalDevice): FiscalDevice
    {
        return $this->fiscalDeviceRepository->delete($fiscalDevice);
    }
}
