<?php

namespace Modules\Admin\Services;

use Modules\Admin\Repositories\RoleRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Permission;
use Modules\Common\Models\Role;
use Modules\Common\Services\BaseService;
use RuntimeException;

class RoleService extends BaseService
{
    protected RoleRepository $roleRepository;

    /**
     * RoleService constructor.
     *
     * @param RoleRepository $roleRepository
     */
    public function __construct(
        RoleRepository $roleRepository
    ) {
        $this->roleRepository = $roleRepository;

        parent::__construct();
    }

    public function getAll()
    {
        return $this->roleRepository->getAll();
    }

    /**
     * @param array $data
     *
     * @return mixed
     *
     * @throws NotFoundException
     */
    public function create(array $data)
    {
        return $this->roleRepository->create($data);
    }

    public function edit(Role $role, array $data, Administrator $administrator)
    {
        $this->canManageRole($administrator, $role);

        return $this->roleRepository->edit($role, $data);
    }

    public function enable(Role $role, Administrator $administrator)
    {
        $this->canManageRole($administrator, $role);

        if ($role->isActive()) {
            throw new RuntimeException(__('roleCrud.roleEnable'));
        }

        $this->roleRepository->enable($role);

        return true;
    }

    public function disable(Role $role, Administrator $administrator)
    {
        $this->canManageRole($administrator, $role);
        if (!$role->isActive()) {
            throw new RuntimeException(__('roleCrud.roleDisable'));
        }

        $this->roleRepository->disable($role);

        return true;
    }

    public function delete(Role $role, Administrator $administrator)
    {
        $this->canManageRole($administrator, $role);

        $this->roleRepository->delete($role);

        return true;
    }

    public function canManageRole(
        Administrator $administrator,
        Role $role,
        bool $throwException = true
    ) {
        $result = ($administrator->getMaxPriority() >= $role->priority);

        if (!$result && $throwException) {
            throw new RuntimeException(__('admin::adminCrud.adminAccessDenied'));
        }

        return $result;
    }

    public function canManageRoles(
        Administrator $administrator,
        array $roleIds,
        bool $throwException = true
    ) {
        $result = ($administrator->getMaxPriority() >=
            $this->roleRepository->getBiggestPriorityRole($roleIds));

        if (!$result && $throwException) {
            throw new RuntimeException(__('admin::adminCrud.adminAccessDenied'));
        }

        return $result;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        $whereConditions = $this->getWhereConditions($data);

        return $this->roleRepository->getAll(
            $limit,
            $whereConditions
        );
    }


    /**
     * @param array $roles
     * @return mixed
     */
    public function getPermissionsByRole(
        $roles,
        ?Role $defRole = null,
        ?Administrator $admin = null
    ) {
        $adminPermissions = $admin?->permissions?->where('active', 1);
        $adminRole = $admin?->role();

        $groups = [];
        foreach ($roles as $role){

            if (!empty($defRole->id) && $role->id != $defRole->id) {
                continue;
            }

            /** @var Permission $permission **/
            foreach ($role->permissions->where('active', 1) as $permission) {

                $isChecked = true;
                if (
                    !empty($admin->administrator_id) // only if admin passet to this method
                    && !empty($adminRole->id)
                    && $adminRole->id == $role->id // only when we loop over admins role
                    && !empty($adminPermissions) // only when admin have any permisssions for his role
                    && $adminPermissions->count() > 0
                    && !$adminPermissions->contains($permission) // only if current role permission is not in admins list
                ) {
                    $isChecked = false;
                }

                $permission->isChecked = $isChecked;


                $modName = str_replace(' ', '', $permission->module_name);
                $contrName = str_replace(' ', '', $permission->controller_name);

                if (!isset($groups[$role->name])) {
                    $groups[$role->name] = [];
                }
                if (!isset($groups[$role->name][$modName])) {
                    $groups[$role->name][$modName] = [
                        'isChecked' => false,
                        'controllers' => [],
                    ];
                }
                if (!isset($groups[$role->name][$modName]['controllers'][$contrName])) {
                    $groups[$role->name][$modName]['controllers'][$contrName] = [
                        'isChecked' => false,
                        'permissions' => [],
                    ];
                }


                $groups[$role->name][$modName]['controllers'][$contrName]['permissions'][] = $permission;


                if ($isChecked) {
                    $groups[$role->name][$modName]['isChecked'] = true;
                    $groups[$role->name][$modName]['controllers'][$contrName]['isChecked'] = true;
                }
            }
        }

        return $groups;
    }
}

