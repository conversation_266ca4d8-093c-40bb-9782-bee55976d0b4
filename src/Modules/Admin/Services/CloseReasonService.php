<?php

namespace Modules\Admin\Services;

use Modules\Admin\Repositories\CloseReasonRepository;
use Modules\Common\Models\CloseReason;
use Modules\Common\Services\BaseService;
use RuntimeException;

class CloseReasonService extends BaseService
{
    private CloseReasonRepository $closeReasonRepository;

    /**
     * CloseReasonService constructor.
     *
     * @param CloseReasonRepository $closeReasonRepository
     */
    public function __construct(CloseReasonRepository $closeReasonRepository)
    {
        $this->closeReasonRepository = $closeReasonRepository;

        parent::__construct();
    }

    public function update(CloseReason $closeReason, array $data)
    {
        try {
            $this->closeReasonRepository->update($closeReason, $data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::closeReasonCrud.EditionFailed'),
                $exception
            );
        }

        return $closeReason;
    }

    public function create(array $data)
    {
        try {
            $this->closeReasonRepository->create($data);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::closeReasonCrud.CreationFailed'),
                $exception
            );
        }

        return true;
    }

    public function delete(CloseReason $closeReason)
    {
        try {
            $this->closeReasonRepository->delete($closeReason);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::closeReasonCrud.DeletionFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(CloseReason $closeReason)
    {
        if (!$closeReason->isActive()) {
            throw new RuntimeException(__('admin::closeReasonCrud.DisableForbidden'));
        }

        try {
            $this->closeReasonRepository->disable($closeReason);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('admin::closeReasonCrud.DisableFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(CloseReason $closeReason)
    {
        try {
            $this->closeReasonRepository->enable($closeReason);
        } catch (\Exception  $exception) {
            throw new RuntimeException(
                __('admin::closeReasonCrud.EnableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->closeReasonRepository->getAll(
            $limit,
            $joins,
            $whereConditions
        );
    }

    /**
     * @param array $data
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }
}
