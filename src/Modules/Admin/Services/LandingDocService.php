<?php

namespace Modules\Admin\Services;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\FileStorage;
use Modules\Common\Models\FileType;
use Modules\Common\Models\LandingDoc;
use Modules\Common\Services\BaseService;
use Modules\Common\Services\StorageService;
use RuntimeException;
use Throwable;

class LandingDocService extends BaseService
{
    private LandingDoc $landingDoc;
    private StorageService $storageService;

    public function __construct() {

        $this->landingDoc = new LandingDoc;
        $this->storageService = new StorageService;
        parent::__construct();
    }

    public function getById($landingDocId): ?LandingDoc
    {
        $landingDocId = intval($landingDocId);
        $landingDoc = null;

        if (empty($landingDocId)) {
            return $landingDoc;
        }

        $landingDoc = $this->landingDoc->find($landingDocId);

        return $landingDoc;
    }

    public function getAll($orderBy = 'landing_section_id', $order = 'ASC', $onlyActive = false): Collection
    {
        $list = (
            !empty($onlyActive) ?
            $this->landingDoc->where('active', '=', 1)->orderBy($orderBy, $order)->get() :
            $this->landingDoc->orderBy($orderBy, $order)->get()
        );

        return $list;
    }

    public function create(array $data)
    {
        if (
            empty($data['landing_doc_id']) ||
            $data['landing_doc_id'] === '0'
        ) {
            $data['priority'] = 0;
        }

        if (!empty($data['file'])) {

            $file = $data['file'];

            try {
                $fileStore = $this->storageService->uploadLandingDoc($file);
            } catch (Throwable $e) {
                throw new RuntimeException(
                    __('docs::documentTemplateCrud.storeDocumentProblem'),
                    $e
                );
            }

            $fileData = [];
            $fileData['hash'] = Hash::make($file->getClientOriginalName());
            $fileData['file_path'] = $fileStore[0];
            $fileData['file_size'] = $file->getSize();
            $fileData['file_type'] = $file->getClientMimeType();
            $fileData['file_name'] = $fileStore[1];
            $fileData['file_type_id'] = FileType::getIdFromCode(FileTypeEnum::LANDING_DOC);
            $fileData['file_storage_id'] = FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID;

            $savedFile = $this->storageService->getFileRepository()->create($fileData);
            if (empty($savedFile->file_id)) {
                throw new Exception('Failed to save file: ' . $fileStore[1]);
            }

            $data['file_id'] = $savedFile->file_id;
        }

        try {

            $landingDoc = (
                empty($data['landing_doc_id']) ||
                $data['landing_doc_id'] === '0' ?
                new LandingDoc :
                $this->getById($data['landing_doc_id'])
            );

            $landingDoc->fill($data);
            $landingDocCreateState = $landingDoc->save();

            if (
                (
                    empty($data['landing_doc_id']) ||
                    $data['landing_doc_id'] === '0'
                ) &&
                $landingDocCreateState
            ) {

                $landingDoc->fill([
                    'landing_doc_id' => $landingDoc['landing_doc_id'],
                    'priority' => $landingDoc['landing_doc_id']
                ]);

                $landingDocCreateState = $landingDoc->save();
            }
        } catch (Exception $exception) {
            throw new RuntimeException(
                __('LandingDocCreationFailed'),
                $exception
            );
        }

        return $landingDocCreateState;
    }
}
