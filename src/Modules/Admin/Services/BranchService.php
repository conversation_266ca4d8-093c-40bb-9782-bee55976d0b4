<?php

namespace Modules\Admin\Services;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Admin\Repositories\BranchRepository;
use Modules\Common\Models\Branch;
use Modules\Common\Services\BaseService;
use RuntimeException;

class BranchService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    public function __construct(private BranchRepository $branchRepository) {
        parent::__construct();
    }

    public function getAllBranches()
    {
        $branches = \Cache::remember('all_branches',self::CACHE_TTL,function(){
            return $this->branchRepository->getAllBranches();
        });

        if (!$branches) {
            throw new RuntimeException(__('admin::officeCrud.BranchesNotFound'));
        }

        return $branches;
    }

    public function update(Branch $branch, array $data)
    {
        try {
            $this->branchRepository->update($branch, $data);
            $branch->adopt('bankAccount', $data['bank_account_id']);

            /** @var \Modules\Common\Models\Office $office **/
            foreach ($branch->offices as $office) {
                $office->bankAccount()->sync($data['bank_account_id']);
            }
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('branchCrud.branchEditionFailed'),
                $exception
            );
        }

        return $branch;
    }

    public function create(array $data)
    {
        try {
            $branch = $this->branchRepository->create($data);
            $branch->adopt('bankAccount', $data['bank_account_id']);
        } catch (\Throwable $exception) {
            throw new RuntimeException(
                __('branchCrud.branchCreationFailed'),
                $exception
            );
        }

        return true;
    }

    public function delete(Branch $branch)
    {
        try {
            $this->branchRepository->delete($branch);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('branchCrud.branchDeletionFailed'),
                $exception
            );
        }

        return true;
    }

    public function disable(Branch $branch)
    {
        if (!$branch->isActive()) {
            throw new RuntimeException(__('admin::branchCrud.branchDisableForbidden'));
        }

        try {
            $this->branchRepository->disable($branch);
        } catch (\Exception $exception) {
            throw new RuntimeException(
                __('branchCrud.branchDisableFailed'),
                $exception
            );
        }

        return true;
    }

    public function enable(Branch $branch)
    {
        try {
            $this->branchRepository->enable($branch);
        } catch (\Exception  $exception) {
            throw new RuntimeException(
                __('branchCrud.branchEnableFailed'),
                $exception
            );
        }

        return true;
    }

    public function getByFilters(int $limit, array $data)
    {
        $order = $this->getOrderConditions($data);
        unset($data['order']);

        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->branchRepository->getAll(
            $limit,
            $joins,
            $whereConditions,
            $order
        );
    }

    public function getJoins(array $data): array
    {
        return [];
    }

    public function getBranches()
    {
        $branches = \Cache::remember('branches', self::CACHE_TTL, function () {
            return $this->branchRepository->getBranches();
        });

        if (!$branches) {
            throw new RuntimeException(__('admin::branchCrud.branchesNotFound'));
        }

        return $branches;
    }
}
