<?php

namespace Modules\Admin\Services;

use Modules\Admin\Repositories\OfficeTypeRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Services\BaseService;
use RuntimeException;

class OfficeTypeService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    private OfficeTypeRepository $officeTypeRepository;

    /**
     * OfficeTypeService constructor.
     *
     * @param OfficeTypeRepository $officeTypeRepository
     */
    public function __construct(
        OfficeTypeRepository $officeTypeRepository
    ) {
        $this->officeTypeRepository = $officeTypeRepository;

        parent::__construct();
    }

    /**
     * @return mixed
     *
     * @throws NotFoundException
     */
    public function allOfficeTypes()
    {
        $officesTypes = \Cache::remember('office_types_name', self::CACHE_TTL, function () {
            return $this->officeTypeRepository->getOfficeTypes();
        });

        if (!$officesTypes) {
            throw new RuntimeException(__('officeCrud.officeTypesNotFound'));
        }

        return $officesTypes;
    }
}
