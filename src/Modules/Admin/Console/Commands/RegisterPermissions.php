<?php

namespace Modules\Admin\Console\Commands;

use Illuminate\Routing\AbstractRouteCollection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use \Module;
use Modules\Common\Console\CommonCommand;
use \ReflectionClass;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Guard;
use Modules\Common\Models\Permission;
use Modules\Common\Models\Role;

class RegisterPermissions extends CommonCommand
{
    protected $signature = 'script:permissions:register';
    protected $description = 'Script for automatic permissions adding to DB.';

    public function handle()
    {
        $this->startLog();

        Artisan::call('route:clear');
        $this->info(Artisan::output());

        DB::table('permission')
            ->where('active', '=', 1)
            ->update(['active' => 0]);

        $routes = Route::getRoutes();
        $routesCount = count($routes->getRoutes());
        $excludedMethods = config('permission.exclude_methods');

        // route routes and create/update permissions for them
        foreach ($routes as $route) {
            $routeName = $route->getName();
            if ($routeName === null) {
                continue;
            }

            $action = $route->getActionMethod();
            if (isset($excludedMethods[$action]) || empty($route->defaults['description'])) {
                continue;
            }

            // @phpstan-ignore-next-line
            $checkModule = explode('\\', $route->getActionName());

            $module = Module::find($checkModule[1]);
            $moduleName = $checkModule[0];
            if ($module) {
                $moduleName = $module->getName();
            }

            // @phpstan-ignore-next-line
            $class = (new ReflectionClass($route->getController()))->getShortName();

            $permission = [
                'description' => $route->defaults['description'],
                'info_bubble' => $route->defaults['info_bubble'] ?? null,
                'guard_name' => Guard::DEFAULT_GUARD_NAME,
                'name' => $routeName,
                'module' => $moduleName,
                'controller' => $class,
                'module_name' => $route->defaults['module_name'] ?? $moduleName,
                'controller_name' => $route->defaults['controller_name'] ?? $class,
                'action' => $action,
                'active' => 1,
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ];
            if (!empty($route->defaults['additional_info'])) {
                $permission['has_additional_info'] = true;
                $permission['additional_info'] = json_encode($route->defaults['additional_info']);
            }

            Permission::updateOrCreate(
                [
                    'name' => $routeName,
                ],
                $permission
            );
        }

        $this->assignAllSuperAdminPermissions();
        $this->updateRolesUsers();

        $this->finishLog([$this->executionTimeString()], $routesCount, $routesCount, 'Done');
    }

    private function assignAllSuperAdminPermissions()
    {
        // add all permission for super admin role
        /** @var \Modules\Common\Models\Role $role */
        $role = Role::find(Role::ROLE_SUPER_ADMIN_ID);
        $role->permissions()->sync(Permission::all());

        // get all admins with role - superAdmin
        $superAdmins = Administrator::whereRaw(
            DB::raw(
                'administrator_id IN (select administrator_id from administrator_role where role_id = ' . Role::ROLE_SUPER_ADMIN_ID . ')'
            )
        )->get();

        // set all permissions for all super admins
        /** @var \Modules\Common\Models\Administrator $administrator */
        foreach ($superAdmins as $administrator) {
            $administrator->permissions()->sync(Permission::all());
        }

        // $administrator = Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID);
        // $administrator->permissions()->sync(Permission::all());
    }

    private function updateRolesUsers(): bool
    {
        $userRoles = Role::where('id', '!=', Role::ROLE_SUPER_ADMIN_ID)->get();
        if ($userRoles->count() < 1) {
            return false;
        }

        foreach ($userRoles as $role) {
            // get all admins with the role
            $roleAdmins = Administrator::whereRaw(
                DB::raw(
                    'administrator_id IN (select administrator_id from administrator_role where role_id = ' . $role->id . ')'
                )
            )->get();

            $rolePermissions = Permission::whereRaw(
                DB::raw('id IN (select permission_id from permission_role where role_id = ' . $role->id . ')')
            )->get();

            /** @var \Modules\Common\Models\Administrator $roleAdmin */
            foreach ($roleAdmins as $roleAdmin) {
                $roleAdmin->permissions()->sync($rolePermissions);
            }
        }

        return true;
    }
}
