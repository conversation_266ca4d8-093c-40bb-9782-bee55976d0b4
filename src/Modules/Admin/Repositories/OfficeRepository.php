<?php

namespace Modules\Admin\Repositories;

use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Traits\AdminTrait;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeModule;
use Modules\Common\Models\OfficeProduct;
use Modules\Common\Models\OfficeType;
use Modules\Common\Models\Product;
use Modules\Common\Repositories\BaseRepository;

class OfficeRepository extends BaseRepository
{
    use AdminTrait;

    public function __construct(
        private readonly Office $office
    ) {
    }

    public function getDbModel(): Office
    {
        return new Office();
    }

    public function getOfficeProductsByOfficeId(
        int $officeId,
        bool $isCompany = false,
        array $columns = ['*'],
    ): Collection {
        return Cache::remember(
            json_encode(compact('officeId', 'isCompany', 'columns')),
            now()->addMinutes(10),
            static function () use ($isCompany, $officeId, $columns) {
                return Product::where([
                    'product.active' => 1,
                    'product.migrated' => 0,
                    'product.legal_status' => $isCompany ? 'company' : 'individual',
                ])->whereRelation('offices', 'office.office_id', $officeId)->get($columns);
            }
        );
    }

    public function getMainPaymentAccountId(int $paymentMethodId)
    {
        $env = app()->environment();

        return config("bank_accounts.{$env}.{$paymentMethodId}.out");
    }

    public function getByFilters(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        return $this->office->filterBy($filters)
            ->with(['branch', 'creator', 'updater'])
            ->orderBy($this->office->getKeyName(), 'DESC')
            ->paginate($perPage);
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return LengthAwarePaginator|null
     */
    public function getAll(
        int $limit = 0,
        array $joins = [],
        array $where = [],
        array $order = ['name' => 'ASC'],
        bool $showDeleted = false
    ) {
        $adminOfficeIds = $this->getAdminOfficeIds();
        if (empty($adminOfficeIds)) {
            return null;
        }

        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = DB::table('office');
        $builder->select(DB::raw('office.*'));

        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        $builder->whereIn('office_id', $adminOfficeIds);

        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        } else {
            $builder->orderBy('office.updated_at', 'DESC');
        }

        if (empty($limit)) {
            $records = $builder->get();
            $result = Office::hydrate($records->toArray());
        } else {
            $result = $builder->paginate($limit);
            $records = Office::hydrate($result->all());
            $result->setCollection($records);
        }

        return $result;
    }

    public function getById(int $officeId): ?Office
    {
        $cacheKey = Office::class . '.' . $officeId;

        return Cache::get($cacheKey, function () use ($cacheKey, $officeId) {
            $office = Office::where('office_id', '=', $officeId)->first();

            Cache::put($cacheKey, $office, Carbon::now()->addHour());

            return $office;
        });
    }

    /**
     * @param Office $office
     *
     * @throws Exception
     */
    public function delete(Office $office)
    {
        $office->delete();
    }

    /**
     * @param Office $office
     */
    public function disable(Office $office)
    {
        $office->disable();
    }

    /**
     * @param Office $office
     */
    public function enable(Office $office)
    {
        $office->enable();
    }


    /**
     * @param array $data
     *
     * @return Office
     * @throws NotFoundException
     */
    public function create(array $data)
    {
        $settings = $data['settings'] ?? [];
        if (!empty($data['workTime'])) {
            $setingRow = [
                'setting_key' => SettingsEnum::work_time_office->value,
                'value' => json_encode($data['workTime']),
            ];
            $settings[] = $setingRow;
        }
        if (isset($data['workTime'])) {
            unset($data['workTime']);
        }
        if (isset($data['bank_account_id'])) {
            unset($data['bank_account_id']);
        }
        if (isset($data['check_service'])) {
            unset($data['check_service']);
        }

        $office = new Office();
        $office->fill($data);
        $office->save();
        $office->administrators()->attach(getAdminId());

        $office->adopt('settings', $settings);

        return $office;
    }

    /**
     * @param Office $office
     * @param array $data
     *
     */
    public function update(Office $office, array $data)
    {
        $office->fill($data);
        $office->updated_at = now();
        $office->updated_by = getAdminId();
        $office->save();
    }

    /**
     * @return Office[]|Collection
     */
    public function getAllOffices($forAdmin = true): Collection
    {
        if (!$forAdmin) {
            // TODOR: put in cache
            return Office::where(['deleted' => 0, 'active' => 1])
                ->orderBy('name', 'ASC')
                ->get();
        }

        $officeIds = getAdminOfficeIds();
        if (empty($officeIds)) {
            return new Collection([]);
        }

        // TODOR: put in cache
        return Office::where(['deleted' => 0, 'active' => 1])
            ->whereIn('office_id', $officeIds)
            ->orderBy('name', 'ASC')
            ->get();
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getOfficeTypes()
    {
        return OfficeType::where(['deleted' => 0, 'active' => 1])->get();
    }

    /**
     * @param $officeId
     *
     * @return bool
     */
    public function isSelfApproved(int $officeId): bool
    {
        return Office::where(
                [
                    'office_id' => $officeId,
                    'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                ]
            )->count() > 0;
    }

    public function getOfficesConsultants(int $loanId): array
    {
        return DB::select(
            DB::raw(
                "
                    select
                        o.name as office_name,
                        TRIM(CONCAT_WS(' ', a.first_name, a.middle_name, a.last_name)) AS consultant_full_name,
                        a.administrator_id as admin_id,
                        o.office_id
                            from office as o
                            join administrator_office ao on ao.office_id = o.office_id
                            join administrator a on a.administrator_id = ao.administrator_id
                            join loan l on l.office_id = ao.office_id
                        where l.loan_id = '$loanId';
                    "
            ),
        );
    }

    public function getOfficeNames(int $loanId)
    {
        return DB::select(
            DB::raw(
                "
                    select o.name  as office_name, l.office_id
                        from office as o
                                 join administrator_office ao on ao.office_id = o.office_id
                                 join administrator a on a.administrator_id = ao.administrator_id
                                 join loan l on l.office_id = ao.office_id
                        where l.loan_id = '$loanId'
                        group by  office_name, l.office_id;
                    "
            ),
        );
    }


    public function updateOfficeConsultant(int $officeId, int $consultantId, int $loanId, int $clientId): void
    {
        Loan::where('loan_id', $loanId)->update(['administrator_id' => $consultantId, 'office_id' => $officeId]);
    }

    public function removeMissingModules(Office $office, array $modules)
    {
        foreach ($office->modules as $module) {
            if (!in_array($module->module, array_column($modules, 'module'))) {
                $module->active = 0;
                $module->end_at = Carbon::now();
                $module->save();
            }
        }
    }

    public function updateOrCreateOfficeModules(Office $office, $modules): void
    {
        foreach ($modules as $module) {
            if (
                empty($module)
                || empty($module['type'])
                || empty($module['module'])
            ) {
                continue;
            }
            OfficeModule::updateOrCreate(
                ['office_id' => $office->getKey(), 'module' => $module['module']],
                ['type' => $module['type'], 'active' => 1]
            );
        }
    }

    public function officeHasProduct(int $officeId, int $productId): bool
    {
        return OfficeProduct::where(
                ['office_id' => $officeId, 'product_id' => $productId]
            )->count() === 1;
    }
}
