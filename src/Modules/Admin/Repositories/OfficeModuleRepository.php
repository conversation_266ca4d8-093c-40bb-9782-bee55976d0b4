<?php

namespace Modules\Admin\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\AdministratorModule;
use Modules\Common\Models\AdministratorOffice;
use Modules\Common\Models\OfficeModule;

class OfficeModuleRepository
{
    public function otherOffice(Administrator $administrator, OfficeModule $officeModule): ?OfficeModule
    {
        return OfficeModule::query()->whereHas(
                AdministratorOffice::class,
                function (Builder $table) use ($administrator, $officeModule) {
                    $table->where('administrator_id', '=', $administrator->getKey());
                    $table->where('office_id', '=', $officeModule->office_id);
                    $table->where('module', '=', $officeModule->module);
                }
            )
            ->orderBy('type')
            ->first();
    }

    public function getAdministratorModule(
        OfficeModule $officeModule,
        Administrator $administrator
    ): ?AdministratorModule {
        return AdministratorModule::where(
            [
                'module' => $officeModule->module,
                'administrator_id' => $administrator->getKey(),
            ]
        )->first();
    }
}
