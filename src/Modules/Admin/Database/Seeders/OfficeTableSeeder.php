<?php

namespace Modules\Admin\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Branch;
use Modules\Common\Models\Module;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeType;
use Modules\Common\Models\Permission;

class OfficeTableSeeder extends Seeder
{
    protected OfficeService $officeService;
    private string $name = "Офис";
    private string $newName = "офис";

    public function __construct(OfficeService $officeService)
    {
        $this->officeService = $officeService;
    }

    public function run()
    {
        $offices = [
            [
                'office_id' => Office::OFFICE_ID_WEB,
                'branch_id' => Branch::BRANCH_ID_SOFIA,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Централата(Онлайн кредити)',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_NOVI_PAZAR_1,
                'branch_id' => Branch::BRANCH_ID_NOVI_PAZAR,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Нови Пазар',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_NOVI_PAZAR_2,
                'branch_id' => Branch::BRANCH_ID_NOVI_PAZAR,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Нови пазар 2',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_SHUMEN_1,
                'branch_id' => Branch::BRANCH_ID_SHUMEN,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Шумен 1',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_SHUMEN_2,
                'branch_id' => Branch::BRANCH_ID_SHUMEN,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Шумен 2',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_KAOLINOVO,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Каолиново',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_TODOR_IKONOV,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Тодор Икономов',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_ORLYAK,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Орляк',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_DULOVO,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Дулово',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_TURGOVISCHTE,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Търговище',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_ISPERIH,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Исперих',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_BELOGRADEC,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Белоградец',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_VARNA_1,
                'branch_id' => Branch::BRANCH_ID_VARNA,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Варна',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_VARNA_2,
                'branch_id' => Branch::BRANCH_ID_VARNA,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Варна2',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_BLAGOEVGRAD,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Благоевград',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_GORNA_ORYAHOVICA,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Горна Оряховица',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_GOCE_DELCHEV,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Гоце Делчев',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_VELIKO_TURNOVO,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Велико Търново',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_VRACA,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Враца',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_KOZLODUI,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Козлодуй',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_MONTANA,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Монтана',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_PETRICH,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Петрич',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_PLEVEN,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Плевен',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_RAZGRAD,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Разград',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_SANDANSKI,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Сандански',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_KYUSTENDIL,
                'branch_id' => Branch::BRANCH_ID_CREDIT_HELP,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Кюстендил',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_ETROPOLE,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Етрополе',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_VETRINO,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Ветрино',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_POPOVO,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_SELF_APPROVE_ID,
                'name' => 'Попово',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_RUSE,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'name' => 'Русе',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_VURBICA,
                'branch_id' => null,
                'name' => 'Върбица',
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_RUDOZEM,
                'branch_id' => null,
                'name' => 'Рудозем',
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_ZLATOGRAD,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'name' => 'Златоград',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_SOFIA_FANTASTIKO,
                'branch_id' => Branch::BRANCH_ID_SOFIA,
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'name' => 'Фантастико',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_SOFIA,
                'branch_id' => Branch::BRANCH_ID_SOFIA,
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'name' => 'София',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'office_id' => Office::OFFICE_ID_IK,
                'branch_id' => null,
                'office_type_id' => OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID,
                'name' => 'Каса ИК',
                'access_start_at' => now(),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
        ];

        $modules = array_flip(Module::getModuleNames());

        DB::table('office')->insert($offices);
        $offices = Office::all();
        foreach ($offices as $office) {
            $this->officeService->update(
                $office,
                [
                    'modules' => [
                        [
                            'module' => array_rand($modules),
                            'type' => array_rand(array_flip(Permission::getTypes())),
                        ],
                    ],
                ]
            );
        }
        DB::statement("SELECT setval('office_office_id_seq', (SELECT MAX(office_id) FROM office)+1);");
        if (Schema::hasTable(Office::getTableName())) {
            Office::where('name', 'LIKE', '%' . $this->name . '%')
                ->update(['name' => DB::raw("REPLACE(name, '" . $this->name . "', '" . $this->newName . "')")]);
        }

        $offices->each(function (Office $office) {
            $office->update([
                'phone' => '0000000000',
                'address' => fake()->address
            ]);
        });
    }
}
