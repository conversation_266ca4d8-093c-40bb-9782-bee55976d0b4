<?php

namespace Modules\Admin\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Enums\ThirdPartyReportServiceEnum;
use Modules\Common\Models\Office;

class OfficeCheckTableSeeder extends Seeder
{
    protected OfficeService $officeService;

    public function __construct(OfficeService $officeService)
    {
        $this->officeService = $officeService;
    }

    public function run()
    {
        if(isTesting()){
            return;
        }
        $offices = Office::all();
        $checkServices = collect(ThirdPartyReportServiceEnum::cases());

        $offices->each(function (Office $office) use ($checkServices) {
            $services = $checkServices->map(function (ThirdPartyReportServiceEnum $checkService) use ($office) {
                if (
                    $checkService === ThirdPartyReportServiceEnum::a4e &&
                    $office->office_id !== Office::OFFICE_ID_WEB
                ) {
                    return null;
                }

                return [
                    'office_id' => $office->office_id,
                    'service_key' => $checkService
                ];
            })->filter()->toArray();
            $office->officeCheck()->createManyQuietly($services);
        });
    }
}
