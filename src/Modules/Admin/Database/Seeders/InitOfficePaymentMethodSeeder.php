<?php

namespace Modules\Admin\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Admin\Entities\Office;
use Modules\Common\Models\BankAccount;
use Modules\Common\Models\PaymentMethod;

class InitOfficePaymentMethodSeeder extends Seeder
{
    public function run(): void
    {
        $offices = Office::all();
        $offices->map(function (Office $office) {

            collect(PaymentMethod::getAllIdsNames())->each(function ($label, $paymentMethodId) use ($office) {
                $bankRow = BankAccount::where('payment_method_id', $paymentMethodId)->first();
                if ($bankRow) {
                    $office->bankAccount()->attach($bankRow);
                }
            });
        });
    }
}
