<?php

namespace Modules\Admin\Http\Controllers;

ini_set('max_execution_time', 120);
ini_set('memory_limit', '2000M');

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Admin\Exports\AdminTestStatsExport;
use Modules\Admin\Entities\AdminTest;
use Modules\Admin\Entities\AdminTestPeriod;
use Modules\Admin\Entities\AdminTestStats;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;

class AdminTestController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /////////////////////////////////// PERIOD /////////////////////////////////

    public function list()
    {
        $testPeriods = AdminTestPeriod::with(['creator'])
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        return view('admin::test.list', compact('testPeriods'));
    }

    public function createPeriod(Request $request)
    {
        // Validate the input
        $request->validate([
            'from' => 'required|date_format:Y-m-d',
            'to' => 'required|date_format:Y-m-d|after_or_equal:from',
        ]);

        // Disable the previous active period (if exists)
        AdminTestPeriod::where('active', 1)->update(['active' => 0]);

        // Create the new test period
        AdminTestPeriod::create([
            'from' => $request->from,
            'to' => $request->to,
            'created_by' => getAdminId(), // Assuming the admin is logged in
            'active' => 1,
        ]);

        return redirect()
            ->route('admin.test.listing')
            ->with('success', 'Test period added successfully.');
    }

    /////////////////////////////////// VOTE ///////////////////////////////////

    public function startTest()
    {
        // Fetch the active period
        $activePeriod = $this->getActiveTestPeriod();

        if (!$activePeriod) {
            return view('admin::test.start', ['error' => 'No test period define.']);
        }

        // Get date range
        $from = Carbon::parse($activePeriod->from)->startOfDay()->toDateTimeString();
        $to = Carbon::parse($activePeriod->to)->endOfDay()->toDateTimeString();

        // Fetch all loan IDs that are already in the `admin_test` table for this period
        $existingLoanIds = AdminTest::where('admin_test_period_id', $activePeriod->id)
            ->where('administrator_id', getAdminId())
            ->pluck('loan_id')
            ->toArray();

        // Fetch loans based on the provided SQL query
        $newLoans = DB::select("
            SELECT
                x.loan_id,
                x.client_id
            FROM loan x
            JOIN loan_actual_stats las ON las.loan_id = x.loan_id AND las.first_loan = 1
            WHERE
                x.created_at >= :from
                AND x.created_at <= :to
                AND x.loan_status_id IN (6, 7, 9)
                AND x.office_id = 1
                " . (!empty($existingLoanIds) ? "AND x.loan_id NOT IN (" . implode(',', $existingLoanIds) . ")" : ""). "
            ORDER BY x.loan_id
        ", ['from' => $from, 'to' => $to]);

        // Prepare data for bulk insertion
        $newTestEntries = [];
        foreach ($newLoans as $loan) {
            $newTestEntries[] = [
                'admin_test_period_id' => $activePeriod->id,
                'administrator_id' => getAdminId(),
                'client_id' => $loan->client_id,
                'loan_id' => $loan->loan_id,
                'processed_at' => null,
                'is_gypsy' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert all new loans in bulk
        if (!empty($newTestEntries)) {
            AdminTest::insert($newTestEntries);
        }

        return view('admin::test.start', ['activePeriod' => $activePeriod]);
    }

    public function finishTest()
    {
        return view('admin::test.finish');
    }

    public function loadVote()
    {
        // Fetch the active period
        $activePeriod = $this->getActiveTestPeriod();

        if (!$activePeriod) {
            return view('admin::test.start', ['error' => 'No test period defined.']);
        }

        // Fetch the test row (with logic to skip invalid rows)
        $data = $this->getNextValidTestRow($activePeriod->id);

        if (empty($data)) {
            return view('admin::test.finish'); // If no valid test rows are left
        }

        return view('admin::test.load', $data);
    }

    public function nextVote()
    {
        // Fetch the active period
        $activePeriod = $this->getActiveTestPeriod();

        // Fetch the next valid test row (with logic to skip invalid rows)
        $data = $this->getNextValidTestRow($activePeriod->id);

        if (!empty($data)) {
            return response()->json($data);
        } else {
            return response()->json([
                'message' => 'no_more_tests'
            ]);
        }
    }

    public function processVote(Request $request)
    {
        $request->validate([
            'test_id' => 'required|exists:admin_test,id',
            'is_gypsy' => 'required|boolean',
        ]);

        $adminTest = AdminTest::where('id', $request->test_id)
            ->where('administrator_id', getAdminId())
            ->firstOrFail();

        // Save the result in admin_test
        $adminTest->update([
            'is_gypsy' => $request->is_gypsy,
            'processed_at' => now(),
        ]);

        return response()->json(['message' => 'next']);
    }

    /////////////////////////////////// STATS //////////////////////////////////
    public function voters(int $id)
    {
        $period = AdminTestPeriod::find($id);
        $count = AdminTest::where('admin_test_period_id', $id)->count();
        $rates = AdminTest::where('admin_test_period_id', $id)
            ->where('is_gypsy', 1)
            ->join('administrator', 'admin_test.administrator_id', '=', 'administrator.administrator_id')
            ->selectRaw('admin_test.administrator_id, administrator.first_name, administrator.last_name, COUNT(*) as gypsy_count')
            ->groupBy('admin_test.administrator_id', 'administrator.first_name', 'administrator.last_name')
            ->orderBy('gypsy_count', 'desc')
            ->get();

        return view('admin::test.voters', compact('period', 'count', 'rates'));
    }


    public function stats(Request $request)
    {
        // Apply filters
        $client_id = $request->input('client_id');
        $loan_id = $request->input('loan_id');
        $from = $request->input('from');
        $to = $request->input('to');
        $id = $request->input('admin_test_period_id');

        if (!empty($id)) {
            $period = AdminTestPeriod::find($id);
            if (!empty($period->from) && !empty($period->to)) {
                $from = $period->from;
                $to = $period->to;
            }
        }

        $periods = AdminTestPeriod::orderBy('id', 'desc')->limit(50)->get();

        // Fetch the stats with pagination
        $stats = AdminTestStats::when($client_id, function ($query) use ($client_id) {
                return $query->where('client_id', $client_id);
            })
            ->when($loan_id, function ($query) use ($loan_id) {
                return $query->where('loan_id', $loan_id);
            })
            ->when($from, function ($query) use ($from) {
                return $query->where('loan_created_at', '>=', Carbon::parse($from)->startOfDay());
            })
            ->when($to, function ($query) use ($to) {
                return $query->where('loan_created_at', '<=', Carbon::parse($to)->endOfDay());
            })
            ->paginate(25);

        return view('admin::test.stats', compact('stats', 'periods', 'client_id', 'loan_id', 'from', 'to'));
    }

    public function statsCalculate(Request $request)
    {
        $adminTestPeriodId = (int) $request->input('id');
        if (empty($adminTestPeriodId)) {
            return response()->json(['success' => false, 'message' => 'No test period selected.']);
        }

        // Fetch distinct loan_ids in chunks to avoid memory overload
        AdminTest::select('loan_id')
            ->where('admin_test_period_id', $adminTestPeriodId)
            ->whereNotNull('processed_at')
            ->distinct()
            ->orderBy('loan_id')
            ->chunk(100, function ($loans) {

                $insert = [];
                foreach ($loans as $l) {

                    // Get all rows for this specific loan_id
                    $loanTestRows = AdminTest::where('loan_id', $l->loan_id)->whereNotNull('processed_at')->get();

                    // Calculate the counts of is_gypsy = 1 and is_gypsy = 0
                    $gypsyCount = $loanTestRows->where('is_gypsy', 1)->count();
                    $nonGypsyCount = $loanTestRows->where('is_gypsy', 0)->count();
                    if ($gypsyCount == 0 && $nonGypsyCount == 0) {
                        continue;
                    }

                    // Fetch the max is_gypsy value (if there's a tie, we prefer 1)
                    $is_gypsy_max = 0;
                    if ($gypsyCount > $nonGypsyCount) {
                        $is_gypsy_max = 1;
                    }

                    // Prepare the details field (e.g., "3/1")
                    $details = "{$gypsyCount}/{$nonGypsyCount}";

                    $loan = Loan::find($l->loan_id);

                    $insert[$loan->loan_id] = [
                        'client_id' => $loan->client_id,
                        'loan_id' => $loan->loan_id,
                        'loan_created_at' => $loan->created_at,
                        'is_gypsy' => $is_gypsy_max,
                        'details' => $details,
                    ];
                }

                $loanIds = array_keys($insert);

                if (!empty($loanIds)) {
                    DB::transaction(function () use ($loanIds, $insert) {
                        DB::statement('DELETE FROM admin_test_stats WHERE loan_id in (' . implode(',', $loanIds) . ')');
                        AdminTestStats::insert($insert);
                    });
                }
            });

        return response()->json(['success' => true, 'message' => 'Stats calculated successfully.']);
    }

    public function statsExport(Request $request)
    {
        $from = $request->from;
        $to = $request->to;

        if (!empty($request->id)) {
            $period = AdminTestPeriod::find($request->id);
            if (!empty($period->from) && !empty($period->to)) {
                $from = $period->from;
                $to = $period->to;
            }
        }

        // Apply filters
        $query = AdminTestStats::query()
            ->when($request->client_id, function ($query) use ($request) {
                return $query->where('client_id', $request->client_id);
            })
            ->when($request->loan_id, function ($query) use ($request) {
                return $query->where('loan_id', $request->loan_id);
            })
            ->when($from, function ($query) use ($from) {
                return $query->where('loan_created_at', '>=', Carbon::parse($from)->startOfDay());
            })
            ->when($to, function ($query) use ($to) {
                return $query->where('loan_created_at', '<=', Carbon::parse($to)->endOfDay());
            });

        return Excel::download(new AdminTestStatsExport($query), 'gypsy_stats.xlsx');
    }

    ////////////////////////////////////////////////////////////////////////////

    private function getActiveTestPeriod(): ?AdminTestPeriod
    {
        return Cache::remember('active_test_period', 600, function () {
            return AdminTestPeriod::where('active', 1)->first();
        });
    }

    private function getTestRow(int $testId): ?AdminTest
    {
        return AdminTest::where('administrator_id', getAdminId())
            ->where('admin_test_period_id', $testId)
            ->whereNull('processed_at')
            ->orderBy('id')
            ->first();
    }

    private function getNextValidTestRow(int $periodId): ?array
    {
        do {
            // Get the next test row
            $row = $this->getTestRow($periodId);

            if (!$row) {
                return null; // No more test rows are available
            }

            // Prepare the test data
            $data = $this->prepareTest($row);

            if (!empty($data)) {
                return $data; // Return valid test data
            }

            // If test row has no images, delete it and try the next one
            DB::statement('DELETE FROM admin_test WHERE id = ' . $row->id);

        } while (true); // Keep trying until a valid test row is found or none are left
    }

    private function prepareTest(AdminTest $row): array
    {
        $loan = $row->loan;
        $client = $loan->client;

        $pictures = [];

        $pictureMVR = $client->getMvrPicture();
        if (!empty($pictureMVR->base64)) {
            $pictures[] = $pictureMVR->base64;
        }

        $picturesManual = $client->getManualUploadedIdCardFiles(2);
        if ($picturesManual->count() > 0) {

            /** @var \Modules\Common\Models\File $pictureFile **/
            foreach ($picturesManual as $pictureFile) {
                $base64 = $pictureFile?->getBase64() ?? '';
                if (!empty($base64)) {
                    $pictures[] = $base64;
                }
            }
        }

        if (empty($pictures)) {
            return [];
        }

        $comment = $loan->comment;
        if (empty($comment)) {
            $comment = $loan->getLastCommunicationComment();
        }

        return [
            'client_id'  => $loan->client_id,
            'loan_id'  => $loan->loan_id,
            'test_id'  => $row->id,
            'pictures' => $pictures,
            'comment'  => $comment,
        ];
    }
}
