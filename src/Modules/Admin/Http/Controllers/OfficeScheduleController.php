<?php

namespace Modules\Admin\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Admin\Application\Actions\Office\GetOfficeScheduleAction;
use Modules\Admin\Application\Actions\Office\StoreOfficeScheduleAction;
use Modules\Admin\Http\Requests\OfficeScheduleRequest;
use Modules\Common\Http\Controllers\BaseController;

class OfficeScheduleController extends BaseController
{
    public function edit(OfficeScheduleRequest $request, GetOfficeScheduleAction $action): View
    {
        $data = [
            'offices'=>$action->getOffices(),
            'weeks'=>$action->getWeeks()
        ];
        $input = $request->validated();
        $officeId = $input['office_id'] ?? $data['offices']->first()->getKey();
        $weekStartingDate = $input['week_from'] ?? $data['weeks'][0];
        $data['officeSchedule'] = $action->execute($officeId, $weekStartingDate);
        return view('admin::office-schedule.crud', $data);
    }

    public function store(
        OfficeScheduleRequest $request,
        StoreOfficeScheduleAction $action
    ): RedirectResponse
    {
        $action->execute($request->asDto());

        return redirect()
            ->route('admin.officeSchedule.edit')
            ->with('success', __('admin::officeCrud.officeScheduleCreatedSuccessfully'));
    }
}
