<?php

namespace Modules\Admin\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\Admin\Application\Actions\ClientRateUs\ClientRateUsIndexDataAction;
use Modules\Admin\Application\Actions\YoutubeImageAction;
use Modules\Admin\Forms\ClientRateUsForm;
use Modules\Admin\Http\Requests\ClientRateUsRequest;
use Modules\Admin\Models\ClientRateUs;
use Modules\Common\Http\Controllers\BaseController;

class ClientRateUsController extends BaseController
{
    public function index(ClientRateUsIndexDataAction $clientRateUsIndexDataAction): View
    {
        $data = $clientRateUsIndexDataAction->execute();

        return view('admin::client-rate-us.index', $data);
    }

    public function createClientRateUs(): View
    {
        $data['clientRateUsForm'] = ClientRateUsForm::create([
            'method' => 'POST',
            'url' => route('admin.client-rate-us.storeClientRateUs'),
            'data-parsley-validate' => 'true',
            'files' => true
        ]);

        return view('admin::client-rate-us.create', $data);
    }

    public function storeClientRateUs(ClientRateUsRequest $request)
    {
        $data = $request->validated();
        $data['slug'] = str($data['client_name'])->slug()->value();
        $data['created_by'] = getAdminId();

        $clientRateUs = ClientRateUs::whereSlug($data['slug'])->first();
        if ($clientRateUs) {
            $data['slug'] = "{$data['slug']}-" . time();
        }

        $clientRateUs = ClientRateUs::create($data);
        if ($clientRateUs->exists) {

            $videoThumbImage = app(YoutubeImageAction::class)->execute($data['youtube_url']);
            if ($videoThumbImage) {
                // @phpstan-ignore-next-line
                $clientRateUs
                    ->clearMediaCollection('clientImage')
                    ->addMediaFromUrl($videoThumbImage)
                    ->toMediaCollection('clientImage');
            }

            return to_route('admin.client-rate-us.index')->with('success', __('Success create client rate'));
        }

        return $this->backError('Error creating client rate us');
    }

    public function edit(ClientRateUs $clientRateUs): View
    {
        $data['clientRateUs'] = $clientRateUs;
        $data['clientRateUsForm'] = ClientRateUsForm::create([
            'method' => 'PUT',
            'url' => route('admin.client-rate-us.updateClientRateUs', $clientRateUs->getKey()),
            'data-parsley-validate' => 'true',
            'model' => $clientRateUs,
            'files' => true
        ]);
        $data['clientImage'] = $clientRateUs->getFirstMediaUrl('clientImage');

        return view('admin::client-rate-us.edit', $data);
    }

    public function updateClientRateUs(ClientRateUsRequest $request, ClientRateUs $clientRateUs): RedirectResponse
    {
        $data = $request->validated();
        $data['created_by'] = getAdminId();
        if ($clientRateUs->update($data)) {

            if (!$clientRateUs->getMedia()->count()) {
                $videoThumbImage = app(YoutubeImageAction::class)->execute($data['youtube_url']);

                // @phpstan-ignore-next-line
                $clientRateUs
                    ->clearMediaCollection('clientImage')
                    ->addMediaFromUrl($videoThumbImage)
                    ->toMediaCollection('clientImage');
            }

            return to_route('admin.client-rate-us.index')->with('success', __('Success update post'));
        }

        return $this->backError('Error something wrong');
    }

    public function destroy(ClientRateUs $clientRateUs): RedirectResponse
    {
        $clientRateUs->deleted_at = date('Y-m-d H:i:s');
        $clientRateUs->deleted_by = getAdminId();

        if ($clientRateUs->exists && $clientRateUs->save()) {
            return $this->backSuccess('Success delete client rate us');
        }
        return $this->backError('Error delete client rate us');
    }
}
