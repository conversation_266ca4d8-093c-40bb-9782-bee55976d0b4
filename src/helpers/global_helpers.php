<?php

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BankAccount;
use Modules\Common\Models\Office;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Setting;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\Product;
use Modules\Head\Services\ClientService;
use Psr\Log\LogLevel;

if (!function_exists('getInsuranceAmount')) {
    function getInsuranceAmount(int $loanAmount = 0): int
    {
        return 5000;
    }
}

if (!function_exists('normalizePhone')) {
    function normalizePhone($value)
    {
        $value = preg_replace('/[\s\-\(\)]/', '', $value);

        $value = ltrim($value, '+');

        if (is_numeric($value)) {
            if (str_starts_with($value, '359')) {
                $local = substr($value, 3);
                $value = (str_starts_with($local, '0') ? '' : '0') . $local;
            }
        }

        return $value;
    }
}

if (!function_exists('replaceNonBulgarianChars')) {
    function replaceNonBulgarianChars(string $string, bool $isName = false): string
    {
        // Define a mapping of non-Bulgarian characters to their Bulgarian counterparts
        $charMap = [
            // Lowercase letters
            'a' => 'а',
            'b' => 'б',
            'c' => 'ц',
            'd' => 'д',
            'e' => 'е',
            'f' => 'ф',
            'g' => 'г',
            'h' => 'х',
            'i' => 'и',
            'j' => 'й',
            'k' => 'к',
            'l' => 'л',
            'm' => 'м',
            'n' => 'н',
            'o' => 'о',
            'p' => 'п',
            'q' => 'к',
            'r' => 'р',
            's' => 'с',
            't' => 'т',
            'u' => 'у',
            'v' => 'в',
            'w' => 'у',
            'x' => 'кс',
            'y' => 'й',
            'z' => 'з',
            // Uppercase letters
            'A' => 'А',
            'B' => 'Б',
            'C' => 'Ц',
            'D' => 'Д',
            'E' => 'Е',
            'F' => 'Ф',
            'G' => 'Г',
            'H' => 'Х',
            'I' => 'И',
            'J' => 'Й',
            'K' => 'К',
            'L' => 'Л',
            'M' => 'М',
            'N' => 'Н',
            'O' => 'О',
            'P' => 'П',
            'Q' => 'К',
            'R' => 'Р',
            'S' => 'С',
            'T' => 'Т',
            'U' => 'У',
            'V' => 'В',
            'W' => 'У',
            'X' => 'КС',
            'Y' => 'Й',
            'Z' => 'З',
            // Other characters
            ' ' => ' ', // Space
            ',' => ',', // Comma
            '.' => '.', // Dot
            'ѝ' => 'и',
            '?' => '',
            // Add more mappings as needed...
        ];

        // Replace non-Bulgarian characters with their Bulgarian counterparts
        foreach ($charMap as $nonBgChar => $bgChar) {
            $string = str_replace($nonBgChar, $bgChar, $string);
        }

        if ($isName) {
            $cleanedString = preg_replace('/[^\p{L}\p{N} \.]/u', '', $string);
        } else {
            $cleanedString = preg_replace('/[^\p{L}\p{N} ,\.]/u', '', $string);
        }

        return $cleanedString;
    }
}

if (!function_exists('getMinRefinanceAmount')) {
    /**
     * By default, will be return int
     */
    function getMinRefinanceAmount(int $amount, bool $formated = false): int|string
    {
        if ($formated) {
            return intToFloat($amount + \Modules\Sales\Domain\Entities\Loan\NewLoan::MIN_REF_AMOUNT);
        }

        return $amount + \Modules\Sales\Domain\Entities\Loan\NewLoan::MIN_REF_AMOUNT;
    }
}

if (!function_exists('ensureJsonEncoded')) {
    function ensureJsonEncoded($data)
    {
        // Check if the input is already a JSON string
        if (is_string($data)) {
            json_decode($data);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $data; // Already JSON, return as is
            }
        }

        // If not JSON, encode it
        return json_encode($data);
    }
}

if (!function_exists('ensureJsonDecoded')) {
    function ensureJsonDecoded($data)
    {
        // If the data is not a string, assume it's already decoded and return
        if (!is_string($data) && is_array($data)) {
            return $data;
        }

        // Try decoding once
        $decoded = json_decode($data, true);

        // If it's valid JSON and is an array, check if it contains double-encoded JSON
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // If first decoding results in a JSON string (double encoding case)
        $doubleDecoded = json_decode($decoded, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($doubleDecoded)) {
            return $doubleDecoded;
        }

        return null;
    }
}


if (!function_exists('getYoutubeVideoId')) {
    function getYoutubeVideoId(string $youtubeVideoUrl): ?string
    {
        $pattern = '/(?:https?:\/\/(?:www\.)?youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
        // Use preg_match to find the video ID
        if (preg_match($pattern, $youtubeVideoUrl, $matches)) {
            return $matches[1];
        }

        return null;
    }
}

if (!function_exists('calculateGpr')) {
    function calculateGpr($annualInterest, ?float $discount = null): float
    {
        if ($discount) {
            $annualInterest = ($annualInterest * (1 - $discount));
        }
        $gpr = round((pow((1 + $annualInterest / 12), 12) - 1), 4) * 100;

        return round($gpr, 2);
    }
}

if (!function_exists('carbonMin')) {
    function carbonMin(Carbon $date1, Carbon $date2): Carbon
    {
        return $date1->lt($date2) ? $date1 : $date2;
    }
}

if (!function_exists('carbonMax')) {
    function carbonMax(Carbon $date1, Carbon $date2): Carbon
    {
        return $date1->gt($date2) ? $date1 : $date2;
    }
}

if (!function_exists('floatToInt')) {
    function floatToInt($amount): int
    {
        $amount = floatval(trim($amount));
        $amount = strval(number_format($amount, 2, '.', ''));

        return round($amount * 100, 2);
    }
}

if (!function_exists('intToFloat')) {
    function intToFloat($amount): string
    {
        return number_format(($amount / 100), 2, '.', '');
    }
}


if (!function_exists('toFloatWithTwoDecimals')) {
    function toFloatWithTwoDecimals($value): string
    {
        if (empty($value)) {
            return '0.00';
        }

        return number_format((float) $value, 2, '.', '');
    }
}


if (!function_exists('isProdOrStage')) {
    function isProdOrStage(): bool
    {
        return app()->environment(['prod', 'stage']);
    }
}

if (!function_exists('isProd')) {
    function isProd(): bool
    {
        return app()->environment('prod');
    }
}

if (!function_exists('isStage')) {
    function isStage(): bool
    {
        return app()->environment('stage');
    }
}

if (!function_exists('isLocal')) {
    /**
     * Check if out environment should execute real checks for ThirdParty apps
     */
    function isLocal(): bool
    {
        return app()->environment('local');
    }
}

if (!function_exists('isTesting')) {
    /**
     * Check if out environment should execute real checks for ThirdParty apps
     */
    function isTesting(): bool
    {
        return app()->environment('testing');
    }
}

if (!function_exists('getCaller')) {
    function getCaller(): string
    {
        return debug_backtrace()[2]['class'] . '::' . debug_backtrace()[2]['function'];
    }
}

if (!function_exists('amount')) {
    /**
     * Transform: 1000.00 -> 1 000.00 лв
     */
    function amount(int $amount, string $currencySign = 'лв'): string
    {
        return intToFloat($amount) . (!empty($currencySign) ? ' ' . $currencySign : '');
    }
}

if (!function_exists('amountEur')) {
    /**
     * Transform: 1000.00 -> 1 000.00 лв
     */
    function amountEur(float|string $amount, string $currencySign = 'eur'): string
    {
        // Try to cast to float if it's a string
        if (is_string($amount)) {
            if (!is_numeric($amount)) {
                throw new \InvalidArgumentException(
                    "Invalid amount provided. Expected a float or a numeric string, got '{$amount}'."
                );
            }

            $amount = (float) $amount;
        }

        $amoutNew = number_format(($amount / 1.95583), 2, '.', ' ');

        if (empty($currencySign)) {
            return $amoutNew;
        }

        return $amoutNew . ' ' . $currencySign;
    }
}

if (!function_exists('rate')) {
    function rate(float $value = null)
    {
        if (empty($value)) {
            return '0.00 %';
        }

        return number_format($value, 2) . ' ' . '%';
    }
}

if (!function_exists('formatDate')) {
    function formatDate(?string $date = null, $format = Setting::SHOW_FORMAT): string
    {
        if (!$date) {
            return Carbon::now()->format($format);
        }

        return Carbon::parse($date)->format($format);
    }
}

if (!function_exists('formatDateOrEmpty')) {
    function formatDateOrEmpty(?string $date = null, $format = Setting::SHOW_FORMAT): string
    {
        if (!$date) {
            return '';
        }

        return Carbon::parse($date)->format($format);
    }
}

if (!function_exists('showDate')) {
    function showDate($date, string $additional = null)
    {
        $format = Setting::SHOW_FORMAT;
        if (!empty($additional)) {
            $format = $format . ' ' . $additional;
        }

        return formatDate($date, $format);
    }
}

if (!function_exists('showDateBg')) {
    function showDateBg($date): string
    {
        $obj = Carbon::parse($date);
        $d = $obj->format('d');
        $y = $obj->format('Y');
        $m = $obj->month;

        $months = [
            1 => "януари",
            2 => "февруари",
            3 => "март",
            4 => "април",
            5 => "май",
            6 => "юни",
            7 => "юли",
            8 => "август",
            9 => "септември",
            10 => "октомври",
            11 => "ноември",
            12 => "декември"
        ];
        $month = $months[$m] ?? '';

        return sprintf('%s %s %s', $d, $month, $y);
    }
}

if (!function_exists('dbDate')) {
    function dbDate($date, string $additional = null)
    {
        $format = Setting::DB_DATE_FORMAT;
        if (!empty($additional)) {
            $format = $format . ' ' . $additional;
        }

        return formatDate($date, $format);
    }
}

if (!function_exists('carbonDateNullable')) {
    function carbonDateNullable($date, string $additional = null): string
    {
        if (empty($date)) {
            return '';
        }

        $date = Carbon::make($date);
        $format = Setting::SHOW_FORMAT;

        if (!empty($additional)) {
            $format = $format . ' ' . $additional;
        }

        return $date->format($format);
    }
}

if (!function_exists('rounder')) {
    function rounder(float $number, int $precision = 2): float
    {
        return round($number, $precision);
    }
}

if (!function_exists('intToMonths')) {
    function intToMonths($days): int
    {
        return floor($days / 30);
    }
}

if (!function_exists('getFileName')) {
    function getFileName(string $string)
    {
        if (preg_match('/([a-zA-z_0-9]+\.xls|xls|csv)/i', $string, $matches)) {
            return array_shift($matches);
        }

        return $string;
    }
}


if (!function_exists('bcsum')) {
    function bcsum(array $numbers): string
    {
        $total = "0";
        foreach ($numbers as $number) {
            $total = bcadd($total, $number, 2);
        }

        return $total;
    }
}

if (!function_exists('getTransactionTypeTranslation')) {
    function getTransactionTypeTranslation($key): string
    {
        return CashOperationalTransaction::getTransactionTypeTranslation($key) ?: $key;
    }
}

if (!function_exists('floatToWord')) {
    function floatToWord(float $number): string
    {
        [$lv, $st] = explode(".", number_format($number, 2, ".", ""));
        $lv = (int) $lv;
        if ($lv >= 2000000000) {
            return "Твърде голямо число";
        }
        $text = numberToText($lv);
        if ($st <> 0) {
            $text = preg_replace("/^един /", "", $text);
        }
        if ($st && $st != 0) {
            $sttext = numberToText($st, true);
            $text .= " и " . numberToText($st, true);
        }

        return $text;
    }
}

if (!function_exists('numberToBgWords')) {
    function numberToBgWords($number)
    {
        [$lv, $st] = explode(".", number_format($number, 2, ".", ""));
        $lv = (int) $lv;
        if ($lv >= 2000000000) {
            return "Твърде голямо число";
        }
        $text = numberToText($lv);
        $text .= $lv == 1 ? " лев" : " лева";
        if ($st <> 0) {
            $text = preg_replace("/^един /", "", $text);
        }
        if ($st && $st != 0) {
            $sttext = numberToText($st, true);
            $text .= " и " . numberToText($st, true);
            $text .= $st == 1 ? " стотинка" : " стотинки";
        }

        return $text;
    }
}

if (!function_exists('numberToLv')) {
    function numberToLv($number)
    {
        list($lv, $st) = explode(".", number_format((float) $number, 2, ".", ""));
        $lv = (int) $lv;
        if ($lv >= 2000000000) {
            return "Твърде голямо число";
        }
        $text = numberToText($lv);
        $text .= $lv == 1 ? " лев" : " лева";
        if ($st <> 0) {
            $text = preg_replace("/^един /", "", $text);
        }
        if ($st && $st != 0) {
            $sttext = numberToText($st, true);
            $text .= " и " . numberToText($st, true);
            $text .= $st == 1 ? " стотинка" : " стотинки";
        }

        return $text;
    }
}

if (!function_exists('checkIfEndsWithOne')) {
    function checkIfEndsWithOne(float $float): bool
    {
        // Convert the float to a string to handle precision issues
        $amountStr = sprintf("%.2f", $float);

        // Split the amount into dollars and cents
        $parts = explode('.', $amountStr);

        // Check if there are exactly 2 parts (dollars and cents)
        if (count($parts) !== 2) {
            return false;
        }

        // Extract the dollars and cents
        $dollars = $parts[0];
        $cents = $parts[1];

        // Check if cents are zero and dollars end with "1" or cents end with "1"
        if ((intval($cents) === 0 && substr($dollars, -1) === '1') || substr($cents, -1) === '1') {
            return true;
        }

        return false;
    }
}

if (!function_exists('formatAmountForDocument')) {
    function formatAmountForDocument($number): string
    {
        return number_format($number, '2', '.', ' ');
    }
}

if (!function_exists('numberToText')) {
    function numberToText($number, $stotinki = false, $female = false)
    {
        $_num0 = array(
            0 => "нула",
            1 => "един",
            2 => "две",
            3 => "три",
            4 => "четири",
            5 => "пет",
            6 => "шест",
            7 => "седем",
            8 => "осем",
            9 => "девет",
            10 => "десет",
            11 => "единадесет",
            12 => "дванадесет"
        );
        if ($female) {
            $_num0[1] = 'една';
        }

        $_num100 = array(1 => "сто", 2 => "двеста", 3 => "триста");

        $number = (int) $number;

        $_div10 = ($number - $number % 10) / 10;
        $_mod10 = $number % 10;
        $_div100 = ($number - $number % 100) / 100;
        $_mod100 = $number % 100;
        $_div1000 = ($number - $number % 1000) / 1000;
        $_mod1000 = $number % 1000;
        $_div1000000 = ($number - $number % 1000000) / 1000000;
        $_mod1000000 = $number % 1000000;
        $_div1000000000 = ($number - $number % 1000000000) / 1000000000;
        $_mod1000000000 = $number % 1000000000;

        if ($number == 0) {
            return $_num0[$number];
        }
        /* До двайсет */
        if ($number > 0 && $number < 20) {
            if ($stotinki && $number == 1) {
                return "една";
            }
            if ($stotinki && $number == 2) {
                return "две";
            }
            if ($number == 2) {
                return "два";
            }

            return isset($_num0[$number]) ? $_num0[$number] : $_num0[$_mod10] . "надесет";
        }
        /* До сто */
        if ($number > 19 && $number < 100) {
            $tmp = ($_div10 == 2) ? "двадесет" : $_num0[$_div10] . "десет";
            $tmp = $_mod10 ? $tmp . " и " . numberToText($_mod10, $stotinki) : $tmp;

            return $tmp;
        }
        /* До хиляда */
        if ($number > 99 && $number < 1000) {
            $tmp = isset($_num100[$_div100]) ? $_num100[$_div100] : $_num0[$_div100] . "стотин";
            if (($_mod100 % 10 == 0 || $_mod100 < 20) && $_mod100 != 0) {
                $tmp .= " и";
            }
            if ($_mod100) {
                $tmp .= " " . numberToText($_mod100);
            }

            return $tmp;
        }
        /* До милион */
        if ($number > 999 && $number < 1000000) {
            /* Damn bulgarian @#$%@#$% два хиляди is wrong :) */
            $tmp = ($_div1000 == 1) ? "хиляда" :
                (($_div1000 == 2) ? "две хиляди" : numberToText($_div1000) . " хиляди");
            $_num0[2] = "два";
            if (($_mod1000 % 10 == 0 || $_mod1000 < 20) && $_mod1000 != 0) {
                if (!(($_mod100 % 10 == 0 || $_mod100 < 20) && $_mod100 != 0)) {
                    $tmp .= " и";
                }
            }
            if (($_mod1000 % 10 == 0 || $_mod1000 < 20) && $_mod1000 != 0 && $_mod1000 < 100) {
                $tmp .= " и";
            }
            if ($_mod1000) {
                $tmp .= " " . numberToText($_mod1000);
            }

            return $tmp;
        }
        /* Над милион */
        if ($number > 999999 && $number < 1000000000) {
            $tmp = ($_div1000000 == 1) ? "един милион" : numberToText($_div1000000) . " милиона";
            if (($_mod1000000 % 10 == 0 || $_mod1000000 < 20) && $_mod1000000 != 0) {
                if (!(($_mod1000 % 10 == 0 || $_mod1000 < 20) && $_mod1000 != 0)) {
                    if (!(($_mod100 % 10 == 0 || $_mod100 < 20) && $_mod100 != 0)) {
                        $tmp .= " и";
                    }
                }
            }
            $and = ", ";
            if (($_mod1000000 % 10 == 0 || $_mod1000000 < 20) && $_mod1000000 != 0 && $_mod1000000 < 1000) {
                if (($_mod1000 % 10 == 0 || $_mod1000 < 20) && $_mod1000 != 0 && $_mod1000 < 100) {
                    $tmp .= " и";
                }
            }
            if ($_mod1000000) {
                $tmp .= " " . numberToText($_mod1000000);
            }

            return $tmp;
        }
        /* Над милиард */
        if ($number > 99999999 && $number <= 2000000000) {
            $tmp = ($_div1000000000 == 1) ? "един милиард" : "";
            $tmp = ($_div1000000000 == 2) ? "два милиарда" : $tmp;
            if ($_mod1000000000) {
                $tmp .= " " . numberToText($_mod1000000000);
            }

            return $tmp;
        }

        /* Bye ... */

        return "";
    }
}

/**
 * @param string $logHeaders
 *
 * @return string
 */
if (!function_exists('extracUserAgentFromLogHeadersAndDispatch')) {
    function extracUserAgentFromLogHeadersAndDispatch($logHeaders)
    {
        if (!$logHeaders) {
            return '';
        }

        $objectVars = get_object_vars(json_decode($logHeaders));
        $userAgent = json_decode($logHeaders) != null ? ($objectVars['user-agent'][0] ?? null) : '';

        return $userAgent;
    }
}

if (!function_exists('fetchOperationSystemFromUserAgent')) {
    /**
     * @param string $userAgent
     *
     * @return string
     */
    function fetchOperationSystemFromUserAgent($userAgent)
    {
        switch ($userAgent) {
            case (preg_match('/linux/i', $userAgent) ? true : false):
                $operatinSystem = 'Linux';
                break;
            case (preg_match('/macintosh|mac os x|mac_powerpc/i', $userAgent) ? true : false):
                $operatinSystem = 'Mac';
                break;
            case (preg_match('/windows|win32|win98|win95|win16/i', $userAgent) ? true : false):
                $operatinSystem = 'Windows';
                break;
            case (preg_match('/ubuntu/i', $userAgent) ? true : false):
                $operatinSystem = 'Ubuntu';
                break;
            case (preg_match('/iphone/i', $userAgent) ? true : false):
                $operatinSystem = 'IPhone';
                break;
            case (preg_match('/ipod/i', $userAgent) ? true : false):
                $operatinSystem = 'IPod';
                break;
            case (preg_match('/ipad/i', $userAgent) ? true : false):
                $operatinSystem = 'IPad';
                break;
            case (preg_match('/android/i', $userAgent) ? true : false):
                $operatinSystem = 'Android';
                break;
            case (preg_match('/blackberry/i', $userAgent) ? true : false):
                $operatinSystem = 'Blackberry';
                break;
            case (preg_match('/webos/i', $userAgent) ? true : false):
                $operatinSystem = 'Mobile';
                break;
            default:
                $operatinSystem = '';
        }

        return $operatinSystem;
    }
}

if (!function_exists('fetchBrowserFromUserAgent')) {
    /**
     * @param string $userAgent
     *
     * @return string
     */
    function fetchBrowserFromUserAgent($userAgent)
    {
        $arrBrowsers = ["Opera", "Edg", "Chrome", "Safari", "Firefox", "MSIE", "Trident"];

        $userBrowser = '';
        foreach ($arrBrowsers as $browser) {
            if (strpos($userAgent, $browser) !== false) {
                $userBrowser = $browser;
                break;
            }
        }

        switch ($userBrowser) {
            case 'Trident':
            case 'MSIE':
                $userBrowser = 'Internet Explorer';
                break;
            case 'Edg':
                $userBrowser = 'Microsoft Edge';
                break;
        }

        return $userBrowser;
    }
}

if (!function_exists('cutText')) {
    function cutText($textOrArray, int $length = 2)
    {
        if (is_array($textOrArray)) {
            $cuttedText = implode(', ', array_slice($textOrArray, 0, $length));
            if (count($textOrArray) > $length) {
                $cuttedText .= '...';
            }

            return $cuttedText;
        }

        $cuttedText = Str::substr($textOrArray, 0, $length);
        if (Str::length($textOrArray) > $length) {
            $cuttedText .= '...';
        }

        return $cuttedText;
    }
}

if (!function_exists('officeName')) {
    function officeName(string $name)
    {
        return preg_replace('/(офис\s)/', '', $name);
    }
}

if (!function_exists('arraysAreEqual')) {
    function arraysAreEqual(array $a, array $b): bool
    {
        return (
            is_array($a)
            && is_array($b)
            && count($a) == count($b)
            && array_diff($a, $b) === array_diff($b, $a)
        );
    }
}

if (!function_exists('getArrayFromDbArrayField')) {
    function getArrayFromDbArrayField(string|array $dbArray): array
    {
        if (is_array($dbArray)) {
            return $dbArray;
        }

        if (empty($dbArray)) {
            return [];
        }

        $dbArray = str_replace(['[', ']', "'", '"'], ['', '', '', ''], $dbArray);
        $dbArray = trim($dbArray);

        if (empty($dbArray)) {
            return [];
        }

        return explode(',', $dbArray);
    }
}

if (!function_exists('showBlockedText')) {
    /**
     * When user.blocked = 1, we must hide some data from client card
     * with this method we replace names,phone,pin,address, client id card
     *
     * client names -> A**** B***** C******
     * client phone -> 088******888
     * client pin -> 93********
     * client address -> 6*** K***** R***** C***********, N* 7****-****
     * client id card->  713*******
     *
     * @return string|string[]|null
     */
    function showBlockedText(
        string $clientValue,
        string $blockedType = ClientService::CLIENT_BLOCK_NAMES_TYPE
    ) {
        //CLIENT_BLOCK_BIRTHDAY_TYPE
        return preg_replace_callback(
            '/[-\w]+/i',
            function ($match) use ($blockedType) {
                $result = str_split($match[0]);

                switch ($blockedType) {
                    case ClientService::CLIENT_BLOCK_PHONE_NUMBER_TYPE:
                        $len = count($result) - 3;
                        $start = 3;
                        break;
                    case ClientService::CLIENT_BLOCK_ID_CARD_NUMBER_TYPE:
                        $len = count($result);
                        $start = 3;
                        break;
                    case ClientService::CLIENT_BLOCK_PIN_TYPE:
                        $len = count($result);
                        $start = 2;
                        break;
                    default:
                        $len = count($result);
                        $start = 1;
                }

                for ($i = $start; $i < $len; $i++) {
                    $result[$i] = $result[$i] == '-' ? '-' : '*';
                }

                return implode($result);
            },
            $clientValue
        );
    }
}

// "Xxxx yyyy" => "xxxx_yyyy"
if (!function_exists('textToUnderScore')) {
    function textToUnderScore(string $text): string
    {
        $text = str_replace(' ', '_', $text);

        return strtolower($text);
    }
}

// "xxxx_yyyy" => "Xxxx yyyy"
if (!function_exists('underScoreToText')) {
    function underScoreToText(string $text): string
    {
        $text = str_replace('_', ' ', $text);

        return ucfirst($text);
    }
}

if (!function_exists('getOfficesFromJson')) {
    function getOfficesFromJson(string $json): array
    {
        $officeIds = json_decode($json, true);

        return getOfficesFromArrayIds($officeIds);
    }
}

if (!function_exists('getOfficesFromArrayIds')) {
    function getOfficesFromArrayIds(array $officeIds): array
    {
        $rows = Office::whereIn('office_id', $officeIds)
            ->where('active', '=', '1')
            ->orderBy('name', 'ASC')
            ->get();

        $result = [];
        foreach ($rows as $row) {
            $result[$row->office_id] = $row->name;
        }

        return $result;
    }
}

if (!function_exists('getDatesForFilter')) {
    function getDatesForFilter(string $date): array
    {
        $dates = explode(' - ', $date);
        if (count($dates) == 2) {
            $dateFrom = new Carbon($dates[0]);
            $dateTo = new Carbon($dates[1]);
        } else {
            if (
                preg_match("/([1-2][0-9]{3})-([0-9]{2})-([0-9]{2})/i", $date)
                || preg_match("/([0-9]{2})-([0-9]{2})-([1-2][0-9]{3})/i", $date)
            ) {
                $dateFrom = new Carbon($date);
                $dateTo = new Carbon($date);
            } else {
                if (preg_match("/(0[1-9]|1[1,2])(\/|-)(0[1-9]|[12][0-9]|3[01])(\/|-)(19|20)\d{2}/", $date)) {
                    $dateFrom = Carbon::createFromFormat('m/d/Y', $date);
                    $dateTo = Carbon::createFromFormat('m/d/Y', $date);
                } else {
                    throw new RuntimeException('Date format is not valid');
                }
            }
        }

        return [
            'from' => $dateFrom->startOfDay()->toDateTimeString(),
            'to' => $dateTo->endOfDay()->toDateTimeString(),
        ];
    }
}

if (!function_exists('getBankAccountIdByPaymentMethod')) {
    function getBankAccountIdByPaymentMethod(int $paymentMethodId): ?int
    {
        if (empty($paymentMethodId)) {
            return null;
        }

        $key = 'pm_ba_' . $paymentMethodId;

        return \Cache::remember($key, 60 * 30, function () use ($paymentMethodId) {
            $row = null;

            if ($paymentMethodId == PaymentMethod::PAYMENT_METHOD_EASYPAY) {
                $row = BankAccount::where('active', 1)
                    ->where('payment_method_id', $paymentMethodId)
                    ->where('name', 'EasyPay')
                    ->first();
            }

            if (!empty($row->bank_account_id)) {
                return $row->bank_account_id;
            }

            return null;
        });
    }
}

if (!function_exists('getPaymentAccountName')) {
    function getPaymentAccountName(?int $bankAccountId = null): string
    {
        if (empty($bankAccountId)) {
            return '';
        }

        $res = getAllPaymentAccounts();

        return (!empty($res[$bankAccountId]) ? $res[$bankAccountId] : '');
    }
}

if (!function_exists('getPaymentMethodByPayemntAccountId')) {
    function getPaymentMethodByPayemntAccountId(?int $bankAccountId = null): string
    {
        if (empty($bankAccountId)) {
            return '';
        }

        $res = getAllPaymentAccountsFull();

        return (!empty($res[$bankAccountId]) ? $res[$bankAccountId]->payment_method_id : '');
    }
}

if (!function_exists('getAllPaymentAccounts')) {
    function getAllPaymentAccounts(): array
    {
        $key = 'all_payment_accounts';

        return \Cache::remember($key, 60 * 60 * 24, function () {
            $rows = BankAccount::where('active', '=', '1')->get()->all();

            $result = [];
            foreach ($rows as $row) {
                $result[$row->bank_account_id] = $row->name;
            }

            return $result;
        });
    }
}

if (!function_exists('getPaymentMethodsForPaymentAccounts')) {
    function getPaymentMethodsForPaymentAccounts(): array
    {
        $key = 'all_payment_accounts_pay_methods';

        return \Cache::remember($key, 1 * 60 * 24, function () {
            $rows = BankAccount::where('active', '=', '1')->get()->all();

            $result = [];
            foreach ($rows as $row) {
                $result[$row->bank_account_id] = $row->payment_method_id;
            }

            return $result;
        });
    }
}

if (!function_exists('getAllPaymentAccountsFull')) {
    function getAllPaymentAccountsFull(): array
    {
        $key = 'all_payment_accounts_full';

        return \Cache::remember($key, 60 * 60 * 24, function () {
            $rows = BankAccount::where('active', '=', '1')->get()->all();

            $result = [];
            foreach ($rows as $row) {
                $result[$row->bank_account_id] = $row;
            }

            return $result;
        });
    }
}

if (!function_exists('getExplainedPaymentDetails')) {
    function getExplainedPaymentDetails(mixed $jsonStr = null): array
    {
        $res = [
            'principal' => 0,
            'interest' => 0,
            'penalty' => 0,
            'late_interest' => 0,
            'late_penalty' => 0,
            'taxes' => 0,
            'late_penalty_and_taxes' => 0,
            'profit' => 0,
        ];

        if (empty($jsonStr)) {
            return $res;
        }

        // crazy shit, strange json decoded twice!
        if (!is_array($jsonStr)) {
            $delivery = json_decode($jsonStr);
            if (is_string($delivery)) {
                $delivery = json_decode($delivery);
            }
        } else {
            $delivery = arrayToObject($jsonStr);
        }

        $taxes = 0;
        if (!empty($delivery->payLoanFees->amount)) {
            $taxes = (int) $delivery->payLoanFees->amount;
            $res['taxes'] = intToFloat($taxes);
        }

        $latePenalty = 0;
        if (!empty($delivery->outstandingLatePenaltyAmount->amount)) {
            $latePenalty = (int) $delivery->outstandingLatePenaltyAmount->amount;
            $res['late_penalty'] = intToFloat($latePenalty);
        }

        $lateInterest = 0;
        if (!empty($delivery->outstandingLateInterestAmount->amount)) {
            $lateInterest = (int) $delivery->outstandingLateInterestAmount->amount;
            $res['late_interest'] = intToFloat($lateInterest);
        }

        $penalty = 0;
        if (!empty($delivery->outstandingPenaltyAmount->amount)) {
            $penalty = (int) $delivery->outstandingPenaltyAmount->amount;
            $res['penalty'] = intToFloat($penalty);
        }

        $interest = 0;
        if (!empty($delivery->outstandingInterestAmount->amount)) {
            $interest = (int) $delivery->outstandingInterestAmount->amount;
            $res['interest'] = intToFloat($interest);
        }

        $principal = 0;
        if (!empty($delivery->outstandingPrincipleAmount->amount)) {
            $principal = (int) $delivery->outstandingPrincipleAmount->amount;
            $res['principal'] = intToFloat($principal);
        }

        $res['late_penalty_and_taxes'] = intToFloat(($latePenalty + $taxes));

        $res['profit'] = intToFloat(
            (
                $taxes
                + $latePenalty
                + $lateInterest
                + $penalty
                + $interest
            )
        );

        return $res;
    }
}

if (!function_exists('array2csv')) {
    function array2csv($data, $delimiter = ',', $enclosure = '"', $escape_char = "\\")
    {
        $f = fopen('php://memory', 'r+');

        foreach ($data as $item) {
            fputcsv($f, $item, $delimiter, $enclosure, $escape_char);
        }

        rewind($f);

        return stream_get_contents($f);
    }
}

if (!function_exists('generateClientPollHash')) {
    function generateClientPollHash($clientId, $emailTemplateId, $date, $loanId = null)
    {
        $convertDate = strtotime($date);
        $date = (
        empty($convertDate) ?
            date('Ymd') :
            date('Ymd', $convertDate)
        );

        $encryptMap = [
            'client_id' => $clientId,
            'email_template_id' => $emailTemplateId,
            'date' => $date,
            'loan_id' => $loanId,
        ];

        $string = json_encode($encryptMap);

        $hash = md5($string, false);

        return $hash;
    }
}

if (!function_exists('getAdminId')) {
    /**
     * @return int
     */
    function getAdminId(): int
    {
        return Auth::user()?->administrator_id ?? Administrator::SYSTEM_ADMINISTRATOR_ID;
    }
}
if (!function_exists('getAdmin')) {
    function getAdmin(): Administrator
    {
        return Auth::user() ?? Administrator::where(
            'administrator_id',
            Administrator::SYSTEM_ADMINISTRATOR_ID
        )->first();
    }
}

if (!function_exists('getAdminOfficeIds')) {
    function getAdminOfficeIds(?int $adminId = null): array
    {
        $admin = $adminId ? Administrator::findOrFail($adminId, ['administrator_id']) : getAdmin();
        $key = 'admin_offices_' . $admin->getKey();

        return \Cache::remember($key, 60 * 60 * 24, function () use ($admin) {
            return $admin->offices()->pluck('office.office_id')->toArray();
        });
    }
}

if (!function_exists('getAdminProductIds')) {
    function getAdminProductIds(?int $adminId = null): array
    {
        $admin = $adminId ? Administrator::findOrFail($adminId, ['administrator_id']) : getAdmin();
        $key = 'admin_products_' . $admin->getKey();

        return \Cache::tags(['admin_products'])->remember($key, 60 * 60 * 24, function () use ($admin) {
            return \Modules\Common\Models\OfficeProduct::whereIn('office_id', getAdminOfficeIds())
                ->distinct('product_id')
                ->pluck('product_id')
                ->toArray();
        });
    }
}

if (!function_exists('getPaymentMethodName')) {
    function getPaymentMethodName(int $paymentMethodId): string
    {
        $pm = [
            PaymentMethod::PAYMENT_METHOD_BANK => PaymentMethod::PAYMENT_METHOD_BANK_DESC,
            PaymentMethod::PAYMENT_METHOD_EASYPAY => PaymentMethod::PAYMENT_METHOD_EASYPAY_DESC,
            PaymentMethod::PAYMENT_METHOD_CASH => PaymentMethod::PAYMENT_METHOD_CASH_DESC,
            PaymentMethod::PAYMENT_METHOD_OFFSET => PaymentMethod::PAYMENT_METHOD_OFFSET_DESC,
        ];

        return $pm[$paymentMethodId] ?? 'undefined';
    }
}

if (!function_exists('getOfficeName')) {
    function getOfficeName(int $officeId): string
    {
        $offices = \Cache::remember('all_offices_id_name', 60 * 60 * 24, function () {
            $rows = Office::all();

            $result = [];
            foreach ($rows as $row) {
                $result[$row->office_id] = $row->name;
            }

            return $result;
        });

        return $offices[$officeId] ?? 'undefined';
    }
}

if (!function_exists('getProductName')) {
    function getProductName(int $productId): string
    {
        // \Cache::forget('all_products_id_name');
        $products = \Cache::remember('all_products_id_name', 60 * 60 * 24, function () {
            $rows = Product::all();

            $result = [];
            foreach ($rows as $row) {
                $result[$row->product_id] = $row->name;
            }

            return $result;
        });

        return $products[$productId] ?? 'undefined';
    }
}

if (!function_exists('getKeyValueArray')) {
    function getKeyValueArray(null|array|ArrayAccess $objects): array
    {
        if (!$objects) {
            return [];
        }
        $result = [];
        foreach ($objects as $object) {
            $result[$object->key] = is_numeric($object->value)
                ? (str_contains($object->value, '.') ? (float) $object->value : (int) $object->value)
                : $object->value;
        }

        return $result;
    }
}

if (!function_exists('getLocationDataByIp')) {
    function getLocationDataByIp(string $ip): array
    {
        $url = env('LOCATION_INFO_URL');
        if (empty($url)) {
            return [];
        }

        $url .= $ip;
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);

        $data = json_decode($response, true);
        if (empty($data)) {
            return [];
        }

        return $data;
    }
}

if (!function_exists('compareByDeclarationDate')) {
    function compareByDeclarationDate($a, $b): int
    {
        if (empty($a["declaration_date"] || $b["declaration_date"])) {
            return 1;
        }

        $timestampA = (DateTime::createFromFormat("d.m.Y", $a["declaration_date"]))->getTimestamp();
        $timestampB = (DateTime::createFromFormat("d.m.Y", $b["declaration_date"]))->getTimestamp();

        if ($timestampA === $timestampB) {
            return 0;
        }

        return ($timestampA < $timestampB) ? -1 : 1;
    }
}

if (!function_exists('compareByMonthAndYear')) {
    function compareByMonthAndYear($a, $b): int
    {
        $timestampA = (new DateTime())->setDate($a['year'], $a['month'], 1)->getTimestamp();
        $timestampB = (new DateTime())->setDate($b['year'], $b['month'], 1)->getTimestamp();

        if ($timestampA === $timestampB) {
            return 0;
        }

        return ($timestampA < $timestampB) ? -1 : 1;
    }
}

if (!function_exists('compareByStartDate')) {
    function compareByStartDate($a, $b)
    {
        if (empty($a["start_date"] || $a["start_date"])) {
            return 1;
        }

        $dateA = strtotime(str_replace('.', '-', $a["start_date"]));
        $dateB = strtotime(str_replace('.', '-', $b["start_date"]));

        if ($dateA == $dateB) {
            return 0;
        }

        return ($dateA < $dateB) ? -1 : 1;
    }
}

if (!function_exists('cyrillicToLatin')) {
    function cyrillicToLatin($text)
    {
        $cyrillicChars = [
            'а' => 'a',
            'б' => 'b',
            'в' => 'v',
            'г' => 'g',
            'д' => 'd',
            'е' => 'e',
            'ж' => 'zh',
            'з' => 'z',
            'и' => 'i',
            'й' => 'y',
            'к' => 'k',
            'л' => 'l',
            'м' => 'm',
            'н' => 'n',
            'о' => 'o',
            'п' => 'p',
            'р' => 'r',
            'с' => 's',
            'т' => 't',
            'у' => 'u',
            'ф' => 'f',
            'х' => 'h',
            'ц' => 'ts',
            'ч' => 'ch',
            'ш' => 'sh',
            'щ' => 'sht',
            'ъ' => 'a',
            'ь' => 'y',
            'ю' => 'yu',
            'я' => 'ya',
            'А' => 'A',
            'Б' => 'B',
            'В' => 'V',
            'Г' => 'G',
            'Д' => 'D',
            'Е' => 'E',
            'Ж' => 'Zh',
            'З' => 'Z',
            'И' => 'I',
            'Й' => 'Y',
            'К' => 'K',
            'Л' => 'L',
            'М' => 'M',
            'Н' => 'N',
            'О' => 'O',
            'П' => 'P',
            'Р' => 'R',
            'С' => 'S',
            'Т' => 'T',
            'У' => 'U',
            'Ф' => 'F',
            'Х' => 'H',
            'Ц' => 'Ts',
            'Ч' => 'Ch',
            'Ш' => 'Sh',
            'Щ' => 'Sht',
            'Ъ' => 'A',
            'Ь' => 'Y',
            'Ю' => 'Yu',
            'Я' => 'Ya',
        ];

        // $text = mb_strtolower($text, 'UTF-8'); // Convert text to lowercase
        $latinText = '';

        for ($i = 0; $i < mb_strlen($text, 'UTF-8'); $i++) {
            $char = mb_substr($text, $i, 1, 'UTF-8');
            if (isset($cyrillicChars[$char])) {
                $latinText .= $cyrillicChars[$char];
            } else {
                $latinText .= $char;
            }
        }

        return $latinText;
    }
}

if (!function_exists('getLoanStartDate')) {
    function getLoanStartDate(
        string $utilisationDate,
        string $oldStartDate,
        string $installmentModifier,
        ?int $period = null // needed only for payday loans, where $installmentModifier = "+1 days"
    )
    {
        $date = new \DateTime($oldStartDate);
        if ('+1 month' == $installmentModifier) {
            $date->modify('first day of this month');
        }
        $originalDate = new \DateTime($utilisationDate);
        $originalDay = (int) $originalDate->format('d');

        switch ($installmentModifier) {
            case '+1 days':
                if (empty($period)) {
                    throw new \Exception('Period is not passed for getLoanStartDate()');
                }
                $date->add(new \DateInterval('P' . $period . 'D'));
                break;

            case '+7 days':
                $date->add(new \DateInterval('P7D'));
                break;

            case '+14 days':
                $date->add(new \DateInterval('P14D'));
                break;

            case '+1 month':
                // Add a month
                $date->add(new \DateInterval('P1M'));

                // Attempt to set the day to the same as the original start date
                $year = $date->format('Y');
                $month = $date->format('m');
                $daysInMonth = $date->format('t'); // Total days in the new month

                if ($originalDay > $daysInMonth) {
                    $date->setDate($year, $month, $daysInMonth);
                } else {
                    $date->setDate($year, $month, $originalDay);
                }
                break;

            default:
                throw new \Exception('Wrong installment modifier for getLoanStartDate()');
        }

        return $date->format('Y-m-d');
    }

    if (!function_exists('getCssClassByLoanStatusId')) {
        function getCssClassByLoanStatusId($loanStatusId)
        {
            return match ($loanStatusId) {
                LoanStatus::NEW_STATUS_ID => 'bg-warning-new',
                LoanStatus::SIGNED_STATUS_ID => 'bg-cyan-new',
                LoanStatus::PROCESSING_STATUS_ID => 'bg-info-new',
                LoanStatus::APPROVED_STATUS_ID => 'bg-primary-new',
                LoanStatus::ACTIVE_STATUS_ID => 'bg-success-new',
                LoanStatus::REPAID_STATUS_ID => 'bg-secondary-new',
                LoanStatus::CANCELLED_STATUS_ID => 'bg-danger-new',
                LoanStatus::WRITTEN_OF_STATUS_ID => '',
            };
        }
    }
}

if (!function_exists('getMetaException')) {
    function getMetaException($msg)
    {
        $metaMsg = 'Exception: ' . $msg;
        $maxLength = 250;
        if (strlen($metaMsg) > $maxLength) {
            $metaMsg = substr($metaMsg, 0, $maxLength - 3) . '...'; // Add "..." to indicate truncation
        }

        return $metaMsg;
    }
}

if (!function_exists('logException')) {
    function logException(Throwable $e, string $level = LogLevel::ERROR, ?string $channel = null)
    {
        Log::channel($channel)->log($level, $e->getMessage() . ', ' . $e->getFile() . ': ' . $e->getLine());
    }
}


if (!function_exists('arrayToObject')) {
    function arrayToObject($array)
    {
        if (is_array($array)) {
            return (object) array_map('arrayToObject', $array);
        } else {
            return $array;
        }
    }
}

if (!function_exists('mvrCodeToOurDbCode')) {
    function mvrCodeToOurDbCode(string $code): string
    {
        $code = trim($code);

        return str_pad($code, 5, '0', STR_PAD_LEFT);
    }
}

if (!function_exists('getScamLoanIds')) {
    function getScamLoanIds(): array
    {
        if (env('PROJECT') === 'stikcredit') {
            return [
                1302377,
                1303289,
                1311768,
                1312821,
                1313069,
                1313327,
                1317292,
                1318105,
                1319432,
                1323540,
                1324046,
                1325353,
                1326473,
                1326490,
                1327048,
                1327566,
                1328008,
                1328338,
                1328501,
                1328795,
                1329153,
                1329945,
                1330440,
                1330487,
                1330631,
                1330656,
                1330820,
                1331126,
                1331328,
                1331586,
                1331678,
                1331693,
                1331748,
                1331879,
                1332212,
                1332423,
                1332761,
                1332794,
                1332859,
                1332914,
                1333046,
                1333073,
                1333082,
                1333361,
                1333484,
                1333834,
                1333836,
                1333923,
                1334120,
                1334295,
                1334405,
                1334474,
                1334556,
                1334687,
                1334723,
                1334724,
                1334723,
                1334724,
                1334724,
                1334723,
                1334821,
                1334936,
                1334947,
                1335041,
                1335579,
                1335716,
                1335722,
                1335901,
                1335913,
                1335950,
                1335972,
                1336442,
                1336517,
                1336580,
                1336786,
                1336827,
                1336826,
                1336633,
                1336852,
                1336874,
                1337165,
                1337430,
                1337437,
                1337452,
                1337469,
                1337481,
                1337489,
                1337508,
                1337583,
                1337881,
                1337288,
                1338208,
                1338520,
                1340324,
                1340629,
                1341103,
                1341516,
                1341673,
                1341847,
                1342302,
                1342463,
                1342476,
                1343060,
                1343345,
                1343535,
                1343573,
                1343941,
                1344002,
                1343905,
                1343979,
                1343945,
                1344039,
                1344224,
                1344277,
                1344438,
                1344515,
                1344702,
                1344729,
                1344758,
                1344793,
                1344884,
                1345213,
                1345584,
                1345553,
                1345761,
                1346035,
                1346139,
                1346461,
                1346874,
                1347084,
                1347161,

                1341100,
                1342209,
                1342276,
                1341792,
                1342249,
                1346349,
                1346163,
                1346079,
                1347002,
                1331496,
                1344733,
                1344241,
                1344239,
                1338378,
                1338290,
                1316323,
                1341661,
                1343106,
                1339256,
                1339849,
                1339706,
                1335698,
                1335665,
                1335480,
                1334734,
                1330021,
                1324091,
                1324027,
                1322943,
                1322262,
                1321666,
                1346461,
                1346874,
                1347084,
                1347161,
                1347400,
                1348005,
                1348431,
                1348453,
                1348464,
                1348904,
                1348963,
                1349009,
                1349018,
                1349493,
                1350430,
                1351357,
                1348763,
                1347742,
                1345433,
                1378357,
                1353192,
                1353449,
                1386781,
                1351367,
                1381720,
                1332307,
                1309005,
            ];
        }

        return [
            157451,
            171106,
            173140,
            174810,
            175481,
            175588,
            175943,
            176996,
            178532,
            178994,
            179118,
            179547,
            179951,
            180335,
            180904,
            181147,
            181798,
            181809,
            182779,
            182854,
            183033,
            183050,
            183410,
            183523,
            184145,
            184295,
            184518,
            184638,
            184948,
            185071,
            185078,
            185202,
            185241,
            185346,
            185423,
            185440,
            185443,
            185467,
            185837,
            185912,
            185922,
            185963,
            186020,
            186074,
            186297,
            186439,
            186442,
            186657,
            186698,
            186723,
            186812,
            186912,
            186959,
            187129,
            187473,
            187500,
            187575,
            187765,
            187811,
            187950,
            188049,
            188164,
            188193,
            187963,
            188258,
            188270,
            188337,
            188535,
            188631,
            188700,
            188693,
            188836,
            188861,
            188992,
            189042,
            189067,
            189074,
            189532,
            189663,
            189541,
            189681,
            189662,
            189698,
            189780,
            189794,
            189809,
            189829,
            190067,
            190062,
            190078,
            190083,
            190100,
            190170,
            190168,
            190210,
            190367,
            190603,
            190563,
            190685,
            190884,
            192135,
            192272,
            192449,
            192498,
            192533,
            192544,
            192940,
            193101,
            193268,
            193377,
            193583,
            193594,
            193814,
            193856,
            194022,
            194303,
            194478,
            194621,
            194690,
            194748,
            194918,
            195031,
            195099,
            195116,
            195229,
            195358,
            195624,
            196062,
            196137,
            196427,
            196658,
            196824,
            196831,
            197070,
            197392,
            197851,
            197948,
            197986,
            198039,
            198085,
            199874,
            199902,
            199942,
            201122,

            196351,
            196111,
            196861,
            197347,
            194381,
            193255,
            193173,
            185765,
            202062,
            201303,
            200013,
            199913,
            197941,
            195739,
            192037,
            180287,
            194749,
            191262,
            195032,
            199921,
            194871,
            210207,
            227605,
            199773,
            219940,
            188794,
            204879,
        ];
    }
}
