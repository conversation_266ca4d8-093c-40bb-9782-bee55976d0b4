<?php

use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
        ],
        'new-app' => [
            'driver' => 'daily',
            'path' => storage_path('logs/new-app.log'),
            'level' => 'debug',
        ],
        'affiliate' => [
            'driver' => 'daily',
            'path' => storage_path('logs/affiliate.log'),
            'level' => 'debug',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0766,
        ],

        'daily_installment_refresh' => [
            'driver' => 'single',
            'path' => storage_path('logs/daily/installment_refresh.log'),
            'permission' => 0766,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => 'debug',
            'handler' => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],

        'mvrExec' => [
            'driver' => 'daily',
            'path' => storage_path('logs/mvr/exec.log'),
            'permission' => 0766,
            'days' => 30,
        ],

        'mvrErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/mvr/errors.log'),
            'permission' => 0766,
            'days' => 30,
        ],

        'mvrParse' => [
            'driver' => 'daily',
            'path' => storage_path('logs/mvr/parsing.log'),
            'permission' => 0766,
            'days' => 30,
        ],

        'bnbExec' => [
            'driver' => 'daily',
            'path' => storage_path('logs/bnb/exec.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'bnbErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/bnb/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'ccrErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/ccr/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'nsiExec' => [
            'driver' => 'daily',
            'path' => storage_path('logs/nsi/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'nsiError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/nsi/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'noiError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/noi/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'noi7Error' => [
            'driver' => 'daily',
            'path' => storage_path('logs/noi/noi7_errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'dtExec' => [
            'driver' => 'daily',
            'path' => storage_path('logs/dt/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'dtError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/dt/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'a4eError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/a4e/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'autegryError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/autegry/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'creditLimitError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/creditLimit/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'autoProcessError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/autoProcess/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'statsRecalculationChanges' => [
            'driver' => 'daily',
            'path' => storage_path('logs/statsRecalculation/changes.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'docsError' => [
            'driver' => 'daily',
            'path' => storage_path('logs/docsError/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'smsExec' => [
            'driver' => 'daily',
            'path' => storage_path('logs/sms/exec.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'smsErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/sms/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'emailExec' => [
            'driver' => 'daily',
            'path' => storage_path('logs/email/exec.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'emailErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/email/errors.log'),
            'command' => [
                'driver' => 'daily',
                'path' => storage_path('logs/command/info.log'),
            ],
            'permission' => 0775,
            'days' => 30,
        ],

        'collector_recall' => [
            'driver' => 'daily',
            'path' => storage_path('logs/collector_recall/info.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'sale_task' => [
            'driver' => 'daily',
            'path' => storage_path('logs/sale_task/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'approveModule' => [
            'driver' => 'daily',
            'path' => storage_path('logs/approve/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'discountsModule' => [
            'driver' => 'daily',
            'path' => storage_path('logs/discounts/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'paymentsModule' => [
            'driver' => 'daily',
            'path' => storage_path('logs/payments/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'settingsModule' => [
            'driver' => 'daily',
            'path' => storage_path('logs/settings/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'paymentDelivery' => [
            'driver' => 'daily',
            'path' => storage_path('logs/paymentDelivery/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'manualPayment' => [
            'driver' => 'daily',
            'path' => storage_path('logs/manualPayment/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'fiscalDevice' => [
            'driver' => 'daily',
            'path' => storage_path('logs/fiscalDevice/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'cashDeskModule' => [
            'driver' => 'daily',
            'path' => storage_path('logs/cashDesk/errors.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'log_cleaner' => [
            'driver' => 'daily',
            'path' => storage_path('logs/command/log_cleaner.log'),
            'permission' => 0775,
            'level' => 'debug',
            'days' => 30,
        ],
        'directService' => [
            'driver' => 'daily',
            'path' => storage_path('logs/command/direct_service.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'easypay' => [
            'driver' => 'daily',
            'path' => storage_path('logs/command/easypay.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'easypay_refund' => [
            'driver' => 'daily',
            'path' => storage_path('logs/command/easypay_refund.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'sms_restart' => [
            'driver' => 'daily',
            'path' => storage_path('logs/command/sms_restart.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'command' => [
            'driver' => 'daily',
            'path' => storage_path('logs/command/command.log'),
            'permission' => 0775,
            'days' => 30,
        ],
        'api' => [
            'driver' => 'daily',
            'path' => storage_path('logs/api/api.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'manualEmailErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/manual-errors/email.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'manualSmsErrors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/manual-errors/sms.log'),
            'permission' => 0775,
            'days' => 30,
        ],

        'lost_cities' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lost-cities/lostCities.log'),
            'permission' => 0775,
            'days' => 30,
        ],
    ],
];
