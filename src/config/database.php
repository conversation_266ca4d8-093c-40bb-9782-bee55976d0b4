<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'pgsql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql_test' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB2_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB2_DATABASE', 'forge'),
            'username' => env('DB2_USERNAME', 'forge'),
            'password' => env('DB2_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        // migration related connections
        'provision' => [
            'driver' => env('PROVISION_DB_CONNECTION', 'mysql'),
            'host' => env('PROVISION_DB_HOST', '127.0.0.1'),
            'port' => env('PROVISION_DB_PORT', '3306'),
            'database' => env('PROVISION_DB_DATABASE', 'forge'),
            'username' => env('PROVISION_DB_USERNAME', 'forge'),
            'password' => env('PROVISION_DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
        ],
        'provision2' => [ // our
            'driver' => env('PROVISION2_DB_CONNECTION', 'mysql'),
            'host' => env('PROVISION2_DB_HOST', '127.0.0.1'),
            'port' => env('PROVISION2_DB_PORT', '3306'),
            'database' => env('PROVISION2_DB_DATABASE', 'forge'),
            'username' => env('PROVISION2_DB_USERNAME', 'forge'),
            'password' => env('PROVISION2_DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    | The reason why Redis does not use strings as DB names but indexes is that the goal and ability
    | of Redis databases is not to provide an outer level of dictionary: Redis dictionaries can't
    | scale to many dictionaries, but just to a small number (it is a tradeoff), nor we want to
    | provide nested data structures per design, so this are just "a few namespaces" and as a result
    | using a numerical small index seemed like the best option.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),
        'retry_after' => 35,

        'default' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', '0'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],

        'cache' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],

        'session' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_SESSION_DB', '2'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],

        'api' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_API_DB', '3'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],

        'docs' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_QUEUES_DB', '4'),
        ],

        'sms' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_SMS_DB', '5'),
        ],

        'email' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_EMAIL_DB', '7'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],

        'reports' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_REPORTS_DB', '8'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],

        'easypay' => [
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_EASYPAY_DB', '9'),
            'read_write_timeout' => env('REDIS_WRITE_TIMEOUT', 60),
        ],
    ],
];
