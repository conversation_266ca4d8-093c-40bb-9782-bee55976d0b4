<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Modules\Admin\Models\BlogPost;
use Modules\Common\Enums\YesNoEnum;

class MigSiteBlogPosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mig:blog-posts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate blog posts';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $stikDatabase = json_decode(file_get_contents(database_path('/stik-newblog.json')), true);
        $postsJson = collect($stikDatabase)->where('name', 'wp_posts')->first()['data'];
        $postsJson = collect($postsJson);

        $stikBlog = $postsJson->whereIn('post_type', ['post', 'page']);
//            ->where(function ($row) {
//                return str_contains($row['guid'], 'https://stikcredit.bg/blog') ||
//                    str_contains($row['guid'], 'http://icredit.bg/blog');
//            });

        $blogPostsMeta = collect($stikDatabase)->where('name', 'wp_postmeta')->first()['data'];
        $blogPostsMeta = collect($blogPostsMeta)->where(function ($row) {
            return str_contains($row['meta_key'], 'metadesc');
        })->keyBy('post_id');

        $getMoneyBlock = $postsJson
            ->where('post_type', 'wp_block')
            ->where('ID', '823')
            ->first();
        $getMoneyBlock = $getMoneyBlock['post_content'] ?? '';

        $block1102 = $postsJson
            ->where('post_type', 'wp_block')
            ->where('ID', '1102')
            ->first();
        $block1102 = $block1102['post_content'] ?? '';

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        $stikBlog->map(function ($row, int $index)
        use ($blogPostsMeta, $getMoneyBlock, $block1102) {
            if ($index === 934) {
                return true;
            }

            $content = str_replace(
                'https://stikcredit.bg/blog/wp-content/uploads',
                config('app.url') . '/images/blog',
                $row['post_content']
            );

            $getMoneyBlock = str_replace(
                'https://stikcredit.bg/blog/wp-content/uploads',
                config('app.url') . '/images/blog',
                $getMoneyBlock
            );

            if (str_contains($content, '<!-- wp:block {"ref":823} /-->')) {
                $content = str_replace(
                    '<!-- wp:block {"ref":823} /-->',
                    $getMoneyBlock,
                    $content
                );
            }

            if (str_contains($content, '<!-- wp:block {"ref":1102} /-->')) {
                $content = str_replace(
                    '<!-- wp:block {"ref":1102} /-->',
                    $block1102,
                    $content
                );
            }

            $insert = [];
            $insert['wp_post_id'] = $row['ID'] ?? null;
            $insert['is_active'] = $row['post_status'] === 'publish' ? YesNoEnum::YES : YesNoEnum::NO;
            $insert['active'] = $row['post_status'] === 'publish' ? 1 : 0;
            $insert['post_type'] = $row['post_type'] ?? 'n/a';
            $insert['created_by'] = getAdminId();
            $insert['created_at'] = ($row['post_date'] == '') ? date('Y-m-d H:i:s') : $row['post_date'];
            $insert['updated_at'] = ($row['post_date'] == '') ? date('Y-m-d H:i:s') : $row['post_date'];
            $insert['published_at'] = ($row['post_date'] == '') ? date('Y-m-d H:i:s') : $row['post_date'];
            $insert['content'] = $content;
            $insert['small_description'] = mb_substr(strip_tags($row['post_content']), 0, 610);
            $insert['title'] = $row['post_title'];
            $insert['slug'] = ($row['post_name'] == '') ? intval($row['ID']) : $row['post_name'];
            $insert['meta_description'] = $blogPostsMeta[$row['ID']]['meta_value'] ?? $row['post_title'];

            $count = app(BlogPost::class)->where(['slug' => $insert['slug']])->count();
            if ($count) {
                $insert['slug'] = $insert['slug'] . '-' . ($count + 1);
            }
            unset($count);
            BlogPost::insert($insert);
        });

        return Command::SUCCESS;
    }
}
