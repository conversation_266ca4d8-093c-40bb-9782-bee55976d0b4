<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeEmailTemplate;
use Modules\Common\Models\OfficeSmsTemplate;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;

class ApproveSmallerAmountTemplates extends Command
{
    protected $signature = 'script:approve-smaller-amount-templates';
    protected $description = 'Insert auto-process templates for smaller amount';

    public function handle(): int
    {
        $this->proceedEmails();
        $this->proceedSmses();

        return Command::SUCCESS;
    }

    private function proceedEmails()
    {
        // Email templates
        $tpls = [
            EmailTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT->value,
            EmailTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF->value,
            EmailTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF_NOT_ENOUGH->value,
        ];

        $emailTplIds = [];
        foreach ($tpls as $tplKey) {
            $emailTemplate = EmailTemplate::where('key', $tplKey)->first();
            if (empty($emailTemplate->email_template_id)) {

                $dict = EmailTemplate::getTemplateByKey($tplKey);
                $htmlFilePath = module_path('Communication', 'Database/Seeders/EmailTemplatesFiles/' . $tplKey . '.html');

                $newEmailTpl = new EmailTemplate();
                $newEmailTpl->key = $tplKey;
                $newEmailTpl->description = $dict['title'];
                $newEmailTpl->title = $dict['title'];
                $newEmailTpl->variables = $dict['variables'];
                $newEmailTpl->body = $dict['title'];
                $newEmailTpl->text = file_get_contents($htmlFilePath);
                $newEmailTpl->active = 1;
                $newEmailTpl->manual = $dict['manual'];
                $newEmailTpl->type = $dict['type'];
                $newEmailTpl->save();

                $emailTplIds[] = $newEmailTpl->email_template_id;
            }
        }

        // add relation for online office if not exists
        foreach ($emailTplIds as $templateId) {
            $exists = OfficeEmailTemplate::where('email_template_id', $templateId)
                ->where('office_id', Office::OFFICE_ID_WEB)
                ->exists();

            if (!$exists) {
                OfficeEmailTemplate::insert([
                    'office_id' => Office::OFFICE_ID_WEB,
                    'email_template_id' => $templateId,
                ]);
            }
        }

        $this->info('Inserted email templates: ' . count($emailTplIds));
    }

    private function proceedSmses()
    {
        // Sms templates
        $tpls = [
            SmsTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT->value,
            SmsTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF->value,
            SmsTemplateKeyEnum::LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF_NOT_ENOUGH->value,
        ];

        $smsTplIds = [];
        foreach ($tpls as $tplKey) {
            $smsTemplate = SmsTemplate::where('key', $tplKey)->first();
            if (empty($smsTemplate->sms_template_id)) {

                $dict = SmsTemplate::getTemplateByKey($tplKey);

                $newSmsTpl = new SmsTemplate();
                $newSmsTpl->key = $tplKey;
                $newSmsTpl->name = $tplKey;
                $newSmsTpl->description = $dict['description'];
                $newSmsTpl->variables = $dict['variables'];
                $newSmsTpl->text = $dict['text'];
                $newSmsTpl->active = 1;
                $newSmsTpl->manual = $dict['manual'];
                $newSmsTpl->type = $dict['type'];
                $newSmsTpl->save();

                $smsTplIds[] = $newSmsTpl->sms_template_id;
            }
        }

        // add relation for online office if not exists
        foreach ($smsTplIds as $templateId) {
            $exists = OfficeSmsTemplate::where('sms_template_id', $templateId)
                ->where('office_id', Office::OFFICE_ID_WEB)
                ->exists();

            if (!$exists) {
                OfficeSmsTemplate::insert([
                    'office_id' => Office::OFFICE_ID_WEB,
                    'sms_template_id' => $templateId,
                ]);
            }
        }

        $this->info('Inserted sms templates: ' . count($smsTplIds));
    }
}
