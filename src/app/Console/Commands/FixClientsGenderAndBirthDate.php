<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\Common\Models\Client;
use Modules\Common\Traits\PinTrait;

class FixClientsGenderAndBirthDate extends Command
{
    use PinTrait;

    protected $signature = 'script:clients-data-fix';
    protected $description = 'Search clients with missings in: birth_date OR gender and fix it';

    public function handle(): int
    {
        $this->fixBirthDates();
        $this->fixGenders();

        return Command::SUCCESS;
    }

    private function fixBirthDates()
    {
        $this->info('----- fixBirthDates() ------');

        $builder = Client::whereNotNull('pin')
            ->whereNull('birth_date');

        $fixed = 0;
        $failed = 0;

        $builder->chunkById(
            200,
            function ($clients) use(&$fixed, &$failed) {
                foreach ($clients as $client) {

                    $birthDate = null;

                    if (!$birthDate) {
                        $parsedData = $this->getAgeAndSex($client->pin);
                        $birthDate = !empty($parsedData['birth_date']) ? Carbon::parse($parsedData['birth_date']) : null;
                    }

                    if (!$birthDate) {
                        $birthDate = $client->getLastMvrReport()?->getData()?->birthDate;
                    }

                    if (!$birthDate) {
                        $this->info('- ' . $client->client_id . ' - no birth_date found');
                        $failed++;
                        continue;
                    }

                    $client->birth_date = $birthDate;
                    $client->saveQuietly();

                    $fixed++;
                }
            },
            'client.client_id',
            'client_id'
        );

        $this->info('---------------');
        $this->info('Fixed: ' . $fixed . ', failed: ' . $failed);
    }

    private function fixGenders()
    {
        $this->info('----- fixGenders() ------');

        $builder = Client::whereNotNull('pin')
            ->whereNull('gender');

        $fixed = 0;
        $failed = 0;

        $builder->chunkById(
            200,
            function ($clients) use(&$fixed, &$failed) {
                foreach ($clients as $client) {

                    $gender = null;

                    if (!$gender) {
                        $parsedData = $this->getAgeAndSex($client->pin);
                        if (!empty($parsedData['sex'])) {
                            $gender = ($parsedData['sex'] == 2 ? 'male' : 'female');
                        }
                    }

                    if (!$gender) {
                        $this->info('- ' . $client->client_id . ' - no gender found');
                        $failed++;
                        continue;
                    }

                    $client->gender = $gender;
                    $client->saveQuietly();

                    $fixed++;
                }
            },
            'client.client_id',
            'client_id'
        );

        $this->info('---------------');
        $this->info('Fixed: ' . $fixed . ', failed: ' . $failed);
    }
}
