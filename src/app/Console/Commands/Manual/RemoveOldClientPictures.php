<?php

namespace App\Console\Commands\Manual;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientPicture;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;
use Symfony\Component\Console\Command\Command as CommandAlias;

/**
 * !!! IMPORTANT command is run once time on prod
 * @todo after deploy remove command
 * @todo check with Roman on live database where last=0 and is just one mvr_picture in this case we need other script
 * which will be update last=1 because we'll lost client pictures.
 */
class RemoveOldClientPictures extends CommonCommand
{
    protected $name = 'script:remove-old-client-pictures';
    protected $signature = 'script:remove-old-client-pictures';
    protected $description = 'Remove old client pictures';

    public function handle(): int
    {
        $this->checkEmailsVars();
        $this->checkSmsVars();

        return 1;
    }

    private function checkSmsVars()
    {
        $this->info('--- checkSmsVars ---');

        $templates = SmsTemplate::all();
        foreach ($templates as $template) {
            $content = $template->text;

            preg_match_all('/{([a-zA-Z0-9_]+)}/', $content, $matches);

            // $matches[1] contains an array of the captured variable names without the curly braces.
            $contentVariables = collect($matches[1])->unique()->values();

            // Decode the JSON string in the 'variables' column to a PHP array.
            $definedVariables = collect($template->variables);

            // Find any variables that are in the content but not in the defined variables.
            $missingVariables = $contentVariables->diff($definedVariables);

            // Check if there are any missing variables.
            if ($missingVariables->isNotEmpty()) {
                $this->error("Template: '{$template->key}' has missing variables!");
                foreach ($missingVariables as $variable) {
                    $this->line("  - Missing variable: {$variable}");
                }
                $this->line(''); // Add a blank line for readability.
            }
        }

        $this->info('Variable check complete.');
    }

    private function checkEmailsVars()
    {
        $this->info('--- checkEmailsVars ---');

        $templates = EmailTemplate::all();
        foreach ($templates as $template) {
            $content = $template->text;

            preg_match_all('/{([a-zA-Z0-9_]+)}/', $content, $matches);

            // $matches[1] contains an array of the captured variable names without the curly braces.
            $contentVariables = collect($matches[1])->unique()->values();

            // Decode the JSON string in the 'variables' column to a PHP array.
            $definedVariables = collect($template->variables);

            // Find any variables that are in the content but not in the defined variables.
            $missingVariables = $contentVariables->diff($definedVariables);

            // Check if there are any missing variables.
            if ($missingVariables->isNotEmpty()) {
                $this->error("Template: '{$template->key}' has missing variables!");
                foreach ($missingVariables as $variable) {
                    $this->line("  - Missing variable: {$variable}");
                }
                $this->line(''); // Add a blank line for readability.
            }
        }

        $this->info('Variable check complete.');
    }

    public function handle2(): int
    {
        $this->startLog($this->description);

        /**
         * 1. update all clients where client id is null
         */
        $updateAllClientIdsWhereClientIdIsNull = $this->updateAllClientIdsWhereClientIdIsNull();

        /**
         * 2. delete all rows where client id is null
         */
        $deleteAllRowsWhereClientIdIsNull = $this->deleteAllRowsWhereClientIdIsNull();

        /**
         * 3. set last flag for every client, type, source orient by created_at: set proper last=1 and other last=0
         */
        $setLastFlagToClientOrientByTypeAndSource = $this->setLastFlagToClientOrientByTypeAndSource();

        /**
         * 4. delete where last != 1
         */
        $deleteAllWhereLastNot1 = $this->deleteAllWhereLastNot1();

        /**
         * 5. Delete duplicated images
         */
        $deleteMvrSignPictures = $this->deleteMvrSignPictures();

        $this->finishLog([
            'updateAllClientIdsWhereClientIdIsNull: ' . $updateAllClientIdsWhereClientIdIsNull,
            'deleteAllRowsWhereClientIdIsNull: ' . $deleteAllRowsWhereClientIdIsNull,
            'setLastFlagToClientOrientByTypeAndSource: ' . $setLastFlagToClientOrientByTypeAndSource,
            'deleteAllWhereLastNot1: ' . $deleteAllWhereLastNot1,
            'deleteMvrSignPictures: ' . $deleteMvrSignPictures,
        ]);

        return CommandAlias::SUCCESS;
    }

    public function deleteMvrSignPictures(): int
    {
    $sql = <<<SQL
DELETE FROM client_picture
WHERE client_picture_id IN (
    SELECT cp.client_picture_id
    FROM client_picture cp
    JOIN client_picture pic
      ON cp.client_id = pic.client_id
     AND cp.base64 = pic.base64
     AND pic.source = 'mvr_picture'
     AND pic.last = 1
    WHERE cp.source = 'mvr_sign'
      AND cp.last = 1
);
SQL;

        return DB::delete($sql);
    }

    public function setLastFlagToClientOrientByTypeAndSource(): int
    {
        $updatedCount = DB::update(
            "
                WITH clients_without_last AS (
                    SELECT client_id
                    FROM client_picture
                    WHERE type = 'mvr'
                      AND source = 'mvr_picture'
                    GROUP BY client_id
                    HAVING SUM(CASE WHEN last = 1 THEN 1 ELSE 0 END) = 0
                ),
                ranked_pictures AS (
                    SELECT client_picture_id
                    FROM (
                        SELECT client_picture_id,
                               ROW_NUMBER() OVER (PARTITION BY client_id ORDER BY created_at DESC) AS rn
                        FROM client_picture
                        WHERE type = 'mvr'
                          AND source = 'mvr_picture'
                          AND client_id IN (SELECT client_id FROM clients_without_last)
                    ) t
                    WHERE rn = 1
                )
                UPDATE client_picture
                SET last = 1
                WHERE client_picture_id IN (SELECT client_picture_id FROM ranked_pictures);
            "
        );

        $updatedCount += DB::update(
            "
                WITH clients_without_last AS (
                    SELECT client_id
                    FROM client_picture
                    WHERE type = 'mvr'
                      AND source = 'mvr_sign'
                    GROUP BY client_id
                    HAVING SUM(CASE WHEN last = 1 THEN 1 ELSE 0 END) = 0
                ),
                ranked_pictures AS (
                    SELECT client_picture_id
                    FROM (
                        SELECT client_picture_id,
                               ROW_NUMBER() OVER (PARTITION BY client_id ORDER BY created_at DESC) AS rn
                        FROM client_picture
                        WHERE type = 'mvr'
                          AND source = 'mvr_sign'
                          AND client_id IN (SELECT client_id FROM clients_without_last)
                    ) t
                    WHERE rn = 1
                )
                UPDATE client_picture
                SET last = 1
                WHERE client_picture_id IN (SELECT client_picture_id FROM ranked_pictures);
            "
        );

        return $updatedCount;
    }

    public function deleteAllWhereLastNot1()
    {
        $cutoff = now()->subDay()->startOfDay();

        return ClientPicture::whereIn('source', [ClientPicture::SOURCE_MVR_PICT, ClientPicture::SOURCE_MVR_SIGN])
            ->where('created_at', '<=', $cutoff)
            ->where('last', 0)
            ->forceDelete();
    }

    public function deleteAllRowsWhereClientIdIsNull(): int
    {
        $cutoff = now()->subDay()->startOfDay();

        return ClientPicture::whereNull('client_id')
            ->where('created_at', '<=', $cutoff)
            ->forceDelete();
    }

    public function updateAllClientIdsWhereClientIdIsNull(): int
    {
        $updatedCount = 0;

        ClientPicture::whereNull('client_id')
            ->chunkById(100, function ($rows) use (&$updatedCount) {
                $pins = $rows->pluck('pin')->unique()->filter()->values();

                if ($pins->isEmpty()) {
                    return;
                }

                $clients = Client::select(['client_id', 'pin'])
                    ->whereIn('pin', $pins)
                    ->get()
                    ->keyBy('pin');

                foreach ($rows as $row) {
                    $client = $clients->get($row->pin);

                    if ($client && $client->client_id) {
                        $row->client_id = $client->client_id;
                        $row->saveQuietly();
                        $updatedCount++;
                    }
                }
            });

        return $updatedCount;
    }
}
