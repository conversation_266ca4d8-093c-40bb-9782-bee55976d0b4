<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\Accounting\Models\AccountingPayment;
use Modules\Common\Models\Payment;
use Modules\Accounting\Services\AccountingService;

class ManualCreationAccountingRowCommand extends Command
{
    protected $signature = 'script:accounting-row {payment_id}';
    protected $description = 'Manual creation of accounting row for certain payment';

    public function handle()
    {
        $paymentId = (int) $this->argument('payment_id');
        if (empty($paymentId)) {
            $this->info('No $paymentId provided');
            return ;
        }

        $payments = [$paymentId];

        foreach ($payments as $paymentId) {

            $payment = Payment::where('payment_id', $paymentId)->first();
            if (empty($payment->payment_id)) {
                $this->info('No payment found by id #' . $paymentId);
                break;
            }

            if ($payment->shouldSkipAccountingPaymentCreation()) {
                $this->info('AccountingPayment is forbidden for payment office.');
                continue;
            }

            $ap = AccountingPayment::where('payment_id', $paymentId)->first();
            if (!empty($ap->payment_id)) {
                $this->info('Accounting row is already exists for payment#' . $paymentId);
                break;
            }

            $date = Carbon::parse($payment->created_at)->format('Y-m-d');
            $result = AccountingService::addAccountingRowForCreatedPayment(
                $payment,
                $date
            );

            $this->info('- #' . $paymentId);
        }
    }
}
