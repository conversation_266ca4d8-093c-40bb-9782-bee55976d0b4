<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeEmailTemplate;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Interfaces\CommunicationInterface;
use Modules\Communication\Models\EmailTemplate;

class UpdateCollectEmailTemplates extends Command
{
    protected $signature = 'script:update-collector-email-templates';
    protected $description = 'Update collect email templates';

    public function handle(): int
    {
        $emailTplIds = [];

        $htmlFilePath = module_path(
            'Communication',
            'Database/Seeders/EmailTemplatesFiles/' . EmailTemplateKeyEnum::COL_TASK_EXIT_NO_SUCH_PHONE->value . '.html'
        );

        $emailTemplate = EmailTemplate::where(
            'key',
            EmailTemplateKeyEnum::COL_TASK_EXIT_NO_SUCH_PHONE->value
        )->firstOrFail();
        $emailTplIds[] = $emailTemplate->email_template_id;

        $emailTemplate->update([
            'text' => file_get_contents($htmlFilePath),
            'variables' => [
                "application_date",
                "client_primary_phone",
                "client_first_name",
                "client_surname",
                "application_id",
                "loan_last_unpaid_installment_date",
                "today_date",
                "due_amount",
                "company_easypay_pin",
                "company_office_iban",
                "company_phone",
                "company_email"
            ]
        ]);

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        $htmlFilePath = module_path(
            'Communication',
            'Database/Seeders/EmailTemplatesFiles/' . EmailTemplateKeyEnum::COL_TASK_EXIT_WRONG_PHONE_OWNER->value . '.html'
        );

        $emailTemplate = EmailTemplate::where(
            'key',
            EmailTemplateKeyEnum::COL_TASK_EXIT_WRONG_PHONE_OWNER->value
        )->firstOrFail();
        $emailTplIds[] = $emailTemplate->email_template_id;

        $emailTemplate->update([
            'text' => file_get_contents($htmlFilePath),
            'variables' => [
                "application_date",
                "client_primary_phone",
                "client_first_name",
                "client_surname",
                "application_id",
                "loan_last_unpaid_installment_date",
                "today_date",
                "due_amount",
                "company_easypay_pin",
                "company_office_iban",
                "company_phone",
                "company_email"
            ]
        ]);

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        $htmlFilePath = module_path(
            'Communication',
            'Database/Seeders/EmailTemplatesFiles/' . EmailTemplateKeyEnum::SALES_TASK_EXIT_NO_SUCH_PHONE->value . '.html'
        );

        $emailTemplate = EmailTemplate::where('key', EmailTemplateKeyEnum::SALES_TASK_EXIT_NO_SUCH_PHONE->value)->first(
        );
        if (!$emailTemplate) {
            $emailTemplateSettings = EmailTemplate::getTemplateByKey(
                EmailTemplateKeyEnum::SALES_TASK_EXIT_NO_SUCH_PHONE->value
            );
            $row = EmailTemplate::create([
                'key' => EmailTemplateKeyEnum::SALES_TASK_EXIT_NO_SUCH_PHONE->value,
                'description' => $emailTemplateSettings['title'],
                'variables' => $emailTemplateSettings['variables'],
                'title' => $emailTemplateSettings['title'],
                'body' => $emailTemplateSettings['title'],
                'text' => file_get_contents($htmlFilePath),
                'gender' => CommunicationInterface::TEMPLATE_GENDER,
                'type' => $emailTemplateSettings['type'] ?? CommunicationInterface::TEMPLATE_TYPE_SYSTEM,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'manual' => $emailTemplateSettings['manual'] ?? CommunicationInterface::TEMPLATE_NOT_MANUAL
            ]);
            $emailTplIds[] = $row->email_template_id;
        }

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        $htmlFilePath = module_path(
            'Communication',
            'Database/Seeders/EmailTemplatesFiles/' . EmailTemplateKeyEnum::SALES_TASK_EXIT_WRONG_PHONE_OWNER->value . '.html'
        );

        $emailTemplate = EmailTemplate::where(
            'key',
            EmailTemplateKeyEnum::SALES_TASK_EXIT_WRONG_PHONE_OWNER->value
        )->first();
        if (!$emailTemplate) {
            $emailTemplateSettings = EmailTemplate::getTemplateByKey(
                EmailTemplateKeyEnum::SALES_TASK_EXIT_WRONG_PHONE_OWNER->value
            );
            $row = EmailTemplate::create([
                'key' => EmailTemplateKeyEnum::SALES_TASK_EXIT_WRONG_PHONE_OWNER->value,
                'description' => $emailTemplateSettings['title'],
                'variables' => $emailTemplateSettings['variables'],
                'title' => $emailTemplateSettings['title'],
                'body' => $emailTemplateSettings['title'],
                'text' => file_get_contents($htmlFilePath),
                'gender' => CommunicationInterface::TEMPLATE_GENDER,
                'type' => $emailTemplateSettings['type'] ?? CommunicationInterface::TEMPLATE_TYPE_SYSTEM,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'manual' => $emailTemplateSettings['manual'] ?? CommunicationInterface::TEMPLATE_NOT_MANUAL
            ]);
            $emailTplIds[] = $row->email_template_id;
        }


        // add relation for online office if not exists
        foreach ($emailTplIds as $templateId) {
            $exists = OfficeEmailTemplate::where('email_template_id', $templateId)
                ->where('office_id', Office::OFFICE_ID_WEB)
                ->exists();

            if (!$exists) {
                OfficeEmailTemplate::insert([
                    'office_id' => Office::OFFICE_ID_WEB,
                    'email_template_id' => $templateId,
                ]);
            }
        }


        return Command::SUCCESS;
    }
}
