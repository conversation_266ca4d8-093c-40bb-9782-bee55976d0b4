<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Entities\CalendarSnapshot;
use Modules\Common\Models\Loan;

class RestoreLoanByCalendarSnapshotCommand extends Command
{
    protected $signature = 'script:restore-calendar-snapshot {id}';
    protected $description = 'Restore installments, taxes, loan stats, etc by calendar snapshot';

    public function handle()
    {
        $id = (int) $this->argument('id');

        $snapshot = CalendarSnapshot::where('id', $id)->first();
        if (empty($snapshot->id)) {
            $this->info('No such snapshot #' . $id);
            return;
        }

        $loanId = (!empty($snapshot->loan['loan_id']) ? $snapshot->loan['loan_id'] : null);
        if (empty($loanId)) {
            $this->info('No loan found, snapshot #' . $id);
            return;
        }

        $loan = Loan::where('loan_id', $loanId)->first();
        if (empty($loan->loan_id)) {
            $this->info('No loan found by id #' . $loanId);
            return;
        }

        $loanSnapshot = $snapshot->loan;
        $taxesSnapshot = $snapshot->taxes;
        $loanStatsSnapshot = $snapshot->loan_stats;
        $clientStatsSnapshot = $snapshot->client_stats;
        $installmentsSnapshot = $snapshot->installments;
        if (empty($loanSnapshot) || empty($loanStatsSnapshot) || empty($clientStatsSnapshot) || empty($installmentsSnapshot)) {
            $this->info('Bad snapshot data');
            return;
        }

        $taxes = $loan->getAllTaxes();
        $client = $loan->client;
        $loanStats = $loan->loanActualStats;
        $clientStats = $client->clientActualStats;
        $installments = $loan->getAllInstallments();

        DB::beginTransaction();
        try {

            // set snapshot usage
            $snapshot->reverted_at = now();
            $snapshot->reverted_by = getAdminId();
            $snapshot->save();

            $loanStats->fill($loanStatsSnapshot);
            $loanStats->saveQuietly();

            $clientStats->fill($clientStatsSnapshot);
            $clientStats->saveQuietly();

            foreach ($installments as $inst) {
                $instData = $installmentsSnapshot[$inst->installment_id];
                $instData['due_date'] = Carbon::parse($instData['due_date'])->format('Y-m-d'); // костъль, because of: 'due_date' => 'date:d-m-Y',

                $inst->fill($instData);
                $inst->saveQuietly();
            }

            // if ($taxes->count() > 0) {
            //     foreach ($taxes as $tax) {
            //         if (isset($taxesSnapshot[$tax->tax_id])) {
            //             $taxData = $taxesSnapshot[$tax->tax_id];
            //             $tax->fill($taxData);
            //             $tax->saveQuietly();
            //         } else {
            //             DB::statement('DELETE FROM tax_history WHERE tax_id = ' . $tax->tax_id);
            //             DB::statement('DELETE FROM tax WHERE tax_id = ' . $tax->tax_id);
            //         }
            //     }
            // }

            DB::commit();

            $this->info('done');

            return Command::SUCCESS;

        } catch (\Throwable $e) {
            DB::rollBack();

            $this->info('failed', $e);
        }

        return Command::SUCCESS;
    }
}
