<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Modules\Common\Libraries\InterfaceForLaravel\InterfaceFile;

class ProjectInitCommand extends Command
{
    protected $signature = 'project:init';

    protected $description = 'Running project setup commands';

    public function handle()
    {
        app(InterfaceFile::class)->removeFile();
        if (isProdOrStage()) {
            $this->info('------- !!! IMPORTANT !!! -------');
            $this->error('-- You are on stage/production --');
            $this->error('--    Run commands manually    --');
            $this->info('---------------------------------');

            return;
        }

        $actions = $this->testingCommands();
        if (isTesting()) {
            $actions = $this->testingCommands();
        }

        $this->info('Starting clean project set-up');
        $operationCount = count($actions, COUNT_RECURSIVE) - count(array_keys($actions));
        $bar = $this->output->createProgressBar($operationCount);
        $bar->start();

        foreach ($actions as $type => $commands) {
            foreach ($commands as $command => $description) {
                switch ($type) {
                    case 'artisan':
                        Artisan::call($command);
                        break;
                    case 'cmd-before':
                    case 'cmd-after':
                        exec($command);
                        break;
                }

                // for fast commands we need to sleep a bit, to show progress bar
                if (!preg_match('/(migrate)/', $command)) {
                    sleep(1);
                }

                $bar->advance();
                $this->info(' - ' . $description);
            }
        }

        Artisan::call('optimize:clear');

        $bar->finish();
        $this->info('-----------------------');
        $this->info('Project is ready for use');
        $this->info('');
    }

    private function devCommands()
    {
        return [
            'cmd-before' => [
                'rm -Rf storage/avatars' => 'Removed avatars symlink',
                '[ -f storage/framework/interfaces/interfaces.php ] && rm storage/framework/interfaces/interfaces.php'=> 'Removed generated interface map'
            ],
            'artisan' => [
                'clear:all-simple' => 'All cache is cleared',
                'migrate:fresh --seed  --seeder=DatabaseSeeder' => 'DB re-created & seeds imported',
                'session:flush' => 'Redis/Session is cleared',
                'storage:link' => 'Storage link is set',
                'script:create-a4e-performance-dirs'=>'a4e dirs created',
                'horizon:install' => 'Install a tool for queues monitoring',
            ],
            'cmd-after' => [
                'composer dump-autoload 2>/dev/null' => 'Update composer autoload file',
            ],
        ];
    }

    private function testingCommands()
    {
        return [
            'cmd-before' => [
                'rm -Rf storage/avatars' => 'Removed avatars symlink',
                'rm .phpunit.result.cache'=> 'Removed phpunit cache',
                'rm storage/framework/interfaces/interfaces.php'=> 'Removed generated interface map'
            ],
            'artisan' => [
                'clear:all-simple' => 'All cache is cleared',
                'migrate:fresh --seed  --seeder=DatabaseSeeder' => 'DB re-created & seeds imported',
                'session:flush' => 'Redis/Session is cleared',
                'storage:link' => 'Storage link is set',
                'horizon:install' => 'Install a tool for queues monitoring',
            ]
            ,
            'cmd-after' => [
                'composer dump-autoload 2>/dev/null' => 'Update composer autoload file',
            ],
        ];
    }

    private function prodCommands()
    {
        return [
            'cmd-before' => [
                'rm -Rf storage/avatars' => 'Removed avatars symlink',
            ],
            'artisan' => [
                'clear:all-simple' => 'All cache is cleared',
                'migrate:fresh --seed  --seeder=DatabaseSeeder' => 'DB re-created & seeds imported',
                'storage:link' => 'Storage link is set',
                'script:create-a4e-performance-dirs'=>'a4e dirs created',
                'horizon:install' => 'Install a tool for queues monitoring',
            ]
            ,
            'cmd-after' => [
                'composer dump-autoload 2>/dev/null' => 'Update composer autoload file',
            ],
        ];
    }
}
