<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Modules\Common\Models\Loan;
use RuntimeException;

class LoanEarlyRepayment extends Component
{
    public Loan $loan;

    public function __construct(Loan $loan)
    {
        if (!$loan->exists) {
            throw new RuntimeException('NO!');
        }

        $this->loan = $loan;
    }

    public function render()
    {
        return view('components.early-repayment');
    }
}
