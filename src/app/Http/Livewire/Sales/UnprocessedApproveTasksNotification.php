<?php

namespace App\Http\Livewire\Sales;

use Illuminate\View\View;
use Livewire\Component;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

class UnprocessedApproveTasksNotification extends Component
{
    public int $tasksForApprovalCount = 0;

    public function mount(): void
    {
        $this->tasksForApprovalCount = $this->countWaitingForApproval();
    }

    public function refreshData(): void
    {
        $this->tasksForApprovalCount = $this->countWaitingForApproval();
    }

    public function render(): View
    {
        return view('livewire.sales.unprocessed-approve-tasks-notification');
    }

    private function countWaitingForApproval(): int
    {
        return Loan::select(['loan_id', 'last_status_update_date'])
            ->where('loan_status_id', LoanStatus::SIGNED_STATUS_ID)
            ->whereIn('office_id', getAdminOfficeIds())
            ->where('last_status_update_date', '<', now()->subSeconds(40))
            ->where(function ($q) {
                $q->whereNull('skip_till')
                    ->orWhere('skip_till', '<=', now());
            })
            ->count();
    }
}
