<?php

namespace App\Providers;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Modules\Common\Traits\StringFormatterTrait;
use Str;

class AppServiceProvider extends ServiceProvider
{
    use StringFormatterTrait;

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment(['local', 'stage'])) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        $this->app->register(UtilsServiceProvider::class);
        $this->app->register(AppObserverServiceProvider::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Paginator::useBootstrap();
        View::share('breadcrumb', $this->getBreadcrumb());

        Factory::guessFactoryNamesUsing(function (string $modelName) {
            $moduleNamespace = Str::before($modelName, '\\Models');

            return sprintf("%s\\Database\\factories\\%sFactory", $moduleNamespace, class_basename($modelName));
        });

        Str::macro('arrayToDot', function (string $str) {
            return preg_replace('!\[(.+?)\]!', '.$1', $str);
        });
    }

    private function getBreadcrumb(): string
    {
        $segments = request()->segments();
        if (is_numeric(end($segments)) || str_contains(end($segments), '_')) {
            array_pop($segments);
        }

        $breadcrumb = '';
        $segmentCount = count($segments);
        for ($i = 1; $i < $segmentCount; $i++) {
            if (is_numeric($segments[$i])) {
                continue;
            }
            $str = __('menu.' . ucfirst($this->fmtSnakeCaseToCamelCase($segments[$i])));
            if ($i != ($segmentCount - 1)) {
                $str = '<a href="'
                    . url(implode('/', array_slice($segments, 0, $i + 1)))
                    . '">' . $str . '</a>';
            }
            $breadcrumb .= '<li class="breadcrumb-item">' . $str . '</li>';
        }

        return $breadcrumb;
    }
}
