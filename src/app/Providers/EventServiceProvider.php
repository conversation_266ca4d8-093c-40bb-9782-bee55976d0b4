<?php

namespace App\Providers;

use App\Listeners\LogFailedLoginAttempt;
use App\Listeners\LoginListener;
use Illuminate\Auth\Events\Failed;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Accounting\Listeners\ClosedJuridicalCase;
use Modules\Accounting\Listeners\OpenJuridicalCase;
use Modules\Accounting\Listeners\RegisterAccountingPayment;
use Modules\Api\Domain\Events\EasyPayPaymentRequestWasProcessed;
use Modules\Approve\Application\Listeners\ApproveDockPackSender;
use Modules\Approve\Application\Listeners\CancelLoanListener;
use Modules\Approve\Domain\Events\LoanWasActivated;
use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Approve\Domain\Events\LoanWasCanceled;
use Modules\Approve\Domain\Events\MaximumApproveAttemptsLimitReached;
use Modules\Approve\Listeners\Loan\ClearAllLoanTasksAndTmpRequests;
use Modules\Approve\Listeners\Loan\ClearOpenedSaleTasks;
use Modules\Approve\Listeners\Loan\RefreshAffiliateReports;
use Modules\Approve\Listeners\Loan\ResetVeriffAction;
use Modules\CashDesk\Application\Listeners\CreateIncomingCashTransactionListener;
use Modules\CashDesk\Events\CashOperationalTransactionSaved;
use Modules\Collect\Application\Listeners\RemoveLoanFromBucketListener;
use Modules\Collect\Application\Listeners\SendOuterCollectorDailyReport;
use Modules\Collect\Domain\Events\CollectTaskFinished;
use Modules\Collect\Domain\Events\DueDatesWereReset;
use Modules\Collect\Domain\Events\LoanBecameJuridical;
use Modules\Collect\Domain\Events\LoanWasExtended;
use Modules\Collect\Domain\Events\MarkedAsClosedJuridicalCase;
use Modules\Collect\Events\OuterCollectorReport\OuterCollectorReportWasCreated;
use Modules\Common\Events\Affiliate\AffiliateMonthlyReportsIsReady;
use Modules\Common\Events\CustomNoiReports\CustomNoiReportReadyEvent;
use Modules\Common\Events\Guarantor\GuarantorWasCreatedEvent;
use Modules\Common\Events\Payment\AfterPaymentDistribute;
use Modules\Common\Events\Tax\TaxWasCreated;
use Modules\Common\Events\UnclaimedMoney\UnclaimedMoneyWasCreated;
use Modules\Common\Listeners\Client\FireBroadcastClientEventsListener;
use Modules\Common\Listeners\CustomNoiReports\CreateExportForUpdatedLoans;
use Modules\Common\Listeners\Guarantor\CreateClientIfNotExists;
use Modules\Common\Listeners\Loan\FireBroadcastLoanEventsListener;
use Modules\Common\Listeners\Payment\CreatePaymentWhenHaveOutstandingAfterExtendLoan;
use Modules\Common\Listeners\Tax\RegisterJuridicalExpense;
use Modules\Common\Listeners\UnclaimedMoney\UnclaimedMoneyListener;
use Modules\Communication\Application\Listeners\SendEmailAboutWrongPhoneListener;
use Modules\Communication\Application\Listeners\SendNewLoanDocsEmailListener;
use Modules\Communication\Events\CommunicationWasCreatedEvent;
use Modules\Communication\Jobs\SendDisapprovedSmsAndEmail;
use Modules\Communication\Listeners\RegisterCommunicationPivot;
use Modules\Communication\Listeners\RepaidLoanCommunication;
use Modules\Communication\Listeners\SendAffiliateMonthlyReport;
use Modules\Docs\Application\Listeners\CreateDockPackListener;
use Modules\Docs\Application\Listeners\GenerateCashDocsListener;
use Modules\Head\Application\Listeners\BucketActions\RemoveFromBucketTaskListener;
use Modules\Head\Application\Listeners\Statistics\Client\CreateInitialClientStatsListener;
use Modules\Head\Application\Listeners\Statistics\Client\UpdateClientStatsOnActivateListener;
use Modules\Head\Application\Listeners\Statistics\Client\UpdateClientStatsOnApproveListener;
use Modules\Head\Application\Listeners\Statistics\Client\UpdateClientStatsOnCancelListener;
use Modules\Head\Application\Listeners\Statistics\Client\UpdateClientStatsOnDayPassListener;
use Modules\Head\Application\Listeners\Statistics\Client\UpdateClientStatsOnNewLoanListener;
use Modules\Head\Application\Listeners\Statistics\Client\UpdateClientStatsOnPaymentListener;
use Modules\Head\Application\Listeners\Statistics\Loan\AutoProcessListener;
use Modules\Head\Application\Listeners\Statistics\Loan\CreateInitialLoanStatsListener;
use Modules\Head\Application\Listeners\Statistics\Loan\RefreshSaleAttemptsListener;
use Modules\Head\Application\Listeners\Statistics\Loan\SendExtendLoanSmsListener;
use Modules\Head\Application\Listeners\Statistics\Loan\SetRelationToReferral;
use Modules\Head\Application\Listeners\Statistics\Loan\SetRelationToTasksListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnActivateListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnApproveListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnCancelListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnDeletePaymentListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnExtensionListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnPaymentListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnRepaymentListener;
use Modules\Head\Application\Listeners\Statistics\Loan\UpdateLoanStatsOnSignListener;
use Modules\Payments\Application\Listeners\ActualizeRefinanceAmountsListener;
use Modules\Payments\Application\Listeners\ChangeEasyPayStatusAfterSendingListener;
use Modules\Payments\Application\Listeners\PaymentDeliveryListener;
use Modules\Payments\Application\Listeners\PaymentSnapshotListener;
use Modules\Payments\Application\Listeners\Source\CreateIncomingPaymentFromEasyPayApiListener;
use Modules\Payments\Application\Listeners\Source\CreatePaymentsFromLoanApprovalListener;
use Modules\Payments\Domain\Events\EasyPayPaymentWasPreparedForSending;
use Modules\Payments\Domain\Events\LoanPayInWasDelivered;
use Modules\Payments\Domain\Events\LoanPaymentAfterDeleted;
use Modules\Payments\Domain\Events\LoanPaymentWasDeleted;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedForStats;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedNew;
use Modules\Payments\Domain\Events\LoanPayoutWasDelivered;
use Modules\Payments\Domain\Events\LoanPayoutWasRefunded;
use Modules\Payments\Domain\Events\LoanWasRepaid;
use Modules\Payments\Domain\Events\RefinancingPaymentWasCreated;
use Modules\Sales\Application\Listeners\ClientStagnator;
use Modules\Sales\Application\Listeners\LoanMetaCreateListener;
use Modules\Sales\Application\Listeners\MarkForVerification;
use Modules\Sales\Application\Listeners\ReportJobsDispatcher;
use Modules\Sales\Application\Listeners\ReturnClientToNew;
use Modules\Sales\Application\Listeners\SendInviteSmsDispatcher;
use Modules\Sales\Domain\Events\ClientWasUpdated;
use Modules\Sales\Domain\Events\DayHasPassedForClient;
use Modules\Sales\Domain\Events\LoanParamsUpdated;
use Modules\Sales\Domain\Events\LoanWasSigned;
use Modules\Sales\Domain\Events\NewClientHasRegistered;
use Modules\Sales\Domain\Events\NewLoanDocsGenerated;
use Modules\Sales\Domain\Events\NewLoanHasArrived;
use Modules\Sales\Domain\Events\NewLoanHasCreatedCommon;
use Modules\ThirdParty\Events\EasyPaySendingHasFinished;
use Modules\ThirdParty\Listeners\CreateInsuranceCertificateListener;
use Modules\ThirdParty\Listeners\MakeLoanOffer;
use Modules\ThirdParty\Listeners\SendEasyPayListener;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        Login::class => [
            LoginListener::class,
        ],
        Failed::class => [
            LogFailedLoginAttempt::class,
        ],

        /**** SALES EVENTS ***/
        NewClientHasRegistered::class => [
            CreateInitialClientStatsListener::class
        ],
        ClientWasUpdated::class => [
            FireBroadcastClientEventsListener::class
        ],

        // always exec when loan created
        NewLoanHasCreatedCommon::class => [
            CreateInitialLoanStatsListener::class,
            UpdateClientStatsOnNewLoanListener::class,
            SetRelationToTasksListener::class,
            SetRelationToReferral::class,
            FireBroadcastLoanEventsListener::class,
        ],
        // exec on loan creation if client is not blocked
        NewLoanHasArrived::class => [
            MarkForVerification::class,
            ReportJobsDispatcher::class,
            ClearOpenedSaleTasks::class,
            CreateDockPackListener::class,
            RefreshSaleAttemptsListener::class,
        ],

        LoanParamsUpdated::class => [
            CreateInitialLoanStatsListener::class,
            CreateDockPackListener::class,
            RefreshSaleAttemptsListener::class,
            FireBroadcastLoanEventsListener::class,
        ],

        LoanWasSigned::class => [
            UpdateLoanStatsOnSignListener::class,
            AutoProcessListener::class
        ],

        /******** DOCUMENT EVENTS ********/
        NewLoanDocsGenerated::class => [
            SendInviteSmsDispatcher::class,
            SendNewLoanDocsEmailListener::class,
        ],

        /***************APPROVAL EVENTS **********/
        LoanWasApproved::class => [
            CreateInsuranceCertificateListener::class,
            LoanMetaCreateListener::class,
            CreatePaymentsFromLoanApprovalListener::class, // for easypay throws EasyPayPaymentWasPreparedForSending
            UpdateClientStatsOnApproveListener::class,
            UpdateLoanStatsOnApproveListener::class,
            ApproveDockPackSender::class,
            RefreshAffiliateReports::class,
            FireBroadcastLoanEventsListener::class,
        ],
        MaximumApproveAttemptsLimitReached::class => [
            CancelLoanListener::class,
        ],

        /******************CANCEL**********************/
        LoanWasCanceled::class => [
            ReturnClientToNew::class,
            RemoveLoanFromBucketListener::class,
            UpdateClientStatsOnCancelListener::class,
            UpdateLoanStatsOnCancelListener::class,
            SendDisapprovedSmsAndEmail::class,
            ClearAllLoanTasksAndTmpRequests::class,
            MakeLoanOffer::class,
            RefreshAffiliateReports::class,
            ResetVeriffAction::class,
            FireBroadcastLoanEventsListener::class,
        ],

        /******************ACTIVATE**********************/
        LoanWasActivated::class => [
            UpdateClientStatsOnActivateListener::class,
            UpdateLoanStatsOnActivateListener::class,
            ClientStagnator::class, // client.new = 0 becomes = 1
            FireBroadcastLoanEventsListener::class,
        ],


        /******************PAYMENT EVENTS ****************/

        // easypay - approval -> outgoing payment
        EasyPayPaymentWasPreparedForSending::class => [
            SendEasyPayListener::class // -> send money to Epay via job -> throws EasyPaySendingHasFinished
        ],
        EasyPaySendingHasFinished::class => [
            ChangeEasyPayStatusAfterSendingListener::class
            // -> if payment done -> status=sent & actviate loan and ccr & -> throws LoanWasActivated
        ],
        LoanPayoutWasRefunded::class => [
            UpdateClientStatsOnPaymentListener::class,
        ],


        // on loan approve with refinance we do a snapshots for incoming closing payments
        RefinancingPaymentWasCreated::class => [
            PaymentSnapshotListener::class,
        ],

        // when outgoing payment go to deliver state OR sent for Easypay
        LoanPayoutWasDelivered::class => [
            RegisterAccountingPayment::class
        ],

        // when manually delete payment
        LoanPaymentWasDeleted::class => [
            RegisterAccountingPayment::class,
        ],
        LoanPaymentAfterDeleted::class => [
            UpdateLoanStatsOnDeletePaymentListener::class,
        ],


        // new income payment flow
        LoanPaymentWasReceivedNew::class => [
            PaymentSnapshotListener::class,
            PaymentDeliveryListener::class,
            CreateIncomingCashTransactionListener::class,
        ],
        LoanPayInWasDelivered::class => [
            RegisterAccountingPayment::class,
            ActualizeRefinanceAmountsListener::class,
        ],
        LoanPaymentWasReceivedForStats::class => [
            UpdateLoanStatsOnPaymentListener::class,
            UpdateClientStatsOnPaymentListener::class,
            RemoveFromBucketTaskListener::class,
        ],
        LoanWasRepaid::class => [
            UpdateLoanStatsOnRepaymentListener::class,
            RemoveLoanFromBucketListener::class,
            RepaidLoanCommunication::class,
            FireBroadcastLoanEventsListener::class
        ],

        /************** COLLECT EVENTS ****************/
        CollectTaskFinished::class => [
            SendEmailAboutWrongPhoneListener::class,
        ],
        LoanBecameJuridical::class => [
            OpenJuridicalCase::class
        ],
        LoanWasExtended::class => [
            UpdateLoanStatsOnExtensionListener::class,
            SendExtendLoanSmsListener::class
        ],

        DueDatesWereReset::class => [
            UpdateLoanStatsOnExtensionListener::class,
            CreateDockPackListener::class,
        ],

        /**************** CashDesk Events *************/
        CashOperationalTransactionSaved::class => [
            GenerateCashDocsListener::class
        ],

        /**************** API EVENTS ******************/
        EasyPayPaymentRequestWasProcessed::class => [
            CreateIncomingPaymentFromEasyPayApiListener::class
        ],

        /**************** HEAD ****************/
        DayHasPassedForClient::class => [
            UpdateClientStatsOnDayPassListener::class
        ],
        MarkedAsClosedJuridicalCase::class => [
            ClosedJuridicalCase::class
        ],


        /**************** Loan fees ****************/
        TaxWasCreated::class => [
            RegisterJuridicalExpense::class
        ],

        /**************** Communication events ****************/
        CommunicationWasCreatedEvent::class => [
            RegisterCommunicationPivot::class
        ],

        /**************** Unclaimed events ****************/
        UnclaimedMoneyWasCreated::class => [
            UnclaimedMoneyListener::class
        ],

        OuterCollectorReportWasCreated::class => [
            SendOuterCollectorDailyReport::class
        ],

        GuarantorWasCreatedEvent::class => [
            CreateClientIfNotExists::class
        ],
        AfterPaymentDistribute::class => [
            CreatePaymentWhenHaveOutstandingAfterExtendLoan::class
        ],
        CustomNoiReportReadyEvent::class => [
            CreateExportForUpdatedLoans::class
        ],
        AffiliateMonthlyReportsIsReady::class => [
            SendAffiliateMonthlyReport::class
        ]
    ];


    public function boot(): void
    {
        parent::boot();
    }
}
