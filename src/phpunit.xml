<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </coverage>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
            <directory suffix="Test.php">./Modules/ThirdParty/Tests/Unit</directory>
            <directory suffix="Test.php">./Modules/Head/Tests/Unit</directory>
            <directory suffix="Test.php">./Modules/Payments/Tests/Unit</directory>
            <directory suffix="Test.php">./Modules/Sales/Tests/Unit</directory>
        </testsuite>
        <testsuite name="Refactored">
            <directory suffix="Test.php">/var/www/Modules/Sales/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Approve/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/CashDesk/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Collect/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Api/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Docs/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Payments/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Admin/Tests</directory>
            <directory suffix="Test.php">/var/www/Modules/Head/Tests</directory>
        </testsuite>
        <testsuite name="AllModules">
            <directory suffix="Test.php">./Modules/*/Tests</directory>
        </testsuite>
    </testsuites>
    <php>
        <ini name="memory_limit" value="2048M"/>
        <!-- Set the environment to testing -->
        <server name="APP_ENV" value="testing"/>
        <!-- Speed up encryption/decryption during tests -->
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="HASHING_ROUNDS" value="4"/>
        <!-- Use in-memory or test-specific cache -->
        <server name="CACHE_DRIVER" value="array"/>
        <!-- Use the test database -->
        <!-- <env name="DB_CONNECTION" value="sqlite"/> -->
        <!-- <env name="DB_DATABASE" value=":memory:"/> -->
        <!-- Email testing -->
        <server name="MAIL_MAILER" value="array"/>
        <!-- Sync queues for testing -->
        <server name="QUEUE_CONNECTION" value="sync"/>
        <!-- Session settings -->
        <server name="SESSION_DRIVER" value="array"/>
        <!-- Disable debugbar or other non-testing packages -->
        <server name="DEBUGBAR_ENABLED" value="false"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>