# GitLab CI/CD Pipeline for Credit Hunter Laravel Application
# This pipeline handles testing, building, and deployment of the application

stages:
  - validate
  - test
  - build
  - deploy

variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  
  # Application variables
  APP_NAME: credit-hunter
  PHP_VERSION: "8.2"
  NODE_VERSION: "18"
  
  # Database configuration for testing
  DB_CONNECTION: pgsql
  DB_HOST: postgres
  DB_PORT: 5432
  DB_DATABASE: credit_hunter_test
  DB_USERNAME: postgres
  DB_PASSWORD: password
  
  # Cache and session
  CACHE_DRIVER: redis
  SESSION_DRIVER: redis
  QUEUE_CONNECTION: redis
  
  # Elasticsearch
  ELASTICSEARCH_HOST: elasticsearch
  ELASTICSEARCH_PORT: 9200

# Cache dependencies between jobs
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - src/vendor/
    - src/node_modules/
    - .composer-cache/

# Services for testing
services:
  - postgres:14-alpine
  - redis:latest
  - elasticsearch:8.14.1

# Job to validate code quality and dependencies
validate:
  stage: validate
  image: php:${PHP_VERSION}-fpm
  before_script:
    - apt-get update && apt-get install -y git unzip libzip-dev libicu-dev libonig-dev libxml2-dev
    - docker-php-ext-install pdo pdo_pgsql zip intl mbstring xml bcmath
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - cd src
    - composer install --prefer-dist --no-interaction --no-progress
  script:
    - cd src
    - composer validate
    - composer audit
    - php artisan --version
    - php -l app/Http/Kernel.php
    - php -l bootstrap/app.php
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Job to install and validate Node.js dependencies
validate_frontend:
  stage: validate
  image: node:${NODE_VERSION}-alpine
  before_script:
    - cd src
    - npm ci
  script:
    - npm audit
    - npm run production --dry-run
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Job to run PHP static analysis
static_analysis:
  stage: validate
  image: php:${PHP_VERSION}-fpm
  before_script:
    - apt-get update && apt-get install -y git unzip libzip-dev libicu-dev libonig-dev libxml2-dev
    - docker-php-ext-install pdo pdo_pgsql zip intl mbstring xml bcmath
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - cd src
    - composer install --prefer-dist --no-interaction --no-progress
  script:
    - cd src
    - vendor/bin/phpstan analyse --memory-limit=2G
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Job to run PHPUnit tests
test_php:
  stage: test
  image: php:${PHP_VERSION}-fpm
  services:
    - postgres:14-alpine
    - redis:latest
    - elasticsearch:8.14.1
  variables:
    POSTGRES_DB: credit_hunter_test
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: password
    POSTGRES_HOST_AUTH_METHOD: trust
    ELASTICSEARCH_HOST: elasticsearch
    ELASTICSEARCH_PORT: 9200
  before_script:
    - apt-get update && apt-get install -y git unzip libzip-dev libicu-dev libonig-dev libxml2-dev
    - docker-php-ext-install pdo pdo_pgsql zip intl mbstring xml bcmath
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - cd src
    - composer install --prefer-dist --no-interaction --no-progress
    - cp .env.example .env.testing
    - php artisan key:generate --env=testing
    - php artisan config:cache --env=testing
    - php artisan migrate --env=testing --force
  script:
    - cd src
    - vendor/bin/phpunit --coverage-text --coverage-report=html
  coverage: '/^\s*Lines:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: src/coverage.xml
    paths:
      - src/tests/coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
