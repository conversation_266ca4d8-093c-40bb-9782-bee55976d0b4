
1. Compile an image
docker-compose build
docker-compose up -d --build --force-recreate
docker-compose build && docker-compose up -d && docker-compose logs -f

2. Run containers in background
docker-compose up -d && docker-compose logs -f
docker-compose up -d
docker-compose down

3. Check if they running
docker-compose ps

4. Check that our file inside the 'app' container
winpty docker-compose exec app ls -l

5. Install composer in container
winpty docker-compose exec app composer install

6. Generate laravel unique key
winpty docker-compose exec app php artisan key:generate

winpty docker-compose exec app npm install & npm run dev
winpty docker-compose exec app npm install & npm run watch

Start Laravel app
7. winpty docker-compose exec app php artisan serve

8. Now go to your browser and access your server’s domain
http://server_domain_or_IP:8000


php artisan --version
php artisan route:list



docker exec -it credit_hunter_postgres sudo -u postgres1 psql
docker exec -it credit_hunter_postgres bash



STOP
---------------------------
docker stop $(docker ps -aq)
docker rm $(docker ps -aq)
docker rmi $(docker images -q)

docker system prune
docker images prune
docker system prune -a -f --volumes
docker image prune -f

docker system df	Show docker disk usage
docker stats $(docker ps --format={{.Names}}) --no-stream

docker stop containerName
docker start containerName






docker container ls
docker inspect 12a468d1ae54
"IPAddress": "**********",


pg: \du - list of users
pg: \l - list of databases



php artisan route:clear
php artisan cache:clear
php artisan config:clear
php artisan view:clear







docker exec myapp-php composer install
docker exec myapp-php php artisan key:generate
docker exec myapp-php php artisan migrate
docker exec myapp-php php artisan passport:install



===========================================================================

CREATE TYPE public.default_statuses AS ENUM ('active', 'disabled');

CREATE TABLE public.administrator
(
    administrator_id SERIAL PRIMARY KEY,
    login character varying(20) COLLATE pg_catalog."default" NOT NULL,
    password character varying(60) COLLATE pg_catalog."default" NOT NULL,
    first_name character varying(20) COLLATE pg_catalog."default" NOT NULL,
    middle_name character varying(20) COLLATE pg_catalog."default" NOT NULL,
    last_name character varying(20) COLLATE pg_catalog."default" NOT NULL,
    phone character varying(15) COLLATE pg_catalog."default",
    email character varying(60) COLLATE pg_catalog."default",
    avatar character varying(200) COLLATE pg_catalog."default",
    created_at timestamp without time zone,
    created_by integer,
    updated_at timestamp without time zone,
    updated_by integer,
    remember_token character varying(100) COLLATE pg_catalog."default",
    is_active smallint default 1,
    is_deleted smallint default 0
);

===========================================================================

php artisan module:make Diagnostic


php artisan module:make-model Administrator Admin


php artisan module:make-migration body_type Diagnostic
php artisan module:make-migration category Diagnostic
php artisan module:make-migration detail Diagnostic
php artisan module:make-migration detail_group Diagnostic

php artisan module:migrate Diagnostic

winpty docker-compose exec app php artisan migrate:rollback --step=4

winpty docker-compose exec app php artisan module:migrate Diagnostic


php artisan module:make-request AdministratorSearchRequest Admin











