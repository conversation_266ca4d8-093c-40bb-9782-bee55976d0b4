[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
# directory=/tmp ; # Add this line if the /var/run directory doesn't exist or lacks write permissions. Can use /tmp if there are issues with /var/run

# ++ Section for creating the UNIX socket ++
[unix_http_server]
file=/var/run/supervisor.sock   ; Path to the socket file
;chmod=0700                 ; Socket file permissions (uncomment and adjust if needed)
;chown=nobody:nogroup        ; Socket file owner (uncomment and adjust if needed)

# ++ Section for enabling the RPC interface ++
[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# ++ Section for configuring the supervisorctl client ++
[supervisorctl]
serverurl=unix:///var/run/supervisor.sock ; Tell supervisorctl how to connect
                                          ; Make sure the path matches 'file' in [unix_http_server]
;username=user              ; If authentication is configured
;password=123               ; If authentication is configured

# -- Programs --
[program:php-fpm]
command=/usr/sbin/php-fpm8.2 -F
user=root
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:laravel_horizon]
command=/usr/bin/php /var/www/artisan horizon
user=user
stderr_logfile=/var/log/supervisor/laravel_horizon.err.log
stdout_logfile=/var/log/supervisor/laravel_horizon.out.log

[program:schedule_worker]
command=/usr/bin/php /var/www/artisan schedule:work
user=user
stderr_logfile=/var/log/supervisor/laravel_schedule.err.log
stdout_logfile=/var/log/supervisor/laravel_schedule.out.log
