FROM ubuntu:22.04

ARG NODE_VERSION=20
ARG POSTGRES_VERSION=14
ARG ACCEPT_EULA=Y
ARG UID

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Europe/Sofia

WORKDIR /var/www

RUN ln -snf /usr/share/zoneinfo/${TZ} /etc/localtime && echo ${TZ} > /etc/timezone

# Install system dependencies, php, composer, postgresql-client
RUN apt-get update \
    && mkdir -p /etc/apt/keyrings \
    && apt-get install -y git gnupg curl ca-certificates zip unzip openssl fswatch ffmpeg apt-transport-https \
    && curl -sS 'https://keyserver.ubuntu.com/pks/lookup?op=get&search=0x14aa40ec0831756756d7f66c4f4ea0aae5267a6c' | gpg --dearmor | tee /etc/apt/keyrings/ppa_ondrej_php.gpg > /dev/null \
    && echo "deb [signed-by=/etc/apt/keyrings/ppa_ondrej_php.gpg] https://ppa.launchpadcontent.net/ondrej/php/ubuntu jammy main" > /etc/apt/sources.list.d/ppa_ondrej_php.list \
    && apt-get update \
    && apt-get install -y php8.2-cli php8.2-fpm php8.2-dev php8.2-pgsql php8.2-gd php8.2-imagick php8.2-exif \
       php8.2-curl php8.2-bz2 php8.2-iconv php8.2-imap php8.2-mbstring php8.2-opcache php8.2-calendar php8.2-xml \
       php8.2-zip php8.2-bcmath php8.2-soap php8.2-intl php8.2-readline php8.2-ldap php8.2-msgpack php8.2-igbinary \
       php8.2-redis php8.2-pcov php8.2-xdebug \
    && curl -sLS https://getcomposer.org/installer | php -- --install-dir=/usr/bin/ --filename=composer \
    && curl -sS https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor | tee /etc/apt/keyrings/pgdg.gpg > /dev/null \
    && echo "deb [signed-by=/etc/apt/keyrings/pgdg.gpg] http://apt.postgresql.org/pub/repos/apt jammy-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    && apt-get update \
    && apt-get install -y postgresql-client-${POSTGRES_VERSION} \
    && apt-get -y autoremove \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_$NODE_VERSION.x | bash - \
    && apt-get update \
    && apt-get install -y nodejs \
    && npm install -g npm \
    && apt-get -y autoremove \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Install additional tools
RUN apt-get update \
    && apt-get install -y sudo gosu supervisor dnsutils mc vim nano gedit wkhtmltopdf \
    && apt-get -y autoremove \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# downgrade openssl TLS for sql nefin connection
#RUN sed -i 's/MinProtocol = TLSv1.2/MinProtocol = TLSv1/' /etc/ssl/openssl.cnf && \
#    sed -i 's/CipherString = DEFAULT@SECLEVEL=2/CipherString = DEFAULT@SECLEVEL=1/' /etc/ssl/openssl.cnf

RUN echo 'MinProtocol = TLSv1' >> /etc/ssl/openssl.cnf && \
    echo 'CipherString = DEFAULT@SECLEVEL=1' >> /etc/ssl/openssl.cnf

RUN setcap "cap_net_bind_service=+ep" /usr/bin/php8.2

## Add user for laravel application
RUN groupadd -g $UID user && useradd -ms /bin/bash -g user -u $UID user

# Copy configs
COPY php.ini /etc/php/8.2/cli/conf.d/99-container.ini
COPY php.ini /etc/php/8.2/fpm/conf.d/99-container.ini
COPY fpm-pool.conf /etc/php/8.2/fpm/pool.d/www.conf
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY start-container /usr/local/bin/start-container
RUN chmod +x /usr/local/bin/start-container

EXPOSE 9000
STOPSIGNAL SIGQUIT

ENTRYPOINT ["start-container"]
