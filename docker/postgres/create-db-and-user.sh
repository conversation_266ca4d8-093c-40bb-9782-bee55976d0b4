#!/bin/bash

# stops the execution of a script if a command or pipeline has an error
set -e

POSTGRES="psql -U ${POSTGRES_DEFAULT_USER}"

echo "Creating user: ${POSTGRES_USER}"
$POSTGRES <<EOSQL
CREATE USER ${POSTGRES_USER} SUPERUSER PASSWORD '${POSTGRES_PASSWORD}';
EOSQL

echo "Creating database: ${POSTGRES_DB}"
$POSTGRES <<EOSQL
CREATE DATABASE ${POSTGRES_DB};
GRANT ALL PRIVILEGES ON DATABASE ${POSTGRES_DB} TO ${POSTGRES_USER};
EOSQL

echo "Creating test database: ${POSTGRES_TEST_DB}"
$POSTGRES <<EOSQL
CREATE DATABASE ${POSTGRES_TEST_DB};
GRANT ALL PRIVILEGES ON DATABASE ${POSTGRES_TEST_DB} TO ${POSTGRES_USER};
EOSQL

#echo "Add password to user: ${POSTGRES_DEFAULT_USER}"
#$POSTGRES <<EOSQL
#ALTER USER ${POSTGRES_DEFAULT_USER} WITH PASSWORD '${POSTGRES_DEFAULT_PASS}';
#EOSQL
