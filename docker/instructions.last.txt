
1. Compile an image
docker-compose build

2. Run containers in background + scan logs
docker-compose up -d && docker-compose logs -f

3. Stop containers
docker-compose down

4. Check if they running
docker-compose ps

5. Now go to your browser and access your server’s domain
http://server_domain_or_IP:8000

6. Postgre set-up
docker exec -it credit_hunter_postgres bash

psql -U postgres
CREATE USER root SUPERUSER PASSWORD 'rootPass';
CREATE DATABASE our_db;
GRANT ALL PRIVILEGES ON DATABASE our_db TO root;
OR
psql -d credit_hunter_db -U root

pg: \du - list of users
pg: \l - list of databases
pg: \dt - list of tables
pg: \d+ administrator


7. Now you can see your db in pgAdmin4
http://127.0.0.1:8080 - <EMAIL>:admin
DB_HOST=postgres or docker container ls, docker inspect , "IPAddress"
DB_PORT=5432
DB_DATABASE=our_db
DB_USERNAME=root
DB_PASSWORD=rootPass

8. Create default login/register/logout/forgotPasw

php artisan ui vue --auth

+

npm install & npm run dev


OR if errors comes out:

npm install
npm i fsevents@latest -f --save-optional
npm audit fix --force

OR

npm install --global cross-env
npm install --no-bin-links




if ON FORM SUBMIN error 419 - Page hui
'driver' => env('SESSION_DRIVER', 'cookie'),



RUN MIGRATIONS
winpty docker-compose exec app php artisan migrate
winpty docker-compose exec app php artisan migrate:rollback



composer dump-autoload


winpty docker-compose exec app composer update


===============================================================================
Not mandatory
===============================================================================
-. Check that our files inside the 'app' container
docker-compose exec app ls -l

-. Install composer in container
docker-compose exec app composer install

-. Generate laravel unique key
docker-compose exec app php artisan key:generate

-. Start Laravel app
docker-compose exec app php artisan serve




php artisan --version
php artisan route:list



docker exec -it credit_hunter_postgres sudo -u postgres1 psql
docker exec -it credit_hunter_postgres bash




LARAVEL CLEAN
---------------------------
php artisan route:clear
php artisan cache:clear
php artisan config:clear
php artisan view:clear

winpty docker-compose exec app php artisan view:clear
winpty docker-compose exec app php artisan cache:clear
winpty docker-compose exec app php artisan config:clear


DOCKER STOP
---------------------------
docker stop $(docker ps -aq)
docker rm $(docker ps -aq)
docker rmi $(docker images -q)
docker stop containerName
docker start containerName


DOCKER SYSTEM CLEAN
----------------------------
docker system prune -a -f --volumes
docker image prune -f


DOCKER INFO
-----------------------------
docker system df	Show docker disk usage
docker stats $(docker ps --format={{.Names}}) --no-stream


DOCKER CONTAINER INFO
-----------------------------
docker container ls
docker inspect 12a468d1ae54
"IPAddress": "**********",


DOCKER INSIDE CONTAINER RUN
-----------------------------
docker exec myapp-php composer install
docker exec myapp-php php artisan key:generate
docker exec myapp-php php artisan migrate
docker exec myapp-php php artisan passport:install



AFTER ADDING NEW CLASSES
========================
winpty docker-compose exec app composer dump-autoload
winpty docker-compose exec app php artisan config:clear
winpty docker-compose exec app php artisan cache:clear


SSL GENERATE
=========================
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /var/www/credit-hunter/docker/nginx/nginx-selfsigned.key -out /var/www/credit-hunter/docker/nginx/nginx-selfsigned.crt

OR

sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout nginx-selfsigned.key -out nginx-selfsigned.crt
sudo openssl dhparam -out dhparam.pem 2048

docker-compose exec nginx sh
ping x
vi /etc/hosts
************** regix-service.egov.bg


COMPOSER WITH NO MEMORY LIMITATION
===========================
COMPOSER_MEMORY_LIMIT=-1 composer update

docker-compose exec app composer dump-autoload
docker-compose exec app composer update
docker-compose exec app COMPOSER_MEMORY_LIMIT=-1 composer update
