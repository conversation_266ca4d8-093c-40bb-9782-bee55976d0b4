### Credit Hunter

The CRM/ERP system of StikCredit

#### Manual installation:

1. <NAME_EMAIL>:stikcredit-dev-team/credit-hunter.git --branch=dev
2. cd credit-hunter
3. Ask your lead to provide you a `.env` file and put it into ./credit-hunter
4. docker-compose build
5. docker-compose up -d && docker-compose logs -f
6. docker-compose exec app php artisan migrate
7. docker-compose exec app php artisan db:seed
8. docker-compose exec app composer install
9. docker-compose exec app npm install & npm run dev (Optional, if Vue.js integrated)
10. Link: http://127.0.0.1:8000
11. Add to vhost file(/etc/vhosts) line: ********** credit-hunter.local
12. If not working take right IP with command:
    `docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' credit_hunter_nginx`

#### Auto installation:

1. <NAME_EMAIL>:stikcredit-dev-team/credit-hunter.git --branch=dev
2. cd credit-hunter
4. Ask your lead to provide you a `.env` file and put it into ./credit-hunter
5. Then run `./run/init_project.sh`(will ask for sudo pass) OR `./run/init_project.sh win` for Windows env
6. Link: http://127.0.0.1:8000

#### Usage plugins:

1.[Laravel form builder](https://kristijanhusak.github.io/laravel-form-builder/overview/installation.html)

2.[Laravel Permissions](https://spatie.be/docs/laravel-permission/v5/introduction)

3.[Laravel Data](https://spatie.be/docs/laravel-data/v2/introduction)

#### Help commands:

php artisan krlove:generate:model TerminalLog --output-path=src/Modules/CashDesk/Models
--namespace=Modules\CashDesk\Models
php artisan ide-helper:models "\Modules\Common\Models\FiscalReceipt"