x-environment: &environment
  VOLUME_PATH_PG: ${VOLUME_PATH_PG:-docker/volume/pg}
  VOLUME_PATH_REDIS: ${VOLUME_PATH_REDIS:-docker/volume/redis}
  TZ: ${APP_TZ:-Europe/Sofia}
  APP_USER: ${APP_USER:-laravel}
  APP_USER_ID: ${APP_USER_ID:-1000}

services:
  app:
    environment:
      <<: *environment
    # can be used with docker-compose.override.yml
    #      XDEBUG_MODE: debug,develop
    #      XDEBUG_CONFIG: discover_client_host=1
    #      PHP_IDE_CONFIG: serverName=localhost
    build:
      args:
        UID: ${APP_USER_ID}
      context: ./docker/app
      dockerfile: Dockerfile
    image: ${APP_NAME}_image
    container_name: ${APP_NAME}_app
    restart: always
    volumes:
      - ~/.ssh:/home/<USER>/.ssh:ro
      - ./src:/var/www
      - ./src/${SELF}:/var/www/.env
    depends_on:
      - postgres
      - redis
      - elasticsearch
    networks:
      - network

  postgres:
    image: postgres:14-alpine
    container_name: ${APP_NAME}_postgres
    restart: always
    environment:
      <<: *environment
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DEFAULT_USER: ${POSTGRES_DEFAULT_USER}
      POSTGRES_DEFAULT_PASS: ${POSTGRES_DEFAULT_PASS}
      POSTGRES_TEST_DB: ${POSTGRES_TEST_DB}
    volumes:
      - ${VOLUME_PATH_PG}:/var/lib/postgresql/data
      - ./docker/postgres:/docker-entrypoint-initdb.d
    ports:
      - ${DB_EXTERNAL_PORT}:${DB_PORT}
    networks:
      - network

  nginx:
    image: nginx:alpine
    container_name: ${APP_NAME}_nginx
    restart: always
    environment:
      <<: *environment
    ports:
      - ${NGINX_PORT}:80
      - ${NGINX_PORT_SSL}:443
    volumes:
      - ./src:/var/www
      - ./docker/nginx/certs:/etc/ssl/certs
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - app
    networks:
      - network

  redis:
    environment:
      <<: *environment
    image: redis:latest
    container_name: ${APP_NAME}_redis
    restart: always
    ports:
      - ${REDIS_EXTERNAL_PORT}:${REDIS_PORT}
    volumes:
      - ${VOLUME_PATH_REDIS}:/data
    networks:
      - network

  elasticsearch:
    image: elasticsearch:8.14.1
    container_name: ${APP_NAME}_es
    restart: unless-stopped
    environment:
      cluster.name: docker-cluster
      node.name: ${APP_NAME}_es
      discovery.type: single-node
      xpack.security.http.ssl.enabled: null
      xpack.security.transport.ssl.enabled: null
      ES_JAVA_OPTS: "-Xms512m -Xmx512m"
      ELASTIC_PASSWORD: ${ELASTIC_BOOT_PASSWORD:-elastic}
    volumes:
      - elasticsearch:/usr/share/elasticsearch/data:Z
    ports:
      - 9200:9200
    networks:
      - network

networks:
  network:
    name: ${APP_NAME}_network
    driver: bridge

volumes:
  pg_data:
    name: ${APP_NAME}_pg_volume
  pg_admin_data:
    name: ${APP_NAME}_pg_admin_volume
  elasticsearch:
    name: ${APP_NAME}_elasticsearch
